pipeline {
    agent {
        label 'agent_node'
    }
    environment {
        DOCKER_CREDENTIALS_ID = credentials('PAT_DOCKERHUB')  
        DOCKER_IMAGE_NAME = 'front-app'
        DOCKER_TAG = "test"  
        DOCKER_IMAGE_TAG = "*************:5003"
        NEXUS_CREDENTIALS_ID = "credential_nexus"
        NEXUS_DOCKER_REGISTRY = "http://*************:5003"
        
        BRANCHE_DEV = 'origin/dev_v2'
        BRANCHE_PROD = 'origin/paris'
    }
    
    stages {
        stage('Checkout') {
            steps {
                checkout scm
                echo "Branche active : ${env.GIT_BRANCH}"
            }
        }
    }
