# thank

## Project setup
```
<PERSON><PERSON> générer le .env en local ou en PreProd:
python3 generate.env.py front_tb_vue Marseille `USERNAME` `PASSWORD`


Pour générer le .env en Prod:
python3 generate.env.py front_tb_vue Paris `USERNAME` `PASSWORD`

npm install
```

### Compiles and hot-reloads for development
```
npm run start
```

### Compiles and minifies for production
```
npm run build
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).
