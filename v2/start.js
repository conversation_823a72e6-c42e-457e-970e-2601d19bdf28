/**
 * Script de démarrage pour l'application Vue
 * Ce script permet de démarrer l'application Vue en mode développement
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

//console.log("Démarrage de l'application Vue...");

// Recherche du chemin de vue-cli-service
const findVueCliService = () => {
  const possiblePaths = [
    path.join(__dirname, 'node_modules', '.bin', 'vue-cli-service'),
    path.join(__dirname, 'node_modules', '.bin', 'vue-cli-service.cmd'),
    path.join(__dirname, 'node_modules', '.bin', 'vue-cli-service.ps1'),
  ];

  for (const binPath of possiblePaths) {
    if (fs.existsSync(binPath)) {
      //console.log('Fichier trouvé:', binPath);
      return binPath;
    }
  }

  return null;
};

const vueCliServicePath = findVueCliService();

if (!vueCliServicePath) {
  //console.error('Erreur: vue-cli-service non trouvé dans node_modules/.bin');
  process.exit(1);
}

// Détermine la commande à exécuter en fonction de l'extension du fichier
let command;
let args;

if (vueCliServicePath.endsWith('.ps1')) {
  command = 'powershell';
  args = ['-ExecutionPolicy', 'Bypass', '-File', vueCliServicePath, 'serve'];
} else if (vueCliServicePath.endsWith('.cmd')) {
  command = vueCliServicePath;
  args = ['serve'];
} else {
  command = 'node';
  args = [vueCliServicePath, 'serve'];
}

// Lance le processus
const child = spawn(command, args, {
  stdio: 'inherit',
  shell: true,
  cwd: __dirname, // Utiliser le répertoire courant comme répertoire de travail
});

child.on('error', (error) => {
  //console.error("Erreur lors du démarrage de l'application:", error);
});

child.on('close', (code) => {
  if (code !== 0) {
    //console.error(`L'application s'est arrêtée avec le code d'erreur ${code}`);
  }
});
