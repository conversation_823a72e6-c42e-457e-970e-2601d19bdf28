<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test de la fonctionnalité de recherche</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .test-case h3 {
            margin-top: 0;
            color: #333;
        }
        .test-link {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px 0;
        }
        .test-link:hover {
            background: #0056b3;
        }
        .description {
            color: #666;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Test de la fonctionnalité de recherche</h1>
    <p>Cette page permet de tester les différents scénarios de recherche pour vérifier que les champs métier et ville fonctionnent ensemble.</p>

    <div class="test-case">
        <h3>Test 1: Recherche par métier uniquement</h3>
        <div class="description">Recherche avec seulement le champ métier rempli</div>
        <a href="http://localhost:8080/recherche?title=développeur" class="test-link" target="_blank">
            Rechercher "développeur"
        </a>
    </div>

    <div class="test-case">
        <h3>Test 2: Recherche par ville uniquement</h3>
        <div class="description">Recherche avec seulement le champ ville rempli</div>
        <a href="http://localhost:8080/recherche?ville=Paris" class="test-link" target="_blank">
            Rechercher à "Paris"
        </a>
    </div>

    <div class="test-case">
        <h3>Test 3: Recherche par métier ET ville</h3>
        <div class="description">Recherche avec les deux champs remplis - C'est le test principal</div>
        <a href="http://localhost:8080/recherche?title=développeur&ville=Paris" class="test-link" target="_blank">
            Rechercher "développeur" à "Paris"
        </a>
    </div>

    <div class="test-case">
        <h3>Test 4: Recherche complexe avec plusieurs paramètres</h3>
        <div class="description">Recherche avec métier, ville et filtres</div>
        <a href="http://localhost:8080/recherche?title=ingénieur&ville=Lyon&contract=CDI" class="test-link" target="_blank">
            Rechercher "ingénieur" à "Lyon" en CDI
        </a>
    </div>

    <div class="test-case">
        <h3>Test 5: Page de recherche vide</h3>
        <div class="description">Page de recherche sans paramètres</div>
        <a href="http://localhost:8080/recherche" class="test-link" target="_blank">
            Page de recherche vide
        </a>
    </div>

    <div class="test-case">
        <h3>Test 6: Recherche de candidats (pour recruteurs)</h3>
        <div class="description">Test de la recherche de candidats avec métier et ville</div>
        <a href="http://localhost:8080/recherche-candidats?recherche=développeur&ville=Paris" class="test-link" target="_blank">
            Rechercher candidats "développeur" à "Paris"
        </a>
    </div>

    <h2>Instructions de test</h2>
    <ol>
        <li>Cliquez sur chaque lien de test</li>
        <li>Vérifiez que les champs de recherche sont pré-remplis avec les bonnes valeurs</li>
        <li>Essayez de modifier les champs et de relancer une recherche</li>
        <li>Vérifiez que l'URL se met à jour correctement</li>
        <li>Confirmez que les résultats de recherche s'affichent</li>
    </ol>

    <h2>Problème résolu</h2>
    <p>✅ <strong>Avant la correction :</strong> Il était impossible de rechercher avec un métier ET une ville en même temps.</p>
    <p>✅ <strong>Après la correction :</strong> Les deux champs fonctionnent ensemble et les paramètres sont correctement transmis dans l'URL.</p>
</body>
</html>
