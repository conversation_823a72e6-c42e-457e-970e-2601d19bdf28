#!/bin/bash

to_kebab_case() {
  echo "$1" | sed -E 's/([a-z0-9])([A-Z])/\1-\2/g' | tr '[:upper:]' '[:lower:]'
}

to_camel_case() {
  echo "$1" | sed -E 's/(^|-)([a-z])/\U\2/g'
}

echo "Quelle type de page ?"
echo "1) Candidate"
echo "2) Recruiter"
echo "3) All"
read -p "Opción: " option

case $option in
  1) directory="candidate" ;;
  2) directory="recruiter" ;;
  3) directory="all" ;;
  *) echo "Invalid"; exit 1 ;;
esac

read -p "Nom de la page (use camelCase: pageName): " pageName

kebabCaseName=$(to_kebab_case "$pageName")
camelCaseName=$(to_camel_case "$pageName")

mkdir -p "./src/views/$directory/$kebabCaseName"

filePath="./src/views/$directory/$kebabCaseName/$camelCaseName.vue"

cat > "$filePath" <<EOF
<template>
  <main class="container">
    
  </main>
</template>

<script>
export default {
  name: '${camelCaseName}',
  components: {
    CandidatureCard,
    SectionPreview,
    NewsCardMini,
  },
  props: {
    user: {
      type: Object,
      required: true,
    },
  },
}
</script>

<style scoped>

</style>
EOF

echo "Page ajoute en : $filePath"
