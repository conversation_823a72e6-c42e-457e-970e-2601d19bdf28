<template>
  <div class="video-container" ref="videoContainer">
    <div>
      <video
        ref="remoteVideo"
        autoplay
        playsinline
        class="webcam-video remote-video"
      ></video>

      <!-- Message d'état de l'appel - affiché uniquement si callStatus n'est pas vide -->
      <div v-if="callStatus && callStatus.trim() !== ''" class="call-status-message">
        {{ callStatus }}
      </div>
    </div>
    <audio ref="audio" autoplay muted playsinline class="webcam-video"></audio>
    <button
      class="fullscreen-button"
      v-if="!isFullscreen"
      @click="toggleFullscreen"
    >
      <img src="../../../assets/icons/full-screen.svg" alt="Agrandir" />
    </button>

    <!-- Quitter le plein écran -->
    <button
      class="exit-fullscreen-button"
      v-if="isFullscreen"
      @click="toggleFullscreen"
    >
      <img
        src="../../../assets/icons/small-screen.svg"
        alt="Sortir du plein écran"
      />
    </button>

    <div class="video-controls">
      <button @click="showSettings" class="settings-button">
        <img src="../../../assets/icons/setting-icon.svg" alt="Paramètres" />
      </button>
      <!-- button @click="toggleVideo">
        <img src="../../../assets/icons/cut-video.svg" alt="Couper la vidéo" />
      </button-->
      <button @click="toggleAudio" :class="{ 'muted-button': isAudioMuted }">
        <img
          src="../../../assets/icons/mute-the-microphone.svg"
          alt="Couper le micro"
        />
      </button>
      <button @click="end_video_call">
        <img src="../../../assets/icons/to-hang-up.svg" alt="Raccrocher" />
      </button>
    </div>
  </div>

  <!-- Section des paramètres Vidéo et Audio -->
  <modal
    :isVisible="isSettingsVisible"
    @close="isSettingsVisible = false"
    @close-alert-panel="isModalVisible = false"
  >
    <h2>Paramètres</h2>
    <div class="flex">
      <label for="videoQuality">Qualité Vidéo:</label>
      <select id="videoQuality" v-model="videoQuality">
        <option value="360p">360p</option>
        <option value="720p">720p</option>
        <option value="1080p">1080p</option>
      </select>
      <label for="webcamSelect">Webcam:</label>
      <select id="webcamSelect" v-model="selectedWebcam" @change="changeWebcam">
        <option
          v-for="webcam in webcams"
          :key="webcam.deviceId"
          :value="webcam.deviceId"
        >
          {{ webcam.label || 'Webcam inconnue' }}
        </option>
      </select>
    </div>

    <!-- Paramètres Audio -->
    <div>
      <label for="microphoneSelect">Microphone:</label>
      <select
        id="microphoneSelect"
        v-model="selectedMicrophone"
        @change="changeMicro"
      >
        <option
          v-for="micro in micros"
          :key="micro.deviceId"
          :value="micro.deviceId"
        >
          {{ micro.label || 'Microphone inconnu' }}
        </option>
      </select>
      <label for="speakerSelect">Haut-parleur:</label>
      <select v-model="selectedSpeakerId" @change="changeSpeaker">
        <option
          v-for="speaker in speakers"
          :key="speaker.deviceId"
          :value="speaker.deviceId"
        >
          {{ speaker.label || 'Haut-parleur ' + (index + 1) }}
        </option>
      </select>
    </div>
  </modal>
</template>
<script>
  import Modal from '@/components/modal/video-settings/video-settings.vue';
  export default {
    components: {
      Modal,
    },
    props: {
      stream: { type: Object, required: true },
      remoteStream: { type: Object, required: false }, // nouvelle prop
    },

    data() {
      return {
        currentStream: null,
        isFullscreen: false,
        selectedWebcam: null,
        webcams: [],
        micros: [],
        speakers: [],
        audioVolume: 50,
        isSettingsVisible: false,
        selectedSpeaker: null,
        videoQuality: '720p',
        selectedMicrophone: null,
        callStatus: 'Appel vocal en cours...', // Message d'état de l'appel
        isAudioMuted: false, // État de l'audio (muet ou non)
      };
    },
    mounted() {
      this.currentStream = this.stream;
      this.getAvailableWebcams();
      this.getMicrophones();
      this.getSpeakers();

      // Initialiser l'état du bouton audio en fonction de l'état actuel du flux
      if (this.stream) {
        const audioTrack = this.stream.getAudioTracks()[0];
        if (audioTrack) {
          this.isAudioMuted = !audioTrack.enabled;
        }
      }

      this.$nextTick(() => {
        const audioEl = this.$refs.audio;
        if (this.remoteStream && audioEl) {
          audioEl.srcObject = this.remoteStream;
          audioEl.volume = 1.0;
          audioEl.muted = false;
          //console.log('[APPEL AUDIO] remoteStream initial injecté dans l’élément audio');

          // ✅ Lecture immédiate
          audioEl.play().then(() => {
            audioEl.muted = false;
            //console.log('[APPEL AUDIO] Lecture du flux distant démarrée (init)');
          }).catch((err) => {
            //console.error('[APPEL AUDIO] Erreur de lecture (init):', err);
          });
        }
      });
    },
    methods: {
      /**
       * Met à jour le message d'état de l'appel
       * @param {string} status - Le nouveau message d'état
       */
      updateCallStatus(status) {
        this.callStatus = status;
        //console.log('%c[APPEL] Mise à jour du statut de l\'appel vocal:', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;', status);
      },

      end_video_call() {
        //console.log('%c[APPEL] Fin de l\'appel vocal', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

        // Mettre à jour le message d'état
        this.updateCallStatus('Fin de l\'appel vocal...');

        this.$emit('end_video_call');
      },
      getWebcamRef() {
        return this.$refs.webcam;
      },
      getRemoteVideoRef() {
        return this.$refs.remoteVideo;
      },
      getRemoteAudioRef() {
        return this.$refs.audio;
      },
      toggleVideo() {
        if (this.stream) {
          const videoTrack = this.stream.getVideoTracks()[0];
          if (videoTrack) {
            videoTrack.enabled = !videoTrack.enabled;
            //console.log(
            //  videoTrack.enabled
            //    ? 'Activación de la webcam.'
            //    : 'Désactivation de la webcam.'
            //);
          }
        } else {
          //console.log('No hay stream de video disponible');
        }
      },
      toggleAudio() {
        if (this.stream) {
          const audioTrack = this.stream.getAudioTracks()[0];
          if (audioTrack) {
            audioTrack.enabled = !audioTrack.enabled;
            // Mettre à jour l'état de l'audio
            this.isAudioMuted = !audioTrack.enabled;
            //console.log(
            //  audioTrack.enabled
            //    ? 'Activation du micro.'
            //    : 'Désactivation du micro.'
            //);
          }
        } else {
          //console.log('No hay stream de audio disponible');
        }
      },

      async getSpeakers() {
        try {
          const devices = await navigator.mediaDevices.enumerateDevices();
          this.speakers = devices.filter(
            (device) => device.kind === 'audiooutput'
          );
          //console.log(this.speakers);

          if (this.speakers.length === 0) {
            toaster.showErrorPopup('Aucun haut-parleur détecté.');
          } else {
            this.selectedSpeakerId = this.speakers[0].deviceId;
          }
        } catch (error) {
          //console.error(
          //  'Erreur lors de la récupération des haut-parleurs : ',
          //  error
          //);
          toaster.showErrorPopup(
            'Erreur lors de la récupération des haut-parleurs.'
          );
        }
      },
      async getMicrophones() {
        try {
          const devices = await navigator.mediaDevices.enumerateDevices();
          this.micros = devices.filter(
            (device) => device.kind === 'audioinput'
          );
          if (this.micros.length > 0) {
            this.selectedMicrophone = this.micros[0].deviceId;
            //console.log('here', this.selectedMicrophone);
          }
        } catch (error) {
          //console.error(
          //  'Erreur lors de la récupération des microphones:',
          //  error
          //);
        }
      },
      async getAvailableWebcams() {
        try {
          const devices = await navigator.mediaDevices.enumerateDevices();
          this.webcams = devices.filter(
            (device) => device.kind === 'videoinput'
          );
          //console.log(this.webcams);

          if (this.webcams.length === 0) {
            toaster.showErrorPopup('Aucune webcam détectée.');
          }

          if (this.webcams.length > 0) {
            this.selectedWebcam = this.webcams[0].deviceId;
          }
        } catch (error) {
          //console.error('Erreur lors de la récupération des webcams : ', error);
          toaster.showErrorPopup('Erreur lors de la récupération des webcams.');
        }
      },
      async changeSpeaker() {
        try {
          const audioElement = this.$refs.audio;

          if (!audioElement) {
            throw new Error("L'élément audio est introuvable.");
          }

          if (!this.selectedSpeakerId) {
            throw new Error('Aucun haut-parleur sélectionné.');
          }

          await audioElement.setSinkId(this.selectedSpeakerId);
          //console.log('Haut-parleur changé avec succès !');
        } catch (err) {
          //console.error('Erreur lors du changement de haut-parleur :', err);
          toaster.showErrorPopup(
            'Erreur lors du changement de haut-parleur : ' + err.message
          );
        }
      },
      // TODO : verifier si les fonction changeMicro et changeWebcam marchent car l'objet stream est dans le parent et on ne peut pas le modifier depuis ici. peut-être c'est mieux faire un emit et le gère dans le pere.
      // changer le object this.currentStream pourrait n'appliquer pas les changements au objet stream du composant parent, donc le emit pourrait être plus efficace
      async changeMicro() {
        try {
          if (this.currentStream) {
            const audioTracks = this.currentStream.getAudioTracks();
            audioTracks.forEach((track) => track.stop());
          }
          const newStream = await navigator.mediaDevices.getUserMedia({
            audio: {
              deviceId: this.selectedMicrophone
                ? { exact: this.selectedMicrophone }
                : undefined,
            },
            video: true,
          });
          this.currentStream = newStream;

          const webcamElement = this.$refs.webcam;
          if (webcamElement) {
            webcamElement.srcObject = newStream;
          }
        } catch (err) {
          //console.error('Erreur lors du changement de microphone : ', err);
          toaster.showErrorPopup('Erreur lors du changement de microphone.');
        }
      },
      async changeWebcam() {
        try {
          if (this.currentStream) {
            this.currentStream.getTracks().forEach((track) => track.stop());
          }

          const constraints = {
            video: {
              deviceId: { exact: this.selectedWebcam },
            },
            audio: true,
          };

          const newStream =
            await navigator.mediaDevices.getUserMedia(constraints);
          this.currentStream = newStream;

          this.$nextTick(() => {
            const webcamElement = this.$refs.webcam;
            if (webcamElement) {
              webcamElement.srcObject = newStream;
            } else {
              //console.error('Élément webcam non trouvé dans $refs.');
            }
          });
        } catch (err) {
          //console.error('Erreur lors du changement de webcam : ', err);
          toaster.showErrorPopup('Impossible de changer la webcam.');
        }
      },
      saveSettings() {
        //console.log('Paramètres sauvegardés :', {
        //  videoQuality: this.videoQuality,
        //  selectedWebcam: this.selectedWebcam,
        //  selectedMicrophone: this.selectedMicrophone,
        //  audioVolume: this.audioVolume,
        //});

        this.isSettingsVisible = false;s
      },
      toggleFullscreen() {
        const voiceWindow = this.$refs.videoContainer;

        if (!this.isFullscreen) {
          if (voiceWindow.requestFullscreen) {
            voiceWindow.requestFullscreen();
          } else if (voiceWindow.webkitRequestFullscreen) {
            voiceWindow.webkitRequestFullscreen();
          } else if (voiceWindow.msRequestFullscreen) {
            voiceWindow.msRequestFullscreen();
          }
        } else {
          if (document.exitFullscreen) {
            document.exitFullscreen();
          } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
          } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
          }
        }

        this.isFullscreen = !this.isFullscreen;
      },
      showSettings() {
        this.isSettingsVisible = !this.isSettingsVisible;
      },
    },

    watch: {
    remoteStream(newRemoteStream) {
      //console.log('[APPEL AUDIO] remoteStream mis à jour:', newRemoteStream);
      //console.log('[APPEL AUDIO] AudioTracks:', newRemoteStream?.getAudioTracks());

      setTimeout(() => {
        this.$nextTick(() => {
          const audioEl = this.$refs.audio;
          if (audioEl && newRemoteStream) {
            audioEl.volume = 1.0;
            audioEl.muted = false;
            audioEl.srcObject = newRemoteStream;
            //console.log('%c[APPEL AUDIO] Flux distant injecté dans l’élément audio (avec delay)', 'color: limegreen');

            audioEl.play()
              .then(() => {
                audioEl.muted = false;
                //console.log('[APPEL AUDIO] Lecture du flux distant démarrée (après delay)');
              })
              .catch((err) => {
                //console.error('[APPEL AUDIO] Erreur lecture audio (delay):', err);
              });
          }
        });
      }, 150); // Délai de sécurité pour s'assurer que le stream est prêt
    }
  }




  };
</script>

<style scoped>
  .flex {
    display: flex;
    flex-direction: column;
  }
  .exit-fullscreen-button {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    cursor: pointer;
  }
  .exit-fullscreen-button img {
    width: 33px;
    height: 33pxx;
  }

  .fullscreen-button {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    cursor: pointer;
  }

  .fullscreen-button img {
    width: 33px;
    height: 33px;
  }

  .video-container {
    display: flex;
    flex-direction: column;
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  .remote-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .local-video {
    position: absolute;
    top: 20px;
    left: 20px;
    max-width: 200px;
    max-height: 150px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid white;
    z-index: 1;
  }
  .video-controls button:hover {
    background-color: #33363a;
  }
  .video-controls button {
    padding: 6px 12px;
    border: none;
    border-radius: 5px;
    background-color: #26282b;
    color: #333;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }
  /* Style pour les boutons en état muet */
  .video-controls button.muted-button {
    background-color: #e74c3c; /* Rouge pour indiquer que c'est muet */
  }
  .video-controls button.muted-button:hover {
    background-color: #c0392b; /* Rouge plus foncé au survol */
  }
  .video-controls {
    position: relative;
    bottom: 10px;
    display: flex;
    gap: 10px;
    background: rgb(38 40 43);
    padding: 6px;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    align-items: center;
    justify-content: center;
  }

  .webcam-on {
    display: grid;
    grid-template-columns: 1fr 1fr !important;
    gap: 24px;
  }
  .webcam-video {
    border-radius: 8px;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* Style pour le message d'état de l'appel */
  .call-status-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 18px;
    font-weight: bold;
    z-index: 10;
    text-align: center;
    max-width: 80%;
    display: none;
  }

  audio {
  display: block;
  width: 1px;
  height: 1px;
  opacity: 0;
  }

</style>
