<template>
  <!-- chat history section -->
  <section class="messaging-chat-history-section" :class="themeClass">
    <!-- search bar -->

    <!-- chat history list -->
    <v-list class="messaging-chat-history-list border-radius-15">
      <h6 class="messaging-chat-history-list-title">Chats IA</h6>
      <div class="search-bar-container" :class="themeClass">
        <input
          placeholder="Recherche"
          v-model="searchConversationValue"
          @keyup.enter="handleSearchConversation"
        />
        <v-icon
          icon="mdi-magnify"
          class="border-radius-5 cursor-pointer"
          @click="handleSearchConversation"
        ></v-icon>
      </div>

      <div
        class="messaging-add-conv-btn"
        :class="isSendingMessage ? 'opacity-btn' : ''"
      >
        <button :disabled="isSendingMessage" @click="createNewConversation">
          Nouvelle conversation
        </button>
      </div>
      <!-- conversation list -->
      <v-list-item
        v-for="conversation in allConversations"
        :key="conversation.conversation_id"
        :title="conversation.titre"
        @click="selectConversation(conversation)"
        :active="
          conversation.conversation_id === selectedConversation.conversation_id
        "
      >
        <template v-slot:prepend>
          <img
            src="@/assets/icons/tb-logo-borderblack.svg"
            alt="Logo Thanks Boss"
          />
        </template>
      </v-list-item>
    </v-list>
  </section>

  <!-- conversation section -->
  <section
    class="messaging-conversation-section border-radius-15"
    :class="themeClass"
  >
    <div
      class="messaging-conversation-section-title-container"
      :class="themeClass"
    >
      <div class="messaging-conversation-section-title">
        <img
          src="@/assets/icons/tb-logo-borderblack.svg"
          alt="logo thanks-boss"
        />
        <div v-if="!titleChanging" class="d-flex">
          <p v-if="!titleChanging">
            IA Thanks-Boss - {{ selectedConversation.titre }}
          </p>
          <img
            v-if="!titleChanging"
            src="@/assets/icons/pen.svg"
            class="cursor-pointer"
            @click="handleTitleState"
            alt="Icône d'édition de titre"
          />
        </div>
        <div v-else class="d-flex a-center">
          <input v-model="selectedConversation.titre" />
          <img
            src="@/assets/icons/validate.svg"
            class="cursor-pointer"
            @click="changeTitle"
            alt="Icône de validation"
          />
        </div>
      </div>
      <div class="actions-container">
        <!-- Bouton pour changer le thème -->
        <button class="theme-toggle-button" @click="toggleTheme">
          <v-icon
            :icon="isLightTheme ? 'mdi-weather-sunny' : 'mdi-weather-night'"
          />
        </button>
        <!-- Bouton des options -->
        <button class="option-button" @click="optionMenuIsOpened ^= 1">
          <v-icon icon="mdi-dots-horizontal" class="cursor-pointer" />
        </button>
      </div>
    </div>
    <!-- messages of user and ia -->
    <div ref="chatSection" class="messaging-conversation-list-container">
      <div v-for="message in selectedConversation.messages" :key="message.id">
        <MessageBubbleUser
          v-if="message.message_applicant"
          :message="message.message_applicant"
          :fileName="message.fileName"
        />

        <MessageBubbleReceived
          v-if="message.message_Mc"
          :message="message.message_Mc"
          :messageId="message.id"
          :detectedClass="message.detected_class"
          :jobOffers="message.recommended_job_offers"
        />
      </div>
      <MessageBubbleReceived
        v-if="hasStreamMessage && !isStreamMessageSaved"
        :key="'stream-' + streamMessage.length + '-' + streamMessageText.length"
        :message="streamMessage"
      />
    </div>

    <!-- input box -->
    <div
      class="messaging-conversation-input-container border-radius-10"
      :class="themeClass"
    >
      <!-- icons -->
      <div class="messaging-conversation-input-icons">
        <div class="messaging-conversation-input-icons-element">
          <img
            src="@/assets/icons/icon-file-yellow.svg"
            class="cursor-pointer"
            @click="openFileInput"
            alt="Icône de fichier"
          />
          <input
            ref="fileInput"
            type="file"
            class="d-none"
            @change="handleFileInput"
            accept="application/pdf"
          />
          <!-- icon du pdf si ajouté -->
          <img
            v-if="isPDF"
            src="@/assets/chatbot/PDF.svg"
            class="pdf-icon"
            alt="PDF Icon"
            @click="openPDF"
          />
        </div>

        <!-- input field -->
        <CustomSelect
          v-model="internalUserMessage"
          :options="predefinedMessages"
          placeholder="Saisis ton message"
          class="messaging-conversation-input-box border-radius-2"
          @enter="sendMessage"
        />

        <div class="messaging-conversation-input-icons-element">
          <v-progress-circular
            indeterminate
            color="var(--primary-1)"
            v-if="isSendingMessage"
          ></v-progress-circular>
          <img
            v-else
            src="@/assets/icons/icon-send-yellow.svg"
            class="cursor-pointer"
            @click="sendMessage"
            alt="Icône d'envoi de message"
          />
        </div>
      </div>
    </div>
  </section>

  <OptionsMenu
    v-if="optionMenuIsOpened"
    class="messaging-option-menu"
    @delete-post="deleteConv"
    @toggle-option-panel="toggleOptionMenu"
  />
</template>

<script>
  import BubbleSelector from '../../../common/CustomSelectBubbles.vue';
  import OptionsMenu from '@/components/menu/options-conversation/OptionsConversation.vue';
  import MessageBubbleReceived from '@/components/views-models/messaging/message-bubble-received/MessageBubbleReceived.vue';
  import MessageBubbleUser from '@/components/views-models/messaging/message-bubble-user/MessageBubbleUser.vue';
  import {
    changeConversationTitle,
    deleteConversation,
  } from '@/services/conversation.service';
  import { toaster } from '@/utils/toast/toast.js';
  import { mapActions } from 'vuex';
  import CustomSelect from '@/components/common/CustomSelect.vue';

  export default {
    name: 'IaSection',
    components: {
      MessageBubbleReceived,
      MessageBubbleUser,
      OptionsMenu,
      CustomSelect,
      BubbleSelector,
    },
    props: {
      conversations: {
        type: Array,
        required: true,
      },
      streamMessage: {
        type: Array,
        required: false,
      },
      isSendingMessage: {
        type: Boolean,
        required: false,
      },
      userMessage: {
        type: String,
        required: true,
      },
    },
    emits: ['send-message-ia', 'create-new-conversation', 'update:userMessage'],
    data() {
      return {
        isLightTheme: false, // Thème par défaut : clair
        searchConversationValue: '',
        selectedConversation: this.conversations && this.conversations.length > 0
          ? this.conversations[0]
          : { conversation_id: null, titre: 'Nouvelle conversation', messages: [] },
        optionMenuIsOpened: false,
        allConversations: this.conversations || [],
        titleChanging: false,
        file: null,
        isPDF: false, // pour indiquer si un PDF est ajouté
        predefinedMessages: [
          "J'améliore mon CV",
          "J'améliore ma lettre de motivation",
          'Je recherche un job',
          "Je prépare un entretien d'embauche",
        ],
        internalUserMessage: this.userMessage,
      };
    },
    computed: {
      themeClass() {
        return this.isLightTheme ? 'light-theme' : 'dark-theme';
      },
      hasStreamMessage() {
        return this.streamMessage && this.streamMessage.length > 0;
      },
      streamMessageText() {
        if (!this.streamMessage || this.streamMessage.length === 0) {
          return '';
        }
        return this.streamMessage.join('');
      },
      isStreamMessageSaved() {
        // Vérifier si le message streamé a déjà été sauvegardé dans la conversation
        if (!this.hasStreamMessage || !this.selectedConversation || !this.selectedConversation.messages) {
          return false;
        }

        const streamText = this.streamMessageText;
        if (!streamText) return false;

        // Chercher si un message IA récent contient le même texte
        const recentMessages = this.selectedConversation.messages.slice(-3); // Vérifier les 3 derniers messages
        return recentMessages.some(msg =>
          msg.message_Mc && msg.message_Mc.includes(streamText.substring(0, 50)) // Comparer les 50 premiers caractères
        );
      },
    },
    watch: {
      conversations: {
        handler() {
          if (this.allConversations.length < this.conversations.length) {
            this.allConversations = this.conversations;
            this.selectedConversation = this.allConversations[0];
          }
          this.allConversations = this.allConversations.sort(
            (a, b) =>
              new Date(b.date_modification) - new Date(a.date_modification)
          );
        },
        deep: true,
      },
      // sort selected conversation messages by date
      'selectedConversation.messages': {
        handler() {
          this.selectedConversation.messages =
            this.selectedConversation.messages.sort(
              (a, b) => new Date(a.date_creation) - new Date(b.date_creation)
            );
        },
        deep: true,
      },
      userMessage(newVal) {
        this.internalUserMessage = newVal;
      },
      internalUserMessage(newVal) {
        this.$emit('update:userMessage', newVal);
      },
      streamMessage: {
        handler(newVal) {
          console.log('IaSection - streamMessage changed, length:', newVal ? newVal.length : 0);
        },
        deep: true,
        immediate: true
      },
    },
    mounted() {
      this.sortConversations();

      const savedTheme = localStorage.getItem('theme');
      this.isLightTheme = savedTheme === 'light';
    },
    methods: {
      toggleTheme() {
        this.isLightTheme = !this.isLightTheme;
        localStorage.setItem('theme', this.isLightTheme ? 'light' : 'dark');
      },

      ...mapActions(['handleDeleteConversation']),
      sendMessageFromBubble(message) {
        this.internalUserMessage = message;
        // this.sendMessage();
        // this.scrollToChat();
      },
      scrollToChat() {
        this.$nextTick(() => {
          const chatSection = this.$refs.chatSection;
          if (chatSection) {
            chatSection.scrollIntoView({
              behavior: 'smooth',
              block: 'start',
            });
          }
        });
      },
      sortConversations() {
        this.allConversations = this.allConversations.sort(
          (a, b) =>
            new Date(b.date_modification) - new Date(a.date_modification)
        );
        if (this.allConversations.length > 0) {
          this.selectedConversation = this.allConversations[0];
        }
      },
      createNewConversation() {
        this.$emit('create-new-conversation');
      },
      handleSearchConversation() {
        this.allConversations = this.conversations.filter((conversation) =>
          conversation.titre.includes(this.searchConversationValue)
        );
      },
      selectConversation(conversation) {
        this.selectedConversation = conversation;
      },
      sendMessage() {
        if (!this.internalUserMessage.trim()) {
          toaster.showErrorPopup("Veuillez saisir un message avant d'envoyer.");
          return;
        }

        this.$emit(
          'send-message-ia',
          this.internalUserMessage,
          this.selectedConversation.conversation_id
        );

        // réinitialise après l'envoi
        this.internalUserMessage = '';
        this.file = null;
        this.isPDF = false;
      },
      toggleOptionMenu() {
        this.optionMenuIsOpened ^= 1;
      },
      handleFileInput(event) {
        this.file = event.target.files[0];
        // make sure there is a file & the file is a PDF
        if (this.file && this.file.type === 'application/pdf') {
          this.isPDF = true;

          toaster.showSuccessPopup(
            `Le fichier "${this.file.name}" a été ajouté avec succès.`
          );
        } else {
          this.isPDF = false;

          toaster.showErrorPopup(
            `Une erreur est survenue au moment de l'ajout de votre fichier, assurez-vous que ce soit un format PDF.`
          );
        }
      },
      openPDF() {
        //console.log('HERE CLICK');

        if (this.file && this.isPDF) {
          const fileURL = URL.createObjectURL(this.file);
          window.open(fileURL, '_blank');
        } else {
          toaster.showErrorPopup('Aucun fichier PDF à afficher.');
        }
      },
      openFileInput() {
        this.$refs.fileInput.click();
      },
      async deleteConv() {
        try {
          await deleteConversation(this.selectedConversation);
          this.allConversations = this.allConversations.filter(
            (conv) =>
              conv.conversation_id !== this.selectedConversation.conversation_id
          );

          if (this.allConversations.length > 0) {
            this.selectedConversation = this.allConversations[0];
          } else {
            this.selectedConversation = null;
          }
          this.$emit('update:conversations', this.allConversations);
          toaster.showSuccessPopup('Conversation supprimée avec succès.');
        } catch (error) {
          //console.error(
          //  'Erreur lors de la suppression de la conversation:',
          //  error
          //);
          toaster.showErrorPopup(
            'Une erreur est survenue lors de la suppression.'
          );
        }
      },
      handleTitleState() {
        this.titleChanging ^= 1;
      },
      async changeTitle() {
        try {
          if (!this.selectedConversation.titre) {
            return;
          }
          await changeConversationTitle(
            this.selectedConversation.conversation_id,
            this.selectedConversation.titre
          );
        } catch (error) {
          //console.error(error);
        } finally {
          this.titleChanging ^= 1;
        }
      },
    },
  };
</script>

<style scoped>
  /* 🌞 Mode Clair */
  .light-theme {
    --background-color: var(--surface-bg-2);
    --text-color: var(--text-1);
    --border-color: var(--surface-bg-4);
    --input-bg: var(--surface-bg-4);
    --input-text: var(--text-1);
    --placeholder-text: var(--text-1);
    --button-bg: var(--primary-1);
    --message-bg: var(--surface-bg-4);
    --message-received-bg: var(--yellow-20);
    --box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  /* 🌙 Mode Sombre (par défaut) */
  .dark-theme {
    --background-color: var(--text-1);
    --text-color: var(--surface-bg-2);
    --border-color: var(--text-3);
    --input-bg: var(--text-3);
    --input-text: var(--surface-bg-2);
    --placeholder-text: var(--surface-bg-2);
    --button-bg: var(--primary-1);
    --message-bg: var(--text-3);
    --message-received-bg: var(--surface-bg-2);
    --box-shadow: none;
  }

  /* ############ chat history section ############ */
  .messaging-chat-history-section {
    height: fit-content;
    width: 100%;
  }

  .messaging-chat-history-list-title {
    margin-top: 10px;
    padding-bottom: 6px;
    display: flex;
    justify-content: center;
    border-bottom: 1px solid var(--border-color);
  }

  .search-bar-container {
    height: 56px;
    margin: 12px 0;
    padding: 10px;
    display: flex;
    align-items: center;
    opacity: inherit;
  }

  .search-bar-container input {
    height: 100%;
    width: 80%;
    border-radius: 5px 0 0 5px;
    background-color: var(--input-bg);
    color: var(--input-text);
  }
  .search-bar-container input::placeholder {
    color: var(--placeholder-text);
  }

  .search-bar-container i {
    height: 100%;
    width: 20%;
    background-color: var(--yellow-100);
    color: var(--surface-bg-2);
  }

  .messaging-add-conv-btn {
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .messaging-add-conv-btn button {
    width: 80%;
    padding: 10px;
    border-radius: 10px;
    margin-bottom: 20px;
    cursor: pointer;
    background-color: var(--primary-1);
    color: var(--text-1);
  }

  .messaging-add-conv-btn button:hover {
    opacity: 0.8;
  }

  .opacity-btn {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .messaging-chat-history-list {
    overflow-y: auto;
    background-color: var(--background-color);
    color: var(--text-color);
  }

  .messaging-chat-history-list img {
    margin-right: 16px;
  }

  .messaging-chat-history-list .v-list-item {
    height: 60px;
    padding-bottom: 6px;
    border-bottom: 1px solid var(--message-bg);
  }

  .messaging-chat-history-list .v-list-item--active {
    background-color: var(--primary-1);
  }

  /* ############ conversation section ############ */
  .messaging-conversation-section {
    height: 720px;
    width: 100%;
    background-color: var(--background-color);
  }

  .messaging-conversation-section-title-container {
    padding-block: 16px;
    padding-inline: 24px;
    padding-bottom: 16px;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
  }

  .messaging-conversation-section-title-container i {
    height: 32px;
    width: 32px;
    border-radius: 5px;
  }

  .messaging-conversation-section-title {
    gap: 8px;
    display: flex;
    align-items: center;
  }

  .messaging-conversation-section-title img:nth-last-child(1) {
    margin-left: 6px;
    height: 20px;
    width: 20px;
  }

  .messaging-conversation-section-title input {
    border-radius: 5px;
    background-color: var(--input-bg);
    color: var(--input-text);
  }

  .messaging-conversation-section-title-container .actions-container {
    display: flex;
    gap: 10px;
  }

  .messaging-conversation-section-title-container .theme-toggle-button,
  .messaging-conversation-section-title-container .option-button {
    background-color: var(--primary-1);
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .messaging-conversation-section-title-container .theme-toggle-button:hover,
  .messaging-conversation-section-title-container .option-button:hover {
    opacity: 0.8;
  }

  .messaging-option-menu {
    position: absolute;
    top: 15px;
    right: 15px;
  }

  .messaging-conversation-list-container {
    padding-top: 20px;
    height: calc(100% - (90px + 90px));
    margin-bottom: 16px;
    overflow-y: auto;
  }

  .messaging-conversation-input-container {
    height: 60px;
    margin-bottom: 16px;
    margin-inline: 16px;
    padding-inline: 16px;
    gap: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    color: var(--text-1);
    background-color: var(--background-color);
    box-shadow: var(--box-shadow);
  }

  :deep(.custom-select-container) {
    width: 100%;
    height: 100%;
    margin-inline: 16px;
  }

  :deep(.custom-select-container input) {
    height: 30px;
    padding-left: 16px;
    border: none;
    outline: none;
    border-radius: 5px;
    background-color: var(--input-bg);
    color: var(--input-text);
  }
  :deep(.custom-select-container input::placeholder) {
    color: var(--placeholder-text);
  }

  .messaging-conversation-input-box {
    height: 50%;
    display: flex;
    width: 100%;
    background-color: transparent;
  }

  .messaging-conversation-input-icons {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
  }

  .messaging-conversation-input-icons-element {
    display: flex;
    gap: 16px;
  }
  .messaging-conversation-input-icons-element:hover {
    opacity: 0.8;
  }

  .messaging-conversation-input-icons-element .pdf-icon {
    width: 30px;
    height: 30px;
  }

  /* ############ bubbles section ############ */
  :deep(.message-user-container) {
    background-color: var(--message-bg);
    color: var(--text-color);
  }

  :deep(.message-received-container) {
    background-color: var(--message-received-bg);
  }

  @media screen and (min-width: 768px) {
    .messaging-chat-history-list {
      height: calc(100% - 80px);
    }

    .messaging-chat-history-list {
      height: 720px;
    }

    .messaging-chat-history-section {
      max-width: 275px;
    }
    /* conversation section */
    .messaging-conversation-section {
      max-width: calc(100% - 275px);
    }
  }

  /* ######## TABLETTE LARGE / TRANSITION VERS DESKTOP ######## */
  @media screen and (min-width: 769px) and (max-width: 991px) {
    .messaging-chat-history-list {
      position: relative;
      top: 50px;
    }
    .messaging-conversation-section {
      position: relative;
      top: 50px;
    }
  }

  /* ######## DÉBUT MODE DESKTOP (Navbar normale) ######## */
  @media screen and (min-width: 992px) {
    .messaging-chat-history-list {
      position: relative;
      top: 50px;
    }
    .messaging-conversation-section {
      position: relative;
      top: 50px;
    }
  }

  /* Écran standard (portable, petit desktop) */
  @media screen and (min-width: 1280px) {
    .messaging-chat-history-list {
      position: inherit;
      top: inherit;
    }
    .messaging-conversation-section {
      position: inherit;
      top: inherit;
    }
  }

  /* Grand écran (Full HD) */
  @media screen and (min-width: 1440px) {
    .messaging-chat-history-list {
      position: inherit;
      top: inherit;
    }
    .messaging-conversation-section {
      position: inherit;
      top: inherit;
    }
  }

  /* ######## DE BASE ########*/ /* Très grand écran (2K / 4K) */
  @media screen and (min-width: 1920px) {
    .messaging-chat-history-list {
      position: inherit;
      top: inherit;
    }
    .messaging-conversation-section {
      position: inherit;
      top: inherit;
    }
  }

  /* Écran 4K et plus */
  @media screen and (min-width: 2560px) {
    .messaging-chat-history-list {
      position: inherit;
      top: inherit;
    }
    .messaging-conversation-section {
      position: inherit;
      top: inherit;
    }
  }
</style>
