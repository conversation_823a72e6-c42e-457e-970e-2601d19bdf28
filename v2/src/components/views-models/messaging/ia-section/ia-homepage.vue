<template>
    <section class="messaging-section">
      <header class="messaging-header">
        <img src="@/assets/icons/tb-logo-borderblack.svg" alt="logo thanks-boss" />
        <h2>IA Thanks-Boss</h2>
      </header>
  
      <div class="messaging-list">
        <!-- Affiche les messages avec un style conditionnel selon l'expéditeur -->
        <div 
          v-for="(message, index) in messages" 
          :key="index" 
          class="message-bubble"
          :class="message.sender === 'user' ? 'user-message' : 'ai-message'"
        >
          {{ message.text }}
        </div>
      </div>
  
      <footer class="messaging-input">
        <input 
          type="text" 
          placeholder="Saisis ton message" 
          v-model="newMessage" 
          @keyup.enter="sendMessage" 
        />
        <button @click="sendMessage" class="border-radius-5 cursor-pointer">
    <img src="@/assets/icons/icon-send-msg-primary1.svg" alt="Envoyer" />
</button>
      </footer>
    </section>
  </template>
  
  <script>
  import axios from 'axios';
  import { baseUrl } from "../../../../services/axios";
  
  export default {
    data() {
      return {
        newMessage: '',
        messages: [],
      };
    },
    methods: {
      async sendMessage() {
        if (!this.newMessage.trim()) return;
  
        // Ajoute le message de l'utilisateur
        this.messages.push({ text: this.newMessage, sender: 'user' });
        //console.log('send',this.messages);
  
        try {
          // Appelle l'IA et ajoute la réponse
          const response = await this.getAIResponse(this.newMessage);
          this.messages.push({ text: response, sender: 'ai' });
        } catch (error) {
          //console.error("Erreur lors de l'envoi du message :", error);
        } finally {
          this.newMessage = ''; 
        }
      },
      async getAIResponse(userMessage) {
        try {
          const response = await axios.post(`${baseUrl}/api/home/<USER>
          //console.log('ici',response.data);
          
          return response.data;
        } catch (error) {
          //console.error("Erreur lors de l'appel à l'IA :", error);
          return "Désolé, je n'ai pas pu obtenir une réponse.";
        }
      },
    },
  };
  </script>
  
  <style scoped>
  .messaging-section {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: var(--text-1);
    height: 50vh;
    padding: 9px;
    width: 100%;
  }
  
  .messaging-header {
    display: flex;
    align-items: center;
  }
  
  .messaging-header h2 {
    font-size: 15px;
    color: #fff;
  }
  
  .messaging-list {
    flex-grow: 1;
    overflow-y: auto;
    padding: 10px;
    background-color: var(--text-1);
    display: flex;
    flex-direction: column;
  }
  
  .message-bubble {
    padding: 10px;
  margin: 5px 0;
  max-width: 80%;
  word-wrap: break-word;
  }
  
  .user-message {
    background-color: var(--surface-bg-3);
    color: white;
    align-self: flex-end; 
  }
  
  .ai-message {
    background-color: #f5f2ef;
    color: black;
    align-self: flex-start; 
  }
  
  .messaging-input {
    display: flex;
    padding: 16px;
    background-color: var(--surface-bg-3);
    border-radius: 10px;
  }
  
  .messaging-input input {
    flex-grow: 1;
    padding: 10px;
    border: 1px solid var(--primary-1);
    border-radius: 5px;
    color: white;
  }
  
  .messaging-input button {
    background-color: var(--primary-1);
    color: var(--text-1);
    border-radius: 10px;
    padding: 8px 8px;
    margin-left: 12px;
    display: flex;
  }
  .user-message,
  .ai-message {
    display: flex; 
    border-radius:  10px 10px 10px 0px;
    }
  .user-message {
    justify-content: flex-end; 
    border-radius: 10px 10px 0px 10px;
  }

  .ai-message {
    justify-content: flex-start; /* Aligne le message de l'IA à gauche */
  }
  </style>
  