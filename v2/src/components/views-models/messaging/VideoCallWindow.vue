<template>
  <div class="video-container" ref="videoContainer">
    <div>
      <video
        ref="webcam"
        autoplay
        playsinline
        muted
        class="webcam-video local-video"
        :srcObject="localStream"
      ></video>

      <video
        id="remote-video"
        ref="remoteVideo"
        autoplay
        playsinline
        class="webcam-video remote-video"
        :srcObject="remoteStream"
      ></video>

      <!-- Message d'état de l'appel - affiché uniquement si callStatus n'est pas vide -->
      <div v-if="callStatus && callStatus.trim() !== ''" class="call-status-message">
        {{ callStatus }}
      </div>
    </div>
    <audio ref="audio" autoplay playsinline class="webcam-video"></audio>
    <button
      class="fullscreen-button"
      v-if="!isFullscreen"
      @click="toggleFullscreen"
    >
      <img src="../../../assets/icons/full-screen.svg" alt="Agrandir" />
    </button>

    <!-- <PERSON><PERSON><PERSON> le plein écran -->
    <button
      class="exit-fullscreen-button"
      v-if="isFullscreen"
      @click="toggleFullscreen"
    >
      <img
        src="../../../assets/icons/small-screen.svg"
        alt="Sortir du plein écran"
      />
    </button>

    <div class="video-controls">
      <button @click="showSettings" class="settings-button">
        <img src="../../../assets/icons/setting-icon.svg" alt="Raccrocher" />
      </button>
      <button @click="toggleVideo" :class="{ 'muted-button': isVideoMuted }">
        <img src="../../../assets/icons/cut-video.svg" alt="Couper la vidéo" />
      </button>
      <button @click="toggleAudio" :class="{ 'muted-button': isAudioMuted }">
        <img
          src="../../../assets/icons/mute-the-microphone.svg"
          alt="Couper le micro"
        />
      </button>
      <button @click="end_video_call">
        <img src="../../../assets/icons/to-hang-up.svg" alt="Réglages" />
      </button>
    </div>
  </div>

  <!-- Section des paramètres Vidéo et Audio -->
  <modal
    :isVisible="isSettingsVisible"
    @close="isSettingsVisible = false"
    @close-alert-panel="isModalVisible = false"
  >
    <h2>Paramètres</h2>
    <div class="flex">
      <label for="videoQuality">Qualité Vidéo:</label>
      <select id="videoQuality" v-model="videoQuality">
        <option value="360p">360p</option>
        <option value="720p">720p</option>
        <option value="1080p">1080p</option>
      </select>
      <label for="webcamSelect">Webcam:</label>
      <select id="webcamSelect" v-model="selectedWebcam" @change="changeWebcam">
        <option
          v-for="webcam in webcams"
          :key="webcam.deviceId"
          :value="webcam.deviceId"
        >
          {{ webcam.label || 'Webcam inconnue' }}
        </option>
      </select>
    </div>

    <!-- Paramètres Audio -->
    <div>
      <label for="microphoneSelect">Microphone:</label>
      <select
        id="microphoneSelect"
        v-model="selectedMicrophone"
        @change="changeMicro"
      >
        <option
          v-for="micro in micros"
          :key="micro.deviceId"
          :value="micro.deviceId"
        >
          {{ micro.label || 'Microphone inconnu' }}
        </option>
      </select>
      <label for="speakerSelect">Haut-parleur:</label>
      <select v-model="selectedSpeakerId" @change="changeSpeaker">
        <option
          v-for="speaker in speakers"
          :key="speaker.deviceId"
          :value="speaker.deviceId"
        >
          {{ speaker.label || 'Haut-parleur ' + (index + 1) }}
        </option>
      </select>
    </div>
  </modal>
</template>
<script>
  import Modal from '@/components/modal/video-settings/video-settings.vue';
  import { nextTick } from 'vue';
  export default {
    components: {
      Modal,
    },
    props: {
      localStream: {
        type: Object,
        required: true,
      },
      remoteStream: {
        type: Object,
        required: false,
        default: null,
      },
    },

    data() {
      return {
        isFullscreen: false,
        selectedWebcam: null,
        webcams: [],
        micros: [],
        speakers: [],
        audioVolume: 50,
        isSettingsVisible: false,
        selectedSpeaker: null,
        videoQuality: '720p',
        selectedMicrophone: null,
        callStatus: 'Appel en cours...', // Message d'état de l'appel
        isVideoMuted: false, // État de la vidéo (muet ou non)
        isAudioMuted: false, // État de l'audio (muet ou non)
      };
    },
    mounted() {
      this.getAvailableWebcams();
      this.getMicrophones();
      this.getSpeakers();

      // Forcer l'affectation des flux (si déjà présents au mount)
      if (this.localStream && this.$refs.webcam) {
        this.$refs.webcam.srcObject = this.localStream;

        // Initialiser l'état des boutons en fonction de l'état actuel des tracks
        const videoTrack = this.localStream.getVideoTracks()[0];
        if (videoTrack) {
          this.isVideoMuted = !videoTrack.enabled;
        }

        const audioTrack = this.localStream.getAudioTracks()[0];
        if (audioTrack) {
          this.isAudioMuted = !audioTrack.enabled;
        }
      }

      if (this.remoteStream && this.$refs.remoteVideo) {
        this.$refs.remoteVideo.srcObject = this.remoteStream;
      }
    },
    beforeDestroy() {
      //console.log('%c[APPEL] Destruction du composant VideoCallWindow', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
      this.cleanupMediaStreams();
    },
    methods: {

      cleanupMediaStreams() {
        //console.log('%c[APPEL] Nettoyage des flux média dans VideoCallWindow', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

        // Arrêter tous les tracks du flux local
        if (this.localStream) {
          this.localStream.getTracks().forEach((track) => {
            track.stop();
            //console.log(`%c[APPEL] Track ${track.kind} arrêté dans VideoCallWindow`, 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          });
        }

        // Arrêter tous les tracks du flux distant
        if (this.remoteStream) {
          this.remoteStream.getTracks().forEach((track) => {
            track.stop();
            //console.log(`%c[APPEL] Track ${track.kind} du stream distant arrêté dans VideoCallWindow`, 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          });
        }

        // Nettoyer les éléments vidéo
        const webcamElement = this.$refs.webcam;
        if (webcamElement && webcamElement.srcObject) {
          const tracks = webcamElement.srcObject.getTracks();
          tracks.forEach(track => {
            track.stop();
            //console.log(`%c[APPEL] Track ${track.kind} de webcamElement arrêté dans VideoCallWindow`, 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          });
          webcamElement.srcObject = null;
        }

        const remoteVideoElement = this.$refs.remoteVideo;
        if (remoteVideoElement && remoteVideoElement.srcObject) {
          const tracks = remoteVideoElement.srcObject.getTracks();
          tracks.forEach(track => {
            track.stop();
            //console.log(`%c[APPEL] Track ${track.kind} de remoteVideoElement arrêté dans VideoCallWindow`, 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          });
          remoteVideoElement.srcObject = null;
        }
      },

      /**
       * Met à jour le message d'état de l'appel
       * @param {string} status - Le nouveau message d'état
       */
      updateCallStatus(status) {
        this.callStatus = status;
        //console.log('%c[APPEL] Mise à jour du statut de l\'appel:', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;', status);
      },

      end_video_call() {
        //console.log('%c[APPEL] Fin de l\'appel dans VideoCallWindow', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

        // Mettre à jour le message d'état
        this.updateCallStatus('Fin de l\'appel...');

        // Nettoyer les flux média avant d'émettre l'événement
        this.cleanupMediaStreams();

        // Émettre l'événement pour informer le parent
        this.$emit('end_video_call');
      },
      getWebcamRef() {
        return this.$refs.webcam;
      },
      getRemoteVideoRef() {
        return this.$refs.remoteVideo;
      },
      toggleVideo() {
        if (this.localStream) {
          const videoTrack = this.localStream.getVideoTracks()[0];
          if (videoTrack) {
            videoTrack.enabled = !videoTrack.enabled;
            // Mettre à jour l'état de la vidéo
            this.isVideoMuted = !videoTrack.enabled;
            //console.log(
            //  videoTrack.enabled
            //    ? 'Activation de la webcam.'
            //    : 'Désactivation de la webcam.'
            //);
          }
        } else {
          //console.log('No hay stream de video disponible');
        }
      },
      toggleAudio() {
        if (this.localStream) {
          const audioTrack = this.localStream.getAudioTracks()[0];
          if (audioTrack) {
            audioTrack.enabled = !audioTrack.enabled;
            // Mettre à jour l'état de l'audio
            this.isAudioMuted = !audioTrack.enabled;
            //console.log(
            //  audioTrack.enabled
            //    ? 'Activation du micro.'
            //    : 'Désactivation du micro.'
            //);
          }
        } else {
          //console.log('No hay stream de audio disponible');
        }
      },

      async getSpeakers() {
        try {
          const devices = await navigator.mediaDevices.enumerateDevices();
          this.speakers = devices.filter(
            (device) => device.kind === 'audiooutput'
          );
          //console.log(this.speakers);

          if (this.speakers.length === 0) {
            toaster.showErrorPopup('Aucun haut-parleur détecté.');
          } else {
            this.selectedSpeakerId = this.speakers[0].deviceId;
          }
        } catch (error) {
          //console.error(
          //  'Erreur lors de la récupération des haut-parleurs : ',
          //  error
          //);
          toaster.showErrorPopup(
            'Erreur lors de la récupération des haut-parleurs.'
          );
        }
      },
      async getMicrophones() {
        try {
          const devices = await navigator.mediaDevices.enumerateDevices();
          this.micros = devices.filter(
            (device) => device.kind === 'audioinput'
          );
          if (this.micros.length > 0) {
            this.selectedMicrophone = this.micros[0].deviceId;
            //console.log('here', this.selectedMicrophone);
          }
        } catch (error) {
          //console.error(
          //  'Erreur lors de la récupération des microphones:',
          //  error
          //);
        }
      },
      async getAvailableWebcams() {
        try {
          const devices = await navigator.mediaDevices.enumerateDevices();
          this.webcams = devices.filter(
            (device) => device.kind === 'videoinput'
          );
          //console.log(this.webcams);

          if (this.webcams.length === 0) {
            toaster.showErrorPopup('Aucune webcam détectée.');
          }

          if (this.webcams.length > 0) {
            this.selectedWebcam = this.webcams[0].deviceId;
          }
        } catch (error) {
          //console.error('Erreur lors de la récupération des webcams : ', error);
          toaster.showErrorPopup('Erreur lors de la récupération des webcams.');
        }
      },
      async changeSpeaker() {
        try {
          const audioElement = this.$refs.audio;

          if (!audioElement) {
            throw new Error("L'élément audio est introuvable.");
          }

          if (!this.selectedSpeakerId) {
            throw new Error('Aucun haut-parleur sélectionné.');
          }

          await audioElement.setSinkId(this.selectedSpeakerId);
          //console.log('Haut-parleur changé avec succès !');
        } catch (err) {
          //console.error('Erreur lors du changement de haut-parleur :', err);
          toaster.showErrorPopup(
            'Erreur lors du changement de haut-parleur : ' + err.message
          );
        }
      },
      // TODO : verifier si les fonction changeMicro et changeWebcam marchent car l'objet stream est dans le parent et on ne peut pas le modifier depuis ici. peut-être c'est mieux faire un emit et le gère dans le pere.
      // changer le object this.currentStream pourrait n'appliquer pas les changements au objet stream du composant parent, donc le emit pourrait être plus efficace
      async changeMicro() {
        try {
          // Émettre un événement pour que le parent gère le changement de microphone
          this.$emit('change-microphone', this.selectedMicrophone);
        } catch (err) {
          //console.error('Erreur lors du changement de microphone : ', err);
          toaster.showErrorPopup('Erreur lors du changement de microphone.');
        }
      },
      async changeWebcam() {
        try {
          // Émettre un événement pour que le parent gère le changement de webcam
          this.$emit('change-webcam', this.selectedWebcam);
        } catch (err) {
          //console.error('Erreur lors du changement de webcam : ', err);
          toaster.showErrorPopup('Impossible de changer la webcam.');
        }
      },
      saveSettings() {
        //console.log('Paramètres sauvegardés :', {
        //  videoQuality: this.videoQuality,
        //  selectedWebcam: this.selectedWebcam,
        //  selectedMicrophone: this.selectedMicrophone,
        //  audioVolume: this.audioVolume,
        //});

        this.isSettingsVisible = false;
      },
      toggleFullscreen() {
        const videoWindow = this.$refs.videoContainer;

        if (!this.isFullscreen) {
          if (videoWindow.requestFullscreen) {
            videoWindow.requestFullscreen();
          } else if (videoWindow.webkitRequestFullscreen) {
            videoWindow.webkitRequestFullscreen();
          } else if (videoWindow.msRequestFullscreen) {
            videoWindow.msRequestFullscreen();
          }
        } else {
          if (document.exitFullscreen) {
            document.exitFullscreen();
          } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
          } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
          }
        }

        this.isFullscreen = !this.isFullscreen;
      },
      showSettings() {
        this.isSettingsVisible = !this.isSettingsVisible;
      },
    },

    watch: {
      async remoteStream(newStream) {
        await nextTick();
        if (this.$refs.remoteVideo && newStream) {
          this.$refs.remoteVideo.srcObject = newStream;
          //console.log('[APPEL] remoteStream injecté dans la balise vidéo');
        }
      },
      async localStream(newStream) {
        await nextTick();
        if (this.$refs.webcam && newStream) {
          this.$refs.webcam.srcObject = newStream;
          //console.log('[APPEL] localStream injecté dans la balise vidéo');

          // Mettre à jour l'état des boutons en fonction de l'état actuel des tracks
          const videoTrack = newStream.getVideoTracks()[0];
          if (videoTrack) {
            this.isVideoMuted = !videoTrack.enabled;
          }

          const audioTrack = newStream.getAudioTracks()[0];
          if (audioTrack) {
            this.isAudioMuted = !audioTrack.enabled;
          }
        }
      }
    }

  };
</script>

<style scoped>
  .flex {
    display: flex;
    flex-direction: column;
  }
  .exit-fullscreen-button {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    cursor: pointer;
  }
  .exit-fullscreen-button img {
    width: 33px;
    height: 33pxx;
  }

  .fullscreen-button {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    cursor: pointer;
  }

  .fullscreen-button img {
    width: 33px;
    height: 33px;
  }

  .video-container {
    display: flex;
    flex-direction: column;
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  .remote-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .local-video {
    position: absolute;
    top: 20px;
    left: 20px;
    max-width: 200px;
    max-height: 150px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid white;
    z-index: 1;
  }
  .video-controls button:hover {
    background-color: #33363a;
  }
  .video-controls button {
    padding: 6px 12px;
    border: none;
    border-radius: 5px;
    background-color: #26282b;
    color: #333;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }
  /* Style pour les boutons en état muet */
  .video-controls button.muted-button {
    background-color: #e74c3c; /* Rouge pour indiquer que c'est muet */
  }
  .video-controls button.muted-button:hover {
    background-color: #c0392b; /* Rouge plus foncé au survol */
  }
  .video-controls {
    position: relative;
    bottom: 10px;
    display: flex;
    gap: 10px;
    background: rgb(38 40 43);
    padding: 6px;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    align-items: center;
    justify-content: center;
  }

  .webcam-on {
    display: grid;
    grid-template-columns: 1fr 1fr !important;
    gap: 24px;
  }
  .webcam-video {
    border-radius: 8px;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* Style pour le message d'état de l'appel */
  .call-status-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 18px;
    font-weight: bold;
    z-index: 10;
    text-align: center;
    max-width: 80%;
    display: none;
  }
</style>
