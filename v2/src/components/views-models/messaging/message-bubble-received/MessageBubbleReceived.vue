<template>
  <div class="message-received-container" :class="{ 'best-jobs-message-container': show_job_offers }"
    @mouseleave="setMenuOpened(false)">
    <!-- Affichage unifié pour tous les types de messages -->
    <p class="cursor-pointer retour-ligne" @mouseenter="setMenuOpened(true)" v-html="formattedMessage"></p>

    
    <JobOffersDisplay v-if="show_job_offers" :jobOffers="jobOffers" />

    <OptionsMessageReceived
      v-if="isMenuOpened"
      isIa
      class="option-message-menu-received"
      @thumbs-up="likeMessageClick"
      @thumbs-down="dislikeMessageClick"
      @signal-post="signalPostClick"
      @copy-message="copyMessage"
    />
  </div>
  <FeedbackMessaging
    id="feedbackMessagingComponent"
    v-if="feedbackMessagingIsClosed"
    raisonFeedback
    :messageSent="messageSent"
    @send-feedback="sendFeedback"
    @close="feedbackMessagingIsClosed = false"
    @keyup.enter="sendFeedback"
  />
  <FeedbackMessaging
    id="feedbackSignalComponent"
    v-if="feedbackSignalIsClosed"
    :raisonFeedback="false"
    :messageSent="signalMessageSent"
    @send-feedback="signalPost"
    @close="feedbackSignalIsClosed = false"
    @keyup.enter="signalPost"
  />
</template>

<script>
  import OptionsMessageReceived from '@/components/menu/options-message/OptionsMessageReceived.vue';
  import FeedbackMessaging from '@/components/modal/messaging/feedback/FeedbackMessaging.vue';
  import JobOffersDisplay from './JobOffersDisplay.vue';
  import {
    likeMessage,
    dislikeMessage,
    signalMessage,
  } from '../../../../services/conversation.service';
  import { toaster } from '../../../../utils/toast/toast';

  export default {
    name: 'MessageBubbleReceived',
    components: {
      OptionsMessageReceived,
      FeedbackMessaging,
      JobOffersDisplay,
    },
    props: {
      message: {
        required: true,
        default: () => ({}),
      },
      messageId: {
        type: Number,
        required: false,
      },
      detectedClass: {
        type: String,
        required: false,
      },
      jobOffers: {
        type: Array,
        required: false,
      },
      jobTitle: {
        type: Array,
        required: false,
      },
    },
    data() {
      return {
        isMenuOpened: false,
        feedbackMessagingIsClosed: false,
        messageSent: false,
        signalMessageSent: false,
        feedbackSignalIsClosed: false,
      };
    },
    jobOffers: {
      type: Array,
      required: false,
    },
    jobTitle: {
      type: Array,
      required: false,
    },
    computed: {
      formattedMessage() {
        // Gérer les messages streamés (tableau) et les messages normaux (string)
        let messageText = '';
        if (Array.isArray(this.message)) {
          messageText = this.message.join('');
        } else if (typeof this.message === 'string') {
          messageText = this.message;
        } else {
          messageText = String(this.message || '');
        }

        // Si le message est vide, retourner une chaîne vide
        if (!messageText) {
          return '';
        }

        return messageText
          .replace(/\*\*([^*]+)\*\*/g, (match, p1) => {
              return `<h3 style="display: flex; font-weight: 900; font-size: 24px; justify-content: center; margin: 10px;">${p1}</h3>`;
          })
          .replace(/(?:^|[\s"\.])(\d+)\./g, (match, p1, offset, fullText) => {
              if (offset > 0 && /[a-zA-ZÀ-ÿ]/.test(fullText[offset - 1])) {
                  return match;
              }
              return `<br><div style="display: flex; justify-content: center;width:100%;">
                  <span style="display: flex; justify-content: center; align-items: center; font-weight: 800; font-size: 23px; color: #f7bc53; width: 40px; margin: 10px; height: 40px; border: 2px solid #f7bc53; border-radius: 50%;">
                      ${p1}
                  </span>
              </div>`;
          });
      },

    show_job_offers() {
        const shouldShow = (this.detectedClass === "best_jobs" || this.detectedClass === "feedback_negative") &&
          Array.isArray(this.jobOffers) &&
          this.jobOffers.length > 0;

        console.log('🎯 MessageBubbleReceived - show_job_offers check:', {
          detectedClass: this.detectedClass,
          jobOffers: this.jobOffers,
          jobOffersLength: this.jobOffers ? this.jobOffers.length : 0,
          shouldShow: shouldShow
        });

        return shouldShow;
      },
    },
  methods: {
    setMenuOpened(value) {
      this.isMenuOpened = value;
    },
    async likeMessageClick() {      
      try {
        await likeMessage(this.messageId);
      } catch (error) {
        //console.error(error);
      }
    },
      dislikeMessageClick() {
        this.feedbackSignalIsClosed = false;
        this.feedbackMessagingIsClosed = true;
        // scroll to FeedbackMessaging component
        this.$nextTick(() => {
          const feedbackElement = document.getElementById(
            'feedbackMessagingComponent'
          );
          if (feedbackElement) {
            feedbackElement.scrollIntoView({ behavior: 'smooth' });
          }
        });
      },
      async sendFeedback(feedback) {
        try {
          await dislikeMessage(this.messageId, feedback);
          this.messageSent = true;
        } catch (error) {
          toaster.showErrorPopup(
            "Erreur lors de l'envoi du feedback. Veuillez réessayer."
          );
          //console.error(error);
        }
      },
      signalPostClick() {
        this.feedbackMessagingIsClosed = false;
        this.feedbackSignalIsClosed = true;
        // scroll to FeedbackMessaging component
        this.$nextTick(() => {
          const feedbackElement = document.getElementById(
            'feedbackSignalComponent'
          );
          if (feedbackElement) {
            feedbackElement.scrollIntoView({ behavior: 'smooth' });
          }
        });
      },
      async signalPost(feedback) {
        try {
          await signalMessage(this.messageId, feedback);
          this.signalMessageSent = true;
          toaster.showSuccessPopup('Message signalé. Merci pour votre retour.');
        } catch (error) {
          toaster.showErrorPopup('Erreur lors du signalement du message');
          //console.error(error);
        }
      },
      async copyMessage() {
        try {
          await navigator.clipboard.writeText(this.message);
          console.log('Message copié dans le presse-papiers');
        } catch (error) {
          console.error('Erreur lors de la copie:', error);
          // Fallback pour les navigateurs qui ne supportent pas l'API clipboard
          try {
            const textArea = document.createElement('textarea');
            textArea.value = this.message;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            console.log('Message copié avec fallback');
          } catch (fallbackError) {
            console.error('Erreur lors de la copie avec fallback:', fallbackError);
          }
        }
      },
  },
  };
</script>

<style scoped>
  .message-received-container {
    min-height: 40px;
    width: 51%;
    max-width: 51%;

    margin-left: 10px;
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 10px;
    border-bottom-left-radius: 0;

    position: relative;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;

    word-break: break-all;

    background-color: var(--yellow-20);
  }
  .best-jobs-message-container {
  width: 97%;
  max-width: 97%;
}
.fade {
  opacity: 0;
  transition: opacity 1s ease;
  color: black;
}
  .message-received-container p {
    flex-direction: column;
    overflow: auto;
  }

  .retour-ligne {
    word-wrap: break-word;
    word-break: keep-all;
  }
</style>
