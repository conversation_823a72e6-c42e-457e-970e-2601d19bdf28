<template>
    <v-slide-group class="job-offers-wrapper" v-model="model" show-arrows>
        <v-slide-group-item v-for="jobOffer in jobOffers">
            <MiniCard 
              class="job-offer-card" 
              :job="jobOffer" 
              :isFavorite="jobOffer.isFavorite" 
              @heart-click="onHeartClick" 
            />
        </v-slide-group-item>
    </v-slide-group>
</template>

<script>
import { toggleJobOfferIsFavorite } from '@/services/favoriteJob.service';
import { toaster } from '@/utils/toast/toast';
import MiniCard from '@/components/cards/job-cards/MiniCard.vue';

export default {
    data() {
        return {
            model: null,
        };
    },
    props: {
        jobOffers: {
            type: Array,
            required: true
        }
    },
    methods: {
      async onHeartClick(id) {
        const jobOffer = this.jobOffers.find(job => job.id === id);
        let message = "L'offre a été supprimée de vos favoris.";

        if (await toggleJobOfferIsFavorite(id)) {
          this.$store.commit('addFavoris', jobOffer);  // Ajout dans Vuex
          message = "L'offre a été ajoutée à vos favoris.";
          jobOffer.isFavorite = true;
        } else {
          this.$store.commit('deleteFavoris', id);  // Suppression dans Vuex
          jobOffer.isFavorite = false;
        }
        toaster.showSuccessPopup(message);
      }
    },
    components: {
        MiniCard,
    },
};
</script>

<style scoped>
.job-offers-wrapper {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
}

.job-offer-card {
    height: 16rem;
    margin-inline: 0.5rem;
}
</style>
