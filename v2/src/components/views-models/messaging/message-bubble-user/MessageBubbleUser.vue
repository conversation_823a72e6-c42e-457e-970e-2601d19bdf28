<template>
  <div class="message-user-container" @mouseleave="setMenuOpened(false)">
    <p class="cursor-pointer" @mouseenter="setMenuOpened(true)">
      {{ message }}
    </p>

    <OptionsMessageMenuUser
      v-if="isMenuOpened"
      :isIa="false"
      class="option-message-menu-user"
      @copy-message="copyMessage"
    />
  </div>
  <div v-if="fileName" class="message-bubble-file">
    <img src="@/assets/chatbot/PDF.svg" class="pdf-icon" />
    <p class="file-name">{{ fileName }}</p>
  </div>
</template>

<script>
  import OptionsMessageMenuUser from '@/components/menu/options-message/OptionsMessageUser.vue';

  export default {
    name: 'MessageBubbleUser',
    components: {
      OptionsMessageMenuUser,
    },
    props: {
      message: {
        type: String,
        required: true,
        default: () => ({}),
      },
      fileName: {
        type: String,
        required: false,
      },
    },
    data() {
      return {
        isMenuOpened: false,
      };
    },
    methods: {
      setMenuOpened(value) {
        this.isMenuOpened = value;
      },
      copyMessage() {
        navigator.clipboard.writeText(this.message);
      },
    },
  };
</script>

<style scoped>
  .message-user-container {
    min-height: 40px;
    width: fit-content;
    max-width: 51%;
    margin-left: auto;
    margin-right: 10px;
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 10px;
    border-bottom-right-radius: 0;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: flex-end;
    word-break: break-all;
    background-color: var(--surface-bg-4);
    color: var(--text-1);
  }

  .message-bubble-file {
    margin-top: 5px;
    text-align: right;
    margin-right: 20px;
  }

  .pdf-icon {
    height: 50px;
    margin-bottom: 4px;
    cursor: pointer;
  }

  .file-name {
    margin-bottom: 4px;
    cursor: pointer;
    color: #fff;
  }
</style>
