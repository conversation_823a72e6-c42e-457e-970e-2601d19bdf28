<template>
  <section class="about-section">
    <div class="background-img-wrapper">
      <div class="content-wrapper">
        <div class="text-container">
          <h2>Qui sommes-nous ?</h2>
          <p v-html="text"></p>
          <div class="button-container">
            <PrimaryRoundedButton
              textContent="En savoir plus"
              @click="gotoPage('/a-propos')"
              color="#FFFFFF"
              textColor="#000000"
              :style="{
                borderRadius: '1rem',
                minWidth: '12.5rem',
              }"
            />
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import gotoPage from '@/utils/router';

  export default {
    name: 'AboutUs',

    components: {
      PrimaryRoundedButton,
    },

    data() {
      return {
        text: `Combien de personnes ont essuyé refus, postulé en vain, rempli des formulaires sans réponse, subi des chefs toxiques ou attendu en vain une augmentation ? Trop. C'est pourquoi nous avons créé Thanks-Boss. <br><br>
      Tout commence avec <PERSON>lorentin, diplômé en IA, qui a vécu sept ans sans challenge avant de vouloir transformer l'expérience RH. Grâce à l'IA, Thanks-Boss assure un matching précis entre talents et recruteurs. <br><br>
      Notre promesse ? Simplifier le recrutement avec une approche plus qualitative et transparente. Rejoignez-nous pour révolutionner l’embauche !`,
      };
    },

    methods: {
      gotoPage,
    },
  };
</script>

<style scoped>
  /* Base styles */
  .about-section {
    display: flex;
    justify-content: center;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    padding: 40px 0;
    width: 100vw;
  }

  /* Background Image */
  .background-img-wrapper {
    width: 100%;
    height: 650px;
    background-image: url('../../../../documentation/pages/Home/image homepage 4Fichier 1.svg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 20px;
    margin: 0 3vw;
  }

  /* Content wrapper (Initially over image) */
  .content-wrapper {
    position: relative;
    left: 10%;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
    width: 50%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }

  /* Text container */
  .text-container {
    max-width: 500px;
    color: white;
  }
  .text-container p {
    margin-top: 30px;
  }

  .button-container {
    margin-top: 40px;
  }

  /* ✅ MOBILE : petits smartphones (iPhone SE, etc.) */
  @media screen and (max-width: 480px) {
    .background-img-wrapper {
      background-image: inherit;
      background-color: #003569;
      width: calc(100% - 6vw);
    }
    .content-wrapper {
      width: 100%;
      left: 0%;
      text-align: center;
    }
    .text-container p {
      margin-top: 30px;
      margin-left: 5%;
      margin-right: 5%;
    }
  }

  /* ✅ MOBILE LARGE : smartphones standards (iPhone 12, Galaxy S21, etc.) */
  @media screen and (min-width: 481px) and (max-width: 767px) {
    .background-img-wrapper {
      background-image: inherit;
      background-color: #003569;
      width: calc(100% - 6vw);
    }
    .content-wrapper {
      width: 100%;
      left: 0%;
      text-align: center;
    }
    .text-container p {
      margin-top: 30px;
      margin-left: 5%;
      margin-right: 5%;
    }
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .background-img-wrapper {
      width: 1300px;
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .background-img-wrapper {
      width: 1300px;
    }
  }
</style>
