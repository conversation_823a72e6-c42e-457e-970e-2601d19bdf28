<template>

  <section class="hero">
    <div class="hero-content">
      <div class="content-wrapper">
        <h3 class="candidat">Thanks-Boss Candi<PERSON>t</h3>
        <h2 class="title">
          Libère ton talent et trouve le job de tes rêves avec notre IA !
        </h2>
        <p class="description">
          Nous comprenons les attentes et frustrations des recruteurs : des
          profils non pertinents, un processus long et compliqué et des
          difficultés à suivre les performances.<br />
          Avec Thanks Boss, recrutez efficacement grâce à notre plateforme
          propulsée par l'IA !
        </p>
        <div class="features">
          <div class="">
            <h6>Accède aux fonctionnalités plus avancées :</h6>
            <ul class="list">
              <li>Optimisation de CV</li>
              <li>Optimisation de lettre de motivation</li>
              <li>Aide à la préparation des entretiens</li>
              <li v-if="!isLoggedIn">
                <PERSON>ur bénéficier de la recommandation d'offres d'emploi, il
                suffit de s'inscrire
              </li>
              <li v-else>Recommandation d'offres d'emploi</li>
            </ul>
          </div>
          <div class="button-container">
            <PrimaryRoundedButton
              v-if="!this.$store.getters.isLoggedIn"
              textContent="Je m'inscris"
              id="register"
              @btn-click="handleButtonClick"
              color="#FFFFFF"
              textColor="#000000"
              :style="{
                borderRadius: '1rem',
                minWidth: '12.5rem',
              }"
            />
          </div>

        </div>
      </div>
      <!-- <div class="img-IA">
        <img
          :src="
            require('../../../../documentation/pages/Home/fille hompageFichier 1.svg')
          "
          alt="Illustration de l'IA sur la page d'accueil de Thanks-Boss"
        />
      </div> -->
    </div>
  </section>
</template>

<script>
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import gotoPage from '@/utils/router.js';

  export default {
    name: 'HeroSection',
    components: {
      PrimaryRoundedButton,
    },
    mounted() {
      //console.log('HeroSection has been mounted');
    },
    computed: {
      isLoggedIn() {
        return this.$store.getters.isLoggedIn;
      },
    },
    methods: {
      handleButtonClick() {
        gotoPage('/inscription');
      },
    },
  };
</script>

<style scoped>
  .hero {
    background-color: var(--primary-1);
    width: 100vw;
    display: flex;
    justify-content: center;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
  }

  .hero-content {
    width: 100%;
    max-width: 1440px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 700px;

    background-image: url('../../../assets/home/<USER>');
    background-size: contain;
    background-position: right bottom;
    animation: none;
    margin-right: 170px;
  }

  .content-wrapper {
    width: 60%;
    margin-left: 9%;
  }

  .candidat {
    color: var(--text-3);
    font-weight: 500;
  }

  .description {
    margin: 30px 0;
  }

  .features .list {
    margin-left: 30px;
    display: inherit;
  }

  .button-container {
    margin: 40px 0;
  }

  /* ✅ MOBILE */
  @media screen and (max-width: 480px) and (max-width: 767px) {
    .hero-content {
      background-image: inherit;
      margin-right: inherit;
    }
    .content-wrapper {
      width: 100%;
      margin: 0 3vw;
    }
    .button-container {
      display: flex;
      justify-content: center;
    }
  }

  /* ✅ TABLETTE LARGE / PETIT DESKTOP : transition vers écran large */
  @media screen and (min-width: 1024px) and (max-width: 1279px) {
    .content-wrapper {
      width: 50%;
    }
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .hero-content {
      margin-right: 190px;
    }
    .content-wrapper {
      margin-left: 15%;
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .hero-content {
      margin-right: 190px;
    }
    .content-wrapper {
      margin-left: 15%;
    }
  }


</style>
