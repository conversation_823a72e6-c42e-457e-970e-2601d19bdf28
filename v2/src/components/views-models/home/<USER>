<template>
</template>

<script>
  export default {
    name: 'TheySpeakOfUs',

    data() {
      return {
        sponsors: [
          {
            logo: require('@/assets/partners/frenchtech.svg'),
            alt: 'French Tech',
            link: 'https://lafrenchtech.gouv.fr/',
          },
          {
            logo: require('@/assets/partners/Logo_Eva.svg'),
            alt: 'Eigsi Violet Alumni',
            link: 'https://www.linkedin.com/company/eva-eigsi-violet-alumni-le-r%C3%A9seau-des-ing%C3%A9nieurs-violet-eemi-eigsi/',
          },
          {
            logo: require('@/assets/partners/airoomsstyle.svg'),
            alt: 'AI Room Styles',
            link: 'https://www.airoomstyles.com/',
          },
          {
            logo: require('@/assets/partners/logo-reso-incubateurspepinieres.svg'),
            alt: 'Réso Incubateur',
            link: 'https://www.agence-adocc.com/-evenements-/la-startup-se-met-au-vert/',
          },
          {
            logo: require('@/assets/partners/Logo_Beauvoir.svg'),
            alt: 'Beauvoir',
            link: 'https://www.beauvoir.co/',
          },
        ],
      };
    },
  };
</script>

<style scoped>
  .sponsor-section {
    background-color: var(--surface-bg-2);
    width: 100vw;
    display: flex;
    justify-content: center;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    padding: 20px 0;
  }

  .sponsor-content {
    width: 100%;
    max-width: 1440px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 8%;
  }

  .content-wrapper {
    padding-top: 30px;
    padding-bottom: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    width: 100%;
  }

  .content-wrapper h2 {
    margin-bottom: 20px;
  }

  .content-wrapper p {
    margin-bottom: 30px;
  }

  /* Logo Container */
  .sponsor-wrapper {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    position: relative;
  }

  /* Moving Container */
  .sponsor-track {
    display: flex;
    align-items: center;
    gap: 20px;
    animation: loopLogos 15s linear infinite;
    width: max-content;
  }

  /* Sponsor Logos */
  .sponsor {
    display: flex;
    justify-content: center;
  }

  .logo {
    flex-shrink: 0;
    max-height: 100px;
    width: 130px;
    margin: 10px;
    opacity: 0.6;
  }

  /* Animation */
  @keyframes loopLogos {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(-50%);
    }
  }

  /* Responsive Design */
  @media screen and (max-width: 768px) {
    .sponsor-track {
      animation: loopLogos 10s linear infinite;
    }

    .logo {
      max-width: 100px;
      max-height: 80px;
    }
  }
</style>
