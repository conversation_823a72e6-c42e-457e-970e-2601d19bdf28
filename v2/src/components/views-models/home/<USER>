<template>
  <section class="values-section">
    <figure class="background-img-wrapper">
      <img
        :src="require('@/assets/home/<USER>')"
        class="background-img"
      />
    </figure>

    <div class="content-wrapper">
      <div class="card-wrapper">
        <InfoCard
          v-for="(card, index) in cards"
          :key="index"
          :title="card.title"
          :textContent="card.textContent"
          :titleTag="'h3'"
          class="card-container"
        />
      </div>
    </div>
  </section>
</template>

<script>
  import InfoCard from '@/components/cards/InfoCard.vue';

  export default {
    name: 'OurValues',
    components: {
      InfoCard,
    },
    props: {
      userType: {
        type: String,
        required: true,
      },
    },
    data() {
      return {
        cards: [
          {
            title: 'Qualité exceptionnelle',
            textContent:
              'Notre IA te propose des opportunités taillées sur mesure correspondant à ton profil et à tes attentes. Chaque offre est minutieusement sélectionnée pour correspondre parfaitement à ton profil. Finies les annonces génériques et les candidatures sans réponse !',
          },
          {
            title: 'Simplicité absolue',
            textContent:
              "Postule facilement et concentre-toi sur l'essentiel. Grâce à notre interface intuitive, Thanks-Boss te guide à travers chaque étape du processus de candidature. Plus besoin de remplir des formulaires interminables ou de naviguer sur des sites compliqués.",
          },
          {
            title: 'Rapidité imbattable',
            textContent:
              "Obtiens des offres pertinentes en un temps record. En quelques clics seulement, tu reçois des recommandations d’emploi qui correspondent parfaitement à ton profil. Ton parcours fluide sur Thanks-Boss te permet désormais de passer à l'étape suivante de ta carrière sans perdre de temps.",
          },
        ],
      };
    },
  };
</script>

<style scoped>
  /* Base Section Styling */
  .values-section {
    width: 100vw;
    height: 600px; /* Ajout de la hauteur fixe */
    margin: 0 auto;
    display: flex;
    position: relative;
    justify-content: flex-start;
    align-items: center;
    left: 50%;
    transform: translateX(-50%);
    overflow: hidden; /* Ajout pour éviter le scroll horizontal */
  }

  /* Background Image Styling */
  .background-img-wrapper {
    width: 100%;
    max-width: 100vw; /* Modification ici */
    height: 100%; /* Modification pour correspondre à la hauteur du parent */
    position: relative;
    margin: 0; /* Modification ici */
  }

  .background-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    z-index: 1;
  }

  /* Content Container */
  .content-wrapper {
    position: absolute;
    top: 50%;
    left: 10%;
    transform: translateY(-50%);
    z-index: 2;
    width: 30%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
  }

  /* Cards Wrapper */
  .card-wrapper {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 20px;
  }

  /* Individual Card Styling */
  :deep(.card-container) {
    padding: 20px;
  }

  :deep(.card-container p) {
    font-size: 0.8rem;
  }

  :deep(.card-container h2) {
    font-size: 1.5rem;
    text-align: center;
  }

  /* ✅ MOBILE : petits smartphones (iPhone SE, etc.) */
  @media screen and (max-width: 480px) {
    .values-section {
      height: auto; /* Pour mobile, on laisse la hauteur s'adapter au contenu */
    }
    .background-img-wrapper {
      height: 700px;
    }
    .background-img {
      display: none;
    }
    .content-wrapper {
      left: 0%;
      width: calc(100% - 6vw);
      margin: 0 3vw;
    }
    :deep(.card-container) {
      min-width: 20%;
    }
  }

  /* ✅ MOBILE LARGE : smartphones standards (iPhone 12, Galaxy S21, etc.) */
  @media screen and (min-width: 481px) and (max-width: 767px) {
    .values-section {
      height: auto;
    }
    .background-img-wrapper {
      height: 600px;
    }
    .background-img {
      display: none;
    }
  }

  /* ✅ TABLETTE : format portrait (iPad, Galaxy Tab) */
  @media screen and (min-width: 768px) and (max-width: 1023px) {
    .values-section {
      height: auto;
    }
    .background-img-wrapper {
      height: 500px;
    }
    .background-img {
      display: none;
    }
    .content-wrapper {
      width: 80%;
    }
  }

  /* ✅ TABLETTE LARGE / PETIT DESKTOP : transition vers écran large */
  @media screen and (min-width: 1024px) and (max-width: 1279px) {
    .content-wrapper {
      width: 50%;
    }
    :deep(.card-container) {
      padding: 15px;
    }
    :deep(.card-container p) {
      font-size: 0.7rem;
    }
  }

  /* ✅ DESKTOP STANDARD : PC portable / écrans normaux */
  @media screen and (min-width: 1280px) and (max-width: 1439px) {
    .content-wrapper {
      width: 40%;
    }
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .content-wrapper {
      left: 17%;
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .content-wrapper {
      left: 30%;
    }
  }
</style>
