<template>
  <section class="recruiter-ai-overview">
    <div class="content-wrapper">
      <h2 class="section-title">Qui sommes-nous?</h2>
      <p class="txt-p">
        Combien de personnes ont essuyé refus, postulé en vain, rempli des
        formulaires sans réponse, subi des chefs toxiques ou attendu en vain une
        augmentation ? Trop. C'est pourquoi nous avons créé Thanks-Boss.
        <br /><br />
        Tout commence avec Florentin, diplômé en IA, qui a vécu sept ans sans
        challenge avant de vouloir transformer l'expérience RH. Grâce à l'IA,
        Thanks-Boss assure un matching précis entre talents et recruteurs.
        <br /><br />
        Notre promesse ? Simplifier le recrutement avec une approche plus
        qualitative et transparente. Rejoignez-nous pour révolutionner
        l’embauche !
      </p>

      <div class="features-container">
        <div
          ref="feature1"
          class="global-div margin-bottom"
          data-feature-key="feature1"
        >
          <div class="feature-card relative-1">
            <div class="feature-content">
              <h3 class="h3-style-card">En développement</h3>
              <h4 class="feature-title">
                Nous travaillons activement pour améliorer cette section.
              </h4>
            </div>
          </div>

          <img
            src="../../../assets/home/<USER>"
            alt="image card"
            class="img-card img-1"
          />
        </div>
        <div ref="feature2" class="global-div" data-feature-key="feature2">
          <div class="feature-card relative-2">
            <div class="feature-content">
              <h3 class="h3-style-card">En développement</h3>
              <h4 class="feature-title">
                Nous travaillons activement pour améliorer cette section.
              </h4>
            </div>
          </div>
          <img
            src="../../../assets/home/<USER>"
            alt="image card"
            class="img-card img-2"
          />
        </div>
        <div
          ref="feature3"
          class="global-div margin"
          data-feature-key="feature3"
        >
          <div class="feature-card relative-3">
            <div class="feature-content">
              <h3 class="h3-style-card">En développement</h3>
              <h4 class="feature-title">
                Nous travaillons activement pour améliorer cette section.
              </h4>
            </div>
          </div>
          <img
            src="../../../assets/home/<USER>"
            alt="image card"
            class="img-card"
          />
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  import { reactive } from 'vue';
  export default {
    name: 'RecruiterHighlights',
    data() {
      return {
        isVisible: reactive({
          feature1: false,
          feature2: false,
          feature3: false,
        }),
      };
    },
    mounted() {
      const options = {
        rootMargin: '0px',
      };

      const observer = new IntersectionObserver(
        this.handleIntersection,
        options
      );
      this.$refs.feature1 && observer.observe(this.$refs.feature1);
      this.$refs.feature2 && observer.observe(this.$refs.feature2);
      this.$refs.feature3 && observer.observe(this.$refs.feature3);
    },
    methods: {
      handleIntersection(entries) {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (!entry.target.classList.contains('visible')) {
              entry.target.classList.add('visible');
            }
          }
        });
      },
    },
  };
</script>

<style scoped>
  .recruiter-ai-overview {
    padding: 3rem 1.5rem;
    background: linear-gradient(
      to right,
      rgb(223 223 223),
      rgb(222 222 222),
      rgb(224 224 224),
      rgb(242 242 242),
      rgb(241 241 241)
    );
    text-align: center;

    width: 100vw;
    display: flex;
    justify-content: center;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
  }

  .h3-style-card {
    font-family: 'Anton';
    font-weight: 500;
    font-size: 28px;
    color: #f6b336;
  }

  .txt-p {
    font-size: 16px;
    max-width: 50rem;
    text-align: left;
  }

  .content-wrapper {
    max-width: 1440px;
    margin: 0 auto;
    padding-top: 30px;
    padding-bottom: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .section-title {
    font-size: 2rem;
    margin-bottom: 2rem;
    max-width: 1000px;
  }

  .features-container {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-around;
    width: 100%;
    margin-bottom: 14rem;
    margin-top: -5rem;
  }

  .img-card {
    width: 22rem;
  }
  .global-div {
    width: 40%;
    height: 17rem;
    opacity: 0;
    transform: translateY(20px);
  }

  .global-div[data-feature-key='feature1'].visible {
    opacity: 0;
    transform: translateY(0);
    animation: fadeInUp 1s ease forwards;
    animation-delay: 0.2s;
  }

  .global-div[data-feature-key='feature2'].visible {
    opacity: 0;
    transform: translateY(0);
    animation: fadeInUp 1s ease forwards;
    animation-delay: 0.6s;
  }

  .global-div[data-feature-key='feature3'].visible {
    opacity: 0;
    transform: translateY(0);
    animation: fadeInUp 1s ease forwards;
    animation-delay: 1s;
  }

  @keyframes fadeInUp {
    0% {
      opacity: 0;
      transform: translateY(30px) scale(0.8);
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  .feature-card {
    background: #fff;
    border-radius: 10%;
    padding: 1.5rem;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
    width: 18rem;
    position: relative;
    height: 12rem;
  }

  .relative-1 {
    position: relative;
    right: 3rem;
    top: 11rem;
    z-index: 2;
  }
  .relative-2 {
    position: relative;
    left: -4rem;
    top: 14rem;
    z-index: 2;
  }
  .img-1 {
    position: relative;
    bottom: -1rem;
    z-index: 1;
  }
  .img-2 {
    position: relative;
    top: -3rem;
    z-index: 1;
  }
  .relative-3 {
    position: relative;
    right: -15rem;
    top: 19rem;
  }
  .feature-content {
    padding: 14px;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
    justify-content: space-between;
  }

  .feature-icon {
    font-size: 2.5rem;
    color: #f39c12;
    margin-bottom: 1rem;
  }

  .feature-title {
    font-size: 15px;
    color: #333;
  }

  .feature-description {
    font-size: 0.95rem;
    color: #666;
    line-height: 1.4;
  }

  .cta-button {
    padding: 0.8rem 1.5rem;
    background-color: #f39c12;
    color: black;
    font-weight: bold;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    margin-top: 2rem;
  }
  @media screen and (max-width: 991px) {
    .cta-container {
      margin-top: 0;
      display: flex;
      flex-direction: column;
    }
    .cta-container {
      padding: 5px;
    }
    .features-container {
      display: flex;
      flex-direction: column;
      margin-top: 5rem;
      align-items: center;
      margin-bottom: 0;
    }

    .relative-1 {
      top: -4rem;
      right: 0;
    }

    .relative-2 {
      left: 0;
      top: -7rem;
    }

    .relative-3 {
      top: -4rem;
      right: 0;
    }
    .txt-p {
      text-align: center;
    }
    .global-div {
      width: 100%;
      height: auto;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column-reverse;
    }
    .feature-card {
      background: #fff;
      border-radius: 10%;
      padding: 1.5rem;
      box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
      width: 18rem;
      position: relative;
      height: auto;
    }
    .margin {
      margin-top: -5rem;
    }
    .margin-bottom {
      margin-bottom: 1rem;
    }
  }
</style>
