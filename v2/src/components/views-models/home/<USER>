<template>
  <section class="how-section">
    <div class="content-wrapper">
      <h2>Comment ça marche ?</h2>

      <div class="card-display">
        <NumberedCard
          cardNumber="1"
          title="Rechercher des candidats"
          :textContent="card1text"
          :btnText="btn1"
          @card-btn-click="test"
        />

        <NumberedCard
          cardNumber="2"
          title="Mettre en favoris les meilleurs profils"
          :textContent="card2text"
          :btnText="btn2"
          @card-btn-click="test"
        />

        <NumberedCard
          cardNumber="3"
          title="Prendre contact"
          :textContent="card3text"
          :btnText="btn3"
          @card-btn-click="test"
        />
      </div>
    </div>
  </section>
</template>

<script>
  import NumberedCard from '@/components/cards/NumberedCard.vue';
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';

  export default {
    name: 'HowDoesItWorkRecruiter',

    components: {
      NumberedCard,
      PrimaryRoundedButton,
    },
    data() {
      return {
        card1text: `Entamez une conversation avec notre assistant virtuel pour définir les profils de candidats que vous recherchez.`,
        card2text: `Une fois vos besoins définis, vous pouvez poster une offre et accéder à une large base de candidats présélectionnés par Thanks-Boss."`,
        card3text: `Grâce à Thanks-Boss, recrutez rapidement les candidats les plus qualifiés qui correspondent parfaitement à vos exigences.`,
        btn1: `example 1`,
        btn2: `example 2`,
        btn3: `example 3`,
      };
    },

    methods: {
      // TODO implémenter les fonctionnalités des btns
      test() {
        //console.log(
        //  'l-36 HowDoesItWorkRecruiter.vue => getting the click from children (main comp > card comp > btn comp)'
        //);
      },
    },
  };
</script>

<style scoped>
  .how-section {
    background-color: var(--home-how-section-bg-color);
    width: 100%;
    display: flex;
    justify-content: center;
    padding: 20px;
  }

  .content-wrapper {
    padding-top: 30px;
    padding-bottom: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .content-wrapper h2 {
    margin-bottom: 40px;
  }

  .card-display {
    height: 1000px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
  }

  /* desktop */
  @media screen and (min-width: 992px) {
    .card-display {
      height: 2000px;
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 80px;
      height: fit-content;
    }
  }

  @media screen and (min-width: 990px) and (max-width: 1250px) {
    .card-display {
      gap: 10px;
      height: fit-content;
    }

    .card-display .card {
      transform: scale(0.5);
    }

    .content-wrapper h2 {
      font-size: 1.8rem;
    }
  }

  /* large desktop */
  @media screen and (min-width: 1800px) {
    .content-wrapper {
      width: 70%;
    }

    .card-display {
      gap: 170px;
    }
  }

  /* x-large desktop */
  @media screen and (min-width: 2400px) {
    .content-wrapper {
      width: 60%;
    }
  }
</style>
