<template>
  <section class="how-section">
    <div class="content-wrapper">
      <h2>Booste ta recherche d'emploi avec Thanks-Boss 🚀</h2>

      <div class="image-card-container">
        <!-- Each image and card are grouped together -->
        <div class="image-card-group">
          <img
            src="../../../../documentation/pages/Home/stepsjob1.svg"
            alt="Image 1"
            class="image"
          />
          <NumberedCard
            class="card-overlay"
            cardNumber="1"
            title="Je me fais aider"
            :textContent="card1text"
          />
        </div>

        <div class="image-card-group">
          <img
            src="../../../../documentation/pages/Home/stepsjob2.svg"
            alt="Image 2"
            class="image"
          />
          <NumberedCard
            class="card-overlay"
            cardNumber="2"
            title="Je postule"
            :textContent="card2text"
          />
        </div>

        <div class="image-card-group">
          <img
            src="../../../../documentation/pages/Home/stepsjob3.svg"
            alt="Image 3"
            class="image"
          />
          <NumberedCard
            class="card-overlay"
            cardNumber="3"
            title="Je me lance"
            :textContent="card3text"
          />
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  import NumberedCard from '@/components/cards/NumberedCard.vue';

  export default {
    name: 'HowDoesItWork',

    components: {
      NumberedCard,
    },
    data() {
      return {
        card1text: `Tu entames une conversation avec notre assistant virtuel autour de tes attentes et tes aspirations. <br> <br> Thanks-Boss t’offre une sélection de jobs qui matchent avec tes critères les plus pertinents.`,
        card2text: `Une fois ton profil complété, tu peux postuler à une large variété de jobs présélectionnés par Thanks-Boss.<br> <br> À toi de saisir l’opportunité qui te convient !`,
        card3text: `Grâce à Thanks-Boss, commence ta nouvelle aventure professionnelle qui correspond parfaitement à tes attentes.<br> <br> Lance-toi dans une carrière épanouissante, adaptée à tes préférences et à ton style de vie.`,
      };
    },
  };
</script>

<style scoped>
  .how-section {
    text-align: center;
    background-color: var(--surface-bg);
    position: relative;
    width: 100vw;
    display: flex;
    justify-content: center;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
  }

  .content-wrapper {
    width: 100%;
  }

  h2 {
    padding: 35px;
  }

  .image-card-container {
    width: 100%;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 30px;
    flex-wrap: wrap;
  }

  .image-card-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    align-self: center;
  }

  .image {
    width: 100%;
    max-width: 400px;
    height: 260px;
    border-radius: 15px;
    object-fit: cover;
  }

  .card-overlay {
    width: 90%;
    max-width: 320px;
    min-height: 250px; /* Assurer un bon rendu même si le texte est court */
    display: flex;
    flex-direction: column;
    justify-content: center;
    border-radius: 15px;
    background-color: white;
    text-align: center;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
    margin-top: -50px;
  }

  /* ✅ MOBILE : petits smartphones (iPhone SE, etc.) */
  @media screen and (max-width: 480px) {
    .image-card-container {
      flex-direction: column;
      gap: 20px;
    }

    .image-card-group {
      max-width: 100%;
    }

    .image {
      width: 100%;
      max-width: 380px;
    }

    .card-overlay {
      width: 90%;
      max-width: 280px;
    }
  }

  /* ✅ MOBILE LARGE : smartphones standards (iPhone 12, Galaxy S21, etc.) */
  @media screen and (min-width: 481px) and (max-width: 767px) {
    .image-card-container {
      flex-direction: column;
      gap: 20px;
    }

    .image-card-group {
      max-width: 100%;
    }

    .image {
      width: 100%;
      max-width: 380px;
    }

    .card-overlay {
      width: 90%;
      max-width: 280px;
    }
  }

  /* ✅ TABLETTE : format portrait (iPad, Galaxy Tab) */
  @media screen and (min-width: 768px) and (max-width: 1023px) {
    .image {
      width: 100%;
      max-width: 350px;
    }

    .card-overlay {
      max-width: 320px;
    }
  }

  /* ✅ TABLETTE LARGE / PETIT DESKTOP : transition vers écran large */
  @media screen and (min-width: 1024px) and (max-width: 1279px) {
    .image {
      max-width: 380px;
    }
    .card-overlay {
      max-width: 340px;
    }
  }
</style>
