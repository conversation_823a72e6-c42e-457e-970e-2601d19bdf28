<template>
  <section class="sponsor-section">
    <div class="sponsor-content">
      <div class="content-wrapper">
        <h2>Nos partenaires</h2>

        <div class="sponsor-wrapper">
          <div class="sponsor-track">
            <div
              v-for="(sponsor, index) in sponsors.concat(sponsors)"
              :key="index"
              class="sponsor"
            >
              <a target="_blank" rel="noopener noreferrer" :href="sponsor.link">
                <img :src="sponsor.logo" :alt="sponsor.alt" class="logo" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  export default {
    name: 'OurPartners',

    data() {
      return {
        sponsors: [
          {
            logo: require('@/assets/partners/frenchtech.svg'),
            alt: 'Logo cci Toulouse Haute-Garonne',
            link: 'https://lafrenchtech.gouv.fr/',
            description: 'Accompagnement stratégique',
          },

          {
            logo: require('@/assets/partners/Logo_CCI.svg'),
            alt: 'Logo cci Toulouse Haute-Garonne',
            link: 'https://www.toulouse.cci.fr/',
          },
          {
            logo: require('@/assets/partners/Logo_BlackHive.svg'),
            alt: 'Logo Black Hive',
            link: 'https://blackhive.fr/',
          },
          {
            logo: require('@/assets/partners/logo_eigsi_alumni-2.png'),
            alt: 'Eigsi Violet Alumni',
            link: 'https://www.linkedin.com/company/eva-eigsi-violet-alumni/',
          },
          {
            logo: require('@/assets/partners/Insight_Nest.svg'),
            alt: 'Insight Nest',
            link: 'https://www.insightnest.fr/',
          },
          {
            logo: require('@/assets/partners/logo-reso-incubateurspepinieres.svg'),
            alt: 'Réso Incubateur',
            link: 'https://www.agence-adocc.com/',
          },
          {
            logo: require('@/assets/partners/Logo_Beauvoir.svg'),
            alt: 'Beauvoir',
            link: 'https://www.beauvoir.co/',
          },
          {
            logo: require('@/assets/partners/airoomsstyle.svg'),
            alt: 'AI Room Styles',
            link: 'https://www.airoomstyles.com/',
          },
          {
            logo: require('@/assets/partners/logo_digitair_noir.png'),
            alt: 'Digit Air',
            link: 'https://digit-air.com/',
          },
          {
            logo: require('@/assets/partners/logo_fractal-apps.png'),
            alt: 'Fractal Apps',
            link: 'https://www.fractal-apps.com/',
          },
          {
            logo: require('@/assets/partners/gaiaconnect.svg'),
            alt: 'Logo Gaia Connect',
            link: '',
          },
        ],
      };
    },
  };
</script>

<style scoped>
  .sponsor-section {
    background-color: var(--surface-bg-2);
    width: 100vw;
    display: flex;
    justify-content: center;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    padding: 20px 0;
  }

  .sponsor-content {
    width: 100%;
    max-width: 1440px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 8%;
  }

  .content-wrapper {
    padding-top: 30px;
    padding-bottom: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
  }

  h2 {
    margin-bottom: 40px;
  }

  /* Logo Container */
  .sponsor-wrapper {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    position: relative;
  }

  /* Moving Container */
  .sponsor-track {
    display: flex;
    align-items: center;
    gap: 20px;
    animation: loopLogos 15s linear infinite;
    width: max-content;
  }

  /* Sponsor Logos */
  .sponsor {
    display: flex;
    justify-content: center;
  }

  .logo {
    flex-shrink: 0;
    max-height: 100px;
    width: 130px;
    margin: 10px;
    opacity: 0.6;
  }

  /* Animation */
  @keyframes loopLogos {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(-50%);
    }
  }

  /* Responsive Design */
  @media screen and (max-width: 768px) {
    .sponsor-track {
      animation: loopLogos 10s linear infinite;
    }

    .logo {
      max-width: 100px;
      max-height: 80px;
    }
  }
</style>
