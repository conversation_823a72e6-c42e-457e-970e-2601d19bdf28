<template>

    <section class="search-section">

        <div class=content-wrapper>

            <div class="content">
                <h2>Lancer la recherche avec Thanks-Boss !</h2>
                <p>Lorem ipsum dolor sit amet consectetur. Eget ac mauris integer aenean sed malesuada volutpat pulvinar auctor. Pellentesque id interdum adipiscing lectus magna quam amet risus. Eu tellus aliquet massa maecenas nunc pellentesque fringilla et nulla. Lectus tortor cras tortor auctor integer enim at pellentesque scelerisque.</p>
                
                <div class="btn-wrapper">
                    <PrimaryRoundedButton textContent="Lancer la recherche" btnColor="secondary" @btn-click="gotoPage('search')"/>
                </div>
            </div>
            
        </div>

    </section>

</template>

<script setup>
import gotoPage from '@/utils/router';
</script>

<script>
import PrimaryRoundedButton from '../../buttons/PrimaryRoundedButton.vue';

export default {
    name: 'TryThanksBoss',

    components: {
        PrimaryRoundedButton,
    },
};
</script>

<style scoped>
.search-section {
    background-color: var(--home-search-section-bg-color);
    width: 100%;
    display: flex;
    justify-content: center;
    padding: 20px;
}

.content-wrapper {
    padding-top: 30px;
    padding-bottom: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.content {
    background-color: var(--home-searchdiv-bg-color);
    border-radius: 30px;
    padding: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    min-height: 400px;
}

.btn-wrapper {
    display: flex;
    justify-content: center;
}

/* small desktop */
@media screen and (min-width: 992px) {
    .content-wrapper{
        width: 80%;
    }

    .content {
        min-height: 300px;
        max-width: 1200px;
    }
}

/* medium desktop */
@media screen and (min-width: 1400px) {
    .content-wrapper{
        width: 70%;
    }
}

/* large desktop */
@media screen and (min-width: 1600px) {
    .content-wrapper{
        width: 60%;
    }
}

/* x-large desktop */
@media screen and (min-width: 1800px) {
    .content-wrapper{
        width: 50%;
    }
}

/* xx-large desktop */
@media screen and (min-width: 2200px) {
    .content-wrapper{
        width: 40%;
    }
}
</style>