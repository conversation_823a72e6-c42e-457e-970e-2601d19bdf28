<template>
  <section class="recruiter-steps">
    <div class="recruiter-content">
      <div class="white-extension"></div>
      <div class="content-wrapper">
        <h3 class="txt-light">Thanks-Boss Recruteur</h3>
        <h2 class="section-title">
          Découvrez et embauchez des talents qualifiés partout en France avec
          notre plateforme propulsée par l'IA
        </h2>
        <p class="section-intro">
          Nous comprenons les attentes et frustrations des recruteurs: des
          profils non pertinents, un processus long et compliqué, et des
          difficultés à suivre les performances.
        </p>
        <div class="cta-container">
          <button class="cta-button" @click="redirectToContact">
            Contactez-nous
          </button>
          <button
            v-if="!$store.state.isLoggedIn"
            class="cta-button cta-button-secondary"
            @click="$router.push('/recruteur/inscription')"
          >
            Créer un compte
          </button>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  export default {
    name: 'RecruiterProcess',
    methods: {
      redirectToContact() {
        this.$router.push('/contact');
      },
    },
  };
</script>

<style scoped>
  .recruiter-steps {
    background-color: #a5b7b6;
    width: 100vw;
    display: flex;
    justify-content: center;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    overflow: hidden;
  }

  .recruiter-content {
    width: 100%;
    max-width: 1300px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 700px;
    background-image: url('../../../assets/home/<USER>');
    background-size: contain;
    background-position: calc(100% + 80px) bottom;
    background-repeat: no-repeat;
    position: relative;
    padding: 0 2rem;
  }

  .white-extension {
    position: absolute;
    bottom: -1px;
    left: -100vw;
    right: -100vw;
    height: 159px;
    z-index: -1;
    background: linear-gradient(to bottom, #e2e2e3, #e1e2e2);
  }

  .content-wrapper {
    width: 50%;
    max-width: 600px;
    padding-left: 0;
    margin: 0 0 10%;
    z-index: 2;
    text-align: center;
  }

  /* Animation de gauche à droite */
  @keyframes slideIn {
    0% {
      background-position: -500% center;
    }
    100% {
      background-position: left center;
    }
  }

  .txt-light {
    font-weight: 500;
    color: var(--text-3);
  }

  .section-title {
    max-width: 650px;
  }

  .section-intro {
    margin: 2rem 0;
    max-width: 600px;
  }

  .steps-container {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin: 2rem auto;
    max-width: 1000px;
  }

  .step-card {
    background: #fff;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    width: 19rem;
    min-height: 300px;
    text-align: left;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    position: relative;
  }

  .step-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    height: 100%;
    padding-top: 2rem;
  }

  .step-icon {
    font-size: 2.5rem;
    color: #f39c12;
    margin-bottom: 1rem;
  }

  .step-title {
    font-size: 1.2rem;
    margin: 1rem 0;
    color: #333;
  }

  .cta-container {
    margin-top: 2rem;
    display: flex;
    justify-content: center;
  }

  .step-description {
    font-size: 0.95rem;
    color: #666;
    line-height: 1.4;
  }

  .cta-button {
    margin: 0 1rem;
    padding: 0.8rem 1.5rem;
    border-radius: 20px;
    font-size: 1rem;
    cursor: pointer;
    background-color: #f6b336;
    color: black;
    font-weight: 400;
  }

  /* ✅ MOBILE : petits smartphones (iPhone SE, etc.) */
  @media screen and (max-width: 480px) {
    .recruiter-content {
      background-image: none;
      height: fit-content;
    }
    .content-wrapper {
      width: 100%;
      margin: 8%;
    }
    .white-extension {
      display: none; /* Cache la banderole sur mobile */
    }
  }

  /* ✅ MOBILE LARGE : smartphones standards (iPhone 12, Galaxy S21, etc.) */
  @media screen and (min-width: 481px) and (max-width: 767px) {
    .recruiter-content {
      background-image: none;
      height: fit-content;
    }
    .content-wrapper {
      width: 100%;
      margin: 8%;
    }
    .white-extension {
      display: none; /* Cache la banderole sur mobile large */
    }
  }

  /* ✅ TABLETTE : format portrait (iPad, Galaxy Tab) */
  @media screen and (min-width: 768px) and (max-width: 1023px) {
    .recruiter-content {
      height: 600px;
    }
    .content-wrapper {
      width: 50%;
    }
    .white-extension {
      display: none;
    }
  }

  /* ✅ TABLETTE LARGE / PETIT DESKTOP : transition vers écran large */
  @media screen and (min-width: 1024px) and (max-width: 1279px) {
    .recruiter-content {
      height: 650px;
    }
    .content-wrapper {
      width: 50%;
    }
    .white-extension {
      display: none;
    }
  }

  /* ✅ DESKTOP STANDARD : PC portable / écrans normaux */
  @media screen and (min-width: 1280px) and (max-width: 1439px) {
  }

  /* ✅ TON ÉCRAN DE BASE : 1440px */
  @media screen and (min-width: 1440px) and (max-width: 1919px) {
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
  }
</style>
