<template>
  <section class="feedback-section">
    <div class="feedback-section">
      <div class="content-wrapper">
        <h2>Vos témoignages</h2>

        <div class="card-display">
          <FeedbackCard
            v-for="(item, index) in feedbackList"
            :key="index"
            :feedback="item"
            class="custom-feedback-card"
          />
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  import FeedbackCard from '../../cards/feedback-card/FeedbackCard.vue';
  import Image9 from '@/assets/image9.png';
  import Image10 from '@/assets/image10.webp';
  import Image7 from '@/assets/image7.png';

  export default {
    name: 'Feedbacks',

    components: {
      FeedbackCard,
    },
    data() {
      return {
        feedbackList: [
          {
            id: 1,
            img: Image9,
            title: '<PERSON><PERSON>a <PERSON>',
            subtitle: 'Chargé des Ressources Humaines',
            note: 4,
            description:
              "Mes préférences ont été parfaitement comprises. J'ai trouvé un job qui me correspond vraiment, avec une meilleure rémunération et un bon équilibre vie pro/vie perso. Merci, Thanks-Boss ! 👌",
          },
          {
            id: 2,
            img: Image10,
            title: '<PERSON>hazi B.',
            subtitle: 'Développeur',
            note: 5,
            description:
              'Je viens de décrocher un job en postulant sur Thanks-Boss.Merci du fond du cœur 🥹. Et félicitations pour votre lancement 👏 ',
          },
          {
            id: 3,
            img: Image7,
            title: 'Théo S.',
            subtitle: 'Responsable commercial',
            note: 4,
            description:
              'Pratique, efficace et à l’écoute des attentes des candidats, Thanks-Boss est un bon appui dans toute recherche d’emploi. Je recommande vivement 🤩',
          },
        ],
      };
    },

    methods: {
      // TODO implémenter les fonctionnalités des btns
      test() {
        //console.log(
        //  'l-36 feedbackDoesItWork.vue => getting the click fron children children (main comp > card comp > btn comp)'
        //);
      },
    },
  };
</script>

<style scoped>
  /* Assure que le conteneur des cartes respecte les marges et paddings */
  .feedback-section {
    background: linear-gradient(
      to top,
      rgb(247 190 85),
      rgb(250 226 183),
      var(--surface-bg-2)
    );

    width: 100vw;
    display: flex;
    justify-content: center;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
  }

  .feedback-content {
    width: 100%;
    max-width: 1440px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .content-wrapper {
    padding-top: 30px;
    padding-bottom: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .content-wrapper h2 {
    margin-bottom: 40px;
  }

  .card-display {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    gap: 20px; /* Espace entre les éléments en mobile */
  }

  /* Styles pour chaque feedback card */
  .custom-feedback-card {
    padding: 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 350px;
    max-height: 500px;
    position: relative;
    width: 20rem;
    border-radius: 16px;
    box-shadow: 7px 7px 11px rgba(0, 0, 0, 0.2);
  }

  .custom-feedback-card .description {
    max-height: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
  }

  .custom-feedback-card p {
    margin-bottom: 10px;
  }

  .custom-feedback-card .footer {
    margin-top: auto;
  }

  /* Responsive styling pour desktop */
  @media screen and (min-width: 992px) {
    .card-display {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 40px;
    }

    .custom-feedback-card {
      max-height: 550px;
    }
  }

  /* Large desktop */
  @media screen and (min-width: 1800px) {
    .content-wrapper {
      width: 80%;
    }

    .custom-feedback-card {
      min-height: 450px;
      max-height: 600px;
    }
  }

  /* x-large desktop */
  @media screen and (min-width: 2400px) {
    .content-wrapper {
      width: 70%;
    }

    .custom-feedback-card {
      min-height: 500px;
      max-height: 650px;
    }
  }
</style>
