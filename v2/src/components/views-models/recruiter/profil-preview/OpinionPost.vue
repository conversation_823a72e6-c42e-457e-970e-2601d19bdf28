<template>  
    
    <section class="redact-post-container">
        
        <div class="redact-post">
            
            <!-- button to open editor -->
            <div v-if="!isQuillContainerOpen" class="redact-post-btn" @click="openQuillContainer">
                <p><i>Clique pour commencer à écrire ton avis</i></p>
            </div>
            

            <!-- quill container -->
            <div v-else class="quill-container">

                <!-- editor -->
                <div id="editor"></div>

                <!-- toolbar -->
                <div id="toolbar">

                    <!-- hidden text toolbar -->
                    <div class="hidden2" id="hidden-toolbar">
                        <button class="ql-underline" ></button>
                        <button class="ql-bold"></button>
                        <button class="ql-italic"></button>
                    </div>

                    <!-- visible toolbar -->
                    <div class="toolbar-btns-wrapper">

                        <!-- btn toggle text toolbar -->
                        <button id="custom-button" @click="toggleToolbarTextOptionsPanel">
                            <img src="@/assets/social/social-page-redact-post-text.svg" alt="text" class="btn-icon" />
                        </button>

                        <!-- btn add image -->
                        <button id="custom-button" @click="openFileInput">
                            <img src="@/assets/social/social-page-redact-post-addimg.svg" alt="plus" class="btn-icon"/>
                        </button>
                        <input ref="fileInput" type="file" class="hidden" @change="handlePhotoChange" accept="image/*" />

                        <!-- btn tag -->
                        <button id="custom-button">
                            <img src="@/assets/social/social-page-redact-post-tag.svg" alt="tag" class="btn-icon" />
                        </button>

                        <!-- btn add file (ex: cv) -->
                        <button id="custom-button">
                            <img src="@/assets/social/social-page-redact-post-pj.svg" alt="pj" class="btn-icon" />
                        </button>

                        <!-- btn smiley -->
                        <button id="custom-button">
                            <img src="@/assets/social/social-page-redact-post-emoji.svg" alt="emoji" class="btn-icon" />
                        </button>

                    </div>

                </div>

            </div>

            <div class="btn-container">

                <!-- button to save & close post -->
                <img 
                    v-if="isQuillContainerOpen" 
                    src="@/assets/social/social-page-redact-post-close.svg" 
                    alt="cloturer" 
                    class="btn-icon" 
                    @click="closeQuillContainerAndEditor"
                />

                <!-- button to submit post -->
                <img 
                    src="@/assets/social/social-page-redact-post-letter.svg" 
                    alt="lettre" 
                    class="btn-icon"
                    @click="submitPost"
                />
            </div>

        </div>

    </section>

</template>

<script>
import Quill from 'quill';
import "quill/dist/quill.core.css";
import 'quill/dist/quill.snow.css';
import { postPost } from '@/services/post.service';


export default {
    name: 'RedactPost',

    components: {
        Quill,
    },

    data() {
        return {
            isQuillContainerOpen: false,        //  state of quill container
            isEditorSetup: false,               //  state of quill editor
            isToolbarTextOptionsPanelOn: false, //  state of toolbar text option panel
            quillInstance: null,                //  quill instance
            postBodyContent: null,              //  post body data content
            postContent: null,                  //  postContent
            photoUploaded: [],                  //  list of uploaded photos
            previewPhoto: [],                   //  previews uploaded photos
        }
    },

    updated() {
        //  if quill container is open and editor is not open => setup editor
        if (this.isQuillContainerOpen && !this.isEditorSetup) this.setupQuillEditor();
    },

    methods: {
        //  open quill container
        openQuillContainer() {
            this.isQuillContainerOpen = true;
        },

        //  close quill container & close quill editor
        closeQuillContainerAndEditor() {
            this.isQuillContainerOpen = false;
            this.isEditorSetup = false;
        },

        //  setup quill editor & save quill instance in this.quillInstance
        setupQuillEditor() {
            const quill = new Quill(`#editor`, {
                theme: "snow",
                placeholder: 'Commence a écrire ton avis',
                modules: {
                    toolbar: "#toolbar",
                },
            });

            quill.on('editor-change', (eventName, ...args) => {
                if (eventName === 'text-change') {
                    // args[0] will be delta
                    this.savePostContent();
                } return;
                /*else if (eventName === 'selection-change') {
                    // args[0] will be old range
                }*/
            });


            if (this.postBodyContent) {
                quill.setContents(this.postBodyContent);
            }

            this.quillInstance = quill;
            this.isEditorSetup = true;
        },

        //  switch between state for isToolbarTextOptionsPanelOn, open/close text options panel
        toggleToolbarTextOptionsPanel() {
            if (this.isToolbarTextOptionsPanelOn) document.getElementById("hidden-toolbar").classList = "hidden2";
            else document.getElementById("hidden-toolbar").classList = "text-toolbar";
            this.isToolbarTextOptionsPanelOn = !this.isToolbarTextOptionsPanelOn;
        },

        // save post content
        savePostContent() {
            const deltaContent = this.quillInstance.getContents();      //  get raw html quill instance content
            //const content = JSON.stringify(deltaContent);             //  save content as json
            this.postBodyContent = deltaContent;
        },

        //  TODO submit post
        submitPost() {
            this.postContent["body"] = this.postBodyContent;
            this.postContent["photoList"] = this.photoUploaded;
            const response = postPost(this.postContent);
        },

        //  upload img
        openFileInput() {
            this.$refs.fileInput.click();
        },

        //  handle user photo changes
        handlePhotoChange(event) {
            const file = event.target.files[0];
            if (file) {
                this.photoUploaded.push(file);
                const reader = new FileReader();
                reader.onload = () => {this.previewPhoto.push(reader.result);};
                reader.readAsDataURL(file);
            } 
        },

        //  delete photo from this.photoUploaded
        deletePhoto(photoIndex) {
            this.previewPhoto.splice(photoIndex, 1);
        },
    }
}
</script>

<style scoped>
.redact-post-container {
    background-color: var(--surface-bg-2);
    border-radius: 10px;
    box-shadow: 0px 0px 10px 0px rgba(38, 40, 43, 0.1);
    display: flex;
    justify-content: center;
}

.redact-post {
    width: 100%;
    padding-block: 24px;
    padding-inline: 0.5rem;
    display: flex;
    justify-content: space-between;
}

.redact-post-btn {
    width: 95%;
    background-color: rgba(246, 179, 55, 0.2);
    border-radius: 2px;
    padding-inline: 0.2rem;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.btn-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.btn-icon {
    cursor: pointer;
    height: 32px;
    width: 32px;
}

.quill-container {
    border-radius: 2px;
    width: 100%;
    display: flex;
    flex-direction: column;
    margin-inline-end: 1rem;
}

.ql-container {
    background-color: rgba(246, 179, 55, 0.2);
    border-radius: 2px;
    border: none;
    min-height: 120px;
    width: 100%;
    padding: 0px;
}

.ql-toolbar {
    border: none;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.hidden {
    display: none;
}

.hidden2 {
    visibility: hidden;
}

.toolbar-btns-wrapper {
    height: fit-content;
    display: flex;
    gap: 20px
}

/* editor photo gallery */
.gallery-container {
    width: 100%;
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.uploaded-photo {
    width: 150px;
    height: fit-content;
    display: flex;
    flex-direction: column;
}

.photo-delete-btn {
    position: relative;
    top: 35px;
    left: 5px;
    width: 25px;
    height: 25px;
}

.v3-emoji-picker {
    position: absolute;
}
</style>