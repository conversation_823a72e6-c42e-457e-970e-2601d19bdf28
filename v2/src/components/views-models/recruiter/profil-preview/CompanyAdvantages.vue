<template>
  <div class="packet-chips">
    <div v-for="(advantage, index) in advantageList" :key="index" class="chip-wrapper">
      <Chip :textContent="advantage.nom" />
    </div>
  </div>
</template>

<script>
import Chip from '@/components/chips/Chip.vue';
/* Importer les données futures
import { getCompanyAdvantages } from '@/...';*/
export default {
  name: 'CompanyAdvantages',

  components: {
    Chip,
  },

  data() {
    return {
      advantageList: [
        // Liste des Avantages simulés
        { nom: 'Avantage 1' },
        { nom: 'Avantage 2' },
        { nom: 'Avantage 3' },
      ], 
    };
  },

  async mounted() {
    try {
      const formData = await getUserAdvantagesSectionDatas();
      this.advantageList = formData.advantagesList || [];
    } catch (error) {
      //console.error('Erreur lors de la récupération des compétences:', error);
    }
  },

  methods: {
    async fetchAdvantages() {
      try {
        const formData = await getUserAdvantagesSectionDatas();
        this.advantageList = formData.advantagesList || [];
      } catch (error) {
        //console.error('Erreur lors de la récupération des compétences:', error);
      }
    },
  },

};
</script>

<style scoped>
.packet-chips {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.chip-wrapper {
  border-radius: 5px;
}
</style>
