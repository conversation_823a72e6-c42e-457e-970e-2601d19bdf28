<template>
  <!-- Fond sombre -->
  <div
    v-if="showConfirmationModal"
    class="overlay"
    @click="showConfirmationModal = false"
  ></div>

  <div class="width" v-if="offer">
    <OfferCardTemplate
      :offer="offer"
      :status-label="statusLabel"
      :status-icon="statusIcon"
      @archive="handleArchive(offer.id)"
      @unarchive="handleUnarchive(offer.id)"
      @delete="handleShowConfirmation(offer.id)"
      @edit="handleModify(offer.id)"
      @view="handleView(offer.id)"
    />
  </div>

  <!-- Boîte de confirmation personnalisée -->
  <ConfirmationModal
    v-if="showConfirmationModal"
    class="confirmation-modal"
    title="Confirmer la suppression"
    description="Êtes-vous sûr de vouloir supprimer cette offre ?"
    @close="showConfirmationModal = false"
    @confirm="handleDeleteConfirmed"
  />
</template>

<script>
  import ConfirmationModal from '@/components/modal/confirmation/ConfirmationModal.vue';
  import gotoPage from '@/utils/router.js';
  import { toaster } from '@/utils/toast/toast.js';
  import OfferCardTemplate from '../../../../components/cards/recruiter/job-cards/OfferCardTemplate.vue';
  import {
    archiveJobOffers,
    deleteJobOffers,
    getJobOffersById,
    unarchiveJobOffers,
  } from '../../../../services/offer.service';

  export default {
    name: 'OfferCard',
    props: {
      offer: {
        type: Object,
        required: true,
      },
      offers: Array,
    },
    components: {
      OfferCardTemplate,
      ConfirmationModal,
    },
    data() {
      return {
        showConfirmationModal: false, // Etat pour afficher ou cacher le modal
        offerToDelete: null, // Pour garder la référence de l'offre à supprimer
      };
    },
    computed: {
      statusLabel() {
        if (this.offer.closed === true) {
          this.offer.publie = false;
          return 'Offre archivée';
        } else if (this.offer.closed === false && this.offer.publie === true) {
          return 'Offre publiée';
        }
        if (this.offer.status === 'deleted') {
          this.offer.publie = false;
          return 'Offre supprimée';
        }
        return this.offer.publie ? 'Offre publiée' : 'Offre non publiée';
      },
      statusIcon() {
        if (this.offer.closed === true || this.offer.status === 'deleted') {
          return require('@/assets/icons/deleted-icon.svg');
        }
        return this.offer.publie
          ? require('@/assets/icons/valid-icon.svg')
          : require('@/assets/icons/unpublished-icon.svg');
      },
    },
    methods: {
      gotoPage,

      // Fonction pour afficher le modal de confirmation
      handleShowConfirmation(offerId) {
        this.offerToDelete = offerId; // Enregistrer l'ID de l'offre à supprimer
        this.showConfirmationModal = true; // Afficher le modal
      },

      // Fonction confirmée pour supprimer l'offre
      async handleDeleteConfirmed() {
        if (this.offerToDelete) {
          await this.handleDelete(this.offerToDelete); // Appeler la méthode de suppression
          this.showConfirmationModal = false; // Cacher le modal après confirmation
          this.offerToDelete = null; // Réinitialiser l'ID de l'offre
        }
      },

      async handleDelete(offerId) {
        try {
          const result = await deleteJobOffers(offerId);
          toaster.showSuccessPopup('Offre supprimée avec succès.');

          // Mise à jour de l'état local de l'offre
          const offerIndex = this.$parent.offers.findIndex(
            (offer) => offer.id === offerId
          );
          if (offerIndex !== -1) {
            // Retirer l'offre de la liste
            this.$parent.offers.splice(offerIndex, 1);
          }

          this.offer.statut = 'deleted';
        } catch (error) {
          //console.error(error.message);
        }
      },

      async handleArchive(offerId) {
        try {
          const result = await archiveJobOffers(offerId);

          // Trouver l'offre dans la liste locale et mettre à jour son état
          const offerIndex = this.$parent.offers.findIndex(
            (offer) => offer.id === offerId
          );
          if (offerIndex !== -1) {
            this.$parent.offers[offerIndex].closed = true;
          }

          toaster.showSuccessPopup('Offre archivée avec succès.');
        } catch (error) {
          //console.error("Erreur d'archivage:", error.message);
        }
      },

      async handleUnarchive(offerId) {
        try {
          const result = await unarchiveJobOffers(offerId);

          // Trouver l'offre dans la liste locale et mettre à jour son état
          const offerIndex = this.$parent.offers.findIndex(
            (offer) => offer.id === offerId
          );
          if (offerIndex !== -1) {
            this.$parent.offers[offerIndex].closed = false; // Définir closed sur false
          }

          toaster.showSuccessPopup('Offre désarchivée avec succès.');
        } catch (error) {
          //console.error("Erreur d'archivage:", error.message);
        }
      },

      async handleModify(offerId) {
        try {
          //console.log('offerId', offerId);

          // Récupérer l'offre
          const result = await getJobOffersById(offerId);
          //console.log('Objet offerData avant la navigation:', result);

          // Stocker l'objet dans localStorage
          localStorage.setItem('offerData', JSON.stringify(result));

          // Naviguer vers la page sans passer l'objet dans l'URL
          this.$router.push({ path: '/recruteur/offre/redaction' });
        } catch (error) {
          //console.error('Erreur dans handleModify:', error.message);
        }
      },

      async handleView(offerId) {
        //console.log("Prévisualisation de l'offre ID:", offerId);
        try {
          const result = await getJobOffersById(offerId);
          //console.log('Offre pour prévisualisation:', result);

          // Redirige vers la page de prévisualisation avec l'ID de l'offre
          gotoPage(`/recruteur/offre/${offerId}/aperçu`, result);
        } catch (error) {
          //console.error('Erreur lors de la prévisualisation:', error.message);
        }
      },
    },
  };
</script>

<style scoped>
  .width {
    width: 100%;
  }

  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Couleur semi-transparente */
    z-index: 999; /* Juste en dessous de la modale */
  }

  .confirmation-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000; /* Assurez-vous que la modale est au-dessus du contenu */
    width: 100%; /* Cela peut être ajusté selon vos besoins */
    max-width: 500px; /* Ajustez selon la taille de votre modale */
    padding: 20px; /* Optionnel, pour ajouter du padding autour de la modale */
    text-align: center;
  }
</style>
