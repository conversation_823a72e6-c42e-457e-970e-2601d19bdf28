<template>

    <section class="section-container">

        <!-- logo, name and favorite -->
        <div class="presentation-section">

            <div class="avatar-title">

                <div class="img-wrapper">
                    <img v-if="candidate.photo || previewImage" :src="previewImage || getFullImageUrl()"
                        class="avatar-icon" alt="avatar image" />
                    <img v-else src="@/assets/icons/avatar.png" alt="avatar image" class="avatar-icon">
                </div>

                <div class="names">
                    <h5>{{ candidate.first_name || 'Nom' }} {{ candidate.last_name || 'Prénom candidat' }}</h5>
                </div>

            </div>

        </div>

        <!-- contact and location -->
        <div class="section-contact">

            <div class="content-container">

                <div class="contact">

                    <div class="input">
                        <h5>E-mail</h5>
                        <p class="champ">{{ candidate.email || '<EMAIL>' }}</p>
                    </div>

                    <div class="input">
                        <h5>Téléphone</h5>
                        <p class="champ">{{ candidate.numberPhone || '0X XX XX XX XX' }}</p>
                    </div>

                </div>

                <div class="location">
                    <h5>Localisation</h5>
                    <p class="champ">{{ candidate.ville || 'Ville' }}</p>
                </div>

                <div class="btn-wrapper">
                    <PrimaryRoundedButton class="btn-voirProfil" look textContent="Voir le profil" @click="$router.push(`/utilisateur/${candidateId}`)" />
                </div>

            </div>

        </div>

    </section>

</template>

<script>
import { getUserById } from '@/services/account.service.js';
import { baseUrl } from '@/services/axios';
import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';

export default {
    name: 'CandidateBasicsInfo',

    components: {
        PrimaryRoundedButton
    },

    props: {
        candidateId: {
            type: String,
            required: true,
        },
    },

    data() {
        return {
            candidate: {},       
            previewImage: "",   
        };
    },

    async mounted() {
        try {
            if (this.candidateId) {
                this.candidate = await getUserById(this.candidateId);
            } else {
                //console.error("L'ID du candidat est manquant.");
            }
        } catch (error) {
            //console.error("Erreur lors de la récupération des informations du candidat :", error);
        }
    },

    methods: {
        // get photo url
        getFullImageUrl() {
            if (this.candidate.photo) {
                if (this.candidate.photo.name) {
                    if (typeof (this.candidate.photo.name) === 'string') {
                        return URL.createObjectURL(this.candidate.photo);
                    }
                }

                return baseUrl + this.candidate.photo;
            } else if (this.previewImage) {
                return this.previewImage;
            } else {
                return "";
            }
        },
    },
};
</script>

<style scoped>
/* layout */

.section-container {
    display: flex;
    flex-direction: column;
    background-color: var(--surface-bg-2);
    border-radius: 5px;
    margin: 1.2rem 0rem;
    padding: 0.5rem 1.5rem 0rem 0rem;
    width: 100%;
    cursor: default;
}

.content-container {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: fit-content;
    padding: 1rem 0rem 1rem 1.5rem;
}

.presentation-section {
    display: flex;
    flex-direction: row;
    gap: 20px;
    width: 100%;
}

/* wrappers */
.input {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.avatar-title {
    display: flex;
    flex-direction: row;
    justify-content: left;
    width: 100%;
}

.img-wrapper {
    /*width: 10%;*/
    display: flex;
    justify-content: center;
}

.contact {
    display: flex;
    flex-direction: row;
    width: auto;
}

.location {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.champ {
    margin: 1.2rem 0.3rem;
    border-bottom: 1px solid var(--primary-rounded-btn-bg-color); 
    padding-bottom: 10px;
    width: fit-content;
}

/*utils*/
.favorite-wrapper {
    align-content: center;
}

.avatar-icon {
    width: auto;
    height: 75px;
    margin: 10px;
    cursor: pointer;
}

.names {
    width: auto;
    align-content: center;
}

.favicon {
    cursor: pointer;
}

@media screen and (max-width: 491px) {
  .contact{
    flex-direction: column;
}
}

@media screen and (max-width: 800px) {
  .contact{
    flex-direction: column;
}


}
</style>
