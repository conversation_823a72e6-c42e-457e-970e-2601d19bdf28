<template>
  <section class="section-container">

    <!-- container header band -->
    <div class="header-container">
      <h5 class="title">Formations</h5>
    </div>

    <!-- container content -->
    <div class="content-container">

      <!-- list of all formations -->
      <div v-for="(formation, index) in candidate.formation" :key="index" class="formations-container">

        <div class="title-date">

          <div class="input-field">
            <h5>Titre de la formation</h5>
            <v-text-field
                  v-model="formation['titre']"
                  :label="formation['titre']"
                  type="text"
                  readonly
                  single-line
                  variant="solo-filled"
                  flat
                />
          </div>

          <div class="input-field">
            <h5>Date d'obtention</h5>
            <label for="date" hidden>Date</label>
                <input
                  type="date"
                  v-model="formation['obtention']"
                  class="date-input-field"
                  disabled
                />
          </div>

        </div>

        <div class="school-degree">

          <div class="input-field">
            <h5>Organisme de formation</h5>
            <v-text-field
                  v-model="formation['organisme']"
                  :label="formation['organisme']"
                  type="text"
                  readonly
                  single-line
                  variant="solo-filled"
                  flat
                />
          </div>

          <div class="input-field">
            <h5>Niveau d'études</h5>
            <v-text-field
                  v-model="formation['etude']"
                  :label="formation['etude']"
                  type="text"
                  readonly
                  single-line
                  variant="solo-filled"
                  flat
                />
          </div>

        </div>

      </div>

    </div>

  </section>
</template>

<script>
import { getUserById } from '@/services/account.service.js';

export default {
  name: 'CandidateFormationsSection',

  props: {
        candidateId: {
            type: String,
            required: true,
        },
    },

  data() {
    return {
      candidate: {},
    }
  },

  async mounted() {
    try {
      if (this.candidateId) {
          this.candidate = await getUserById(this.candidateId);
      } else {
          //console.error("L'ID du candidat est manquant.");
      }
    } catch (error) {
      //console.error("Erreur lors de la récupération des informations du candidat :", error);
    }
  },
}
</script>

<style scoped>
/* layout */
.section-container {
  background-color: var(--white-200);
  width: 100%;
  border-radius: 2px;
  pointer-events: none;
  cursor: default;
}

.header-container {
  padding: 0.5rem 1.5;
  border-bottom: 1.5px solid var(--surface-bg);
  cursor: default;
}

.content-container {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: fit-content;
  padding: 24px;
}

.formations-container {
  display: flex;
  flex-direction: column;
  padding-top: 1.5rem;
  border-top: 1.5px solid rgba(246, 179, 55, 0.2);
  border-bottom: 1.5px solid rgba(246, 179, 55, 0.2);
}

/* wrappers */
.input-field {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.title-date,
.school-degree {
  display: flex;
  flex-direction: row;
  gap: 10px;
}

/* utils */
.title {
  margin: 0.4rem 0.85rem;
}

.packet-chips {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin: 0.4rem 0rem;
}

.v-input {
  height: 4.1rem;
}

@media screen and (max-width: 638px) {
  .school-degree, .title-date {
    flex-direction: column;
    align-items: center;
  }

}
</style>
