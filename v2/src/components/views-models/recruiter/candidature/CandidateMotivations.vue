<template>
  <section class="section-container">
    <!-- container header band -->
    <div class="header-container">
      <h2 class="title">Motivation</h2>
    </div>

    <div class="content-container">
      <div v-if="getLetterMotivationLink !== '#'" class="motivation-container">
        <div
          class="icons-container"
          v-if="candidate.postulation && candidate.postulation.length > 0"
        >
          <!-- Icône de téléchargement seulement si lettre_motivation est disponible -->
          <a
            v-if="getLetterMotivationLink !== '#'"
            :href="getLetterMotivationLink"
            target="_blank"
            rel="noopener noreferrer"
          >
            <img
              src="@/assets/icons/download-icon.svg"
              alt="icône télécharger"
            />
          </a>
          <!-- Icône de prévisualisation seulement si disponible -->
          <a
            v-if="previewAvailable"
            :href="getLetterMotivationPreviewLink"
            target="_blank"
            rel="noopener noreferrer"
          >
            <img
              src="@/assets/icons/preview-icon.svg"
              alt="icône prévisualisation"
            />
          </a>
        </div>
      </div>

      <div class="aboutme">
        <h5>Pourquoi moi ?</h5>
        <p>{{ messageForJob }}</p>
      </div>
    </div>
  </section>
</template>

<script>
  import { baseUrl } from '@/services/axios';

  export default {
    name: 'CandidateMotivationSection',

    props: {
      candidate: {
        type: Object,
        required: true,
      },
      jobofferId: {
        // Assurez-vous de passer l'ID de l'offre ici
        type: String,
        required: true,
      },
    },
    computed: {
      messageForJob() {
        const postulation =
          this.candidate.postulation &&
          Array.isArray(this.candidate.postulation)
            ? this.candidate.postulation.find((postulation) => {
                return postulation.job.id === Number(this.jobofferId);
              })
            : null;

        return postulation ? postulation.lettre : 'Aucun message disponible.';
      },

      getLetterMotivationLink() {
        const postulation =
          this.candidate.postulation &&
          Array.isArray(this.candidate.postulation)
            ? this.candidate.postulation.find((postulation) => {
                return postulation.job.id === Number(this.jobofferId);
              })
            : null;

        return postulation && postulation.lettre_motivation
          ? `${baseUrl}${postulation.lettre_motivation}`
          : '#';
      },

      getLetterMotivationPreviewLink() {
        const postulation =
          this.candidate.postulation &&
          Array.isArray(this.candidate.postulation)
            ? this.candidate.postulation.find((postulation) => {
                return postulation.job.id === Number(this.jobofferId);
              })
            : null;

        return postulation && postulation.lettre_motivation
          ? `${baseUrl}${postulation.lettre_motivation}`
          : '#';
      },

      previewAvailable() {
        const postulation =
          this.candidate.postulation &&
          Array.isArray(this.candidate.postulation)
            ? this.candidate.postulation.find((postulation) => {
                return postulation.job.id === Number(this.jobofferId);
              })
            : null;
        return postulation && postulation.lettre_motivation !== null;
      },
    },
  };
</script>

<style scoped>
  /* layout */
  .section-container {
    background-color: var(--white-200);
    width: 100%;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .header-container {
    padding: 0.5rem 1.5;
    border-bottom: 1.5px solid var(--surface-bg);
  }

  .content-container {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: fit-content;
    padding: 24px;
  }

  .motivation-container {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin: 0.7rem 0rem 1rem 0rem;
  }

  /*wrappers*/
  .icons-container {
    display: flex;
    gap: 20px;
    cursor: pointer;
  }
  .title {
    margin: 0.4rem 0.85rem;
  }
</style>
