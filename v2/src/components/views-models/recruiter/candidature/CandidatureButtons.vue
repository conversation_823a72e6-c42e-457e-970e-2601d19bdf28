<template>
  <!-- buttons candidatures gestion-->
  <section class="btn-container">
    <!-- Fond sombre -->
    <div
      v-if="showConfirmationModal || showSuccessModal"
      class="overlay"
      @click="showConfirmationModal = false"
    ></div>

    <div class="btn-radio subgrid">
      <CustomRadio
        :field="'Statut de la candidature'"
        :fields="['Acceptée', 'A l\'étude', 'Refusée']"
        :cValue="tempSelectedStatus"
        @radio-selection="handleRadioSelection"
      />
    </div>
    <!-- Modale selon le statut -->
    <template v-if="showConfirmationModal">
      <!-- Acceptée : nouvelle modale -->
      <ConfirmationModalEntretien
        v-if="tempSelectedStatus === 'Acceptée'"
        :candidateName="`${candidate.first_name} ${candidate.last_name}`"
        :candidatePicture="getImgPath(candidate.avatar)"
        @close="showConfirmationModal = false"
        @confirm="handleConfirmation"
      />

      <!-- Autres statuts -->
      <ConfirmationModal
        v-else
        class="confirmation-modal"
        title=""
        :description="confirmationDescription"
        @close="showConfirmationModal = false"
        @confirm="handleConfirmation"
      />
    </template>
  </section>
</template>

<script>
  import CustomRadio from '@/components/buttons/CustomRadio.vue';
  import ConfirmationModal from '@/components/modal/confirmation/ConfirmationModal.vue';
  import ConfirmationModalEntretien from '@/components/modal/confirmation/ConfirmationModalEntretien.vue';
  import {
    acceptCandidateStatus,
    holdCandidateStatus,
    refuseCandidateStatus,
  } from '@/services/search.service.js';
  import { toaster } from '@/utils/toast/toast.js';
  import getImgPath from '@/utils/imgpath.js';

  export default {
    name: 'CandidatureButtons',

    components: {
      CustomRadio,
      ConfirmationModal,
      ConfirmationModalEntretien,
    },

    props: {
      candidate: {
        type: Object,
        required: true,
      },
      offerId: {
        type: String,
        required: true,
      },
    },

    data() {
      return {
        selectedStatus: '',
        tempSelectedStatus: '', // Statut temporaire sélectionné avant confirmation
        showConfirmationModal: false,
        showSuccessModal: false,
        confirmationDescription: '',
        confirmAction: null,
      };
    },

    computed: {
      // Mappage entre le statut réel du candidat et le libellé pour l'affichage
      statusMapping() {
        return {
          "A l'étude": "A l'étude",
          Accepté: 'Acceptée',
          Refuser: 'Refusée',
        };
      },
      reverseStatusMapping() {
        // Mappage inverse pour convertir le libellé affiché en statut réel
        return {
          "A l'étude": "A l'étude",
          Acceptée: 'Accepté',
          Refusée: 'Refuser',
        };
      },
    },

    methods: {
      getImgPath,

      handleRadioSelection(field, value) {
        // Stocke la sélection temporaire
        this.tempSelectedStatus = value;

        // Affiche la modale de confirmation
        this.showConfirmationForRadio(value);
      },

      showConfirmationForRadio(value) {
        const messages = {
          "A l'étude":
            'Êtes-vous sûr de vouloir mettre cette candidature en étude ?',
          Refusée: 'Êtes-vous sûr de vouloir refuser cette candidature ?',
        };

        this.confirmationDescription = messages[value];
        this.confirmAction = async () => {
          await this.updateCandidateStatus(value);
          this.selectedStatus = this.tempSelectedStatus;
        };

        this.showConfirmationModal = true;
      },

      showConfirmation(title, description, action) {
        this.confirmationTitle = title;
        this.confirmationDescription = description;
        this.confirmAction = action;
        this.showConfirmationModal = true;
      },

      handleConfirmation() {
        if (this.confirmAction) {
          this.confirmAction();
        }
        this.showConfirmationModal = false;
      },

      handleCancel() {
        // Réinitialise tempSelectedStatus à la valeur précédente
        this.tempSelectedStatus = this.selectedStatus;
        this.showConfirmationModal = false;
      },

      async updateCandidateStatus(status) {
        const statusMapping = {
          "A l'étude": "A l'étude",
          Acceptée: 'Accepté',
          Refusée: 'Refuser',
        };

        try {
          const realStatus = statusMapping[status];

          if (realStatus === 'Accepté') {
            await acceptCandidateStatus(this.offerId, this.candidate.id);
            toaster.showSuccessPopup(
              `Candidature acceptée et proposition d'entretien envoyée à ${this.candidate.first_name} ${this.candidate.last_name} avec succès`
            );
          } else if (realStatus === 'Refuser') {
            await refuseCandidateStatus(this.offerId, this.candidate.id);
            toaster.showSuccessPopup('Candidature refusée avec succès');
          } else if (realStatus === "A l'étude") {
            await holdCandidateStatus(this.offerId, this.candidate.id);
            toaster.showSuccessPopup('Candidature mise en attente avec succès');
          }
        } catch (error) {
          toaster.showErrorPopup("Erreur lors de l'application du statut");
        }
      },

      proposerEntretien() {
        this.showSuccessModal = false;
        this.$router.push('/recruteur/calendrier');
      },

      voirCalendrier() {
        this.showSuccessModal = false;
        this.$router.push('/recruteur/calendrier');
      },

      closeModals() {
        this.showConfirmationModal = false;
        this.showSuccessModal = false;
      },
    },

    mounted() {
      try {
        const postulationForJob = this.candidate.postulation.find((post) => {
          return post.job.id === Number(this.offerId); // Conversion de offerId en nombre
        });
        if (postulationForJob) {
          const candidatureStatus = postulationForJob.statut;
          // Mapper le statut réel vers le libellé affiché
          this.selectedStatus = this.statusMapping[candidatureStatus] || '';
          this.tempSelectedStatus = this.selectedStatus; // Synchroniser avec la valeur initiale
        } else {
          console.warn('Pas de postulation trouvée pour cette offre.');
          this.selectedStatus = '';
          this.tempSelectedStatus = ''; // Valeur par défaut
        }
      } catch (error) {
        //console.error(
        //  'Erreur lors de la récupération des informations du candidat :',
        //  error
        //);
      }
    },
  };
</script>

<style scoped>
  /* layout */
  .btn-container {
    display: flex;
    flex: 1;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: left;
  }

  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Couleur semi-transparente */
    z-index: 999; /* Juste en dessous de la modale */
  }

  /* wrappers*/
  .btn-wrapper {
    width: fit-content;
    height: fit-content;
  }

  .btn-radio {
    width: 70%;
  }
  .subgrid {
    display: flex;
  }

  .confirmation-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000; /* Assurez-vous que la modale est au-dessus du contenu */
    width: 100%; /* Cela peut être ajusté selon vos besoins */
    max-width: 500px; /* Ajustez selon la taille de votre modale */
    padding: 20px; /* Optionnel, pour ajouter du padding autour de la modale */
    text-align: center;
  }
  .blur-content {
    filter: blur(5px);
  }

  @media screen and (max-width: 800px) {
    .btn-container {
      gap: 0.5rem;
    }

    .btn-wrapper {
      width: 38%;
      height: auto;
    }
    .subgrid {
      display: flex;
      flex-wrap: wrap;
    }

    .field {
      /* width: 85% !important; */
      /* height: 70% !important; */
      margin-bottom: 0rem !important;
    }
  }

  @media screen and (max-width: 524px) {
    .btn-container {
      gap: 0.5rem;
      flex-direction: column;
    }

    .btn-wrapper {
      width: 38%;
      height: auto;
    }
    .subgrid {
      display: flex;
      flex-wrap: wrap;
    }
  }

  @media screen and (max-width: 415px) {
    .btn-wrapper {
      width: 125px;
      height: auto;
    }
  }

  @media screen and (max-width: 328px) {
    .btn-container {
      flex-direction: column;
      align-items: center;
    }
  }
</style>
