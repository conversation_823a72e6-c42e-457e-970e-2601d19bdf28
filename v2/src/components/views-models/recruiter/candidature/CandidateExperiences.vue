<template>
  <section class="section-container">

    <!-- container header band -->
    <div class="header-container">
      <h5 class="title">Expériences professionnelles</h5>
    </div>

    <!-- container content -->
    <div class="content-container">

      <!-- list of all experiences -->
      <div v-for="(experience, index) in candidate.experience" :key="index" class="formations-container">

        <div class="experience-wrapper">

          <div class="title-date">

            <div class="input-field">
              <h5>Titre du poste</h5>
              <v-text-field 
                v-model="experience['position']" 
                readonly
                single-line 
                variant="solo-filled" 
                flat 
              />
            </div>

            <div class="input-field">
              <h5>Dates</h5>
              <div class="date-field">

                <div>
                  <label for="date-debut" class="no-bold">De</label>
                  <input 
                    type="date" 
                    v-model="experience['debut']" 
                    class="date-input-field" 
                    disabled 
                  />
                </div>

                <div>
                  <label for="date-fin" class="no-bold">à</label>
                  <input 
                    type="date" 
                    v-model="experience['fin']" 
                    class="date-input-field" 
                    disabled 
                  />
                </div>

              </div>

            </div>

          </div>

          <div class="company-location">

            <div class="input-field">
              <h5>Entreprise</h5>
              <v-text-field
                  v-model="experience['company']"
                  readonly
                  single-line
                  variant="solo-filled"
                  flat
                />
            </div>

            <div class="input-field">
              <h5>Lieu</h5>
              <v-text-field
                  v-model="experience['place']"
                  readonly
                  single-line
                  variant="solo-filled"
                  flat
                />
            </div>

          </div>

        </div>

        <div class="input-field">

          <h5>Détails</h5>
          <v-text-field
                v-model="experience['exp_detail']"
                readonly
                single-line
                variant="solo-filled"
                flat
              />
        </div>

      </div>

    </div>

  </section>
</template>


<script>
import { getUserById } from '@/services/account.service.js';

export default {
  name: 'CandidateExperiencesSection',

  props: {
        candidateId: {
            type: String,
            required: true,
        },
    },

  data() {
    return {
      candidate: {},
    }
  },

  async mounted() {
    try {
      if (this.candidateId) {
          this.candidate = await getUserById(this.candidateId);
      } else {
          //console.error("L'ID du candidat est manquant.");
      }
    } catch (error) {
      //console.error("Erreur lors de la récupération des informations du candidat :", error);
    }
  },
}

</script>

<style>
.v-input__details {
  height: 2.5rem !important;
}
</style>

<style scoped>
/* layout */
.section-container {
  background-color: var(--white-200);
  width: 100%;
  border-radius: 2px;
}

.header-container {
  padding: 0.5rem 1.5;
  border-bottom: 1.5px solid var(--surface-bg);
}

.bg-color {
  background-color: rgba(246, 179, 55, 0.2);
  border-bottom: none;
}

.content-container {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: fit-content;
  padding: 24px;
}

.formations-container {
  display: flex;
  flex-direction: column;
  padding-top: 1.5rem;
  border-top: 1.5px solid rgba(246, 179, 55, 0.2);
  border-bottom: 1.5px solid rgba(246, 179, 55, 0.2);
}

/* wrappers */

.custom-btn {
  border-radius: 5px;
  border: 1.5px solid rgba(246, 179, 55, 1);
}

.input-field {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.date-input-field {
  width: fit-content;
  background-color: transparent;
  min-height: 56px;
  padding: 16px;
  border-radius: 2px;
}

.title-date,
.company-location {
  display: flex;
  flex-direction: row;
  gap: 10px;
}

.delete-btn {
  display: flex;
  justify-content: end;
}

.experience-wrapper, .header-container {
  display: flex;
  flex-direction: column;
  pointer-events: none;
}

/* utils */
.title {
  margin: 0.4rem 0.85rem;
}

.date-field {
  display: flex;
  align-items: end;
  flex-wrap: wrap;
}

.date-field label {
  align-self: center;
}

.no-bold {
  font-weight: normal;
}

@media screen and (max-width: 638px) {
  .company-location, .title-date {
    flex-direction: column;
    align-items: center;
  }

  .experience-wrapper {
    gap: 2rem;
  }
}
</style>