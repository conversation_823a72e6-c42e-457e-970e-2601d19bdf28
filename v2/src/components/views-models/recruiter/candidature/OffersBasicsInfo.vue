<template>
  <div class="job-container">
    <header class="section-header">
      <div class="img-wrapper">
        <img
          class="logo"
          :src="joboffer.logo_url ? getImgPath(joboffer.logo_url) : defaultLogo"
          alt="candidature logo"
        />
      </div>
      <div class="title-wrapper">
        <h2 class="align-start">{{ joboffer.title }}</h2>
        <h5 class="align-start">
          <u>{{ joboffer.nom_recruteur }}</u>
        </h5>
      </div>
    </header>

    <footer class="section-footer">
      <div class="tag">
        <img src="@/assets/icons/localisation-mini.svg" alt="" />
        <p class="fs12">{{ joboffer.local }}</p>
      </div>

      <div class="tag">
        <img src="@/assets/icons/list-mini.svg" alt="icône de localisation" />
        <p class="fs12">{{ joboffer.contract }}</p>
      </div>

      <div class="tag">
        <img src="@/assets/icons/pc-mini.svg" alt="icône de liste" />
        <p class="fs12">{{ remoteWorkLabel }}</p>
      </div>

      <div class="tag">
        <img src="@/assets/icons/suitcase-mini.svg" alt="icône d'ordinateur" />
        <p class="fs12">{{ joboffer.expérience }}</p>
      </div>

      <div class="tag">
        <img src="@/assets/icons/calendly.svg" alt="icône de calendrier" />
        <p class="fs12">{{ formatDate(joboffer.created_at) }}</p>
      </div>
    </footer>
  </div>
</template>

<script>
  import { getJobOffers } from '@/services/search.service.js';
  // import imgpath from '@/utils/imgpath.js';
  import defaultLogo from '@/assets/icons/company-logo.png';
  import getImgPath from '@/utils/imgpath.js';

  export default {
    name: 'OffersBasicsInfo',

    props: {
      offerId: {
        type: String,
        required: true,
      },
    },

    data() {
      return {
        joboffer: {},
      };
    },

    computed: {
      // Mapper la valeur numérique en texte
      remoteWorkLabel() {
        const jours = Number(this.joboffer.jours_teletravail); // Force la conversion
        switch (jours) {
          case 5:
            return 'Télétravail complet';
          case 0:
            return 'En présentiel';
          case 2:
          case 3:
            return 'Hybride';
          case 1:
          case 4:
            return 'Télétravail partiel';
          default:
            return 'Non spécifié'; // Au cas où la valeur serait invalide ou non définie
        }
      },
    },

    async mounted() {
      try {
        if (this.offerId) {
          // Appel à getJobOffers pour obtenir la liste complète des offres
          const jobOffers = await getJobOffers();
          const offer = jobOffers.results.find(
            (offer) => offer.id == this.offerId
          );
          if (offer) {
            this.joboffer = offer; // Stocker les infos de l'offre trouvée
          } else {
            //console.error(
            //  `L'offre avec l'id ${this.offerId} n'a pas été trouvée.`
            //);
          }
        } else {
          //console.error("L'ID de l'offre d'emploi est manquant.");
        }
      } catch (error) {
        //console.error(
        //  "Erreur lors de la récupération des informations de l'offre :",
        //  error
        //);
      }
    },

    methods: {
      getImgPath,

      getTodayDate() {
        const today = new Date();
        return today.toLocaleDateString('fr-FR', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        });
      },

      formatDate(dateString) {
        if (!dateString) return this.getTodayDate(); // Si la date est absente, retourne la date actuelle.
        const date = new Date(dateString);
        return date.toLocaleDateString('fr-FR', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        });
      },
    },
  };
</script>

<style scoped>
  .job-container {
    margin-bottom: 20px;
  }

  .section-header {
    background-color: var(--surface-bg-2);
    padding: 15px 30px;
    display: flex;
    align-items: center;
    width: 100%;
    min-height: 150px;
    gap: 40px;
  }

  .section-footer {
    background-color: var(--text-1);
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    justify-content: space-evenly;
    flex-wrap: wrap;
    gap: 5px;
    padding: 8px;
    width: 100%;
  }

  .company-logo-wrapper {
    width: fit-content;
    height: fit-content;
  }

  .title-wrapper {
    display: flex;
    flex-direction: column;
    align-items: left;
    text-align: left;
  }

  .tag {
    background-color: var(--surface-bg-2);
    padding: 2px 10px;
    border-radius: 5px;
    display: flex;
    justify-content: start;
    align-items: center;
    gap: 5px;
    min-width: 100px;
    height: 35px;
  }

  .fs12 {
    font-size: 10px;
  }

  .section-container-top {
    display: flex;
    flex-direction: column;
    background-color: var(--surface-bg-2);
    border-radius: 5px;
    margin: 1.2rem 0rem;
    padding: 1.5rem;
    width: 100%;
    gap: 0.9rem;
    pointer-events: none;
    cursor: default;
  }

  /*wrappers*/
  .head-section-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: left;
    gap: 1.3rem;
  }

  .company-logo-wrapper {
    display: flex;
    width: auto;
    height: auto;
    align-items: center;
  }

  .tag-wrapper {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  .title-wrapper {
    width: auto;
  }

  /*utils*/
  .logo {
    border: 1px solid rgba(223, 219, 214, 1);
    /* width: 48px; */
    height: 92px;
    border-radius: 8px;
  }

  .fs12 {
    white-space: nowrap;
  }

  @media screen and (max-width: 820px) {
    .section-header {
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    .section-header h2 {
      font-size: 2rem;
    }
    .tag-wrapper {
      justify-content: flex-start;
    }
    .tag {
      flex: 1 1 45%;
      margin-bottom: 0.5rem;
    }
  }
  @media screen and (max-width: 438px) {
    .tag-wrapper {
      flex-direction: column;
      justify-content: space-between;
      align-items: flex-start;
    }
  }
</style>
