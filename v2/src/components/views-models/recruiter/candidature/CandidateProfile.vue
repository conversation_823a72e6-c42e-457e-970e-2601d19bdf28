<template>

    <section class="section-container">

        <!-- container header band -->
        <div class="header-container">
            <h5 class="title-profile">Profil candidat</h5>
        </div>

        <!-- container content -->
        <div class="content-container">

            <div class="cv-container">
                <h5>CV du candidat</h5>

                <div class="icons-container">
                    <img src="@/assets/icons/download-icon.svg" alt="icône télécharger" />
                    <img src="@/assets/icons/preview-icon.svg" alt="icône prévisualisation" />
                </div>

            </div>

            <div id="activity-field" class="input-field">
                <h5>Secteur d'activité</h5>
                <p class="champ">{{ candidate.activitySector || "Aucun" }}</p>
            </div>

            <div class="activity-and-salary-container">

                <div class="experience-container">

                    <div class="input-field">
                        <h5>Expérience</h5>
                        <p class="champ">{{ candidate.experiences || "Aucun" }}</p>
                    </div>

                </div>

                <!-- Salary -->
                <div class="input">
                    <h5>Salaire brut annuel en euros</h5>
                    <p class="champ">{{ candidate.salaire_souhaite || "Aucun" }} €</p>
                </div>

            </div>

            <div class="aboutme">
                <h5>A propos de moi</h5>
                <p>{{ candidate.about || "Aucun" }}</p>
            </div>

            <div class="activity-and-salary-container">

                <div class="websites">

                    <div class="input-case">
                        <h5>Site internet</h5>
                        <p>{{ candidate.site_url || "Aucun" }}</p>
   
                    </div>

                    <div class="input-case">
                        <h5>Portfolio</h5>
                        <p>{{ candidate.porfolio_url || "Aucun" }}</p>
                    </div>

                </div>

                <div class="websites2">
                    
                    <div class="input-case">
                        <h5>Compte linkedin</h5>
                        <p>{{ candidate.linkedin || "Aucun" }}</p>
                    </div>

                    <div class="input-case">
                        <h5>Autre site</h5>
                        <p>{{ candidate.autre_url || "Aucun" }}</p>
                    </div>

                </div>

            </div>

        </div>

    </section>
</template>


<script>
import { getUserById } from '@/services/account.service.js';

export default {
    name: 'CandidateCriteriaSection',

    props: {
        candidateId: {
            type: String,
            required: true,
        },
    },

    data() {
        return {
            candidate: {
                site_url: 'https://monsite.com', // Exemple d'URL
            },
        };
    },

    async mounted() {
        try {
            if (this.candidateId) {
                this.candidate = await getUserById(this.candidateId);
            } else {
                //console.error("L'ID du candidat est manquant.");
            }
        } catch (error) {
            //console.error("Erreur lors de la récupération des informations du candidat :", error);
        }
    },

    methods: {
        openUrl() {
            window.open(this.candidate.site_url, '_blank');
        },
    },
};
</script>

<style>
/*wrappers*/
.field:hover {
    background-color: transparent !important;
}

.field {
    width: 85% !important;
    height: 70% !important;
    margin-bottom: 0rem !important;
}

.v-input__details {
    visibility: hidden !important;
    min-height: 0px !important;
    min-width: 0px !important;
}

.v-field__overlay {
    background-color: var(--surface-bg-2) !important;
}

.underline2 {
    border-bottom: 1.5px solid rgba(246, 179, 55, 1) !important;
}

.underline1 {
    border-bottom: 1.5px solid rgba(245, 242, 239, 1) !important;
}

.v-field__input {
    background-color: var(--surface-bg-2) !important;
}

/*utils*/
.v-label {
    font-weight: normal !important;
}

</style>

<style scoped>
/* Layout */
.section-container {
    display: flex;
    flex-direction: column;
    background-color: var(--surface-bg-2);
    border-radius: 5px;
    margin: 1.2rem 0rem;
    width: 100%;
}

.header-container {
    padding: 0.5rem 1.5;
    border-bottom: 1.5px solid var(--surface-bg);
}

.bg-color {
    background-color: rgba(246, 179, 55, 0.2);
    border-bottom: none;
}

.content-container {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    height: fit-content;
    padding: 24px 24px 0px 24px;
}

/* wrappers */
.btn-row {
    display: flex;
    justify-content: end;
}

.input-field {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.input-field2 {
    display: flex;
    flex-direction: column;
    width: 49%;
}

.checkbox-wrapper {
    width: fit-content;
}

.cv-container {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin: 0.7rem 0rem 1rem 0rem;
}

.icons-container {
    display: flex;
    gap: 20px;
}

.icons-container img {
    cursor: pointer;
}

.contract-and-remote-options-container {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.subgrid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    width: 75%;
    margin: 0.5rem 0;
}

.experience-container {
    display: flex;
    flex-direction: column;
    width: 50%;
}


.activity-and-salary-container {
    display: flex;
    flex-direction: row;
    width: 100%;
}

.champ {
    margin: 1.2rem 0.3rem;
}

#acitivity-field {
    margin: 0.5rem;
}

.input-case {
    margin: 0.7rem 0;
}

h5, .experience-container, .activity-and-salary-container, .input-field {
    pointer-events: none;
    cursor: default;
}

/* utils*/
.websites2 {
    width: 50%;
}

.websites {
    width: 50%;
}

.title-profile {
    margin: 0.4rem 0.85rem;
}

@media screen and (max-width: 820px) {
    
.experience-container {
    width: 100%;
}
}

@media screen and (max-width: 661px) {
    
    .subgrid {
    display: flex;
    flex-direction: column;
    align-items:center ;
    width: 75%;
    margin: 0.5rem 0;
}

.activity-and-salary-container {
    flex-direction: column;
    margin: 0.5rem 0;
}

.websites2 {
    width:100%;
}

.websites {
    width: 100%;
}


    }

    @media screen and (max-width: 407px) {
    

    }

</style>
