<template>
  <section class="section-container">

    <!-- container header band -->
    <div class="header-container">
      <h5 class="title">Compétences</h5>
    </div>

    <!-- container content -->
    <div class="content-container">

      <!-- skills -->
      <div class="skills">

        <h5>Savoirs-faire</h5>

        <div class="packet-chips">

          <div v-for="(skill, index) in skillList" :key="index" class="chip-wrapper">
            <Chip :textContent="skill.nom" />
          </div>

        </div>

      </div>

      <!-- softskills -->
      <div class="skills">

        <h5>Savoirs-être</h5>

        <div class="packet-chips">

          <div v-for="(softskill, index) in softskillList" :key="index" class="chip-wrapper">
            <Chip :textContent="softskill.nom" />
          </div>

        </div>

      </div>

      <!-- Langages -->
      <div class="langage">
        <h5>Langues</h5>
        <div class="packet-chips">

          <div v-for="(lang, index) in langList" :key="index" class="chip-wrapper">
            <Chip :textContent="lang.langue" />
          </div>

        </div>

      </div>

      <!-- Permis and Mobility -->
      <div class="permit-mobility">

        <div class="input-field">
          <h5>Permis</h5>
          <div class="packet-chips">

            <div v-for="(permit, index) in permitList" :key="index" class="chip-wrapper">
              <Chip :textContent="permit" />
            </div>

          </div>

        </div>

        <div class="input-field">
          <h5>Mobilité</h5>
          <div class="packet-chips">

            <div v-for="(mobility, index) in mobilityList" :key="index" class="chip-wrapper">
              <Chip :textContent="mobility" />
            </div>

          </div>

        </div>
        
      </div>

    </div>

  </section>
</template>


<script>
import Chip from '@/components/chips/Chip.vue';
import CustomCheckbox from '@/components/buttons/CustomCheckbox.vue';

export default {
  name: 'CandidateSkillsSection',

  props: {
    import: {
      type: Boolean,
      default: false,
    }
  },

  components: {
    Chip,
    CustomCheckbox,
  },

  data() {
    return {
      formData: {},
      skillList: [
        { nom: 'Savoir-faire1' },
        { nom: 'Savoir-faire2' },
        { nom: 'Savoir-faire3' },
      ],
      softskillList: [
        { nom: 'Savoir-être1' },
        { nom: 'Savoir-être2' },
        { nom: 'Savoir-être3' },
        { nom: 'Savoir-être4' },
        { nom: 'Savoir-être5' },
        { nom: 'Savoir-être6' },
        { nom: 'Savoir-être7' }
      ],
      langList: [
        { langue: 'Langue1' },
        { langue: 'Langue2' }
      ],
      permitList: [
        'Permis B',
      ],
      mobilityList: [
        'Voiture',
      ],
    }
  },

  mounted() {
    // Mock data instead of real API calls
    this.formData = {}; // Mocked data
    this.permitList = this.formData["permisList"] || this.permitList;
    this.skillList = this.formData["skillsList"] || this.skillList;
    this.mobilityList = this.formData["mobilityList"] || this.mobilityList;
  },

  methods: {
    getFormDatas(field, datas, state) {
      //console.log(field, datas, state);
      this.formData[field] = datas;
      this.isCheckedSkills = state;
    },
  }
}
</script>

<style scoped>
/* layout */
.section-container {
  background-color: var(--white-200);
  width: 100%;
  border-radius: 2px;
  pointer-events: none;
  cursor: default;
}

.header-container {
  padding: 0.5rem 1.5;
  border-bottom: 1.5px solid var(--surface-bg);
  cursor: default;
}

.content-container {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: fit-content;
  padding: 24px;
}

/* wrappers */
.input-field {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.skills {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 10px;
  margin: 0.5rem 0rem 0.5rem 0rem;
}

.langage {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 10px;
  margin: 0.5rem 0rem 0.5rem 0rem;
}

.permit-mobility {
  display: flex;
  flex-direction: row;
  width: 100%;
  margin-top: 0.5rem;
}

/*utils*/
.title {
  margin: 0.4rem 0.85rem;
}

.packet-chips {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}
</style>
