<template>
  <!-- question for candidate -->
  <section class="section-container">
    <!-- container header band -->
    <div class="header-container">
      <h5 class="title">Questions</h5>
    </div>

    <div class="content-container">
      <h5>Question recruteur 1 ?</h5>
      <v-textarea v-model="formData.questions[0]" type="text" readonly variant="solo-filled" flat />
      <h5>Question recruteur 2 ?</h5>
      <v-textarea v-model="formData.questions[1]" type="text" readonly variant="solo-filled" flat />
    </div>

  </section>
</template>

<script>
export default {
  name: 'CandidateAnswers',
  data() {
    return {
      formData: {
        questions: [
          'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
          'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
        ]
      }
    };
  },
  methods: {
    
  },
  computed: {
    
  },
  mounted() {
    
  }
};
</script>

<style scoped>
/*layout*/
.section-container {
  background-color: var(--white-200);
  width: 100%;
  border-radius: 2px;
  margin-bottom: 1.2rem;
  pointer-events: none;
  cursor: default;
}

.header-container {
  padding: 0.5rem 1.5;
  border-bottom: 1.5px solid var(--surface-bg);
  cursor: default;
}

.content-container {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: fit-content;
  padding: 24px;
  margin: 0.7rem 0rem 1rem 0rem;;
}

/* wrappers */

/* utils */
.title {
  margin: 0.4rem 0.85rem;
}

.v-input {
  height: 4.1rem;
  margin: 0rem;
  padding: 0rem;
}
</style>