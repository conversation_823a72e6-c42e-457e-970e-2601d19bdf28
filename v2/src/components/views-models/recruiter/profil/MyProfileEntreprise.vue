<template>
  <section id="my-profile-entreprise" class="section-container">
    <!-- Container header bar -->
    <div
      class="header-container bg-color"
      @click="$emit('toggleSection', 'my-profile-entreprise')"
    >
      <h5>
        Profil entreprise
        <span v-if="notifications > 0" class="notification-span">{{
          notifications +
          (notifications > 1
            ? ' informations manquantes !'
            : ' information manquante !')
        }}</span>
      </h5>
      <img
        v-if="isPanelOpen"
        src="@/assets/icons/arrow-top.svg"
        alt="arrow up"
      />
      <img v-else src="@/assets/icons/arrow-bottom.svg" alt="arrow down" />
    </div>

    <!-- Container content -->
    <div v-show="isPanelOpen" class="content">
      <!-- Activity -->
      <div class="activity">
        <h5>Secteur d'activité</h5>
        <v-combobox
          v-model="formData.secteur"
          label="Choisir le secteur d'activité de l'entreprise"
          :items="activité"
          chips
          clearable
          hide-selected
          :filter="customFilter"
        ></v-combobox>
      </div>

      <div class="taille-container">
        <div class="input-field">
          <h5>Taille</h5>
          <!-- Subgrid for radio buttons -->
          <div class="radio-group-container">
            <v-radio-group v-model="formData.taille">
              <div class="radio-columns">
                <div
                  v-for="(radio, index) in tailleInputList"
                  :key="index"
                  class="radio-item"
                >
                  <v-radio
                    :key="index"
                    :label="radio.label"
                    :value="radio.value"
                    color="rgb(240,179,55)"
                    :class="
                      radio.value === formData.taille
                        ? 'field underline2'
                        : 'field underline1'
                    "
                  />
                </div>
              </div>
            </v-radio-group>
          </div>
        </div>
        <div class="input-foundationYear">
          <h5>Année de fondation</h5>
          <v-text-field
            v-model="formData.company_creation_year"
            label="AAAA"
            type="number"
            :min="1900"
            :max="new Date().getFullYear()"
          />
        </div>
      </div>

      <div class="about-container">
        <h5>Qui sommes-nous</h5>
        <v-textarea
          v-model="formData.company_details"
          label="Ajouter une description"
          rows="4"
          :rules="rules500"
          counter
        ></v-textarea>

        <h5>Pourquoi nous rejoindre</h5>
        <v-textarea
          v-model="formData.rejoindre"
          label="Ajouter une description"
          rows="4"
          :rules="rules1550"
          counter
        ></v-textarea>
      </div>

      <!-- avantages -->
      <div class="avantages">
        <h5>Avantages</h5>
        <v-select
          v-model="formData.avantages"
          label="Choisir les avantages de l'entreprise"
          :items="avantages"
          variant="underlined"
          multiple
          chips
          closable-chips
          hide-selected
          clearable
        ></v-select>
      </div>

      <div class="about-container">
        <h5>Processus de recrutement</h5>
        <v-textarea
          v-model="formData.processus_recrutement"
          label="Ajouter une description"
          rows="4"
          :rules="rules1550"
          counter
        ></v-textarea>
      </div>

      <div class="btn-row">
        <PrimaryNormalButton textContent="Enregistrer" @click="$emit('save')" />
      </div>
    </div>
  </section>
</template>

<script>
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import { updateCompanyProfile } from '@/services/profile.service';
  import { ACTIVITY_FIELDS } from '@/utils/base/activity_sector';
  import { AVANTAGE_FIELDS } from '@/utils/base/avantage';
  import { takeUserNotifications } from '@/utils/userUtilities';
  export default {
    name: 'MyProfileEntreprise',
    components: {
      PrimaryNormalButton,
    },

    props: {
      activeSection: String,
    },

    computed: {
      isPanelOpen() {
        return this.activeSection === 'my-profile-entreprise';
      },
    },

    data() {
      return {
        currentUser: this.$store.getters.getUser,
        notifications: 0,
        formData: {
          secteur: '',
          taille: '',
          company_creation_year: '',
          company_details: '',
          rejoindre: '',
          avantages: [],
          processus_recrutement: '',
          rejoindre: '',
        },
        isPanelOn: false,
        activité: ACTIVITY_FIELDS.map((activity) => activity.nom),
        avantages: AVANTAGE_FIELDS.map((advantage) => advantage.nom),
        tailleInputList: [
          { label: 'Micro-entreprise (< 10)', value: 'small' },
          { label: 'Petite entreprise (< 50)', value: 'petite' },
          { label: 'Moyenne entreprise (< 250)', value: 'moyenne' },
          { label: 'Grande entreprise (≥ 250)', value: 'grande' },
        ],
        rules500: [
          (v) => v?.length <= 500 || 'ne doit pas dépasser 500 caractères',
        ],
        rules1550: [
          (v) => v?.length <= 1550 || 'ne doit pas dépasser 1550 caractères',
        ],
      };
    },

    mounted() {
      this.formData = {
        secteur: this.currentUser.secteur,
        taille: this.currentUser.taille,
        company_creation_year: this.currentUser.company_creation_year,
        company_details: this.currentUser.company_details,
        avantages: Array.isArray(this.currentUser.avantages)
          ? this.currentUser.avantages.filter((adv) => adv && adv.trim() !== '')
          : this.currentUser.avantages
            ? this.currentUser.avantages
                .split(',')
                .map((adv) => adv.trim())
                .filter((adv) => adv !== '')
            : [],
        processus_recrutement: this.currentUser.processus_recrutement,
        rejoindre: this.currentUser.rejoindre,
      };
      this.notifications = takeUserNotifications(this.currentUser, [
        'secteur',
        'taille',
        'company_creation_year',
        'company_details',
        'avantages',
        'processus_recrutement',
        'rejoindre',
      ]);
      if (this.notifications > 0) {
        this.togglePanel();
      }
    },
    methods: {
      togglePanel() {
        this.isPanelOn = !this.isPanelOn;
      },

      customFilter(item, queryText) {
        const text = queryText.toLowerCase();
        return item.toLowerCase().includes(text);
      },

      updateCompanyDatas() {
        const updatedData = {
          secteur: this.formData.secteur,
          taille: this.formData.taille,
          company_creation_year: this.formData.company_creation_year,
          company_details: this.formData.company_details,
          avantages: Array.isArray(this.formData.avantages)
            ? this.formData.avantages.join(',')
            : this.formData.avantages, // Si déjà une string, la garder
          processus_recrutement: this.formData.processus_recrutement,
          rejoindre: this.formData.rejoindre,
        };
        //console.log('updateCompanyProfile', updatedData);
        updateCompanyProfile(updatedData);
      },
    },
  };
</script>

<style scoped>
  /* Section container & layout */
  .section-container {
    background-color: var(--white-200);
    width: 100%;
    border-radius: 2px;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid black;
    cursor: pointer;
  }
  .header-container h5 {
    display: flex;
    gap: 10px;
    align-items: center;
  }
  .notification-span {
    background-color: rgba(254, 85, 85, 0.403);
    border: solid 1px red;
    border-radius: 10px;
    padding: 0 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
  }
  .bg-color {
    background-color: var(--secondary-2b2);
    border-bottom: none;
  }

  .content {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: fit-content;
    padding: 24px;
  }

  /* Buttons */
  .btn-row {
    display: flex;
    justify-content: end;
  }

  .field {
    width: 100%;
    margin-bottom: 10px;
  }

  .field:hover {
    background-color: rgba(246, 179, 55, 0.2);
    cursor: pointer;
  }

  .underline2 {
    border-bottom: 2px solid rgba(246, 179, 55, 1);
  }

  .underline1 {
    border-bottom: 2px solid rgba(245, 242, 239, 1);
  }

  /* Inputs */
  .input-field {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .input-foundationYear {
    width: 30%;
  }

  .taille-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;
  }

  .radio-group-container {
    display: flex;
    flex-direction: column;
  }

  .radio-columns {
    display: flex;
    flex-wrap: wrap;
  }

  .radio-item {
    flex-basis: calc(50% - 10px); /* Adjust width based on container */
    margin-right: 10px;
    margin-bottom: 10px;
  }

  /* Content */
  .informations {
    border-radius: 5px;
    padding-inline: 8px;
    padding-block: 18px;
    background-color: rgba(88, 160, 150, 0.2);
  }

  .activity .about-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 10px;
  }

  .avantages {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 10px;
  }

  @media screen and (min-width: 992px) {
    .input-field {
      width: 49%;
    }

    .about-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      gap: 10px;
    }
  }
</style>
