<template>
  <section id="my-info-entreprise" class="section-container">
    <!-- Container header bar -->
    <div
      class="header-container bg-color"
      @click="$emit('toggleSection', 'my-info-entreprise')"
    >
      <h5>
        Coordonnées de votre entreprise
        <span v-if="notifications > 0" class="notification-span">{{
          notifications +
          (notifications > 1
            ? ' informations manquantes !'
            : ' information manquante !')
        }}</span>
      </h5>
      <img
        v-if="isPanelOpen"
        src="@/assets/icons/arrow-top.svg"
        alt="arrow up"
      />
      <img v-else src="@/assets/icons/arrow-bottom.svg" alt="arrow down" />
    </div>

    <!-- Container content -->
    <div v-show="isPanelOpen" class="content">
      <div class="avatar-title">
        <div class="img-wrapper">
          <div class="image-container">
            <img
              v-if="formData.logo_img || previewImage"
              :src="previewImage || getFullImageUrl()"
              @click="openFileInput"
              class="avatar-icon"
              alt="avatar image"
            />
            <img
              v-else
              src="@/assets/icons/company-logo.png"
              alt="company logo"
              class="avatar-icon"
              @click="openFileInput"
            />
            <!-- Icone de modification par défaut -->
            <!-- <v-icon
              v-if="!showErrorIcon && !showSuccessIcon"
              color="var(--surface-bg-2)"
              class="img-state-icon"
            >
              mdi-pencil
            </v-icon> -->
            <!-- Icône de statut (affichée uniquement lorsqu'une image est valide) -->
            <v-icon
              v-if="showSuccessIcon"
              class="img-state-icon"
              color="rgb(var(--v-theme-success))"
            >
              mdi-check-circle
            </v-icon>
            <!-- Icône d'erreur -->
            <v-icon
              v-if="showErrorIcon"
              class="img-state-icon"
              color="rgb(var(--v-theme-error))"
            >
              mdi-alert-circle
            </v-icon>
          </div>

          <!-- commentaire de mise à jour  -->
          <!-- Message d'erreur affiché dynamiquement -->
          <p v-if="fileError" class="error-message">{{ fileError }}</p>

          <v-file-input
            ref="fileInput"
            :rules="rulesSize"
            accept="image/*"
            @change="handlePhotoChange"
            class="hidden"
          ></v-file-input>
        </div>

        <div class="title">
          <h5>Nom de votre entreprise</h5>
          <v-text-field
            v-model="formData.company"
            label="Saisir le nom"
            type="text"
            :rules="[...nameRules, ...notEmptyRules]"
          />
        </div>
      </div>

      <div class="contact"></div>
      <div class="contact1">
        <div class="input">
          <h5>Adresse complète du siège social</h5>
          <v-text-field
            v-model="formData.adress"
            label="Saisir l'adresse"
            type="text"
            :rules="[...nameRules, ...notEmptyRules]"
          />
        </div>
        <div class="input">
          <h5>Numéro de SIRET</h5>
          <v-text-field
            v-model="formData.siret"
            label="XXX XXX XXX XXXX"
            type="text"
            :rules="noSpecialCharRules"
          />
        </div>
      </div>

      <div class="btn-row">
        <PrimaryNormalButton textContent="Enregistrer" @click="$emit('save')" />
      </div>
    </div>
  </section>
</template>

<script>
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import { baseUrl } from '@/services/axios';
  import { updateCompanyInformations } from '@/services/profile.service';
  import { takeUserNotifications } from '@/utils/userUtilities';
  import {
    validateEmail,
    validateMobile,
    validateName,
    validateNoSpecialChar,
    validateNotEmpty,
    validateWebsite,
  } from '@/utils/validationRules';

  export default {
    name: 'MyInfoEntreprise',

    components: {
      PrimaryNormalButton,
    },

    props: {
      activeSection: String,
    },

    computed: {
      isPanelOpen() {
        return this.activeSection === 'my-info-entreprise';
      },
    },

    data() {
      return {
        user: {},
        isPanelOn: false,
        notifications: 0,
        formData: {
          photo: null,
        },
        previewImage: '',
        emailRules: [(v) => validateEmail(v) || true],
        nameRules: [(v) => validateName(v) || true],
        notEmptyRules: [(v) => validateNotEmpty(v) || true],
        mobileRules: [(v) => validateMobile(v) || true],
        websiteRules: [(v) => validateWebsite(v) || true],
        noSpecialCharRules: [
          (v) =>
            validateNoSpecialChar(v) || 'Should not contain special characters',
        ],
        showErrorIcon: false,
        showSuccessIcon: false,
        fileError: '', // Stocke le message d'erreur
        rulesSize: [
          (value) =>
            !value ||
            !value.length ||
            value[0].size < 1000000 ||
            'Ne doit pas avoir plus de 1 mo',
        ],
      };
    },

    mounted() {
      this.user = this.$store.getters.getUser;
      this.formData = {
        logo_img: this.user.logo_img || null,
        company: this.user.company || '',
        adress: this.user.adress || '',
        siret: this.user.siret || '',
      };
      this.notifications = takeUserNotifications(this.user, [
        'logo_img',
        'company',
        'adress',
        'siret',
      ]);
      if (this.notifications > 0) {
        this.togglePanel();
      }
    },

    methods: {
      formatURL(url) {
        if (url && !/^https?:\/\//i.test(url)) {
          return 'https://' + url;
        }
        return url;
      },

      togglePanel() {
        this.isPanelOn = !this.isPanelOn;
      },

      // update user datas in the store/server
      updateUserDatas() {
        updateCompanyInformations(this.formData);
        /* this.$emit('update:modelValue', this.formData);
        this.$emit('save'); */
      },

      // handle user photo changes
      handlePhotoChange(event) {
        let file = event.target.files[0];

        if (file) {
          if (file.size < 1000000) {
            this.showErrorIcon = false;
            this.showSuccessIcon = true; // Taille valide
            this.fileError = '';
          } else {
            this.showErrorIcon = true; // Taille trop grande
            this.showSuccessIcon = false;
            this.fileError = 'La taille du fichier ne doit pas dépasser 1 Mo.';
          }

          const newFileName = `company_logo_${this.user.id}_${new Date().getTime()}.${file.type.split('/')[1]}`;

          const renamedFile = new File([file], newFileName, {
            type: file.type,
          });

          this.formData.logo_img = renamedFile;

          const reader = new FileReader();
          reader.onload = () => {
            this.previewImage = reader.result;
          };
          reader.readAsDataURL(renamedFile);
        }
      },

      // trigger hidden input to upload photo
      openFileInput() {
        this.$refs.fileInput.click();
      },

      // get photo url
      getFullImageUrl() {
        if (this.formData.logo_img) {
          if (this.formData.logo_img.name) {
            if (typeof this.formData.logo_img.name === 'string') {
              return URL.createObjectURL(this.formData.logo_img);
            }
          }
          return baseUrl + this.formData.logo_img;
        } else if (this.previewImage) {
          return this.previewImage;
        } else {
          return '';
        }
      },
    },
  };
</script>

<style scoped>
  .section-container {
    background-color: var(--white-200);
    width: 100%;
    border-radius: 2px;
  }
  .header-container h5 {
    display: flex;
    gap: 10px;
    align-items: center;
  }
  .notification-span {
    background-color: rgba(254, 85, 85, 0.403);
    border: solid 1px red;
    border-radius: 10px;
    padding: 0 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
  }
  .header-container {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid black;
    cursor: pointer;
  }

  .bg-color {
    background-color: var(--secondary-2b2);
    border-bottom: none;
  }

  .content {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: fit-content;
    padding: 24px;
  }

  .avatar-icon {
    width: 92px;
    height: 92px;
    object-fit: contain;
    overflow: hidden;
    cursor: pointer;
  }

  .btn-row {
    display: flex;
    justify-content: end;
  }

  .Localisation {
    display: flex;
    flex-direction: row;
    gap: 20px;
  }

  /* Input content */
  .input {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .avatar-title {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
  }

  .image-container {
    position: relative;
    display: inline-block;
  }

  .img-wrapper {
    width: 100%;
    position: relative;
  }
  .img-wrapper img {
    border-radius: 5px;
    object-fit: cover;
  }
  .img-wrapper .error-message {
    color: rgb(var(--v-theme-error));
    font-size: 12px;
    margin-top: 5px;
    width: 400%;
  }

  .img-state-icon {
    position: absolute;
    bottom: 8px;
    right: 8px;
    font-size: 22px;
    /*background-color: var(--surface-bg-5);
    border-radius: 50%;
    padding: 16px;*/
    cursor: pointer;
  }

  .title {
    width: 100%;
  }

  .name {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 20px;
  }

  .contact {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 20px;
  }

  .contact1 {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 20px;
  }

  .location {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .aboutme {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .websites {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 20px;
  }

  .websites2 {
    display: flex;
    flex-direction: wrap;
    width: 100%;
    gap: 20px;
  }

  .websites3 {
    display: flex;
    flex-direction: wrap;
    width: 100%;
    gap: 20px;
  }

  .social-input {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 50%;
  }

  .social-icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
  }

  .hidden {
    display: none;
  }

  @media screen and (min-width: 992px) {
    .avatar-title {
      flex-direction: row;
    }

    .img-wrapper {
      width: 10%;
    }

    .title {
      width: 90%;
    }

    .name {
      flex-direction: row;
      justify-content: space-between;
    }

    .contact {
      flex-direction: row;
      justify-content: space-between;
    }

    .contact1 {
      flex-direction: row;
      justify-content: space-between;
    }

    .location {
      width: 49%;
    }

    .websites {
      flex-direction: row;
      justify-content: space-between;
    }

    .websites2 {
      flex-direction: row;
      justify-content: space-between;
    }
  }
</style>
