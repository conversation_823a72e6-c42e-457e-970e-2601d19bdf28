<template>
  <section id="avis" class="section-container">
    <!-- Container header bar -->
    <div
      :class="isPanelOn ? 'header-container bg-color' : 'header-container'"
      @click="togglePanel"
    >
      <h5>Avis</h5>
      <img
        v-if="isPanelOn"
        src="@/assets/icons/arrow-top.svg"
        alt="flèche en bas"
      />
      <img v-else src="@/assets/icons/arrow-bottom.svg" alt="flèche en bas" />
    </div>

    <!-- Container content -->
    <div v-if="isPanelOn" class="content">
      <div class="review-component">
        <div class="header">
          <div class="global-rating">
            <h5>Note globale</h5>
            <p>{{ averageRating }}/5</p>
            <div class="stars">
              <span v-for="n in 5" :key="n">
                <img
                  v-if="n <= averageRating"
                  src="@/assets/icons/star-filled.svg"
                  alt="filled star"
                />
                <img
                  v-else
                  src="@/assets/icons/star-empty.svg"
                  alt="empty star"
                />
              </span>
            </div>
          </div>
          <div class="review-count">
            <h5>Nombre d'avis</h5>
            <p>{{ reviews.length }}</p>
          </div>
          <div class="review-detail">
            <h5>Détail des avis</h5>
            <div class="circle-chart">
              <div class="circle"></div>
              <span>{{ positivePercentage }}%</span>
            </div>
            <p class="description">Description</p>
          </div>
        </div>
        <div class="reviews">
          <h5>Avis laissés par les employés de l’entreprise</h5>
          <div class="filter-bar">
            <button
              :class="{ active: sortKey === 'name' }"
              @click="sortBy('name')"
            >
              Trier par nom
            </button>
            <button
              :class="{ active: sortKey === 'date' }"
              @click="sortBy('date')"
            >
              Trier par date
            </button>
            <button
              :class="{ active: sortKey === 'rating' }"
              @click="sortBy('rating')"
            >
              Trier par note
            </button>
            <button>Supprimés</button>
          </div>
          <div
            v-for="(review, index) in sortedReviews"
            :key="index"
            class="review-item"
          >
            <div class="review-header">
              <div class="title-and-stars">
                <h5>{{ review.title || 'Sans titre ' }}</h5>
                <div class="stars">
                  <span v-for="n in 5" :key="n">
                    <img
                      v-if="n <= review.rating"
                      src="@/assets/icons/star-filled.svg"
                      alt="filled star"
                    />
                    <img
                      v-else
                      src="@/assets/icons/star-empty.svg"
                      alt="empty star"
                    />
                  </span>
                </div>
              </div>

              <div class="date-and-flag">
                <img src="@/assets/icons/flag-white-bg.svg" alt="flag icon" />
                <p>{{ new Date(review.date_creation).toLocaleDateString() }}</p>
              </div>
            </div>
            <p class="job-title">{{ review.jobTitle }}</p>
            <p>{{ review.commentaire }}</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  import FlagWhite from '@/assets/icons/flag-white-bg.svg';
  import StarEmpty from '@/assets/icons/star-empty.svg';
  import StarFilled from '@/assets/icons/star-filled.svg';
  import { getCompanyReviews, getUser } from '@/services/profile.service';

  export default {
    data() {
      return {
        userId: 0,
        isPanelOn: false,
        reviews: [],
        sortKey: 'name',
      };
    },
    components: {
      StarFilled,
      StarEmpty,
      FlagWhite,
    },
    computed: {
      averageRating() {
        if (this.reviews.length === 0) return 0;
        const total = this.reviews.reduce(
          (sum, review) => sum + (review.note || 0),
          0
        );
        const average = (total / this.reviews.length).toFixed(1);
        this.$emit('update-average-rating', Number(average));
        return average;
      },
      positivePercentage() {
        const positiveReviews = this.reviews.filter(
          (review) => review.rating >= 4
        ).length;
        if (this.reviews.length > 0) {
          return ((positiveReviews / this.reviews.length) * 100).toFixed(0);
        }
        return 0;
      },

      circleStyle() {
        return {
          background: `conic-gradient(#f6b337 ${this.positivePercentage}%, #e0e0e0 ${this.positivePercentage}%)`,
        };
      },
      sortedReviews() {
        return this.reviews.sort((a, b) => {
          if (this.sortKey === 'name') {
            return a.title.localeCompare(b.title);
          } else if (this.sortKey === 'date') {
            return new Date(b.date_creation) - new Date(a.date_creation);
          } else if (this.sortKey === 'rating') {
            return b.note - a.note;
          }
        });
      },
    },

    methods: {
      togglePanel() {
        this.isPanelOn = !this.isPanelOn;
      },
      sortBy(key) {
        this.sortKey = key;
      },
      async fetchReviews() {
        try {
          const user = await getUser();
          this.userId = user.id;
          this.reviews = await getCompanyReviews(this.userId);
        } catch (error) {
          //console.error('Erreur lors de la récupération des avis:', error);
        }
      },
    },
    async mounted() {
      await this.fetchReviews();
      this.$emit('update-average-rating', this.averageRating);
    },
  };
</script>

<style scoped>
  /* General container styles */
  .section-container {
    background-color: var(--white-200);
    width: 100%;
    border-radius: 2px;
  }

  /* Header styles */
  .header-container {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid black;
    cursor: pointer;
  }

  .bg-color {
    background-color: rgba(246, 179, 55, 0.2);
    border-bottom: none;
  }

  /* Content container */
  .content {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: fit-content;
    padding: 0;
  }

  /* Review component */
  .review-component {
    padding: 20px 10px 0;
  }

  /* Header section */
  .header {
    display: flex;
    justify-content: space-between;
    padding: 0;

    align-items: start;
    text-align: center;
  }

  .global-rating,
  .review-count,
  .review-detail {
    flex: 1;
    text-align: center;
  }

  .global-rating h5,
  .review-count h5,
  .review-detail h5 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 20px;
  }

  .global-rating p,
  .review-count p {
    font-size: 1.2rem;
    font-weight: bold;
    margin: 4px 0;
  }

  .title-and-stars {
    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  .stars {
    display: flex;
    gap: 5px;
    justify-content: center;
  }

  .stars img {
    height: 16px;
    width: 16px;
  }

  /* Specific styles for the review count section */
  .review-count p {
    background-color: #f6b337;
    padding: 10px 16px;
    border-radius: 8px;
    color: #333;
    display: inline-block;
  }

  /* Circle chart styles */
  .circle-chart {
    position: relative;
    width: 70px;
    height: 70px;
    margin: 0 auto;
  }

  .circle {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    position: absolute;
    background: conic-gradient(#f6b337 30%, #e0e0e0 30%);
  }

  .circle::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    box-sizing: border-box;
    top: 0;
    left: 0;
    clip-path: polygon(50% 0%, 100% 0%, 100% 100%, 50% 100%, 50% 50%);
  }

  .circle::after {
    content: '';
    position: absolute;
    width: 55px;
    height: 55px;
    background-color: #fff;
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .circle-chart span {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.2rem;
    font-weight: bold;
  }

  .description p {
    font-size: 0.8rem;
    font-weight: 300;
    margin: 4px 0;
  }

  /* Review section */
  .filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid #e0e0e0;
  }

  .filter-bar button {
    background: none;
    border: none;
    font-size: 14px;
    color: #666;
    cursor: pointer;
    padding: 0 10px;
    position: relative;
  }

  .filter-bar button.active {
    color: #333;
    font-weight: bold;
  }

  .review-item {
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
  }

  .review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .review-header h5 {
    margin-right: 10px;
    font-size: 14px;
  }

  .review-item p {
    margin: 5px 0;
  }

  /* Button styles */
  .btn-row {
    display: flex;
    justify-content: end;
  }

  .date-and-flag {
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .date-and-flag p {
    margin: 0;
  }
</style>
