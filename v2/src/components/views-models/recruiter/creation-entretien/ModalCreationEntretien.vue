<template>
  <div class="meet-scheduler">
    <header>
      <div class="header-container">
        <img
          :src="recruteur?.photo || 'https://shorturl.at/f9a9s'"
          alt="Profile"
          class="profile-photo"
        />
        <h2>Sélectionnez</h2>
        <button class="close-btn" @click="$emit('close')">&times;</button>
      </div>
    </header>

    <div class="calendar-container">
      <div class="navigation">
        <button @click="previousWeek">&lt;</button>
        <div class="date-range">{{ formattedDateRange }}</div>
        <button @click="nextWeek">&gt;</button>
      </div>
      <div class="weekdays">
        <span
          v-for="(day, index) in days"
          :key="index"
          :class="{
            active: selectedDay === index || isDateSelected(day.formatted),
          }"
          @click="selectDay(index)"
        >
          {{ day.short }} {{ day.date }}
        </span>
      </div>
    </div>

    <div class="time-selection">
      <label> Sélectionnez les horaires </label>
      <div class="time-dropdowns">
        <select v-model="selectedHour">
          <option v-for="hour in hours" :key="hour" :value="hour">
            {{ hour }}
          </option>
        </select>
        <select v-model="selectedMinute">
          <option v-for="minute in minutes" :key="minute" :value="minute">
            {{ minute }}
          </option>
        </select>
      </div>

      <label> Sélectionnez Durée </label>
      <select v-model="selectedDuration">
        <option v-for="duration in durations" :key="duration" :value="duration">
          {{ duration }}
        </option>
      </select>
      <button class="add-btn" @click="addTimeSlot">+</button>
    </div>

    <div class="timezone">
      🌍 Dans votre fuseau horaire, Europe, Paris (GMT+1:00)
    </div>

    <ul class="selected-times">
      <li v-for="(slot, index) in timeSlots" :key="index">
        <span>
          📅 {{ slot.date }} <b>{{ slot.times.join(', ') }}</b>
        </span>
        <button class="remove-btn" @click="removeTimeSlot(index)">❌</button>
      </li>
    </ul>

    <button class="continue-btn">Continuer</button>
  </div>
</template>

<script>
  import { format, addWeeks, subWeeks, startOfWeek, addDays } from 'date-fns';
  import fr from 'date-fns/locale/fr';

  export default {
    props: {
      recruteur: {
        type: Object,
        default: () => ({ photo: '', first_name: '', last_name: '' }),
      },
    },
    data() {
      return {
        currentDate: new Date(),
        selectedDay: null,
        selectedHour: '00',
        selectedMinute: '00',
        selectedDuration: '30 Minutes',
        timeSlots: [],
        hours: [...Array(24).keys()].map((h) => h.toString().padStart(2, '0')),
        minutes: ['00', '15', '30', '45'],
        durations: ['30 Minutes', '1 Heure', '1h 30min'],
        dayShortNames: ['LU', 'MA', 'ME', 'JE', 'VE', 'SA', 'DI'],
      };
    },
    computed: {
      formattedDateRange() {
        const start = format(
          startOfWeek(this.currentDate, { weekStartsOn: 1 }),
          'MMM dd',
          { locale: fr }
        );
        const end = format(
          addDays(startOfWeek(this.currentDate, { weekStartsOn: 1 }), 6),
          'MMM dd',
          { locale: fr }
        );
        return start + ' - ' + end;
      },
      days() {
        return Array.from({ length: 7 }, (_, i) => {
          const date = addDays(
            startOfWeek(this.currentDate, { weekStartsOn: 1 }),
            i
          );
          return {
            date: format(date, 'dd', { locale: fr }),
            formatted: format(date, 'EEEE dd MMM', { locale: fr }),
            short: this.dayShortNames[i],
          };
        });
      },
    },
    methods: {
      selectDay(index) {
        this.selectedDay = index;
      },
      addTimeSlot() {
        if (this.selectedDay !== null) {
          const selectedDate = this.days[this.selectedDay].formatted;
          const selectedTime = this.selectedHour + ':' + this.selectedMinute;
          const existingSlot = this.timeSlots.find(
            (slot) => slot.date === selectedDate
          );

          if (existingSlot) {
            if (!existingSlot.times.includes(selectedTime)) {
              existingSlot.times.push(selectedTime);
            }
          } else {
            this.timeSlots.push({
              date: selectedDate,
              times: [selectedTime],
            });
          }
        }
      },
      removeTimeSlot(index) {
        this.timeSlots.splice(index, 1);
      },
      isDateSelected(date) {
        return this.timeSlots.some((slot) => slot.date === date);
      },
      previousWeek() {
        this.currentDate = subWeeks(this.currentDate, 1);
      },
      nextWeek() {
        this.currentDate = addWeeks(this.currentDate, 1);
      },
      closeScheduler() {
        this.$emit('close'); // Emit the 'close' event when the button is clicked
      },
    },
  };
</script>

<style scoped>
  /* General Container Styling */
  .meet-scheduler {
    background: white;
    border-radius: 15px;
    padding: 20px;
    width: 380px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 100%;
    font-family: 'Inter', sans-serif;
  }

  /* Header Styling */
  .header-container {
    display: flex;
    align-items: center;
    gap: 10px;
    justify-content: flex-start;
  }

  .profile-photo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
  }

  h2 {
    font-size: 22px;
    font-weight: bold;
  }

  /* Calendar Navigation */
  .calendar-container {
    margin-top: 15px;
  }

  .navigation {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: bold;
    padding: 10px;
  }

  .weekdays {
    display: flex;
    justify-content: center;
    margin: 15px 0;
    gap: 8px;
  }

  .weekdays span {
    cursor: pointer;
    padding: 8px 14px;
    border-radius: 50%;
    transition: 0.3s;
    background: #f4f4f4;
    font-size: 14px;
    font-weight: 500;
  }

  .weekdays .active {
    background: #d8e3e7;
    font-weight: bold;
  }

  /* Time Selection */
  .time-selection {
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
    margin-top: 10px;
    width: 100%;
  }

  .time-selection-row {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: nowrap;
    justify-content: space-between;
    width: 100%;
  }

  .time-dropdowns {
    display: flex;
    gap: 10px;
  }

  select,
  input {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 10px;
    font-size: 14px;
    width: 120px;
  }

  button.add-btn {
    background: none;
    border: 1px solid #ccc;
    padding: 8px 12px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 16px;
  }

  /* Timezone */
  .timezone {
    font-size: 12px;
    color: #777;
    display: flex;
    align-items: center;
    gap: 5px;
    margin-top: 10px;
    justify-content: center;
  }

  /* Selected Times */
  .selected-times {
    list-style: none;
    padding: 0;
    margin-top: 15px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }

  .selected-times li {
    background: #f7f7f7;
    padding: 10px 14px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    justify-content: space-between;
  }

  /* Continue Button */
  .continue-btn {
    background: #ffb400;
    border: none;
    padding: 14px;
    border-radius: 25px;
    width: 100%;
    cursor: pointer;
    font-weight: bold;
    margin-top: 20px;
    font-size: 16px;
  }

  .weekdays span {
    cursor: pointer;
    padding: 8px 14px;
    border-radius: 50%;
    transition: 0.3s;
    background: #f4f4f4;
    font-size: 14px;
    font-weight: 500;
  }

  .weekdays .active {
    background: #d8e3e7;
    font-weight: bold;
  }
</style>
