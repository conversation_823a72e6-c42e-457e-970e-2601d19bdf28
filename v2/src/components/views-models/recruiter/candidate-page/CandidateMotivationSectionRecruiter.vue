<template>
    <section class="container padding-container">
        <div>
            <div class="section-container">
                <div class="header-container">
                    <h5>Motivation</h5>
                </div>
                <div class="motivation-container">
                    <h5>Lettre de motivation</h5>
                    <div class="icons-container">
                        <img src="@/assets/icons/download-icon.svg" alt="icône télécharger" />
                        <img src="@/assets/icons/preview-icon.svg" alt="icône prévisualisation" />
                    </div>
                </div>
                <div class="aboutme">
                    <h5>Pourquoi moi ?</h5>
                    <v-textarea
                        v-model="formData.motivation"
                        label=""
                        type="text"
                        readonly
                        single-line
                        variant="solo-filled"
                        flat
                    /> 
                </div>
            </div>
        </div>
    </section>
</template>

<script>
export default {
    name: 'CandidateMotivationSection',

    data() {
        return {
            formData: {
                motivation: `
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

                Curabitur pretium tincidunt lacus. Nulla gravida orci a odio. Nullam varius, turpis et commodo pharetra, est eros bibendum elit, nec luctus magna felis sollicitudin mauris. Integer in mauris eu nibh euismod gravida. Duis ac tellus et risus vulputate vehicula. Donec lobortis risus a elit. Etiam tempor. Ut ullamcorper, ligula eu tempor congue, eros est euismod turpis, id tincidunt sapien risus a quam. Maecenas fermentum consequat mi. Donec fermentum. Pellentesque malesuada nulla a mi. Duis sapien sem, aliquet nec, commodo eget, consequat quis, neque. Aliquam faucibus, elit ut dictum aliquet, felis nisl adipiscing sapien, sed malesuada diam lacus eget erat. Cras mollis scelerisque nunc. Nullam arcu. Aliquam consequat.

                Curabitur augue lorem, dapibus quis, laoreet et, pretium ac, nisi. Aenean magna nisl, mollis quis, molestie eu, feugiat in, orci. In hac habitasse platea dictumst. Cras purus urna, egestas ut, ullamcorper id, varius ac, leo. In velit mauris, tincidunt et, fringilla vel, commodo vitae, tellus. Sed aliquam odio vitae tortor. Proin hendrerit tempus arcu. In hac habitasse platea dictumst. Ut aliquam sollicitudin leo. Cras iaculis ultricies nulla. Donec quis dui at dolor tempor interdum. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Duis quis libero ac nulla euismod ultricies. Donec lobortis eros sit amet ipsum. In sapien sapien, vestibulum ut, placerat eu, commodo quis, est. Pellentesque suscipit.

                Etiam aliquet, dui ut faucibus vehicula, lorem mauris dictum felis, ut suscipit tellus mauris a turpis. Fusce eleifend sapien non elit auctor, ac ultricies nunc bibendum. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Morbi sit amet libero id magna luctus fermentum. Quisque dapibus malesuada arcu, sit amet varius odio tincidunt eget. Cras viverra erat id urna gravida, sed tempus libero fringilla. Nullam nec tortor eget enim viverra auctor nec at mi. Sed sit amet tincidunt lacus. Ut tempor est nec malesuada vehicula. Mauris ut suscipit sem, a fringilla erat. Vestibulum ut justo a turpis consequat dignissim nec et lacus. Aenean mollis malesuada orci, eget vehicula elit. Pellentesque et sem nec sapien ullamcorper gravida.
                `,
            },                  
        }
    },
}
</script>

<style scoped>
.container {
    display: flex;
    width: 100%;
    flex-direction: column;
    gap: 0px;
    height: fit-content;
}

.section-container {
    background-color: var(--white-200);
    width: 100%;
    border-radius: 2px;
}

.header-container {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid rgba(246, 179, 55, 0.2);
}

.motivation-container {
    display: flex;
    flex-direction: row;
    padding: 16px;
    justify-content: space-between;
}

.icons-container {
    display: flex;
    gap: 20px; 
}

.aboutme {
    padding: 16px;
}
</style>
