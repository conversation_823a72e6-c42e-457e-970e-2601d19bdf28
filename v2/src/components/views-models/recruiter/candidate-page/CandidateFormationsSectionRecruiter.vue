<template>
    <section class="container padding-container">
  
      <div class="section-container">
        <!-- container header band -->
        <div class="header-container">
          <h5>Formations</h5>
        </div>
  
        <!-- container content -->
        <div class="content-container">
          <!-- list of all formations -->
          <div v-for="(formation, index) in candidate.formation" :key="index" class="formations-container">
  
            <div class="title-date">
              <div class="input-field">
                <h5>Titre de la formation</h5>
                <v-text-field
                  v-model="formation['titre']"
                  :label="formation['titre']"
                  type="text"
                  readonly
                  single-line
                  variant="solo-filled"
                  flat
                />
              </div>
              <div class="input-field">
                <h5>Date d'obtention</h5>
                <label for="date" hidden>Date</label>
                <input
                  type="date"
                  v-model="formation['obtention']"
                  class="date-input-field"
                  disabled
                />
              </div>
            </div>
  
            <div class="school-degree">
              <div class="input-field">
                <h5>Organisme de formation</h5>
                <v-text-field
                  v-model="formation['organisme']"
                  :label="formation['organisme']"
                  type="text"
                  readonly
                  single-line
                  variant="solo-filled"
                  flat
                />
              </div>
  
              <div class="input-field">
                <h5>Niveau d'études</h5>
                <v-select
                  v-model="formation['etude']"
                  :label="formation['etude']"
                  :items="['California', 'Colorado', 'Florida', 'Georgia', 'Texas', 'Wyoming']"
                  readonly
                  single-line
                  variant="solo-filled"
                  flat
                />
              </div>
            </div>
  
          </div>
        </div>
      </div>
    </section>
  </template>
  
  <script>
  import { getUserById } from '@/services/account.service.js';
  
  export default {
    name: 'CandidateFormationsSection',
  
    data() {
      return {
        candidate: {},
      }
    },
  
    async mounted() {
        this.candidate = await getUserById(this.$route.params.id);
    },
  
    methods: {

    }
  }
  </script>
  

<style scoped>
/* layout */
.container {
    display: flex;
    width: 100%;
    flex-direction: column;
    gap: 0px;
    height: fit-content;
}

.section-container {
    background-color: var(--white-200);
    width: 100%;
    border-radius: 2px;
}

.header-container {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid rgba(246, 179, 55, 0.2);
    cursor: auto;
}

.bg-color {
    background-color: rgba(246, 179, 55, 0.2);
    border-bottom: none;
}

.content-container {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: fit-content;
    padding: 24px;
}

/* buttons */
.btn-row {
    display: flex;
    justify-content: end;
}

.custom-btn {
    border-radius: 5px;
    border: 1px solid rgba(246, 179, 55, 1);
}

/* inputs */
.input-field, .input-field2 {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.date-input-field {
    width: 150px;
    background-color: transparent;
    min-height: 56px;
    text-align: center;
    border-radius: 2px;
    padding: 16px;
}

.checkbox-wrapper {
    width: fit-content;
}

/* content */
.formations-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding-top: 30px;
    border-top: 1px solid rgba(246, 179, 55, 0.2);
    border-bottom: 1px solid rgba(246, 179, 55, 0.2);
}

.title-date {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 10px;
}

.school-degree {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 10px;
}

.delete-btn {
    display: flex;
    justify-content: end;
}


@media screen and (min-width: 992px) {
    .input-field {
        width: 49%;
    }

    .input-field2 {
        width: 40%;
    }

    .title-date {
        flex-direction: row;
        justify-content: space-between;
        gap: 0px;
    }

    .school-degree {
        flex-direction: row;
        justify-content: space-between;
        gap: 10px;
    }
}
</style>
