<template>
    <section class="container padding-container">
  
      <div class="section-container">
  
        <!-- container header band -->
        <div class="header-container">
          <h5>Compétences</h5>
        </div>
  
        <!-- container content -->
        <div class="content-container">

          <!-- skills -->
          <div class="skills">
            <h5>Skills</h5>
              <div class="packet-chips">
                  <div v-for="(skill, index) in candidate.skill" :key="index" class="chip-wrapper">
                      <Chip :textContent="skill.name" />
                  </div>
              </div>
          </div>
  
          <!-- Langages -->
          <div class="langage">
            <h5>Langues</h5>
            <div class="packet-chips">
              <div v-for="(langue, index) in candidate.langue" :key="index" class="chip-wrapper">
                <Chip :textContent="langue.name" />
              </div>
            </div>
          </div>
  
          <!-- Permis and Mobility -->
          <div class="permit-mobility">
            <div class="input-field">
              <h5>Permis</h5>
              <div class="packet-chips">
                <div v-for="(permit, index) in candidate.permit" :key="index" class="chip-wrapper">
                  <Chip :textContent="permit.name" />
                </div>
              </div>
            </div>
  
            <div class="input-field">
              <h5>Mobilité</h5>
              <div class="packet-chips">
                <div v-for="(mobility, index) in candidate.mobilité" :key="index" class="chip-wrapper">
                  <Chip :textContent="mobility.name" />
                </div>
              </div>
            </div>
          </div>
  
        </div>
      </div>
  
    </section>
  </template>
  

  <script>
  import { getUserById } from '@/services/account.service.js';
  import Chip from '@/components/chips/Chip.vue';

  
  export default {
    name: 'CandidateSkillsSection',
  
    components: {
      Chip,
    },
  
    data() {
      return {
        candidate: {},
      }
    },

    async mounted() {
        this.candidate = await getUserById(this.$route.params.id);
    },
  
    methods: {

    }
  }
  </script>
  

<style scoped>
/* layout */
.container {
    display: flex;
    width: 100%;
    flex-direction: column;
    gap: 0px;
    height: fit-content;
}

.section-container {
    background-color: var(--white-200);
    width: 100%;
    border-radius: 2px;
}

.header-container {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid rgba(246, 179, 55, 0.2);
    cursor: auto;
}

.bg-color {
    background-color: rgba(246, 179, 55, 0.2);
    border-bottom: none;
}

.content-container {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: fit-content;
    padding: 24px;
}

/* buttons */
.btn-row {
    display: flex;
    justify-content: end;
}

/* inputs */
.input-field, .input-field2 {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.checkbox-wrapper {
    width: fit-content;
}

/* content */
.informations {
    border-radius: 5px;
    padding-inline: 8px;
    padding-block: 18px;
    background-color: rgba(88, 160, 150, 0.2);
}

.packet-chips {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.skills {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 10px;
}

.soft-skills {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 10px;
}

.langage {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 10px;
}

.permit-mobility {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 24px;
}

@media screen and (min-width: 992px) {
    .input-field {
        width: 49%;
    }

    .input-field2 {
        width: 40%;
    }

    .permit-mobility {
        flex-direction: row;
        justify-content: space-between;
        gap: 0px;
    }
}
</style>
