<template>
    <section class="container padding-container">
  
      <div class="section-container">
        <!-- container header band -->
        <div class="header-container">
          <h5>Expériences professionnelles</h5>
        </div>
  
        <!-- container content -->
        <div class="content-container">
          <!-- list of all experiences -->
          <div v-for="(experience, index) in candidate.experience" :key="index" class="formations-container">
  
            <div class="title-date">
              <div class="input-field">
                <h5>Titre du poste</h5>
                <p></p>
                <v-text-field
                  v-model="experience['position']"
                  readonly
                  single-line
                  variant="solo-filled"
                  flat
                />
              </div>
              <div class="input-field">
                <h5>Dates</h5>
                <div class="date-field">
                  <label for="date-debut">De</label>
                  <input
                    type="date"
                    v-model="experience['debut']"
                    class="date-input-field"
                    disabled
                  />
                  <label for="date-fin">à</label>
                  <input
                    type="date"
                    v-model="experience['fin']"
                    class="date-input-field"
                    disabled
                  />
                </div>
              </div>
            </div>
  
            <div class="company-location">
              <div class="input-field">
                <h5>Entreprise</h5>
                <v-text-field
                  v-model="experience['company']"
                  readonly
                  single-line
                  variant="solo-filled"
                  flat
                />
              </div>
              <div class="input-field">
                <h5>Lieu</h5>
                <v-text-field
                  v-model="experience['place']"
                  readonly
                  single-line
                  variant="solo-filled"
                  flat
                />
              </div>
            </div>
  
            <div>
              <h5>Détails</h5>
              <v-text-field
                v-model="experience['exp_detail']"
                readonly
                single-line
                variant="solo-filled"
                flat
              />
            </div>
  
          </div>
        </div>
      </div>
    </section>
  </template>
  

<script>
  import { getUserById } from '@/services/account.service.js';
  
  export default {
    name: 'CandidateExperiencesSection',
  
    data() {
      return {
        candidate: {},
      }
    },
  
    async mounted() {
        this.candidate = await getUserById(this.$route.params.id);
    },
  
    methods: {

    }
  }
</script>
  

<style scoped>

/* layout */
.container {
    display: flex;
    width: 100%;
    flex-direction: column;
    gap: 0px;
    height: fit-content;
}

.section-container {
    background-color: var(--white-200);
    width: 100%;
    border-radius: 2px;
}

.header-container {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid rgba(246, 179, 55, 0.2);
    cursor: auto;
}

.bg-color {
    background-color: rgba(246, 179, 55, 0.2);
    border-bottom: none;
}

.content-container {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: fit-content;
    padding: 24px;
}

/* buttons */
.btn-row {
    display: flex;
    justify-content: end;
}

.custom-btn {
    border-radius: 5px;
    
}

/* inputs */
.input-field, .input-field2 {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.date-input-field {
    width: fit-content;
    background-color: transparent;
    min-height: 56px;
    padding: 16px;
    border-radius: 2px;
}

.checkbox-wrapper {
    width: fit-content;
}

/* content */
.formations-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding-top: 30px;
    border-top: 1px solid rgba(246, 179, 55, 0.2);
    border-bottom: 1px solid rgba(246, 179, 55, 0.2);
}

.title-date {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 10px;
}

.company-location {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 10px;
}

.date-field {
    display: flex;
    align-items: end;
    gap: 10px;
}

.date-field label {
    align-self: center;
}

.delete-btn {
    display: flex;
    justify-content: end;
}

@media screen and (min-width: 992px) {
    .input-field {
        width: 49%;
    }

    .input-field2 {
        width: 40%;
    }

    .title-date {
        flex-direction: row;
        justify-content: space-between;
        gap: 0px;
    }

    .company-location {
        flex-direction: row;
        justify-content: space-between;
        gap: 10px;
    }

    /*.date-field {
        padding-top: 10px;
    }*/

}
@media screen and (max-width: 767px) {

.date-field {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
}

.date-field label {
    align-self: flex-start;
}
.date-field label {
align-self: start;
}}
</style>
