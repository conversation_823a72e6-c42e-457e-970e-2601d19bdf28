<template>
    <section class="container padding-container">
        <div>
            <div class="section-container">
                
                <!-- container content -->
                <div class="content-container">
                    <div class="cv-container">
                        <h5>Mon CV / Titre</h5>
                        <div class="icons-container">
                            <img src="@/assets/icons/download-icon.svg" alt="icône télécharger" />
                            <img src="@/assets/icons/preview-icon.svg" alt="icône prévisualisation" />
                        </div>
                    </div>
                    <div class="input-field">
                        <h5>Secteur d'activité</h5>
                        <p>{{ candidate.secteur || "Aucun" }}</p>
                    </div>
                    <div class="activity-and-salary-container">
                        <div class="experience-container">
                            <div class="input-field">
                                <h5>Expérience</h5>
                                <p>{{ candidate.experiences || "Aucun" }}</p>
                            </div>
                        </div>
                        <div class="input-field">
                            <h5>Salaire brut annuel en euros</h5>
                            <p>{{ candidate.salaire_souhaite || "Aucun" }} €</p>
                        </div>
                    </div>
                    <div class="aboutme">
                        <h5>A propos de moi</h5>
                        <p>{{ candidate.about || "Aucun" }}</p>
                    </div>
                    <div class="activity-and-salary-container">
                        <div class="websites">
                            <div class="input">
                                <h5>Site internet</h5>
                                <p>
                                    <a v-if="candidate.site_url" :href="candidate.site_url" target="_blank" class="link-style">{{ candidate.site_url }}</a>
                                    <span v-else>Aucun</span>
                                </p>
                            </div>
                            <div class="input">
                                <h5>Compte linkedin</h5>
                                <p>
                                    <a v-if="candidate.linkedin" :href="candidate.linkedin" target="_blank" class="link-style">{{ candidate.linkedin }}</a>
                                    <span v-else>Aucun</span>
                                </p>
                            </div>
                        </div>
                        <div class="websites2">
                            <div class="input">
                                <h5>Portfolio</h5>
                                <p>
                                    <a v-if="candidate.porfolio_url" :href="candidate.porfolio_url" target="_blank" class="link-style">{{ candidate.porfolio_url }}</a>
                                    <span v-else>Aucun</span>
                                </p>
                            </div>
                            <div class="input">
                                <h5>Autre site</h5>
                                <p>
                                    <a v-if="candidate.autre_url" :href="candidate.autre_url" target="_blank" class="link-style">{{ candidate.autre_url }}</a>
                                    <span v-else>Aucun</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>


<script>
import { getUserById } from '@/services/account.service.js';

export default {
    name: 'CandidateCriteriaSection',

    data() {
        return {
            candidate: {},
        }
    },

    async mounted() {
        this.candidate = await getUserById(this.$route.params.id);
    },
}
</script>

<style>
.v-selection-control-group {
    background-color: transparent;
}

</style>


<style scoped>
/* section container & layout */
.link-style {
    color: black; 
    text-decoration: none; 
    font-weight: 500;
    transition: color 0.3s, text-decoration 0.3s; 
}

.link-style:hover {
    color: #********;
    text-decoration: none;
}

.container {
    display: flex;
    width: 100%;
    flex-direction: column;
    gap: 0px;
    height: fit-content;
}

.section-container {
    background-color: var(--white-200);
    width: 100%;
    border-radius: 2px;
}

.header-container {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid rgba(246, 179, 55, 0.2);
    cursor: auto;
}

.bg-color {
    background-color: rgba(246, 179, 55, 0.2);
    border-bottom: none;
}

.content-container {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: fit-content;
    padding: 24px;
}

/* buttons */
.btn-row {
    display: flex;
    justify-content: end;
}

/* inputs */
.input-field {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.input-field2 {
    display: flex;
    flex-direction: column;
    width: 49%;
}

.checkbox-wrapper {
    width: fit-content;
}
/* content */
.cv-container {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.icons-container {
    display: flex;
    gap: 20px; 
}

.icons-container img {
    cursor: pointer;
}

.contract-and-remote-options-container {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.subgrid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 40px;
    margin-bottom: 30px;
}

.experience-container {
    display: flex;
    flex-direction: column;
    width: 100%;
}


.activity-and-salary-container {
    display: flex;
    flex-direction: column;
    width: 100%;
}
.websites2{
    width: 50%;
}
.websites{
    width: 50%;
}

@media screen and (min-width: 992px) {
    .input-field {
        width: 49%;
    }

    .input-field2 {
        width: 40%;
    }

    .contract-and-remote-options-container {
        flex-direction: row;
        justify-content: space-between;
    }

    .activity-and-salary-container {
        flex-direction: row;
        justify-content: space-between;
    }
}
@media screen and (max-width: 767px) {
    .websites2{
    width: 100%;
}
.websites{
    width: 100%;

}}
</style>
