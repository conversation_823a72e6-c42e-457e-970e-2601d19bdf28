<!-- <template>
    <section class="container padding-container">
      <div>  
        <div class="section-container">
         container header band -->
          <!-- <div class="avatar-title">
                <div class="img-wrapper">
                    <img 
                        v-if="candidate.photo || previewImage" 
                        :src="previewImage || getFullImageUrl()"
                        class="avatar-icon" 
                        alt="avatar image"
                    />
                    <img v-else src="@/assets/icons/avatar.png" alt="avatar image" class="avatar-icon" >
                </div>

                <div class="title">
                    <h5>{{ candidate.first_name }} {{ candidate.last_name }} </h5>
                    <p>{{ candidate.metier }}</p>
                    <p>{{ candidate.email }}</p>
                    <p>{{ candidate.phone }}</p>
                </div>

                </div>
            </div>
        </div>
    </section> -->
  <!-- </template> -->

  <template>
  <section class="container padding-container">
    <div class="card">
      <!-- Header -->
      <div class="card-header">
        <div class="img-wrapper">
          <img
            v-if="candidate.photo || previewImage"
            :src="previewImage || getFullImageUrl()"
            class="avatar-icon"
            alt="avatar image"
          />
          <img
            v-else
            src="@/assets/icons/avatar.png"
            alt="avatar image"
            class="avatar-icon"
          />
        </div>
        <div class="card-title">
          <h3>{{ candidate.first_name }} {{ candidate.last_name }}</h3>
        </div>
        <!-- <div class="favorite-icon">
          <i class="far fa-heart"></i>
        </div> -->
        <!-- Favorite Icon -->
        <div class="fav-wrapper" @click="handleFavoriteClick(candidate.id)">
          <v-tooltip v-if="candidateIsLiked" activator="parent" location="bottom">
            Retirer des favoris
          </v-tooltip>
          <v-tooltip v-else activator="parent" location="bottom">
            Ajouter aux favoris
          </v-tooltip>
          <img
            v-if="candidateIsLiked"
            src="@/assets/search/search-page-card-like-icon-filled.svg"
            alt="heart like filled"
            class="favorite-icon"
          />
          <img
            v-else
            src="@/assets/search/search-page-card-like-icon.svg"
            alt="heart like empty"
            class="favorite-icon"
          />
        </div>
      </div>

      <!-- Body -->
      <div class="card-body">
        <div class="info-row">
          <div class="info-column">
            <h4>E-mail</h4>
            <p>{{ candidate.email }}</p>
          </div>
          <div class="info-column">
            <h4>Téléphone</h4>
            <p>{{ candidate.phone }}</p>
          </div>
        </div>
        <div class="info-row">
          <div class="info-column">
            <h4>Localisation</h4>
            <p>{{ candidate.location || "Ville" }}</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
  
  

<script>
import { addFavoriteProfil, removeFavoriteProfil, isCandidateFavoriteById } from "@/services/favoriteProfil.service";
import { toaster } from "@/utils/toast/toast";
import { getUserById } from '@/services/account.service.js';
import { baseUrl } from "../../../../services/axios";

export default {
    name: 'CandidateProfileSections',

    data() {
        return {
            candidate: {},
            candidateIsLiked: false,
        }
    },

    async mounted() {
        this.candidate = await getUserById(this.$route.params.id);
        await this.checkIfCandidateIsLiked(this.candidate.id);
    },

    methods: {
        async checkIfCandidateIsLiked(candidateId) {
      try {
        const favoritesList = await isCandidateFavoriteById();
        this.candidateIsLiked = favoritesList.some(
          (fav) => fav.apply_user[0].id === candidateId
        );
      } catch (error) {
        //console.error("Erreur lors de la vérification des favoris :", error);
      }
    },
    async handleFavoriteClick(candidateId) {
      try {
        if (this.candidateIsLiked) {
          await removeFavoriteProfil(candidateId);
          this.candidateIsLiked = false;
          toaster.showSuccessPopup("Profil retiré des favoris.");
        } else {
          await addFavoriteProfil(candidateId);
          this.candidateIsLiked = true;
          toaster.showSuccessPopup("Profil ajouté aux favoris.");
        }
      } catch (error) {
        //console.error("Erreur lors de la mise à jour des favoris :", error);
        toaster.showErrorPopup("Une erreur est survenue.");
      }
    },

        //  get photo url
        getFullImageUrl() {
            if (this.candidate.photo) {
                if (this.candidate.photo.name) {
                if (typeof (this.candidate.photo.name) === 'string') {
                    return URL.createObjectURL(this.candidate.photo);
                }
                }
                
                return baseUrl + this.candidate.photo;
            } else if (this.previewImage) {
                return this.previewImage;
            } else {
                return "";
            }
        },
    }
}
</script>
<style>
.v-field--variant-solo-filled .v-field__overlay {
    background-color: transparent;
}
.v-autocomplete .v-field .v-text-field__prefix, .v-autocomplete .v-field .v-text-field__suffix, .v-autocomplete .v-field .v-field__input, .v-autocomplete .v-field.v-field {
    cursor: text;
    background-color: white;
    padding: 0;
}
.v-field--center-affix .v-field__append-inner, .v-field--center-affix .v-field__clearable, .v-field--center-affix .v-field__prepend-inner {
    display: none;
}



</style>

<style scoped>
/* layout */
.container {
    display: flex;
    width: 100%;
    flex-direction: column;
    gap: 20px;
    height: fit-content;
}

.card {
  background-color: #fff;
  border-radius: 10px;
  width: 100%;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 15px;
}

.card-title {
  flex-grow: 1;
  padding-left: 20px;
}

.card-title h3 {
  margin: 0;
  font-size: 20px;
  font-weight: bold;
  gap: 10px;
}

.favorite-icon {
  font-size: 20px;
  color: #999;
  cursor: pointer;
}
.fav-wrapper {
  cursor: pointer;
  position: relative;
}

.section-container:first-of-type {
    margin-bottom: 60px;
}

.section-container {
    background-color: var(--white-200);
    width: 100%;
    border-radius: 5px;
    padding: 16px;
}

.header-container {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid rgba(246, 179, 55, 0.2);
    cursor: auto;
}

.content-container {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: fit-content;
    padding: 24px;
}

.avatar-icon {
    width: auto;
    height: 92px;
    border-radius: 50%;
    object-fit: cover;
    cursor: pointer;
}

.btn-row {
    display: flex;
    justify-content: end;
}

/* input content */
.checkbox-wrapper {
    width: fit-content;
}

.input {
    display: flex;
    flex-direction: column;
    width: 100%;
}

/* content */
.avatar-title {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
    padding: 16px;
    align-items: center;
}

.img-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    flex: 0 0 50px;
}

.title {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
    align-items: center;
    justify-content: center;
}

.contact {
    display: flex;
    flex-direction: column;
    width: 100%;    
    gap: 20px;
}

.location {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.aboutme {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.websites {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 20px;
}

.websites2 {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 20px;
}

.card-body {
  margin-top: 20px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.info-column {
  flex: 1;
  padding-right: 15px;
}

.info-column h4 {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
}

.info-column p {
  font-size: 16px;
  color: #333;
  margin: 0;
}

@media screen and (min-width: 992px) {
    .padding-container {
         padding-inline: 5vw; 
    }
    .avatar-title {
        flex-direction: row;
    }

    .img-wrapper {
        width: 10%;
    }

    .input {
        width: 49%;
    }

    .title {
        width: 90%;
        align-items: baseline;
    }

    .contact {
        flex-direction: row;
        justify-content: space-between;   
    }

    .location {
        width: 49%;
    }

    .websites {
        flex-direction: row;
        justify-content: space-between;
    }

    .websites2 {
        flex-direction: row;
        justify-content: space-between;
    }
}


</style>