<template>
  <div class="container">
    <div class="header-container padding-container">
      <h2>Facturation</h2>
      <div class="facturation-container">
        <div>
          <h5>Historique de facturation</h5>
          <p>Retrouvez toutes vos factures dans votre historique.</p>
        </div>
        <div v-if="factures && factures.length === 0">
          <p>Vous n'avez pas de factures</p>
        </div>
        <div v-else-if="factures && factures.length > 0">
          <table>
            <thead>
              <tr>
                <th scope="col">Date</th>
                <th scope="col">Devis</th>
                <th scope="col">Facture</th>
                <th scope="col">Abonnement</th>
                <th scope="col">Prix</th>
                <th scope="col">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="facture in paidFactures" :key="facture.id">
                <td>{{ new Date(facture.date).toLocaleDateString() }}</td>
                <td>Devis n°{{ facture.number }}</td>
                <td>Facture n°{{ facture.number }}</td>
                <td>{{ getSubscriptionName(facture.type_produit) }}</td>
                <td>{{ facture.prix_total_ttc }}€</td>
                <td>
                  <a @click.prevent="openPDF(facture)"
                    ><img src="../../../../assets/icons/eye.svg" alt="eye icon"
                  /></a>
                  <a @click.prevent="downloadPDF(facture)"
                    ><img
                      src="../../../../assets/icons/download-icon-noborder.svg"
                      alt="download icon"
                  /></a>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import pdfMake from 'pdfmake/build/pdfmake';
  import pdfFonts from 'pdfmake/build/vfs_fonts';

  pdfMake.vfs = pdfFonts.vfs;

  export default {
    name: 'FacturationComponent',

    props: {
      factures: {
        type: Array,
        default: () => [],
      },
      subscriptions: {
        type: Array,
        default: () => [],
      },
      user: {
        type: Object,
        required: true,
      },
    },

    computed: {
      paidFactures() {
        return this.factures.filter((facture) => facture.payer === true);
      },
    },

    methods: {
      getSubscriptionName(typeProduitId) {
        if (!typeProduitId || !Array.isArray(this.subscriptions))
          return 'Inconnu';

        const match = this.subscriptions.find(
          (sub) => sub.id === typeProduitId
        );

        return match ? match.nom : 'Inconnu';
      },

      generateDevisPDF(facture) {
        const subscription = this.subscriptions.find(
          (sub) => sub.id === facture.type_produit
        );

        const {
          first_name,
          last_name,
          company,
          email,
          siret,
          adress,
          code_postal,
          ville,
        } = this.user;

        const date = new Date(facture.date).toLocaleDateString();
        const prix_ht = facture.prix_total_ht;
        const prix_ttc = facture.prix_total_ttc;
        const tva = (prix_ttc - prix_ht).toFixed(2);

        return {
          content: [
            { text: `Facture n° : ${facture.number}`, style: 'header' },
            { text: `Client : ${first_name} ${last_name}`, style: 'subheader' },
            { text: `Société : ${company}`, style: 'subheader' },
            { text: `SIRET : ${siret}`, style: 'subheader' },
            {
              text: `Adresse : ${adress}, ${code_postal} ${ville}`,
              style: 'subheader',
            },
            { text: `Email : ${email}`, style: 'subheader' },
            { text: `Date de la facture : ${date}`, style: 'subheader' },
            { text: ' ', margin: [0, 10] },
            {
              table: {
                widths: ['*', '*'],
                body: [
                  ['Abonnement', subscription?.nom || 'Inconnu'],
                  ['Prix HT', `${prix_ht} €`],
                  ['TVA', `${tva} €`],
                  ['Prix total', `${prix_ttc} €`],
                ],
              },
            },
          ],
          styles: {
            header: {
              fontSize: 18,
              bold: true,
            },
            subheader: {
              fontSize: 12,
              margin: [0, 2],
            },
          },
          defaultStyle: {
            font: 'Roboto',
          },
        };
      },

      openPDF(facture) {
        const pdfDoc = this.generateDevisPDF(facture);
        pdfMake.createPdf(pdfDoc).open();
      },

      downloadPDF(facture) {
        const pdfDoc = this.generateDevisPDF(facture);
        pdfMake.createPdf(pdfDoc).download(`devis-${facture.number}.pdf`);
      },
    },
  };
</script>

<style scoped>
  .facturation-container {
    background: var(--white-200);
    padding: 24px 80px;
    margin: 24px 0;
  }

  /* * {
  border: red 1px solid;
} */

  table {
    width: 100%;
    margin-top: 36px;
    text-align: center;
  }

  td,
  th {
    padding-inline: 10px;
    padding-top: 15px;
    padding-bottom: 15px;
    border-bottom: 1px var(--primary-1) solid;
  }

  td a {
    cursor: pointer;
  }

  @media screen and (min-width: 1800px) {
    .facturation-container {
      background: var(--white-200);
      padding: 24px 80px;
      margin: auto;
      width: 80%;
    }

    h2 {
      margin: auto;
      width: 80%;
      margin-bottom: 20px;
    }
  }

  @media screen and (min-width: 2400px) {
    .facturation-container {
      background: var(--white-200);
      padding: 24px 80px;
      margin: auto;
      width: 70%;
    }

    h2 {
      margin: auto;
      width: 70%;
      margin-bottom: 20px;
    }
  }
</style>
