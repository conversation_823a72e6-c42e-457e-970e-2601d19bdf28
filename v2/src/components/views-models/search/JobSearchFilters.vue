<template>
  <section class="filters">
    <!-- <div class="btn-open-panel">
      <v-btn flat @click="toggleFilterPanel"
        >Tous mes filtres
        <template v-slot:append>
          <img src="@/assets/search/search-page-deploy-button-icon.svg" />
        </template>
      </v-btn>
    </div> -->
    <!-- Bouton pour ouvrir/fermer les filtres en mode mobile -->
    <div class="btn-open-panel">
      <v-btn flat @click="toggleFilterPanel">
        <span>Tous mes filtres</span>
        <img
          :src="
            filterPanelOn
              ? require('@/assets/icons/arrow-top.svg')
              : require('@/assets/icons/arrow-bottom.svg')
          "
          alt="Toggle Icon"
        />
      </v-btn>
    </div>

    <div :class="filterPanelOn ? 'wrapper visible' : 'wrapper'">
      <!-- Trier par -->
      <JobSearchAccordion
        v-if="sortFields.length > 0"
        field="sort"
        title="Trier par"
        :fields="sortFields"
        :multiple="false"
        @checkbox-state="getFormDatas"
        nobox
        chips
        @input="validateFilters"
      />
      <!-- Type de contrat -->
      <JobSearchAccordion
        v-if="contractOptionsInputList.length > 0"
        field="contract"
        title="Type de contrat"
        :fields="contractOptionsInputList"
        @checkbox-state="getFormDatas"
        multiple
        chips
        @input="validateFilters"
      />
      <!-- Type de travail -->
      <JobSearchAccordion
        v-if="remoteOptionsInputList.length > 0"
        field="remote"
        title="Type de travail"
        :fields="remoteOptionsInputList"
        @checkbox-state="getFormDatas"
        multiple
        chips
        @input="validateFilters"
      />
      <!-- Secteur d'activité
      <JobSearchAccordion
        v-if="activitySectorInputList.length > 0"
        field="sector"
        title="Secteur d'activité"
        :fields="activitySectorInputList"
        @checkbox-state="getFormDatas"
        multiple
        chips
        @input="validateFilters"
      />
      <div class="sliders-wrapper">
       <div class="slider-wrapper">
          <p>Localisation (km)</p>
          <v-tooltip activator="parent" location="start"
            >{{ locationValue }}km</v-tooltip
          >
          <v-slider
            v-model="formData.locationValue"
            :min="0"
            :max="50"
            :step="5"
            color="#F6B337"
            thumb-label
            strict
            @end="emitSliderState('location', formData.locationValue)"
          /> 
        </div>

        <div class="slider-wrapper">
          <p>Salaire brut par an (euros)</p>
          <v-tooltip activator="parent" location="start">
            {{ salaryValue[0] }}€
          </v-tooltip>
          <v-tooltip activator="parent" location="end">
            {{ salaryValue[1] }}€
          </v-tooltip>
          <v-range-slider
            v-model="salaryValue"
            :min="0"
            :max="120000"
            :step="1000"
            color="#F6B337"
            thumb-label
            strict
            @end="emitSliderState('salary', salaryValue)"
          />
        </div>
      </div> -->
      <div class="btns-wrapper">
        <div @click="createAlert">
          <PrimaryNormalButton
            textContent="Créer une alerte"
            btnColor="secondary"
            width="100%"
            alert
            alt="Image cloche pour créer une alerte"
          />
        </div>
      </div>
    </div>
  </section>

  <!--SideAd /-->

</template>

<script>
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import { ACTIVITY_FIELDS } from '@/utils/base/activity_sector.js';
  import { CONTRACT_FIELDS } from '@/utils/base/contract.js';
  import { TELETRAVAIL_FIELDS } from '@/utils/base/teletravail.js';
  import { toaster } from '@/utils/toast/toast.js';
  import store from '../../../store';
  import JobSearchAccordion from './JobSearchAccordion.vue';
  import SideAd from '../../../components/google-ad/SideAd.vue';


  export default {
    name: 'FiltersJobSearch',

    components: {
      PrimaryNormalButton,
      JobSearchAccordion,
      SideAd,
    },

    data() {
      return {
        formData: {
          sort: null,
          contract: null,
        },
        sortFields: ['Date', 'Salaire'], // Options de tri
        locationValue: 0,
        salaryValue: [0, 120000],
        filterPanelOn: false,
        activitySectorInputList: [],
        remoteOptionsInputList: [],
        contractOptionsInputList: [],
      };
    },

    mounted() {
      this.activitySectorInputList = this.generateList(ACTIVITY_FIELDS, 'nom');
      this.remoteOptionsInputList = this.generateList(
        TELETRAVAIL_FIELDS,
        'teletravail'
      );
      this.contractOptionsInputList = this.generateList(
        CONTRACT_FIELDS,
        'contrat'
      );
    },

    methods: {
      toggleFilterPanel() {
        this.filterPanelOn = !this.filterPanelOn;
      },
      createAlert() {
        const isLoggedIn = store.getters.isLoggedIn;
        if (!isLoggedIn) {
          toaster.showErrorPopup(
            'Veuillez vous connecter pour créer une alerte.'
          );
          return;
        }
        this.$emit('open-alert');
      },
      generateList(objectList, fieldName) {
        let list = [];
        for (let i = 0; i < objectList.length; i++) {
          list.push(objectList[i][fieldName]);
        }
        return list;
      },
      getFormDatas(field, datas) {
        if (typeof datas === 'object' && datas !== null) {
          // Filtrer les clés dont la valeur est true
          const dataKeys = Object.keys(datas).filter(
            (key) => datas[key] === true
          );

          this.formData[field] = dataKeys; // Assigner les clés correspondantes au champ formData

          // Gérer le champ 'sort' séparément si c'est le cas
          if (field === 'sort') {
            this.formData.sort = dataKeys.length > 0 ? dataKeys : []; // Assurez-vous que c'est un tableau même si vide
          }
        } else {
          console.warn(`${field} is not a valid object:`, datas); // Log de l'erreur
          this.formData[field] = []; // Ou initialisez-le à un tableau vide
        }

        //console.log('Form Data:', this.formData); // Pour vérifier les données collectées
        this.validateFilters();
      },
      emitSliderState(type, value) {
        if (type === 'salary') {
          this.formData.salaire_min = value[0];
          this.formData.salaire_max = value[1];
        } else if (type === 'location') {
          this.formData.locationValue = value;
        }

        // Valider les filtres après mise à jour
        this.validateFilters();
      },
      validateFilters() {
        if (!Array.isArray(this.salaryValue) || this.salaryValue.length !== 2) {
          //console.error(
          //  'Erreur : salaryValue doit être un tableau avec deux valeurs.'
          //);
          return;
        }

        // Mise à jour des données du formulaire
        this.formData.salaire_min = this.salaryValue[0];
        this.formData.salaire_max = this.salaryValue[1];
      },
    },
  };
</script>

<style scoped>
  .filters {
    width: 100%;
    border-radius: 15px;
    background-color: var(--surface-bg-2);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-block: 10px;
    flex-shrink: 1;
  }

  .btn-open-panel {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    cursor: pointer;
  }

  .wrapper {
    display: none;
    width: 100%;
    height: fit-content;
    padding: 20px;
  }

  .btns-wrapper {
    display: flex;
    flex-direction: column;
    margin-block: 10px;
    width: 100%;
    height: 140px;
    padding: 10px;
    gap: 10px;
  }

  .sliders-wrapper {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    height: 250px;
  }

  .visible {
    display: initial;
  }

  @media screen and (min-width: 992px) {
    .btn-open-panel {
      display: none;
    }

    .wrapper {
      display: initial;
    }
  }
</style>
