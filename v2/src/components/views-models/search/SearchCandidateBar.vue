<template>
  <section class="search-bar">
    <div class="field-container1">
      <h5>Recherche</h5>
      <v-text-field
        v-model="formData.query"
        label="Saisis un métier, un prénom ou un nom"
        type="text"
        clearable
        :items="optionsFieldEmploi"
        :hide-no-data="true"
        :hide-selected="true"
        @click:clear="handleQueryClear"
        @input="handleQueryInput"
        @keydown.enter="handleSearch"
      />
    </div>

    <div class="field-container2">
      <h5>Lieu</h5>
      <!--LocationInput
        :paramLocation="formData.ville"
        textContent="Saisis une ville"
        @city-and-postal-code="getCityAndPostalCodeValue"
      /-->
      <v-text-field
        v-model="formData.ville"
        label="Ex: Paris"
        type="text"
        clearable
        @click:clear="handleVilleClear"
        @input="handleVilleInput"
        @keydown.enter="handleSearch"
      />
    </div>

    <div class="btn-container">
      <v-btn
        color="var(--search-candidate-searchbtn-bg-color)"
        :ripple="true"
        class="btn-magnifier"
        @click="handleSearch"
      >
        <img
          src="@/assets/search/search-page-searchbar-icon.svg"
          alt="magnifier glass"
        />
      </v-btn>
    </div>
  </section>
</template>

<script>
  import LocationInput from '@/components/inputs/LocationInput.vue';
  // import { normalizeString } from '../../../utils/stringUtils';

  export default {
    name: 'SearchCandidateBar',

    components: {
      LocationInput,
    },

    data() {
      return {
        formData: {
          query: null,
          ville: null,
          postalCode: null,
        },
        optionsFieldEmploi: [
          'Agent immobilier',
          'Analyste de données',
          'Analyste en sécurité',
          'Analyste financier',
          'Animateur socioculturel',
          'Architecte',
          'Architecte de systèmes',
          'Assistant administratif',
          'Auditeur',
          'Avocat',
          'Chargé de clientèle',
          'Chargé de communication',
          'Chargé de mission',
          'Chargé de projet immobilier',
          'Chef de cuisine',
          'Chef de produit digital',
          'Chef de projet',
          'Chef de rayon',
          'Chercheur',
          'Charpentier',
          'Comptable',
          'Comédien',
          'Conseiller en développement personnel',
          'Conseiller en image',
          'Conseiller en orientation',
          'Conseiller financier',
          'Conseiller juridique',
          'Consultant en développement durable',
          'Consultant en IT',
          'Consultant en ressources humaines',
          'Consultant en stratégie',
          'Consultant en transformation numérique',
          "Coordinateur d'événements",
          'Créateur de contenu',
          'Data Scientist',
          'Designer graphique',
          'Détective privé',
          'Développeur',
          'Développeur Full Stack',
          "Développeur d'applications",
          'Développeur front end',
          'Développeur logiciel',
          'Développeur mobile',
          'Développeur web',
          'Directeur artistique',
          'Directeur de la communication',
          'Directeur de production',
          'Écrivain',
          'Éducateur spécialisé',
          'Électricien',
          'Enseignant',
          'Expert en assurance',
          'Expert-comptable',
          'Gestionnaire de contenu',
          'Gestionnaire de la production',
          'Gestionnaire de la qualité',
          "Gestionnaire de l'approvisionnement",
          'Gestionnaire de portefeuille',
          'Gestionnaire de produit',
          'Gestionnaire de projet IT',
          'Graphiste',
          'Guide touristique',
          'Infirmier',
          'Ingénieur civil',
          'Ingénieur en conception',
          'Ingénieur en cybersécurité',
          'Ingénieur en énergie',
          'Ingénieur en intelligence artificielle',
          'Ingénieur en mécanique',
          'Ingénieur en robotique',
          'Ingénieur en télécommunications',
          'Jardinier paysagiste',
          'Journaliste',
          'Maçon',
          'Médecin',
          'Moniteur de sport',
          'Musicien',
          'Notaire',
          'Pharmacien',
          'Photographe',
          'Plombier',
          'Producteur',
          'Professeur',
          'Psychologue',
          'Rédacteur technique',
          'Rédacteur web',
          'Réalisateur',
          'Réparateur automobile',
          'Responsable de la conformité',
          'Responsable de la conformité réglementaire',
          'Responsable de la formation',
          'Responsable de la logistique',
          'Responsable de la production',
          'Responsable de la R&D',
          'Responsable de la relation client',
          'Responsable de la stratégie',
          'Responsable de la stratégie digitale',
          "Responsable de l'expérience utilisateur",
          "Responsable de l'innovation",
          'Responsable des achats',
          'Responsable des opérations',
          'Responsable des partenariats',
          'Responsable des ressources humaines',
          'Responsable du développement commercial',
          'Responsable du service après-vente',
          'Responsable e-commerce',
          'Scénariste',
          'Spécialiste en gestion des risques',
          'Spécialiste en marketing digital',
          'Spécialiste en recrutement',
          'Spécialiste en réseaux sociaux',
          'Technicien de laboratoire',
          'Technicien de maintenance',
          'Technicien de maintenance industrielle',
          'Technicien en électromécanique',
          'Technicien en informatique',
          'Technicien en maintenance',
          'Technicien en réseaux',
          'Technicien en sécurité incendie',
          'Traducteur',
          'Vétérinaire',
        ],
      };
    },

    methods: {
      updateURL(newParams) {
        const params = { ...this.$route.query };

        Object.keys(newParams).forEach((key) => {
          if (
            newParams[key] === null ||
            newParams[key] === undefined ||
            newParams[key] === ''
          ) {
            delete params[key];
          } else {
            params[key] = newParams[key];
          }
        });

        this.$router.replace({ query: params }).catch((err) => {
          //console.error("Erreur lors de la mise à jour de l\'URL:", err);
        });
      },
      handleQueryInput(event) {
        if (event && event.target) {
          this.formData.query = event.target.value;
        } else {
          this.formData.query = event;
        }
      },

      handleVilleInput(event) {
        if (event && event.target) {
          this.formData.ville = event.target.value;
        } else {
          this.formData.ville = event;
        }
      },

      handleQueryClear() {
        this.formData.query = '';
        this.updateURL({ metier: null, nom: null });
      },

      handleVilleClear() {
        this.formData.ville = '';
        this.formData.postalCode = null;
        this.updateURL({ ville: null, cp: null });
      },

      handleSearch() {
        const query = {};

        if (!this.formData.query) {
          query.metier = null;
          query.nom = null;
        } else {
          if (this.optionsFieldEmploi.includes(this.formData.query)) {
            query.metier = this.formData.query;
            // query.metier = normalizeString(this.formData.query);
          } else {
            query.metier = null;
            query.nom = this.formData.query;
          }
        }

        if (!this.formData.ville) {
          query.ville = null;
          query.cp = null;
        } else {
          if (this.formData.ville) {
            query.ville = this.formData.ville;
          }
          if (this.formData.postalCode) {
            query.cp = this.formData.postalCode;
          }
        }

        this.updateURL(query);
      },
      // this.$emit('search-btn-click', this.formData);

      getCityAndPostalCodeValue(cityAndPostalCode) {
        if (cityAndPostalCode) {
          this.formData.ville = cityAndPostalCode[0];
          this.formData.postalCode = cityAndPostalCode[1];
        }
      },
    },
    created() {
      const query = this.$route.query;
      if (query.metier) {
        this.formData.query = query.metier;
      } else if (query.nom) {
        this.formData.query = query.nom;
      }
      if (query.ville) {
        this.formData.ville = query.ville;
      }
      if (query.cp) {
        this.formData.postalCode = query.cp;
      }
    },
  };
</script>

<style scoped>
  .search-bar {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 400px;
    justify-content: space-between;
    align-items: center;
  }

  h5 {
    margin-bottom: 10px;
  }

  .field-container1 {
    padding-top: 20px;
    padding-bottom: 0;
    padding-left: 24px;
    padding-right: 24px;
    background-color: var(--search-candidate-searchfield-bg-color);
    width: 100%;
    border-radius: 15px;
  }

  .field-container2 {
    padding-top: 20px;
    padding-bottom: 0;
    padding-left: 24px;
    padding-right: 24px;
    background-color: var(--search-candidate-searchfield-bg-color);
    width: 100%;
    border-radius: 15px;
  }

  .btn-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .btn-magnifier {
    width: 100%;
    height: 75px;
    border-radius: 15px;
  }

  @media screen and (min-width: 992px) {
    .search-bar {
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      height: fit-content;
    }

    .field-container1 {
      width: 45%;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    .field-container2 {
      width: 45%;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }

    .btn-container {
      width: 5%;
      height: 136px;
    }

    .btn-magnifier {
      width: 5%;
      height: 100%;
    }
  }
</style>
