<template>
  <section class="job-offer-grid">
    <div
      v-for="jobOffer in jobOfferList"
      :key="jobOffer.id"
      class="job-offer-wrapper"
    >
      <LargeJobCard :jobOffer="jobOffer" :favoriteJobList="favoriteJobList" />
    </div>
  </section>
</template>

<script>
  import LargeJobCard from '@/components/cards/job-cards/LargeJobCard.vue';

  export default {
    name: 'JobOfferGridDisplay',

    props: {
      //  list of all the jobs offer
      jobOfferList: {
        type: Array,
        default: null,
      },

      //  list of user favorite job
      favoriteJobList: {
        type: Array,
        default: null,
      },
    },

    components: {
      LargeJobCard,
    },
  };
</script>

<style scoped>
  .job-offer-grid {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-left: 15px;
    margin-right: 15px;
    box-sizing: border-box;
  }

  .job-offer-wrapper {
    display: flex;
    justify-content: center;
    width: 100%;
  }

  /* ✅ MOBILE : petits smartphones (iPhone SE, etc.) */
  @media screen and (max-width: 480px) {
    .job-offer-grid {
      margin: 20px 0;
      display: flex;
      flex-direction: column;
      margin-left: 0px;
      margin-right: 0px;
    }
  }

  /* ✅ MOBILE LARGE : smartphones standards (iPhone 12, Galaxy S21, etc.) */
  @media screen and (min-width: 481px) and (max-width: 767px) {
    .job-offer-grid {
      margin: 20px 0;
      display: flex;
      flex-direction: column;
      margin-left: 0px;
      margin-right: 0px;
    }
  }

  /* ✅ TABLETTE : format portrait (iPad, Galaxy Tab) */
  @media screen and (min-width: 768px) and (max-width: 1023px) {
    .job-offer-grid {
      margin: 20px 0;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      margin-left: 5px;
      margin-right: 5px;
    }
  }

  /* ✅ DESKTOP : écrans larges */
  @media screen and (min-width: 1024px) {
    .job-offer-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }
</style>
