<template>
  <section class="job-offer-grid">
    <template v-for="(item, index) in processedCandidateList" :key="item.key">
      <div v-if="!item.isAd" class="job-offer-wrapper">
        <LargeCandidateCard :candidate="item.data" :currentUserId="currentUserId" />
      </div>
      <div v-else class="job-offer-wrapper">
        <AdSimulation />
      </div>
    </template>
  </section>
</template>

<script>
  import LargeCandidateCard from '../../cards/candidate-card/LargeCandidateCard.vue';
  import AdSimulation from '../../google-ad/GridAd.vue';

  export default {
    name: 'CandidateGridDisplay',

    props: {
      candidateList: {
        type: Array,
        default: () => [],
      },
      favoriteJobList: {
        type: Array,
        default: () => [],
      },
    },

    components: {
      LargeCandidateCard,
      AdSimulation,
    },

    mounted() {
      console.log('Candidate List:', this.candidateList);
      console.log('Favorite Job List:', this.favoriteJobList);
    },

    computed: {
      processedCandidateList() {
        const result = [];
        console.log('Processing Candidate List:', this.candidateList);

        if (this.candidateList && this.candidateList.length > 0) {
          this.candidateList.forEach((candidate, index) => {
            // Vérifiez si candidate est un Proxy et déréférencez-le si nécessaire
            const candidateData = candidate._isProxy
              ? candidate._object
              : candidate;

            if (candidateData) {
              result.push({
                isAd: false,
                data: candidateData,
                key: `candidate-${candidateData.id || index}`,
              });
            }
          });
        }

        console.log('Processed Candidate List:', result);
        return result;
      },
    },
  };
</script>

<style scoped>
  .job-offer-grid {
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
    margin-left: 20px;
  }

  .job-offer-wrapper {
    display: flex;
    justify-content: center;
    width: 100%;
  }

  /* ✅ Responsive inchangé */
  @media screen and (max-width: 480px) {
    .job-offer-grid {
      margin: 20px 0;
      display: flex;
      flex-direction: column;
      margin-left: 0px;
    }
  }

  @media screen and (min-width: 481px) and (max-width: 767px) {
    .job-offer-grid {
      margin: 20px 0;
      display: flex;
      flex-direction: column;
      margin-left: 0px;
    }
  }

  @media screen and (min-width: 768px) and (max-width: 1023px) {
    .job-offer-grid {
      margin: 20px 0;
      display: flex;
      flex-direction: column;
    }
  }
</style>
