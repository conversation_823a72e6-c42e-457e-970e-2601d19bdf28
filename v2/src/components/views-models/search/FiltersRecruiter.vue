<template>
  <section class="filters">
    <div class="btn-open-panel">
      <v-btn flat @click="toggleFilterPanel">
        Tous mes filtres
        <template v-slot:append>
          <img
            src="@/assets/search/search-page-deploy-button-icon.svg"
            alt="Icône du bouton de déploiement de la page de recherche"
          />
        </template>
      </v-btn>
    </div>

    <div :class="filterPanelOn ? 'wrapper visible' : 'wrapper'">
      <!-- contract type -->
      <SearchProfilesAccordion
        v-if="contractOptionsInputList.length > 0"
        :key="'accordion-contract'"
        field="contract"
        title="Type de contract"
        :fields="contractOptionsInputList"
        multiple
        chips
      />

      <!-- work type -->
      <SearchProfilesAccordion
        v-if="remoteOptionsInputList.length > 0"
        :key="'accordion-remote'"
        field="remote"
        title="Type de travail"
        :fields="remoteOptionsInputList"
        multiple
        chips
      />

      <div class="btns-wrapper">
        <div @click="createAlert">
          <PrimaryNormalButton
            textContent="Créer une alerte"
            btnColor="secondary"
            width="100%"
            alert
          />
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  import SearchProfilesAccordion from '@/components/buttons/SearchProfilesAccordion.vue';
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import { CONTRACT_FIELDS } from '@/utils/base/contract.js';
  import { TELETRAVAIL_FIELDS } from '@/utils/base/teletravail.js';
  import { toaster } from '@/utils/toast/toast.js';

  export default {
    name: 'FiltersRecruiter',

    components: {
      SearchProfilesAccordion,
      PrimaryNormalButton,
    },

    data() {
      return {
        formData: {
          contract: [],
          remote: [],
          sector: [],
        },
        filterPanelOn: false, //  toggle panel
        remoteOptionsInputList: [], //  select options for remotes options input
        contractOptionsInputList: [], //  select options for contract input
      };
    },
    mounted() {
      this.$nextTick(() => {
        this.remoteOptionsInputList = this.generateList(
          TELETRAVAIL_FIELDS,
          'teletravail'
        );
        this.contractOptionsInputList = this.generateList(
          CONTRACT_FIELDS,
          'contrat'
        );
      });
    },

    methods: {
      createAlert() {
        const isLoggedIn = this.$store.getters.isLoggedIn;
        if (!isLoggedIn) {
          toaster.showErrorPopup(
            'Veuillez vous connecter pour créer une alerte.'
          );
          return;
        }
        this.$emit('open-alert');
      },

      generateList(objectList, fieldName) {
        return objectList.map((item) => item[fieldName]);
      },

      toggleFilterPanel() {
        this.filterPanelOn = !this.filterPanelOn;
      },
    },
  };
</script>
<style scoped>
  .filters {
    width: 100%;
    border-radius: 15px;
    background-color: var(--surface-bg-2);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-block: 10px;
  }

  .btn-open-panel {
    display: flex;
    justify-content: center;
  }

  .wrapper {
    display: none;
    width: 100%;
    height: fit-content;
    padding: 20px;
  }

  .btns-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-block: 10px;
    width: 100%;
    height: 140px;
    margin-top: 40px;
    padding: 10px;
  }

  .sliders-wrapper {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    height: 250px;
  }

  .visible {
    display: initial;
  }

  @media screen and (min-width: 992px) {
    .btn-open-panel {
      display: none;
    }

    .wrapper {
      display: initial;
    }
  }
</style>
