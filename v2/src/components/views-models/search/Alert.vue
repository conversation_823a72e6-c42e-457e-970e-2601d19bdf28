<template>
  <section class="alert-panel">
    <div class="content-wrapper">
      <div class="top-content">
        <h2>Mon alerte</h2>

        <v-btn
          @click="$emit('close-alert-panel')"
          class="close-btn size-btn"
          flat
        >
          <img
            src="@\assets\search/search-page-alert-panel-close-icon.svg"
            class="close-img"
          />
        </v-btn>
      </div>
      <!-- name & mail -->
      <div class="input-row">
        <div class="input-field">
          <h5>Nom de l'alerte</h5>
          <v-text-field
            v-model="formData.alertName"
            label="Saisi le nom de ton alerte"
            type="text"
            :rules="notEmptyRules"
            fast-fail
          />
        </div>

        <div class="input-field">
          <h5>E-mail</h5>
          <v-text-field
            v-model="formData.mail"
            label="<EMAIL>"
            type="text"
            :rules="[...emailRules, ...notEmptyRules]"
            fast-fail
          />
        </div>
      </div>

      <!-- job title -->
      <div class="input-row">
        <div class="input-field2">
          <h5>Poste</h5>
          <v-text-field
            v-model="formData.title"
            label="Sélectionne le métier recherché"
            type="text"
            :rules="notEmptyRules"
            fast-fail
          />
        </div>
      </div>

      <!-- Frequency selection -->
      <div class="input-row">
        <div class="input-field">
          <h5>Fréquence</h5>
          <v-select
            v-model="formData.frequency_best_jobs"
            :items="frequencyOptions"
            label="Choisissez la fréquence de l'alerte"
            @change="updateStartDate"
          />
        </div>
      </div>

      <!-- location & remote option -->
      <div class="input-row">
        <div class="input-field">
          <h5>Localisation</h5>
          <LocationInput
            @city-and-postal-code="getCityAndPostalCodeValue"
            :textContent="formData.city"
          />

          <v-tooltip activator="parent" location="start"
            >{{ radius }}km</v-tooltip
          >
          <v-slider
            v-model="formData.radius"
            :min="0"
            :max="50"
            :step="5"
            color="#F6B337"
            thumb-label
            strict
          />
        </div>

        <div class="input-field">
          <h5>Type de travail</h5>
          <CustomCheckbox
            field="remote"
            :fields="remoteOptionList"
            @checkbox-stringarray="getFormDatas"
            multiple
            :cValue="formData.remote"
          />
        </div>
      </div>

      <!-- contract type & activity sector -->
      <div class="input-row">
        <div class="input-field">
          <h5>Contrat</h5>
          <div class="subgrid">
            <CustomCheckbox
              field="contract"
              :fields="contractTypeList"
              @checkbox-stringarray="getFormDatas"
              multiple
              :cValue="formData.contract"
            />
          </div>
        </div>

        <div class="input-field">
          <h5>Secteur d'activité</h5>
          <v-select
            clearable
            v-model="formData.sector"
            label="Choisis ton secteur d'activité professionnelle"
            :items="activitySectorList"
            multiple
            variant="underlined"
          ></v-select>
        </div>
      </div>

      <!-- experience & anual salary -->
      <div class="input-row">
        <div class="input-field">
          <h5>Expérience</h5>
          <div class="subgrid">
            <CustomRadio
              field="experience"
              :fields="experienceTypeList"
              @radio-selection="getFormDatas"
              :cValue="formData.experience"
            />
          </div>
        </div>

        <div class="input-field salary-bar">
          <h5>Salaire brut annuel en euros</h5>
          <v-tooltip activator="parent" location="start"
            >{{ salaryValue[0] }}€</v-tooltip
          >
          <v-tooltip activator="parent" location="end"
            >{{ salaryValue[1] }}€</v-tooltip
          >
          <v-range-slider
            v-model="formData.salaryValue"
            :min="0"
            :max="120000"
            :step="1000"
            color="#F6B337"
            thumb-label="true"
            strict
            v-bind="props"
          />
        </div>
      </div>

      <!-- soft skills -->
      <!-- <div class="input-row">

                <div class="input-field2">
                    <h5>Savoirs-faire</h5>
                    <v-select
                        clearable
                        v-model="formData.skills"
                        label="Sélectionne tes savoir-faire"
                        :items="softSkillList"
                        multiple
                    ></v-select>
                </div>

            </div>

            <div class="input-row">

                <div class="input-field2">
                    <h5>Savoirs-être</h5>
                    <v-select
                        clearable
                        v-model="formData.softSkills"
                        label="Sélectionne tes savoir-être"
                        :items="skillList"
                        multiple
                    ></v-select>
                </div>

            </div> -->

      <!-- btn cancel & btn create alert -->
      <div v-if="!readOnly" class="btns-row">
        <div class="btns-wrapper">
          <div class="btn-wrapper" @click="$emit('close-alert-panel')">
            <PrimaryNormalButton textContent="Annuler" btnColor="secondary" />
          </div>
          <div class="btn-wrapper">
            <PrimaryNormalButton
              textContent="Créer mon alerte"
              @click="createAlert()"
            />
          </div>
        </div>
      </div>

      <!-- btn delete & modify -->
      <div v-else class="btns-row">
        <div class="btns-wrapper">
          <div class="btn-wrapper" @click="deleteThisJobAlert()">
            <PrimaryNormalButton textContent="Supprimer" btnColor="secondary" />
          </div>
          <div class="btn-wrapper" @click="modifyAlert()">
            <PrimaryNormalButton textContent="Modifier mon alerte" />
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  import CustomCheckbox from '@/components/buttons/CustomCheckbox.vue';
  import CustomRadio from '@/components/buttons/CustomRadio.vue';
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import LocationInput from '@/components/inputs/LocationInput.vue';
  import {
    createNewJobAlert,
    deleteJobAlert,
    getAlertById,
    getSkillList,
    getSoftSkillList,
    modifyJobAlert,
  } from '@/services/alert.service.js';
  import { ACTIVITY_FIELDS } from '@/utils/base/activity_sector.js';
  import { CONTRACT_FIELDS } from '@/utils/base/contract.js';
  import { EXPERIENCE_FIELDS } from '@/utils/base/experience.js';
  import { PROFESSION_FIELDS } from '@/utils/base/profession.js';
  import { TELETRAVAIL_FIELDS } from '@/utils/base/teletravail.js';
  import gotoPage from '@/utils/router';
  import { mapGetters } from 'vuex';
  import {
    validateEmail,
    validateNotEmpty,
  } from '../../../utils/validationRules';

  export default {
    name: 'Alert',

    components: {
      CustomCheckbox,
      CustomRadio,
      PrimaryNormalButton,
      LocationInput,
    },

    props: {
      alertId: {
        type: String,
        default: null,
      },

      //  alert index in the list
      alertIndex: {
        type: Number,
      },
    },

    data() {
      return {
        formData: {
          city: '',
          radius: 0,
          remote: [], // Initialisation par défaut avec un tableau vide
          contract: [], // Initialisation par défaut avec un tableau vide
          sector: [], // Initialisation par défaut avec un tableau vide
          experience: '',
          salaryValue: [0, 120000], // Initialisation par défaut pour le slider de salaire
          frequency_best_jobs: 'Jamais', // Changement ici
          startDate: null,
        },
        frequencyOptions: [
          'Jamais',
          'Tous les jours',
          'Tous les 7 jours',
          'Tous les mois',
        ],
        activitySectorList: [], //  list of select for activity sector input
        contractTypeList: [], //  list of select for contract input
        remoteOptionList: [], //  list of select for remote option input
        workTypeList: [], //  list of select for work type input
        experienceTypeList: [], //  list of select for experience type input
        radius: 0, //  value of location field
        salaryValue: [0, 120000], //  value of salary field
        alert: null, //  alert object if an alert id is passed as props
        skillList: [], //  list of skill
        softSkillList: [], //  list of soft skill
        readOnly: false, //  toggle read only
        notEmptyRules: [(v) => validateNotEmpty(v) || false],
        emailRules: [(v) => validateEmail(v) || true],
      };
    },

    computed: {
      ...mapGetters(['getUser']),
    },
    mounted() {
      this.formData.mail = this.getUser.email;
    },
    beforeMount() {
      //  if an id is passed as props, hydrate alert with values
      if (this.alertId && typeof this.alertId == 'number') {
        this.alert = getAlertById(this.alertId);
        if (!this.alert) return;

        this.readOnly = true;

        //  format string as list
        let teletravail, contrat, activite, salaire;

        if (this.alert.teletravail != null)
          teletravail = this.alert.teletravail.split(',');
        if (this.alert.contrat != null) contrat = this.alert.contrat.split(',');
        if (this.alert.activite != null)
          activite = this.alert.activite.split(',');
        if (this.alert.salaire != null) salaire = this.alert.salaire.split(',');
        if (this.alert.radius != null) this.alert.radius = this.alert.radius;

        //  get value from alert object to form data to hydrate html
        this.formData = {
          alertName: this.alert.nom, //  string
          mail: this.alert.email, //  string
          title: this.alert.quoi, //  string
          city: this.alert.ville, //  string
          radius: this.alert.radius,
          remote: teletravail, //  list
          contract: contrat, //  list
          sector: activite, //  list
          experience: this.alert.experience, //  string
          salaryValue: salaire, //  list
          frequency_best_jobs: this.alert.frequency_best_jobs || 'Jamais', // Mise à jour ici
          startDate: this.alert.startDate || null,
        };
      }

      //  get skill and soft skill lists
      // this.getSkillsLists();
      //  get all select input list
      this.activitySectorList = this.generateList(ACTIVITY_FIELDS, 'nom');
      this.contractTypeList = this.generateList(CONTRACT_FIELDS, 'contrat');
      this.remoteOptionList = this.generateList(
        TELETRAVAIL_FIELDS,
        'teletravail'
      );
      this.workTypeList = this.generateList(PROFESSION_FIELDS, 'nom');
      this.experienceTypeList = this.generateList(
        EXPERIENCE_FIELDS,
        'experience_job'
      );
    },

    methods: {
      gotoPage,
      //  bind skills lists
      async getSkillsLists() {
        this.skillList = await getSkillList();
        this.softSkillList = await getSoftSkillList();
      },

      //  bind list to variable for input select options
      generateList(objectList, fieldName) {
        let list = [];
        for (let i = 0; i < objectList.length; i++) {
          list.push(objectList[i][fieldName]);
        }
        return list;
      },

      //  get datas from alert fields and hook them to formData
      getFormDatas(field, datas) {
        this.formData[field] = datas;
      },
      updateStartDate() {
        const now = new Date();
        switch (this.formData.frequency_best_jobs) {
          case 'Jamais':
            this.formData.startDate = null;
            break;
          case 'Tous les jours':
            this.formData.startDate = new Date(now - 24 * 60 * 60 * 1000);
            break;
          case 'Tous les 7 jours':
            this.formData.startDate = new Date(now - 7 * 24 * 60 * 60 * 1000);
            break;
          case 'Tous les mois':
            this.formData.startDate = new Date(now - 30 * 24 * 60 * 60 * 1000);
            break;
          default:
            this.formData.startDate = null;
            break;
        }
      },

      async createAlert() {
        this.updateStartDate();
        try {
          const response = await createNewJobAlert(
            this.formData,
            this.alertIndex
          );
          //console.log({ response });
          if (response.statusText === 'Created') {
            this.$emit('close-alert-panel');
          }
        } catch (error) {
          //console.error("Erreur lors de la création de l'alerte :", error);
        }
      },

      //  delete alert
      async deleteThisJobAlert() {
        const deletionSuccessfull = await deleteJobAlert(
          this.alertId,
          this.alertIndex
        );
        this.$emit('close-alert-panel');
      },

      //  modify alert
      async modifyAlert() {
        this.updateStartDate();
        const modificationSuccessfull = await modifyJobAlert(
          this.alertId,
          this.formData,
          this.alertIndex
        );
        if (modificationSuccessfull) this.$emit('close-alert-panel');
      },

      //  get city and postal code value and bind them to formData
      getCityAndPostalCodeValue(cityAndPostalCode) {
        if (cityAndPostalCode) {
          this.formData['city'] = cityAndPostalCode[0];
          this.formData['postalCode'] = cityAndPostalCode[1];
        }
      },
    },
  };
</script>

<style scoped>
  h5 {
    margin-bottom: 7px;
  }
  .size-btn {
    padding: 0 !important;
    min-width: 4px !important;
  }

  .alert-panel {
    background-color: var(--surface-bg-2);
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-block: 50px;
    border-radius: 5px;
    margin-block: 50px;
  }

  .content-wrapper {
    width: 80%;
  }

  .top-content {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2rem;
  }

  .close-btn {
    position: absolute;
    left: 78%;
    background-color: var(--black-100);
  }

  .close-img {
    width: 31px;
    height: 23px;
  }

  .input-row {
    display: flex;
    flex-direction: column;
    width: 100%;
    justify-content: space-between;
    margin-bottom: 30px;
  }

  .input-field {
    width: 100%;
  }

  .input-field2 {
    width: 100%;
  }

  .subgrid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 40px;
    margin-bottom: 30px;
  }

  .btns-row {
    display: flex;
    justify-content: center;
  }

  .btns-wrapper {
    display: flex;
    width: 100%;
    justify-content: space-between;
  }

  .salary-bar {
    height: 100px;
  }

  @media screen and (min-width: 992px) {
    .input-row {
      flex-direction: row;
    }

    .input-field {
      width: 48%;
    }

    .btns-wrapper {
      width: 48%;
    }
  }
</style>
