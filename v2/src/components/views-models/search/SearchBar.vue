<template>
  <section class="search-bar">
    <div class="field-container1">
      <h5>Emploi</h5>
      <v-text-field
        ref="titleField"
        v-model="formData.title"
        label="Ex: développeur"
        type="text"
        clearable
        :items="optionsFieldEmploi"
        :hide-no-data="true"
        :hide-selected="true"
        id="job-title-field"
        name="job-title"
        @click:clear="handleTitleClear"
        @update:model-value="handleTitleUpdate"
        @keydown.enter="handleSearch"
      />
    </div>

    <div class="field-container2">
      <h5>Lieu</h5>
      <!--LocationInput
        :paramLocation="formData.ville"
        textContent="Saisis une ville"
        @city-and-postal-code="handleCitySelection"
      /-->
      <v-text-field
        ref="villeField"
        v-model="formData.ville"
        label="Ex: Paris"
        type="text"
        clearable
        id="location-field"
        name="location"
        @click:clear="handleVilleClear"
        @update:model-value="handleVilleUpdate"
        @keydown.enter="handleSearch"
      />
    </div>

    <div class="btn-container">
      <v-btn
        color="var(--search-candidate-searchbtn-bg-color)"
        :ripple="true"
        class="btn-magnifier"
        @click="handleSearch"
      >
        <img
          src="@/assets/search/search-page-searchbar-icon.svg"
          alt="magnifier glass"
        />
      </v-btn>
    </div>
  </section>
</template>

<script>
  import LocationInput from '@/components/inputs/LocationInput.vue';

  export default {
    name: 'SearchBar',

    components: {
      LocationInput,
    },

    data() {
      return {
        formData: { title: '', ville: null, postalCode: null },
        optionsFieldEmploi: [
          'Développeur logiciel',
          'Data Scientist',
          'Designer graphique',
          'Chef de projet',
          'Ingénieur en cybersécurité',
          'Architecte de systèmes',
          'Analyste de données',
          'Consultant en stratégie',
          'Chargé de communication',
          'Responsable marketing',
          'Rédacteur technique',
          'Assistant administratif',
          'Comptable',
          'Responsable des ressources humaines',
          'Gestionnaire de produit',
          'Développeur web',
          'Chef de produit digital',
          'Ingénieur en intelligence artificielle',
          'Graphiste',
          'Photographe',
          'Traducteur',
          'Journaliste',
          'Responsable de la relation client',
          'Chef de cuisine',
          'Médecin',
          'Infirmier',
          'Pharmacien',
          'Psychologue',
          'Architecte',
          'Ingénieur civil',
          'Technicien en maintenance',
          'Électricien',
          'Plombier',
          'Charpentier',
          'Maçon',
          'Jardinier paysagiste',
          'Vétérinaire',
          'Professeur',
          'Enseignant',
          'Chercheur',
          'Avocat',
          'Notaire',
          'Agent immobilier',
          'Détective privé',
          'Conseiller financier',
          'Gestionnaire de portefeuille',
          'Auditeur',
          'Responsable de la sécurité',
          'Expert-comptable',
          'Réparateur automobile',
          'Technicien en informatique',
          'Éducateur spécialisé',
          'Moniteur de sport',
          'Animateur socioculturel',
          'Guide touristique',
          'Technicien de laboratoire',
          'Écrivain',
          'Scénariste',
          'Musicien',
          'Comédien',
          'Réalisateur',
          'Producteur',
          'Créateur de contenu',
          'Développeur mobile',
          'Responsable e-commerce',
          'Chef de rayon',
          'Conseiller en orientation',
          'Ingénieur en énergie',
          'Consultant en développement durable',
          'Gestionnaire de projet IT',
          "Coordinateur d'événements",
          'Directeur artistique',
          'Responsable de la production',
          'Ingénieur en télécommunications',
          'Technicien en électromécanique',
          'Gestionnaire de la qualité',
          'Chargé de mission',
          'Responsable de la logistique',
          'Consultant en ressources humaines',
          'Spécialiste en recrutement',
          'Chargé de clientèle',
          'Responsable de la conformité',
          "Développeur d'applications",
          'Conseiller juridique',
          'Technicien en réseaux',
          'Responsable de la stratégie',
          'Directeur de la communication',
          'Analyste financier',
          'Responsable des achats',
          'Chargé de projet immobilier',
          'Responsable des opérations',
          'Ingénieur en mécanique',
          'Consultant en IT',
          'Spécialiste en marketing digital',
          'Responsable de la formation',
          "Responsable de l'innovation",
          'Ingénieur en robotique',
          'Gestionnaire de la production',
          'Expert en assurance',
          'Responsable de la conformité réglementaire',
          'Conseiller en image',
          'Rédacteur web',
          'Gestionnaire de contenu',
          'Responsable des partenariats',
          'Développeur Full Stack',
          'Consultant en transformation numérique',
          'Responsable de la stratégie digitale',
          "Responsable de l'expérience utilisateur",
          'Technicien en sécurité incendie',
          'Spécialiste en gestion des risques',
          'Conseiller en développement personnel',
          "Gestionnaire de l'approvisionnement",
          'Technicien de maintenance industrielle',
          'Responsable de la R&D',
          'Directeur de production',
          'Ingénieur en conception',
          'Analyste en sécurité',
          'Responsable du développement commercial',
          'Spécialiste en réseaux sociaux',
          'Responsable du service après-vente',
        ],
      };
    },

    watch: {
      'formData.title'(newVal, oldVal) {
        // Debug uniquement en développement
        if (process.env.NODE_ENV === 'development') {
          console.log('[DEBUG] formData.title changed:', { oldVal, newVal });
        }
      },
      'formData.ville'(newVal, oldVal) {
        // Debug uniquement en développement
        if (process.env.NODE_ENV === 'development') {
          console.log('[DEBUG] formData.ville changed:', { oldVal, newVal });
        }
      }
    },

    methods: {
      // Nouvelle approche avec @update:model-value (plus fiable avec Vuetify 3)
      handleTitleUpdate(newValue) {
        if (process.env.NODE_ENV === 'development') {
          console.log('[DEBUG] handleTitleUpdate:', {
            newValue,
            currentTitle: this.formData.title,
            currentVille: this.formData.ville,
            timestamp: Date.now()
          });
        }

        // Validation et nettoyage de la valeur
        const cleanValue = (newValue || '').toString();

        // Seulement mettre à jour si la valeur a vraiment changé
        if (this.formData.title !== cleanValue) {
          this.formData.title = cleanValue;
        }
      },

      handleVilleUpdate(newValue) {
        if (process.env.NODE_ENV === 'development') {
          console.log('[DEBUG] handleVilleUpdate:', {
            newValue,
            currentTitle: this.formData.title,
            currentVille: this.formData.ville,
            timestamp: Date.now()
          });
        }

        // Validation et nettoyage de la valeur
        const cleanValue = (newValue || '').toString();

        // Seulement mettre à jour si la valeur a vraiment changé
        if (this.formData.ville !== cleanValue) {
          this.formData.ville = cleanValue;
        }
      },

      // Méthodes de fallback pour compatibilité
      handleTitleInput(value) {
        if (process.env.NODE_ENV === 'development') {
          console.log('[DEBUG] handleTitleInput (fallback):', { value });
        }
        this.handleTitleUpdate(value);
      },

      handleVilleInput(value) {
        if (process.env.NODE_ENV === 'development') {
          console.log('[DEBUG] handleVilleInput (fallback):', { value });
        }
        this.handleVilleUpdate(value);
      },

      handleTitleClear() {
        if (process.env.NODE_ENV === 'development') {
          console.log('[DEBUG] handleTitleClear called');
        }
        this.$nextTick(() => {
          this.formData.title = '';
          this.updateURL({ title: null });
        });
      },

      handleVilleClear() {
        if (process.env.NODE_ENV === 'development') {
          console.log('[DEBUG] handleVilleClear called');
        }
        this.$nextTick(() => {
          this.formData.ville = '';
          this.formData.postalCode = null;
          this.updateURL({ ville: null, cp: null });
        });
      },
      updateURL(newParams) {
        const params = { ...this.$route.query };

        Object.keys(newParams).forEach((key) => {
          if (
            newParams[key] === null ||
            newParams[key] === undefined ||
            newParams[key] === ''
          ) {
            delete params[key];
          } else {
            params[key] = newParams[key];
          }
        });

        this.$router.replace({ query: params }).catch((err) => {
          //console.error("Erreur lors de la mise à jour de l\'URL:", err);
        });
      },
      handleSearch() {
        let query = {};

        // Gérer le champ métier/titre
        if (!this.formData.title || this.formData.title.trim() === '') {
          query.title = null;
        } else {
          query.title = this.formData.title.trim();
        }

        // Gérer le champ ville
        if (!this.formData.ville || this.formData.ville.trim() === '') {
          query.ville = null;
        } else {
          query.ville = this.formData.ville.trim();
        }

        // Gérer le code postal si présent
        if (this.formData.postalCode) {
          query.cp = this.formData.postalCode;
        } else {
          query.cp = null;
        }

        this.updateURL(query);
      },

      handleCitySelection(cityAndPostalCode) {
        if (cityAndPostalCode) {
          this.formData.ville = cityAndPostalCode[0];
          this.formData.postalCode = cityAndPostalCode[1];
        }
        this.updateURL({
          ville: this.formData.ville,
          cp: this.formData.postalCode,
        });
      },
    },
    created() {
      const query = this.$route.query;
      if (query.title) {
        this.formData.title = query.title;
      }
      if (query.ville) {
        this.formData.ville = query.ville;
      }
      if (query.cp) {
        this.formData.postalCode = query.cp;
      }
    },
  };
</script>

<style scoped>
  .search-bar {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 400px;
    justify-content: space-between;
    align-items: center;
  }

  h5 {
    margin-bottom: 10px;
  }

  .field-container1 {
    padding-top: 20px;
    padding-bottom: 0;
    padding-left: 24px;
    padding-right: 24px;
    background-color: var(--search-candidate-searchfield-bg-color);
    width: 100%;
    border-radius: 15px;
  }

  .field-container2 {
    padding-top: 20px;
    padding-bottom: 0;
    padding-left: 24px;
    padding-right: 24px;
    background-color: var(--search-candidate-searchfield-bg-color);
    width: 100%;
    border-radius: 15px;
  }

  .btn-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .btn-magnifier {
    width: 100%;
    height: 75px;
    border-radius: 15px;
  }

  @media screen and (min-width: 992px) {
    .search-bar {
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      height: fit-content;
    }

    .field-container1 {
      width: 45%;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    .field-container2 {
      width: 45%;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }

    .btn-container {
      width: 5%;
      height: 136px;
    }

    .btn-magnifier {
      width: 5%;
      height: 100%;
    }
  }
</style>
