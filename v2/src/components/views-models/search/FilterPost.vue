<!-- Reste filtre Sorting by action À FAIRE -->

<template>
    <div class="filter-component">
      <!-- Wrapper for filters -->
      <div class="filters-wrapper">
        <!-- Sorting by date -->
        <CustomAccordion 
          field="date"
          title="Trier par date" 
          :fields="['Plus récent', 'Plus ancien']" 
          @checkbox-state="sortByDate" />
        
        <!-- Sorting by type -->
        <CustomAccordion 
          field="users"
          title="Trier par utilisateur" 
          :fields="['Tous', 'Recruteurs', 'Candidats']" 
          @checkbox-state="sortByType" />

        <!-- Sorting by action À FAIRE 
        <CustomAccordion 
          v-if="isLoggedIn"
          field="actions"
          title="Mes interactions" 
          :fields="['Tous', 'Mes publications', 'Mes likes', 'Mes commentaires', 'Mes partages']" 
          @checkbox-state="sortByAction" />-->
      </div>
    </div>
  </template>
  
<script>
  import CustomAccordion from '@/components/buttons/CustomAccordion.vue';
  import { mapGetters } from "vuex";
  
  export default {
    name: 'FilterComponent',
    components: {
      CustomAccordion
    },

    props: {
      posts: {
        type: Array,
        required: true
      }
    },

    methods: {
      getSelectedFilter(values) {
        let selectedFilter = '';
          // Parcours l'objet pour trouver la valeur qui est `true`
          for (const [key, value] of Object.entries(values)) {
              //console.log(value)
              if (value === true) {
                  selectedFilter = key;
                  break;
              }
          }

          return selectedFilter;
      },

      sortByDate(e, values) {
        const order = this.getSelectedFilter(values);
        
        // Clone the posts array to avoid mutating the original
        const sortedPosts = [...this.posts];

        if (order === 'Plus récent') {
            sortedPosts.sort((a, b) => new Date(b.date_creation) - new Date(a.date_creation));
        } else if (order === 'Plus ancien') {
            sortedPosts.sort((a, b) => new Date(a.date_creation) - new Date(b.date_creation));
        }

        // Emit the sorted posts back to the parent component
        this.$emit('update-posts', sortedPosts);
      },

      sortByType(e, values) {
        const order = this.getSelectedFilter(values);
        let filteredPosts;

        if (order === 'Recruteurs') {
          filteredPosts = this.posts.filter(post => post.userType === 'recruiter');
        } else if (order === 'Candidats') {
          filteredPosts = this.posts.filter(post => post.userType === 'applicant');
        } else {
          filteredPosts = this.posts; // Retourne tous les posts
        }

        this.$emit('update-posts', filteredPosts);
      },

      // À FAIRE
      //sortByAction(e, values) {
      //  const order = this.getSelectedFilter(values);
      //  let filteredPosts;

      //  if (order === 'Mes publications') {
      //    filteredPosts = this.posts.filter(  );
      //  } else if (order === 'Mes likes') {
      //    filteredPosts = this.posts.filter(  );
      //  } else if (order === 'Mes commentaires') {
      //    filteredPosts = this.posts.filter(  );
      //  } else if (order === 'Mes partages') {
      //    filteredPosts = this.posts.filter(  );
      //  } else {
      //    filteredPosts = this.posts; 
      //  }

      //  this.$emit('update-posts', filteredPosts);
      //},
    },

    computed: {
        // On mappe les getters Vuex vers les computed properties
        ...mapGetters(["isLoggedIn", "getUser"]),
    },

  };
</script>
  
 <style scoped>
  .filters-wrapper {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 15px;
    border-radius: 10px;
  }
</style>
  