<template>

    <section class="container padding-container">


        <div>

            <div class="section-container">

                <!-- container header band -->
                <div class="header-container">
                    <h5>Mes critères de recherche</h5>
                </div>
                
                <!-- container content -->
                <div class="content-container">

                    <div class="cv-container">
                        <h5>Mon CV / Titre</h5>

                        <div class="icons-container">
                            <img src="@/assets/icons/download-icon.svg" alt="icône télécharger" />
                            <img src="@/assets/icons/preview-icon.svg" alt="icône prévisualisation" />
                        </div>
                    </div>

                    <div class="contract-and-remote-options-container">
                        <div class="input-field">
                            <h5>Contrat</h5>
                            <div class="subgrid">
                                <CustomCheckbox 
                                    field="contract" 
                                    :fields="['CDI', 'Alternance', 'Interim', 'CDD', 'Stage', 'Autres']" 
                                    :cValue="formData.contract"
                                    multiple
                                    readonly
                                />
                            </div>
                        </div>
                        <div class="input-field2">
                            <h5>Type de travail</h5>
                            <div class="subgrid">

                            <CustomCheckbox 
                                field="remote" 
                                :fields="['Télétravail complet','Télétravail partiel','Hybride', 'En présentiel']"
                                :cValue="formData.remote"
                                readonly
                            />
                            </div>
                        </div>
                    </div>

                    <div class="experience-container">
                        <div class="input-field">
                            <h5>Expérience</h5>
                            <div class="subgrid">
                                <CustomRadio 
                                    field="experience" 
                                    :fields="['0-1 an', '1-3 ans', '3-5 ans', '>5 ans']" 
                                    :cValue="formData.experience"
                                    readonly
                                />
                            </div>
                        </div>
                    </div>

                    <div class="activity-and-salary-container">
                        <div class="input-field">
                            <h5>Secteur d'activité</h5>
                            <v-select
                                v-model="formData.activitySector"
                                label="Choisis ton secteur d'activité professionnelle"
                                :items="['California', 'Colorado', 'Florida', 'Georgia', 'Texas', 'Wyoming']"
                                bg-color="rgba(246, 179, 55, 0.2)"
                                readonly
                                single-line
                                variant="solo-filled"
                                flat
                            ></v-select>
                        </div>
                        <div class="input-field">
                            <h5>Salaire brut annuel en euros</h5>
                            <v-text-field
                                v-model="formData.salary"
                                label="Saisis le montant de ton salaire idéal ici"
                                type="text"
                                bg-color="rgba(246, 179, 55, 0.2)"
                                readonly
                                single-line
                                variant="solo-filled"
                                flat
                            />   
                        </div>
                    </div>
                    
                </div>

            </div>
        </div>
    </section>

</template>

<script>
import CustomCheckbox from '@/components/buttons/CustomCheckbox.vue';
import CustomRadio from '@/components/buttons/CustomRadio.vue';
import { getUserCriteriasSectionDatas, getCvList } from '@/services/profile.service.js';

export default {
    name: 'MyCriterias',

    props: {
        import: {
            type: Boolean,
            default: false,
        }
    },

    components: {
        CustomCheckbox,
        CustomRadio,
    },

    data() {
        return {
            formData: {},                   //  datas sent
        }
    },

    beforeMount() {
        this.formData = getUserCriteriasSectionDatas(); 
    },

    methods: {
        //  get datas from alert fields and hook them to formData
        getFormDatas(field, datas, state) {
            //console.log(field, datas, state);
            this.formData[field] = datas;
        },

    }
}
</script>

<style scoped>
/* section container & layout */
.container {
    display: flex;
    width: 100%;
    flex-direction: column;
    gap: 0px;
    height: fit-content;
}

.section-container {
    background-color: var(--white-200);
    width: 100%;
    border-radius: 2px;
}

.header-container {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid rgba(246, 179, 55, 0.2);
    cursor: pointer;
}

.bg-color {
    background-color: rgba(246, 179, 55, 0.2);
    border-bottom: none;
}

.content-container {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: fit-content;
    padding: 24px;
}

/* buttons */
.btn-row {
    display: flex;
    justify-content: end;
}

/* inputs */
.input-field {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.input-field2 {
    display: flex;
    flex-direction: column;
    width: 49%;
}

.checkbox-wrapper {
    width: fit-content;
}
/* content */
.cv-container {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.icons-container {
    display: flex;
    gap: 20px; 
}

.icons-container img {
    cursor: pointer;
}

.contract-and-remote-options-container {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.subgrid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 40px;
    margin-bottom: 30px;
}

.experience-container {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.activity-and-salary-container {
    display: flex;
    flex-direction: column;
    width: 100%;
}

@media screen and (min-width: 992px) {
    .input-field {
        width: 49%;
    }

    .input-field2 {
        width: 40%;
    }

    .contract-and-remote-options-container {
        flex-direction: row;
        justify-content: space-between;
    }

    .activity-and-salary-container {
        flex-direction: row;
        justify-content: space-between;
    }
}
</style>
