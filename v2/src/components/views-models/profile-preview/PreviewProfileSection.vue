<template>
  <section class="container padding-container">
    <div class="checkbox-wrapper">
      <input
        type="checkbox"
        :checked="checked"
        @change="handleChange"
        class="custom-checkbox"
        id="custom-checkbox-profil"
      />
      <label class="custom-label" for="custom-checkbox-profil">Importer</label>
    </div>
    <div class="section-container">
      <div class="avatar-title">
        <div class="img-wrapper">
          <img
            v-if="formData.photo || previewImage"
            :src="previewImage || getFullImageUrl()"
            class="avatar-icon"
            alt="avatar image"
          />
          <img
            v-else
            src="@/assets/icons/avatar.png"
            alt="avatar image"
            class="avatar-icon"
          />
        </div>

        <div class="title">
          <h5>{{ formData.firstName }}, {{ formData.lastName }}</h5>
          <p>{{ formData.title }}</p>
        </div>
      </div>
    </div>

    <div>
      <div class="section-container">
        <!-- container header band -->
        <div class="header-container">
          <h5>Mon profil candidat</h5>
        </div>

        <!-- container content -->
        <div class="content-container">
          <div class="contact">
            <div class="input">
              <h5>E-mail</h5>
              <v-text-field
                v-model="formData.mail"
                label="<EMAIL>"
                type="text"
                bg-color="rgba(246, 179, 55, 0.2)"
                readonly
                single-line
                variant="solo-filled"
                flat
              />
            </div>

            <div class="input">
              <h5>Téléphone</h5>
              <v-text-field
                v-model="formData.phone"
                label="0X XX XX XX XX"
                type="text"
                bg-color="rgba(246, 179, 55, 0.2)"
                readonly
                single-line
                variant="solo-filled"
                flat
              />
            </div>
          </div>

          <div class="location">
            <h5>Localisation</h5>
            <div class="location-wrapper">
              <LocationInput :textContent="formData.city" readOnly />
              
            </div>
          </div>

          <div class="aboutme">
            <h5>A propos de moi</h5>
            <v-text-field
              v-model="formData.aboutme"
              label="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."
              type="text"
              bg-color="rgba(246, 179, 55, 0.2)"
              readonly
              single-line
              variant="solo-filled"
              flat
            />
          </div>

          <div class="websites">
            <div class="input">
              <h5>Site internet</h5>
              <v-text-field
                v-model="formData.website"
                label="Saisi l'url de ton site ici"
                type="text"
                bg-color="rgba(246, 179, 55, 0.2)"
                readonly
                single-line
                variant="solo-filled"
                flat
              />
            </div>

            <div class="input">
              <h5>Compte linkedin</h5>
              <v-text-field
                v-model="formData.linkedin"
                label="Saisi l'url de ton site ici"
                type="text"
                bg-color="rgba(246, 179, 55, 0.2)"
                readonly
                single-line
                variant="solo-filled"
                flat
              />
            </div>
          </div>

          <div class="websites2">
            <div class="input">
              <h5>Portfolio</h5>
              <v-text-field
                v-model="formData.portfolio"
                label="Saisis l'url de ton site ici"
                type="text"
                bg-color="rgba(246, 179, 55, 0.2)"
                readonly
                single-line
                variant="solo-filled"
                flat
              />
            </div>

            <div class="input">
              <h5>Autre site</h5>
              <v-text-field
                v-model="formData.otherwebsite"
                label="Saisis l'url de ton site ici"
                type="text"
                bg-color="rgba(246, 179, 55, 0.2)"
                readonly
                single-line
                variant="solo-filled"
                flat
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  import CustomCheckbox from '@/components/buttons/CustomCheckbox.vue';
  import LocationInput from '@/components/inputs/LocationInput.vue';
  import { baseUrl } from '../../../services/axios';

  export default {
    name: 'PreviewProfileSections',

    props: {
      import: {
        type: Boolean,
        default: false,
      },
      checked: {
        type: Boolean,
        default: false,
      },
    },

    components: {
      LocationInput,
      CustomCheckbox,
    },

    data() {
      return {
        formData: {}, // datas taken
        previewImage: '', // string for the user photo
        isCheckedProfile: false,
        user: this.$store.getters.getUser,
      };
    },

    mounted() {
      this.formData = {
        metier: this.user.photo,
        firstName: this.user.first_name,
        lastName: this.user.last_name,
        title: this.user.title,
        mail: this.user.email,
        phone: this.user.numberPhone,
        city: this.user.ville,
        aboutme: this.user.about,
        website: this.user.site_url,
        linkedin: this.user.linkedin,
        portfolio: this.user.porfolio_url,
        otherWebsite: this.user.autre_url,
      };
    },

    methods: {
      handleChange(event) {
        this.$emit('updateChecked', event.target.checked);
        this.getFormDatas();
      },

      // get photo url
      getFullImageUrl() {
        if (this.formData.photo) {
          if (this.formData.photo.name) {
            if (typeof this.formData.photo.name === 'string') {
              return URL.createObjectURL(this.formData.photo);
            }
          }

          return baseUrl + this.formData.photo;
        } else if (this.previewImage) {
          return this.previewImage;
        } else {
          return '';
        }
      },

      // get datas from alert fields and hook them to formData
      getFormDatas(field, datas, state) {
        //console.log(field, datas, state);
        this.formData[field] = datas;
      },
    },
  };
</script>

<style scoped>
  /* layout */
  .container {
    display: flex;
    width: 100%;
    flex-direction: column;
    gap: 20px;
    height: fit-content;
  }

  .section-container {
    background-color: var(--white-200);
    width: 100%;
    border-radius: 5px;
    padding: 16px;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid rgba(246, 179, 55, 0.2);
    cursor: pointer;
  }

  .content-container {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: fit-content;
    padding: 24px;
  }

  .avatar-icon {
    width: auto;
    height: 92px;
    cursor: pointer;
  }

  .btn-row {
    display: flex;
    justify-content: end;
  }

  /* input content */
  .checkbox-wrapper {
    width: fit-content;
  }

  .input {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  /* content */
  .avatar-title {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
    padding: 16px;
  }

  .img-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .title {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
    align-items: center;
    justify-content: center;
  }

  .contact {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 20px;
  }

  .location {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .aboutme {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .websites {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 20px;
  }

  .websites2 {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 20px;
  }
  .custom-checkbox {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
  }

  label {
    font-weight: 300;
  }
  .custom-label {
    position: relative;
    cursor: pointer;
    font-size: 18px;
    padding-left: 40px;
  }

  .custom-label::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 25px;
    height: 25px;
    background-color: #fff;
    border: 2px solid rgba(246, 179, 55, 1);
    border-radius: 5px;
  }

  .custom-checkbox:checked + .custom-label::before {
    background-color: rgba(246, 179, 55, 1);
  }

  .custom-checkbox:checked + .custom-label::after {
    content: '';
    position: absolute;
    left: 8px;
    top: 2px;
    width: 10px;
    height: 16px;
    border: solid black;
    border-width: 0px 3px 2px 0;
    transform: rotate(45deg);
  }
  .checkbox-wrapper {
    width: fit-content;
    margin-top: 20px;
    display: flex;
    align-content: center;
  }

  @media screen and (min-width: 992px) {
    .avatar-title {
      flex-direction: row;
    }

    .contact {
      flex-direction: row;
      gap: 40px;
    }

    .location-wrapper {
      width: 50%;
    }

    .websites {
      flex-direction: row;
      gap: 40px;
    }

    .websites2 {
      flex-direction: row;
      gap: 40px;
    }
  }
</style>
