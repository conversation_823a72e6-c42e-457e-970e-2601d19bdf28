<template>
  <section class="container padding-container">
    <div class="checkbox-wrapper">
      <input
        type="checkbox"
        :checked="checked"
        @change="handleChange"
        class="custom-checkbox"
        id="custom-checkbox-skills"
      />
      <label class="custom-label" for="custom-checkbox-skills">Importer</label>
    </div>
    <div class="section-container">
      <!-- container header band -->
      <div class="header-container">
        <h5>Mes compétences</h5>
      </div>

      <!-- container content -->
      <div class="content-container">
        <!-- skills -->
        <div class="skills">
          <!-- 
                    <div class="checkbox-wrapper">
                        <CustomCheckbox field="importAll" :fields="['Tout importer']" @checkbox-selection="getFormDatas" :underline="false" multiple/>
                    </div> -->

          <h5>Skills</h5>

          <div class="packet-chips">
            <div
              v-for="(skill, index) in skillList"
              :key="index"
              class="chip-wrapper"
            >
              <Chip :textContent="skill.nom" />
            </div>
          </div>
        </div>

        <!-- langage -->
        <div class="langage">
          <!-- 
                    <div class="checkbox-wrapper">
                        <CustomCheckbox field="importAll" :fields="['Tout importer']" @checkbox-selection="getFormDatas" :underline="false" multiple/>
                    </div> -->

          <h5>Langues</h5>

          <div class="packet-chips">
            <div
              v-for="(lang, index) in langList"
              :key="index"
              class="chip-wrapper"
            >
              <Chip :textContent="lang.langue" />
            </div>
          </div>
        </div>

        <!-- permit and mobility -->
        <div class="permit-mobility">
          <div class="input-field">
            <!-- 
                        <div class="checkbox-wrapper">
                            <CustomCheckbox field="importAll" :fields="['Tout importer']" @checkbox-selection="getFormDatas" :underline="false" multiple/>
                        </div> -->

            <h5>Permis</h5>

            <div class="packet-chips">
              <div
                v-for="(permit, index) in permitList"
                :key="index"
                class="chip-wrapper"
              >
                <Chip :textContent="permit" />
              </div>
            </div>
          </div>

          <div class="input-field">
            <!-- 
                        <div class="checkbox-wrapper">
                            <CustomCheckbox field="importAll" :fields="['Tout importer']" @checkbox-selection="getFormDatas" :underline="false" multiple/>
                        </div> -->

            <h5>Mobilité</h5>

            <div class="packet-chips">
              <div
                v-for="(mobility, index) in mobilityList"
                :key="index"
                class="chip-wrapper"
              >
                <Chip :textContent="mobility" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  import CustomCheckbox from '@/components/buttons/CustomCheckbox.vue';
  import Chip from '@/components/chips/Chip.vue';
  import {
    getAllLanguages,
    getUserSkillsSectionDatas,
  } from '@/services/profile.service';

  export default {
    name: 'PreviewSkillsSection',

    props: {
      import: {
        type: Boolean,
        default: false,
      },

      checked: {
        type: Boolean,
        default: false,
      },
    },

    components: {
      Chip,
      CustomCheckbox,
    },

    data() {
      return {
        formData: {}, //  datas of user
        skillList: [], //  list of skills
        softSkillList: [], //  list of soft skills
        langList: [], //  list of langages
        permitList: [], //  list of permit
        mobilityList: [], //  list of mobility options
        isCheckedSkills: false, //  check if all skills are selected
      };
    },

    async mounted() {
      this.formData = await getUserSkillsSectionDatas();

      this.permitList = this.formData['permisList'];

      this.skillList = this.formData['skillsList'];

      this.mobilityList = this.formData['mobilityList'];

      try {
        this.langList = await getAllLanguages();
      } catch (error) {
        //console.error('Erreur lors de la récupération des langues:', error);
      }
    },

    methods: {
      handleChange(event) {
        this.$emit('updateChecked', event.target.checked);
      },
      //  get datas from alert fields and hook them to formData
      getFormDatas(field, datas, state) {
        this.formData[field] = datas;
        this.isCheckedSkills = state;
      },
    },
  };
</script>

<style scoped>
  /* layout */
  .container {
    display: flex;
    width: 100%;
    flex-direction: column;
    gap: 0px;
    height: fit-content;
  }

  .section-container {
    background-color: var(--white-200);
    width: 100%;
    border-radius: 2px;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid rgba(246, 179, 55, 0.2);
    cursor: pointer;
  }

  .bg-color {
    background-color: rgba(246, 179, 55, 0.2);
    border-bottom: none;
  }

  .content-container {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: fit-content;
    padding: 24px;
  }

  /* buttons */
  .btn-row {
    display: flex;
    justify-content: end;
  }

  /* inputs */
  .input-field,
  .input-field2 {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .checkbox-wrapper {
    width: fit-content;
  }

  /* content */
  .informations {
    border-radius: 5px;
    padding-inline: 8px;
    padding-block: 18px;
    background-color: rgba(88, 160, 150, 0.2);
  }

  .packet-chips {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .skills {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 10px;
  }

  .soft-skills {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 10px;
  }

  .langage {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 10px;
  }

  .permit-mobility {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 24px;
  }
  .custom-checkbox {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
  }

  .custom-label {
    position: relative;
    cursor: pointer;
    font-size: 18px;
    padding-left: 40px;
  }

  .custom-label::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 25px;
    height: 25px;
    background-color: #fff;
    border: 2px solid rgba(246, 179, 55, 1);
    border-radius: 5px;
  }

  .custom-checkbox:checked + .custom-label::before {
    background-color: rgba(246, 179, 55, 1);
  }

  .custom-checkbox:checked + .custom-label::after {
    content: '';
    position: absolute;
    left: 8px;
    top: 2px;
    width: 10px;
    height: 16px;
    border: solid black;
    border-width: 0px 3px 2px 0;
    transform: rotate(45deg);
  }
  .checkbox-wrapper {
    width: fit-content;
    margin-top: 20px;
    display: flex;
    align-content: center;
  }
  label {
    font-weight: 300;
  }

  @media screen and (min-width: 992px) {
    .input-field {
      width: 49%;
    }

    .input-field2 {
      width: 40%;
    }

    .permit-mobility {
      flex-direction: row;
      justify-content: space-between;
      gap: 0px;
    }
  }
</style>
