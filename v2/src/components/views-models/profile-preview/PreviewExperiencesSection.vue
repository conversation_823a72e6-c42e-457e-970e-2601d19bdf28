<template>

    <section class="container padding-container">
           
          
        <div class="checkbox-wrapper">
                    <input
                        type="checkbox"
                        :checked="checked"
                        @change="handleChange"
                        class="custom-checkbox"
                        id="custom-checkbox-experience"
                    />
                    <label class="custom-label" for="custom-checkbox-experience">Importer</label>
                </div>

        <div class="section-container">

            <!-- container header band -->
            <div class="header-container">
                <h5>Mes expériences</h5>
            </div>
            
            <!-- container content -->
            <div class="content-container">

                <!-- list of all experience -->
                <div v-for="(experience, index) in experienceList" class="formations-container">
                    
                    <!-- <div class="checkbox-wrapper">
                        <CustomCheckbox field="importAll" :fields="['Tout importer']" @checkbox-selection="getFormDatas" :underline="false" multiple/>
                    </div> -->

                    <div class="title-date">
                        <div class="input-field">
                            <h5>Titre du poste</h5>
                            <v-text-field
                                v-model="experience['position']"
                                :label="experience['position']"
                                type="text"
                                bg-color="rgba(246, 179, 55, 0.2)"
                                readonly
                                single-line
                                variant="solo-filled"
                                flat
                            />
                        </div>
                        <div class="input-field">
                            <h5>Dates</h5>

                            <div class="date-field">
                                <label for="date-debut">De</label>
                                <input
                                    type="date"
                                    v-model="experience['debut']"
                                    class="date-input-field"
                                    disabled
                                />

                                <label for="date-fin">à</label>
                                <input
                                    type="date"
                                    v-model="experience['fin']"
                                    class="date-input-field"
                                    disabled
                                />
                            </div>

                        </div>
                    </div>

                    <div class="company-location">
                        <div class="input-field">
                            <h5>Entreprise</h5>
                            <v-text-field
                                v-model="experience['company']"
                                :label="experience['company']"
                                type="text"
                                bg-color="rgba(246, 179, 55, 0.2)"
                                readonly
                                single-line
                                variant="solo-filled"
                                flat
                            />
                        </div>
                        <div class="input-field">
                            <h5>Lieu</h5>
                            <v-text-field
                                v-model="experience['place']"
                                :label="experience['place']"
                                type="text"
                                bg-color="rgba(246, 179, 55, 0.2)"
                                readonly
                                single-line
                                variant="solo-filled"
                                flat
                            />
                        </div>
                    </div>

                    <div>
                        <h5>Détails</h5>
                        <v-text-field
                            v-model="experience['exp_detail']"
                            :label="experience['exp_detail']"
                            type="text"
                            bg-color="rgba(246, 179, 55, 0.2)"
                            readonly
                            single-line
                            variant="solo-filled"
                            flat
                        />
                    </div>

                </div>  

            </div>

        </div>

    </section>

</template>

<script>
import { getExperienceList } from '@/services/profile.service.js';
import CustomCheckbox from '@/components/buttons/CustomCheckbox.vue';

export default {
    name: 'PreviewExperiencesSection',

    props: {
        import: {
            type: Boolean,
            default: false,
        },
        checked: {
      type: Boolean,
      default: false,
    },
    
    components: {
        CustomCheckbox,
    },

    data() {
        return {
            experienceList: [],                 //  list of experience
            FormData: {},                       //  datas sent
            isCheckedExperiences: false,        //  checked fields
        }
    },

    mounted() {
        //  bind experience list and display it on user interface
        this.experienceList = getExperienceList();
    },

    methods: {
        handleChange(event) {
      this.$emit('updateChecked', event.target.checked);
    },
        //  get datas from alert fields and hook them to formData
        getFormDatas(field, datas, state) {
            //console.log(field, datas, state);
            this.FormData[field] = datas;
            this.isCheckedExperiences = state;
        },
    }
}
}
</script>

<style scoped>
/* layout */
.container {
    display: flex;
    width: 100%;
    flex-direction: column;
    gap: 0px;
    height: fit-content;
}

.section-container {
    background-color: var(--white-200);
    width: 100%;
    border-radius: 2px;
}

.header-container {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid rgba(246, 179, 55, 0.2);
    cursor: pointer;
}

.bg-color {
    background-color: rgba(246, 179, 55, 0.2);
    border-bottom: none;
}

.content-container {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: fit-content;
    padding: 24px;
}

/* buttons */
.btn-row {
    display: flex;
    justify-content: end;
}

.custom-btn {
    border-radius: 5px;
    border: 1px solid rgba(246, 179, 55, 1);
}

/* inputs */
.input-field, .input-field2 {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.date-input-field {
    width: fit-content;
    background-color: rgba(246, 179, 55, 0.2);
    min-height: 56px;
    padding: 16px;
    border-radius: 2px;
}

.checkbox-wrapper {
    width: fit-content;
}

/* content */
.formations-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding-top: 30px;
    border-top: 1px solid rgba(246, 179, 55, 0.2);
    border-bottom: 1px solid rgba(246, 179, 55, 0.2);
}

.title-date {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 10px;
}

.company-location {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 10px;
}

.date-field {
    display: flex;
    align-items: end;
    gap: 10px;
}

.date-field label {
    align-self: center;
}

.delete-btn {
    display: flex;
    justify-content: end;
}
.custom-checkbox {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;

}


.custom-label {
    position: relative;
    cursor: pointer;
    font-size: 18px;
    padding-left: 40px; 
}

.custom-label::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 25px;
    height: 25px;
    background-color: #fff;
    border: 2px solid rgba(246, 179, 55, 1);
    border-radius: 5px;
}


.custom-checkbox:checked + .custom-label::before {
    background-color: rgba(246, 179, 55, 1);
}

.custom-checkbox:checked + .custom-label::after {
    content: '';
    position: absolute;
    left: 8px;
    top: 2px;
    width: 10px;
    height: 16px;
    border: solid black;
    border-width: 0px 3px 2px 0;
    transform: rotate(45deg)
}
.checkbox-wrapper {
    width: fit-content;
    margin-top: 20px;
    display: flex;
    align-content: center;
}
label{
    font-weight: 300;
}
@media screen and (min-width: 992px) {
    .input-field {
        width: 49%;
    }

    .input-field2 {
        width: 40%;
    }

    .title-date {
        flex-direction: row;
        justify-content: space-between;
        gap: 0px;
    }

    .company-location {
        flex-direction: row;
        justify-content: space-between;
        gap: 10px;
    }

    /*.date-field {
        padding-top: 10px;
    }*/

}
</style>
