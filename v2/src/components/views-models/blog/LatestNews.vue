<template>
  <section class="container">
    <div class="content-wrapper">
      <h2>Dernières actualités</h2>
      <h5 v-if="blog">
        À la Une, vous trouverez toutes nos nouveautés et conseils
      </h5>

      <div class="articles-container">
        <div class="card-wrapper" v-if="news.length === 0">
          <p>Aucun article à l'affiche</p>
        </div>
                <!-- Slider Content -->
          <div class="slider">
            <div
              v-for="(article, index) in news"
              :key="index"
              v-show="currentSlide === index"
                :class="['slide', { 'active-slide': currentSlide === index }]"
            >
            <NewsCard
            v-if="article"
            :article="article"
            textContent="Lire la suite"
            @card-btn-click="
              gotoPage(
                `/actualite/${article.categorie}/${slugify(article.theme)}/${slugify(article.titre)}/${article.id}`
              )
            "
          />
            </div>
          </div>
          <div class="slider-container">
          <!-- Dots Navigation -->
          <div class="dots-container">
            <span
              v-for="(slide, index) in news"
              :key="'dot-' + index"
              :class="['dot', { active: currentSlide === index }]"
              @click="goToSlide(index)"
            ></span>
          </div>

          <!-- Buttons -->
          <div class="buttons-container">
            <button @click="prevSlide">
                <i class="fas fa-chevron-left"></i>
                <div>a</div>
                <div>a</div>
            </button>
            <button @click="nextSlide">
              <div>a</div>
              <div>a</div>
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
        </div>
        <!-- <div
          class="card-wrapper"
          v-else
          v-for="(article, index) in news"
          :key="index"
        >
        
          <NewsCard
            v-if="article"
            :article="article"
            textContent="Lire la suite"
            @card-btn-click="
              gotoPage(
                `/actualite/${article.categorie}/${slugify(article.theme)}/${slugify(article.titre)}/${article.id}`
              )
            "
          />
        </div> -->
      </div>
    </div>
  </section>
</template>

<script setup>
  import gotoPage from '@/utils/router';
</script>

<script>
  import NewsCard from '@/components/cards/NewsCard.vue';
  import { getLastTwoArticles } from '@/services/blog.service';
  import { slugify } from '../../../utils/slugify';

  export default {
    name: 'LatestNews',

    props: {
      blog: {
        type: Boolean,
        default: false,
      },
    },

    components: {
      NewsCard,
    },
    data() {
      return {
        news: [],
        currentSlide: 0,
      };
    },
    methods: {
    prevSlide() {
      this.currentSlide = (this.currentSlide - 1 + this.news.length) % this.news.length;
    },
    nextSlide() {
      this.currentSlide = (this.currentSlide + 1) % this.news.length;
    },
    goToSlide(index) {
      this.currentSlide = index;
    }
    },

    async mounted() {
      const articles = await getLastTwoArticles();
      this.news = articles || [];
    },
  };
</script>

<style scoped>
  .container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 40px;
  }
  .content-wrapper {
    width: 100%;
    max-width: 1080px;
    display: flex;
    flex-direction: column;
    align-items: start;
  }

  .articles-container {
    margin-top: 40px;
    display: flex;
    flex-direction: column;
    width: 100%;
    align-items: center;
  }

  .card-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .card-wrapper p {
    text-align: center;
  }

  .slider {
    position: relative;
    overflow: hidden;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 340px; /* hauteur minimale pour éviter les sauts */
    transition: transform 1s ease-in-out;
    background: #fff;
    box-sizing: border-box;
  }

  .slide {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    transition: opacity 1s ease-in-out;
    min-height: 320px;
    box-sizing: border-box;
  }

  .slide.active-slide {
    display: flex;
    opacity: 1;
  }

  /* Correction du NewsCard pour forcer l'image à être bien cadrée et non coupée */
  .slide :deep(.news-card-image),
  .slide img {
    max-width: 340px;
    max-height: 220px;
    width: 100%;
    height: 220px;
    object-fit: contain; /* contain pour éviter de couper l'image */
    border-radius: 12px;
    margin: 0 auto 16px auto;
    display: block;
    background: #f6f6f6;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    padding: 8px;
  }

  /* Amélioration de la zone de navigation du slider */
  .slider-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 10px;
  }

  .dots-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin: 0;
    padding: 0;
  }

  .dot {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #bbb;
    cursor: pointer;
    transition: background 0.2s;
  }

  .dot.active {
    background-color: #f6b337;
    box-shadow: 0 0 0 2px #f6b33733;
  }

  .buttons-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    margin: 0;
    margin-bottom:15px;
  }
  .buttons-container button { 
    background-color: var(--surface-bg-2);
    color: white;
    padding: 10px 18px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
    min-width: 44px;
    min-height: 44px;
  }
  .buttons-container button:hover{ 
    background-color: rgb(224, 216, 216);
  }
  .buttons-container button i {
    color: grey;
    font-size: 1.3em;
  }
  .buttons-container button div{
    display: none;
  }

  /* Responsive: ajustement pour mobile */
  @media screen and (max-width: 767px) {
    .slide :deep(.news-card-image),
    .slide img {
      max-width: 100%;
      height: 140px;
      padding: 4px;
    }
    .slider-container {
      gap: 6px;
    }
    .dot {
      width: 9px;
      height: 9px;
    }
    .buttons-container button {
      padding: 8px 12px;
      min-width: 36px;
      min-height: 36px;
    }
  }

  /* ✅ TABLETTE : iPad, iPad Pro, etc. */
  @media screen and (min-width: 768px) and (max-width: 991px) {
    .content-wrapper {
      margin: 0 3vw;
    }
    .articles-container {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      align-items: center;
      height: fit-content;
      gap: 40px;
    } 
}




  /* ✅ MOBILE : petits smartphones (iPhone SE, etc.) */
  @media screen and (max-width: 480px) {
    .content-wrapper {
      margin: 0 3vw;
    }
    .articles-container {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      align-items: center;
      height: fit-content;
      gap: 40px;
    }
  }

  /* ✅ MOBILE LARGE : smartphones standards (iPhone 12, Galaxy S21, etc.) */
  @media screen and (min-width: 481px) and (max-width: 767px) {
    .content-wrapper {
      margin: 0 3vw;
    }
    .articles-container {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      align-items: center;
      height: fit-content;
      gap: 40px;
    }
  }

  /* @media screen and (min-width: 992px) {
    .container {
      gap: 60px;
    }

    .articles-container {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 34px;
      height: fit-content;
    }
  } */

  /* Amélioration du slider pour les appareils mobiles */
  @media screen and (max-width: 767px) {
    .slider {
      height: auto;
    }
    
    .slide {
      flex-direction: column;
    }
    
    .buttons-container button {
      padding: 8px 15px;
    }
    
    .dot {
      width: 8px;
      height: 8px;
      margin: 0 3px;
    }
  }

  /* Ajustements pour les écrans larges */
  @media screen and (min-width: 992px) {
    .slider-container {
      width: 100%;
      display: flex;
      justify-content: space-between;
      padding: 0 10px;
    }
    
    .buttons-container {
      margin-left: auto;
    }
  }

  /* Ajustements pour les très grands écrans */
  @media screen and (min-width: 1440px) {
    .content-wrapper {
      max-width: 1200px;
    }
    
    .slider {
      max-width: 90%;
      margin: 0 auto;
    }
  }
</style>
