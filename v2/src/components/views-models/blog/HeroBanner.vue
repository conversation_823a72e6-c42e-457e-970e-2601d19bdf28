<template>
  <section class="hero-banner">
    <div class="hero-content">
      <h1 class="title">La vitrine actu pour booster votre carrière</h1>

      <div class="content-wrapper">
        <div class="text-container">
          <p class="text">
            Découvrez des conseils d'experts, des stratégies éprouvées et des
            ressources innovantes pour vous épanouir et atteindre vos objectifs
            professionnels. Avec "La vitrine IA", prenez le contrôle de votre
            carrière. Explorez nos articles pour trouver votre voie, celle qui
            correspond réellement à vos attentes et à vos aspirations. Nous vous
            aidons à construire une vie professionnelle plus épanouissante et
            alignée avec vos ambitions.
          </p>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  export default {
    name: 'HeroSection',
  };
</script>

<style scoped>
  .hero-banner {
    background-image: url('/src/assets/background/HeroActualite.svg');
    background-size: cover;
    width: 100vw;
    min-height: 400px;
    display: flex;
    justify-content: center;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
  }

  .hero-content {
    width: 100%;
    max-width: 1440px;
    margin: 0 8%;
  }

  .title {
    color: var(--surface-bg-2);
    text-align: left;
    text-wrap: balance;
    margin-top: 20px;
  }

  .content-wrapper {
    display: flex;
    justify-content: center;
    max-width: 1000px;
    margin: 0 auto;
  }

  .text-container {
    background-color: rgba(38, 40, 43, 0.85);
    border-radius: 20px;
    padding: 16px;
    width: 100%;
    margin-top: 35px;
  }

  .text {
    color: var(--surface-bg-2);
    font-size: 14px;
  }

  /* ✅ MOBILE */
  @media screen and (max-width: 480px) and (max-width: 767px) {
    .hero-content {
      display: flex;
      flex-direction: column;
      gap: 20px;
      margin: 3% 8%;
    }
  }
</style>
