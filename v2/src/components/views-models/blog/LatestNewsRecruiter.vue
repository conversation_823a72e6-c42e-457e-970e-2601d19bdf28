<template>
  <section class="container">
    <div class="container-content">
      <h2 class="Title">Dernières actualités</h2>
      <h5 v-if="blog">
        À la Une, vous trouverez toutes nos nouveautés et conseils
      </h5>

      <div class="articles-container">
        <div class="card-wrapper" v-if="!news">
          <p>Aucun article à l'affiche</p>
        </div>
        <div class="card-wrapper" v-else v-for="(article, index) in news">
          <NewsCard
            v-if="index <= 1"
            :article="article"
            textContent="Lire la suite"
            @card-btn-click="
              gotoPage(
                `/actualite/${article.categorie}/${slugify(article.theme)}/${slugify(article.titre)}/${article.id}`
              )
            "
          />
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
  import gotoPage from '@/utils/router';
</script>

<script>
  import NewsCard from '@/components/cards/NewsCard.vue';
  import { getLastTwoArticles } from '@/services/blog.service';
  import { slugify } from '../../../utils/slugify';

  export default {
    name: 'LatestNews',

    props: {
      blog: {
        type: Boolean,
        default: false,
      },
    },

    components: {
      NewsCard,
    },

    data() {
      return {
        news: [],
      };
    },

    async mounted() {
      this.news = await getLastTwoArticles();
    },
  };
</script>

<style scoped>
  .container {
    width: 100vw;
    background-color: var(--text-1);
    display: flex;
    justify-content: center;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    padding: 3% 0;
  }

  .container-content {
    width: 100%;
    max-width: 1440px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 8%;
  }

  .Title {
    color: var(--surface-bg);
    background-color: var(--background-color);
    margin-bottom: 3%;
  }

  .Title.inverted {
    color: var(--text-inverted);
    background-color: var(--background-color-black);
  }

  .articles-container {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    height: fit-content;
    gap: 40px;
  }

  .card-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
  }

  /* ✅ MOBILE : petits smartphones (iPhone SE, etc.) */
  @media screen and (max-width: 480px) and (max-width: 767px) {
    .articles-container {
      flex-direction: column;
    }
  }
</style>
