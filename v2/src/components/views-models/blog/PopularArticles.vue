<template>
  <section class="popular-articles">
    <h2>{{ computedTitle }}</h2>
    <h3>{{ computedSubtitle }}</h3>
    <div class="container-content">
      <div class="content-wrapper">
        <!-- <div class="articles-container">
          <div class="card-wrapper" v-for="article in news" :key="article.id">
            <BlockNewsCard
              v-if="article"
              :article="article"
              textContent="Lire la suite"
              
              @card-btn-click="
                gotoPage(
                  `/actualite/${article.categorie}/${slugify(article.theme)}/${slugify(article.titre)}/${article.id}`
                )
              "
            />
          </div>
        </div> -->
      </div>
    </div>
  </section>
  <div class="swiper">
          <div class="swiper-wrapper">
            <div class="swiper-slide"  v-for="article in news" :key="article.id">
            <BlockNewsCard
              v-if="article"
              :article="article"
              textContent="Lire la suite"
              
              @card-btn-click="
                gotoPage(
                  `/actualite/${article.categorie}/${slugify(article.theme)}/${slugify(article.titre)}/${article.id}`
                )
              "
            />
          </div>
          <div class="swiper-slide"  v-for="article in news" :key="article.id">
            <BlockNewsCard
              v-if="article"
              :article="article"
              textContent="Lire la suite"
              
              @card-btn-click="
                gotoPage(
                  `/actualite/${article.categorie}/${slugify(article.theme)}/${slugify(article.titre)}/${article.id}`
                )
              "
            />
          </div>
          </div>

          <div class="swiper-button-prev"></div>
          <div class="swiper-button-next"></div>

        </div>
</template>

<script setup>
  import gotoPage from '@/utils/router';
</script>

<script>
  import BlockNewsCard from '@/components/cards/BlockNewsCard.vue';
  import { getMostPopularArticles } from '@/services/blog.service';
  import { slugify } from '../../../utils/slugify';
  import Swiper from 'swiper/bundle';
  import 'swiper/css/bundle';

  export default {
    name: 'PopularArticles',
    props: {
      title: {
        type: String,
        default: 'Articles populaires'
      },
      subtitle: {
        type: String,
        default: 'Les articles les plus lus'
      }
    },

    computed: {
      computedTitle() {
        return this.title;
      },
      computedSubtitle() {
        return this.subtitle;
      }
    },

    components: {
      BlockNewsCard,
    },

    data() {
      return {
        news: [],
      };
    },

    async mounted() {
      this.news = await getMostPopularArticles();
      this.$nextTick(() => {
      new Swiper('.swiper', {
        loop: true,
        spaceBetween: 20,
        slidesPerView: 3,
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
        },
        breakpoints:{
        600: {
          slidesPerView: 2,
        },
        968: {
          slidesPerView: 3,
        },
      },
      });
    });
    },
  };
</script>

<style scoped>

  .container {
    display: flex;
    justify-content: center;
    padding: 20px 0 20px 0;
  }

  .container-content {
    width: 100%;
    max-width: 1440px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .content-wrapper {
    display: flex;
    flex-direction: column;
    align-items: start;
  }

  .articles-container {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    height: fit-content;
    gap: 50px;
  }
  
  .card-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  
  .swiper{
    height: fit-content;
    width: 100%;
    padding: 20px 10px;
  }
  /* .swiper-button-prev:after,
.swiper-button-next:after {
  content: "";
} */
  .swiper-button-prev::after,
  .swiper-button-next::after {
    width: initial;
    height: initial;
    position: absolute;
    font-size: 14px;
    font-weight: bold;
    color: grey;
    background-color: var(--surface-bg-2);
    padding: 15px 15px;
    border-radius: 5px;
  }

  .swiper-button-prev{
    left: 0;
  }

  .swiper-button-next {
    right: 0;
  }
  .swiper-pagination-bullet {
  background-color: hsl(212, 32%, 40%);
  opacity: 0;
}

.swiper-pagination-bullet-active {
  background-color: var(--second-color);
}

  @media screen and (min-width: 992px) {
    .articles-container {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      height: fit-content;
      gap: 24px;
    }

    .card-wrapper {
      min-width: 350px;
    }
  }
</style>
