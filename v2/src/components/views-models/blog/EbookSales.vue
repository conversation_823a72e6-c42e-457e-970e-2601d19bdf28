<template>

    <section class="padding-container container">
        <h2>V<PERSON>z découvrir notre E-book</h2>

        <p>Lorem ipsum dolor sit amet consectetur. Urna diam tincidunt vitae lectus nulla ullamcorper sed. Lorem ipsum dolor sit amet consectetur. Turpis lobortis fusce dignissim aenean habitasse cursus at. Tortor nulla a morbi elementum. Dictum aliquet tristique et orci non sollicitudin mi facilisis scelerisque.</p>

        <div class="content-container-1">

            <v-form @submit.prevent :disabled="submitted" ref="ebookForm" class="form-container">

                <div class="row1">
                    <div class="field1">
                        <h5>Nom</h5>
                        <v-text-field
                            v-model="formData.nom"
                            :rules="[...nameRules, ...notEmptyRules]"
                            :disabled="submitted"
                            label="Votre nom"
                            variant="solo"
                            flat
                        ></v-text-field>
                    </div>
                                    
                    <div class="field1">
                        <h5>Prénom</h5>
                        <v-text-field
                            v-model="formData.prenom"
                            :rules="[...nameRules, ...notEmptyRules]"
                            :disabled="submitted"
                            label="Votre prénom"
                            variant="solo"
                            flat
                        ></v-text-field>
                    </div>
                </div>

                <div class="row1">
                    <div class="field1">
                        <h5>Email</h5>
                        <v-text-field
                            v-model="formData.email"
                            :rules="[...emailRules , ...notEmptyRules]"
                            :disabled="submitted"
                            label="Votre email"
                            variant="solo"
                            flat
                        ></v-text-field>
                    </div>

                    <div class="field1">
                        <h5>Téléphone</h5>
                        <v-text-field
                            v-model="formData.mobile"
                            :rules="mobileRules"
                            :disabled="submitted"
                            label="Votre numéro de téléphone"
                            variant="solo"
                            flat
                        ></v-text-field>
                    </div>
                </div>


                <div class="checkbox-row">
                    <div class="custom-checkbox">
                        <img v-if="checkbox" src="@/assets/search/search-page-filters-checkedbox.svg" @click="toggleCheckbox(index)"/>
                        <img v-else src="@/assets/search/search-page-filters-checkbox.svg" @click="toggleCheckbox(index)"/>
                    </div>
                    <p>J'ai lu et j'accepte la 
                        <u class="custom-link" @click="gotoPage('/politique-confidentialite', 'blank')">politique de confidentialité</u> 
                        et 
                        <u class="custom-link" @click="gotoPage('/cgu', 'blank')">les CGU</u>.</p>
                </div>


                <div class="row-btn">
                    <div @click="submitForm">
                        <PrimaryNormalButton textContent="Télécharger mon E-book" append-icon="mdi-download" />
                    </div>          
                </div>  

            </v-form>

            <div>
               <img src="@/assets/blog/trophy.png" alt="trophée" class="photo"/>
            </div>
        </div>
    </section>

</template>

<script setup>
import gotoPage from '@/utils/router';
</script>

<script>
import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
import { submitEbookForm } from '@/services/blog.service.js';
import {  validateEmail , validateName , validateNotEmpty , validateMobile} from "../../../utils/validationRules"; 

export default {
    name: 'EbookSales',

    components: {
        PrimaryNormalButton,
    },

    data() {
        return {
            formData: {},                       //  datas sent
            submitted: false,                   //  toggle si le formulaire est envoyé
            checkbox: false,                    //  state of checkbox

            /* form rules */
            emailRules: [
            v => validateEmail(v) || true],
            nameRules: [  
            v => validateName(v) || true],
            notEmptyRules: [
            v => validateNotEmpty(v) || true],
            mobileRules: [
            v => validateMobile(v) || true],
        }
    },

    methods: {
        //  TODO validate & submit form
        async submitForm () {
            const validate = await this.$refs.ebookForm.validate()
            if (validate.valid && this.checkbox) submitEbookForm(this.formData);
            //console.log(this.formData);
        },

        //   toggle between checked and not checked
        toggleCheckbox() {
            this.checkbox = !this.checkbox;
        },
    }
}
</script>

<style scoped>
.container {
    display: flex;
    flex-direction: column;
    gap: 30px;
    padding: 40px 0 60px 0;
}

.content-container-1 {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 20px;
}

.photo {
    width: 100%;
    border-radius: 15px;
}

.form-container {
    box-shadow: 0px 4px 10px 0px #26282B1A;
    background-color: rgba(38, 40, 43, 0.1);
    padding: 16px;
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    height: fit-content;
}

.row1 {
    display: bloc;
    width: 100%;
    justify-content: space-between;
}

.field1 {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.checkbox-row {
    display: flex;
    gap: 10px;
    margin-left: 6px;
}

.custom-checkbox {
    cursor: pointer;
    width: fit-content;
}

.row-btn {
    display: flex;
    width: 100%;
    margin-block: 30px;
    justify-content: space-evenly;
}

.custom-link {
    cursor: pointer;
}

@media screen and (min-width: 992px) {
    .content-container-1 {
        display: grid;
        grid-template-columns: 2fr 1fr;
    }
 
    .row1 {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
    }

    .field1 {
        width: 100%;
    }

    .form-container {
        gap: 20px;
    }

    .row-btn {
        justify-content: baseline;
        margin-block: 0px;
        width: fit-content;
    }
}
@media screen and (min-width: 2000px) {
  .padding-container {
    padding: 2% 18%; 
  }
}
</style>