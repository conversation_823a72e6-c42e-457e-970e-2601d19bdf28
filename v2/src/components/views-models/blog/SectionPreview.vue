<template>
  <section class="container">
    <div class="content-wrapper">
      <h2 >Other Articles</h2>

      <div class="sub-section-container">
        <!-- iterate through each theme -->

        <div v-for="(section, index) in news" :key="index" class="sub-section">
          <!-- if theme list is not empty -->
          <div v-if="section && section.length > 0" class="sub-section">
            

            <!-- iterate through each articles by theme -->
            <div
              v-for="(article, index) in section"
              :key="index"
              class="sub-section"
            >
              <NewsCardMini v-if="article" :article="article" />
            </div>
          </div>

          <div v-else class="sub-section">
            <h5>{{ titles[index] }}</h5>
            <p>Aucun contenu disponible pour ce thème</p>
          </div>
        </div>
      </div>
      <div
            class="btn-wrapper"
            @click="gotoPage(`/actualite-section/${linkTo}`)"
          >
            <PrimaryNormalButton
              textContent="Découvrir plus"
              append-icon="mdi-arrow-right"
            />
      </div>
    </div>
  </section>
</template>

<script setup>
  import gotoPage from '@/utils/router';
</script>

<script>
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import NewsCardMini from '@/components/cards/NewsCardMini.vue';
  import { getMostPopularArticlesByTheme } from '@/services/blog.service';

  export default {
    name: 'SectionPreview',

    props: {
      //  array of the titles and subtitles of the section where index 0 = title, n>0 = subtitles
      titles: {
        type: Array,
        default: () => ['title', 'subtitle1', 'subtitle2', 'subtitle3'],
      },

      //  string for the buttons linking to blog-section
      linkTo: {
        type: String,
        default: 'candidate',
      },
    },

    components: {
      NewsCardMini,
      PrimaryNormalButton,
    },

    data() {
      return {
        news: [], //   multidimensionnal array contening list of lists contening articles by theme
      };
    },

    async mounted() {
      for (let i = 0; i < this.titles?.length; i++) {
        this.news[i] = await getMostPopularArticlesByTheme(this.titles[i]);
      }
    },
  };
</script>

<style scoped>
  .container {
    padding: 40px 0;
  }
  .content-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 50px;
  }
  .sub-section-container {
    display: flex;
    justify-content: center;
    gap: 50px;
  }

  .sub-section {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    gap: 30px;
  }

  .card-wrapper {
    width: 100%;
    display: flex;
  }
  .btn-wrapper{
    width: 100%;
    display: flex;
    justify-content: end;
    align-items: center;
  }
  /* ✅ MOBILE */
  @media screen and (max-width: 480px) and (max-width: 767px) {
    .sub-section-container {
      flex-wrap: wrap;
    }
  }

  /* ✅ TABLETTE : format portrait (iPad, Galaxy Tab) */
  @media screen and (min-width: 768px) and (max-width: 1023px) {
    .sub-section-container {
      flex-wrap: wrap;
    }
  }

  /* desktop */
  @media screen and (min-width: 992px) {
    .sub-section {
      display: flex;
      flex-direction: column;
      gap: 30px;
    }
  }
</style>
