<template>
  <article class="container-general">
    <header class="header-container">
      <div class="title-actions">
        <h2>Aperçu du CV</h2>
        <div
          class="actions"
          :class="{ 'justify-end': !isProfilePage && !isRecruiterProfilePage }"
        >
          <img
            v-if="isProfilePage || isRecruiterProfilePage"
            class="edit-icon"
            src="@/assets/icons/edit-icon-new.svg"
            alt="Edit"
            @click="goToSection('my-cvs')"
          />
          <button @click="downloadPdf">
            <v-tooltip location="top" activator="parent">
              <span>Télécharger le CV personnel</span>
            </v-tooltip>
            <img src="@/assets/icons/download.svg" alt="download" />
          </button>
        </div>
      </div>
    </header>
    <vue-pdf-embed
      v-if="pdfUrl"
      width="415"
      :source="pdfUrl"
      :page="currentPage"
      @loaded="onDocumentLoaded"
      @error="onError"
      class="cv-preview-container"
    />
    <div v-if="loading" class="loading">Chargement du PDF...</div>
    <div v-if="error" class="error">
      Désolé, une erreur s'est produite lors du téléchargement du fichier
    </div>

    <div v-if="totalPages > 1" class="controls">
      <button @click="previousPage" :disabled="currentPage === 1">
        &#x276E;
        <!-- Code Unicode pour "◀" (flèche gauche) -->
      </button>

      <p>page {{ currentPage }} de {{ totalPages }}</p>

      <button @click="nextPage" :disabled="currentPage === totalPages">
        &#x276F;
        <!-- Code Unicode pour "▶" (flèche droite) -->
      </button>
    </div>
  </article>
</template>

<script>
  import VuePdfEmbed from 'vue-pdf-embed';

  export default {
    components: {
      VuePdfEmbed,
    },
    props: {
      candidate: {
        type: Object,
        required: true,
      },
      pdfUrl: {
        type: String,
        required: true,
      },
    },
    data() {
      return {
        currentPage: 1,
        totalPages: 0,
        loading: true,
        error: null,
      };
    },
    watch: {
      pdfUrl: {
        handler() {
          this.currentPage = 1;
          this.totalPages = 0;
          this.loading = true;
          this.error = null;
        },
        immediate: true,
      },
    },
    computed: {
      isProfilePage() {
        return (
          this.$route.path === '/profil' ||
          this.$route.path === '/recruteur/profil'
        );
      },
    },
    methods: {
      previousPage() {
        if (this.currentPage > 1) {
          this.currentPage--;
        }
      },
      nextPage() {
        if (this.currentPage < this.totalPages) {
          this.currentPage++;
        }
      },
      onDocumentLoaded({ numPages }) {
        this.totalPages = numPages;
        this.loading = false;
      },
      onError(error) {
        this.loading = false;
        this.error = error.message || 'Erreur lors de le chargement du CV PDF';
        //console.error('Erreur lors de le chargement du CV PDF:', error);
      },
      goToSection(sectionId) {
        this.$router
          .push({
            path: '/profil/edition',
            query: { section: sectionId },
          })
          .then(() => {
            this.$nextTick(() => {
              this.$emit('toggleSection', sectionId); // Ouvre la bonne section

              setTimeout(() => {
                const element = document.getElementById(sectionId);
                if (element) {
                  const navbarHeight =
                    document.querySelector('.navbar')?.offsetHeight || 80;
                  const elementPosition =
                    element.getBoundingClientRect().top + window.scrollY;
                  window.scrollTo({
                    top: elementPosition - navbarHeight - 20,
                    behavior: 'smooth',
                  });
                }
              }, 300); // Attend que le menu s'affiche avant de scroller
            });
          });
      },
      async downloadPdf() {
        if (
          !this.pdfUrl ||
          !this.candidate ||
          !this.candidate.first_name ||
          !this.candidate.last_name
        ) {
          //console.error(
          //  'PDF indisponible ou informations candidat manquantes.'
          //);
          return;
        }

        try {
          // Définir le nom dynamique du fichier basé sur le candidat consulté
          const fileName = `CV-${this.candidate.first_name}-${this.candidate.last_name}.pdf`;

          // Télécharger le fichier sous forme de blob
          const response = await fetch(this.pdfUrl, { mode: 'cors' });
          const blob = await response.blob();

          // Créer un URL temporaire pour le fichier
          const blobUrl = window.URL.createObjectURL(blob);

          // Créer un lien de téléchargement
          const link = document.createElement('a');
          link.href = blobUrl;
          link.setAttribute('download', fileName); // Utilisation du nom dynamique
          document.body.appendChild(link);
          link.click();

          // Nettoyer l'URL temporaire
          window.URL.revokeObjectURL(blobUrl);
          document.body.removeChild(link);
        } catch (error) {
          //console.error('Erreur lors du téléchargement du PDF:', error);
        }
      },
    },
  };
</script>

<style scoped>
  .container-general {
    background: var(--surface-bg-2);
    border-radius: 8px;
    padding: 1rem;
    width: 100%;
    max-width: 700px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  .cv-preview-container {
    /* width: 100%; */
    /* max-width: 900px; */
    height: 620px;
    overflow: auto;
    margin: 15px auto 0 auto;
    background: var(--surface-bg-2);
    background-color: var(--white-200);
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  .cv-preview-container canvas {
    /* width: -webkit-fill-available;
  width: -moz-available;
  width: stretch; */
    width: 100%;
    height: auto;
  }
  header.header-container {
    border-bottom: 1.5px solid var(--surface-bg);
    padding-left: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: space-between;
    align-items: center;
  }
  .title-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  .actions {
    display: flex;
    flex-grow: 1;
    justify-content: space-between;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .edit-icon {
    margin: 0 0 0 1rem;
    cursor: pointer;
    filter: none;
  }
  .edit-icon svg path {
    stroke: #32394c;
  }
  .edit-icon:hover {
    opacity: 0.6;
  }
  button:hover {
    opacity: 0.6;
  }

  .controls {
    width: 100%;
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
  }

  .controls button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(
      --text-color,
      black
    ); /* Utilisation d'une variable CSS si disponible */
    transition: opacity 0.3s ease-in-out;
  }

  .controls button:disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }

  .controls button:hover:not(:disabled) {
    opacity: 0.6;
  }

  /* .controls {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .controls button {
    margin: 0 0.5rem;
    padding: 0.5rem 0.7rem;
    background-color: var(--yellow-100);
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }

  .controls button:disabled {
    background-color: #a0aec0;
    cursor: not-allowed;
  } */

  .loading,
  .error {
    text-align: center;
    padding: 2rem;
    font-size: 1.2rem;
    color: #4a5568;
  }

  .error {
    color: #e53e3e;
  }
</style>
