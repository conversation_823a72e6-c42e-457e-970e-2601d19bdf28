<template>
  <section id="my-formations" class="section-container">
    <!-- container header band -->
    <div
      class="header-container bg-color"
      @click="$emit('toggleSection', 'my-formations')"
    >
      <h5>
        {{
          user.type_user === 'applicant' ? 'Mes formations' : 'Vos formations'
        }}
        <span v-if="notifications > 0" class="notification-span">
          {{
            user.type_user === 'applicant'
              ? 'Ajoute tes études !'
              : 'Ajoutez vos études !'
          }}</span
        >
      </h5>
      <img
        v-if="isPanelOpen"
        src="@/assets/icons/arrow-top.svg"
        alt="flèche en bas"
      />
      <img v-else src="@/assets/icons/arrow-bottom.svg" alt="flèche en bas" />
    </div>

    <!-- container content -->
    <div v-show="isPanelOpen" class="content">
      <!-- form to add formation -->
      <v-form
        v-if="isFormationInStaging"
        class="formations-container"
        ref="form"
      >
        <div class="title-date">
          <div class="input-field">
            <h5>Titre de la formation</h5>
            <v-text-field
              v-model="newFormation['title']"
              label="Saisis l'intitulé de la formation"
              type="text"
              :rules="stringRules"
            />
          </div>
          <div class="input-field">
            <h5>Date d'obtention</h5>

            <label for="date" hidden>Date</label>
            <input
              type="date"
              v-model="newFormation['date']"
              class="date-input-field"
              id="date"
              name="date"
            />
          </div>
        </div>

        <div class="school-degree">
          <div class="input-field">
            <h5>Organisme de formation</h5>
            <v-text-field
              v-model="newFormation['school']"
              label="Saisis le nom de l'organisme de formation"
              type="text"
              :rules="stringRules"
            />
          </div>

          <div class="input-field">
            <h5>Niveau d'études</h5>
            <v-select
              v-model="newFormation['degree']"
              label="Sélectionne le niveau d'études"
              :items="schoolDegreeList"
              variant="underlined"
              hide-selected
              clearable
            ></v-select>
          </div>
        </div>

        <!-- update user -->
        <div class="btn-row">
          <PrimaryNormalButton
            textContent="Enregistrer"
            @click="postFormations"
          />
        </div>
      </v-form>

      <!-- btn: add a formation -->
      <div v-else class="add-btn">
        <PrimaryNormalButton
          textContent="Ajouter une formation"
          btnColor="secondary"
          @click="addFormation"
          add
        />
      </div>

      <!-- list of all formation -->
      <!-- list of all formations -->
      <div
        v-for="(formation, index) in formationList"
        :key="formation.id"
        class="formations-container"
      >
        <!-- Vérification si cette formation est en cours de modification -->
        <div v-if="newFormation.id !== formation.id">
          <div class="title-date">
            <div class="input-field">
              <h5>Titre de la formation</h5>
              <v-text-field
                v-model="formation['titre']"
                :label="formation['titre']"
                type="text"
                disabled
              />
            </div>
            <div class="input-field">
              <h5>Date d'obtention</h5>

              <label for="date" hidden>Date</label>
              <input
                type="date"
                v-model="formation['obtention']"
                class="date-input-field"
                disabled
              />
            </div>
          </div>

          <div class="school-degree">
            <div class="input-field">
              <h5>Organisme de formation</h5>
              <v-text-field
                v-model="formation['organisme']"
                :label="formation['organisme']"
                type="text"
                disabled
              />
            </div>

            <div class="input-field">
              <h5>Niveau d'études</h5>
              <v-select
                v-model="formation['etude']"
                :label="formation['etude']"
                :items="schoolDegreeList"
                variant="underlined"
                disabled
              ></v-select>
            </div>
          </div>

          <div class="btn-row">
            <PrimaryNormalButton
              textContent="Modifier"
              btnColor="primary"
              @click="editFormation(formation)"
            />
            <v-btn
              flat
              class="custom-btn"
              append-icon="mdi-trash-can-outline"
              @click="deleteThisFormation(formation.id, index)"
            >
              Supprimer
            </v-btn>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import {
    deleteFormation,
    getFormationList,
    postFormation,
  } from '@/services/profile.service.js';
  import { EDUCATION_FIELDS } from '@/utils/base/etudes.js';
  import { toaster } from '@/utils/toast/toast.js';
  import { validateStringRules } from '../../../utils/validationRules';

  export default {
    name: 'MyCompetences',

    components: {
      PrimaryNormalButton,
    },
    props: {
      user: {
        type: Object,
        required: true,
      },
      activeSection: String,
    },
    computed: {
      isPanelOpen() {
        return this.activeSection === 'my-formations';
      },
    },

    data() {
      return {
        notifications: 0,
        isPanelOn: false, //  state of panel
        formationList: [], //  list of formation
        isFormationInStaging: false,
        isEditing: false, //  if a formation is currently stagged before post
        newFormation: {}, //  new formation currently in staging area
        schoolDegreeList: [], //  for input selection

        /* input rules */
        stringRules: [(v) => validateStringRules(v) || true],
      };
    },

    async mounted() {
      try {
        this.formationList = await getFormationList({
          timestamp: new Date().getTime(),
        });
        if (this.formationList.length <= 0) {
          this.notifications = 1;
          this.togglePanel();
        }
      } catch (error) {
        //console.error('Erreur lors de la récupération des formations :', error);
        toaster.showInfoPopup('Impossible de récupérer les formations.');
      }

      // Générer la liste des niveaux d'études
      this.schoolDegreeList = this.generateList(EDUCATION_FIELDS, 'nom');
    },
    methods: {
      //  bind list to variable for input select options
      generateList(objectList, fieldName) {
        let list = [];
        for (let i = 0; i < objectList.length; i++) {
          list.push(objectList[i][fieldName]);
        }
        return list;
      },

      //  toggle section panel visibility
      togglePanel() {
        this.isPanelOn = !this.isPanelOn;
      },
      // Reset the formation form
      resetFormationForm() {
        //console.log('Réinitialisation du formulaire');
        this.newFormation = {
          id: null,
          title: null,
          date: null,
          school: null,
          degree: null,
        };
        this.isFormationInStaging = false;
        //console.log('Formulaire réinitialisé :', this.newFormation);
      },

      // Edit a formation
      editFormation(formation) {
        //console.log('Édition de la formation :', formation);
        if (this.isFormationInStaging) {
          console.warn('Modification déjà en cours');
          toaster.showInfoPopup(
            "Veuillez terminer la modification en cours avant d'en commencer une autre."
          );
          return;
        }
        this.isFormationInStaging = true;
        this.newFormation = {
          id: formation.id,
          title: formation.titre,
          date: formation.obtention,
          school: formation.organisme,
          degree: formation.etude,
        };
        //console.log('Formulaire pré-rempli :', this.newFormation);
      },

      // Validate form and update user information in database
      async postFormations() {
        //console.log('Début de la méthode postFormations');
        const isFormValid = await this.formValidation();
        //console.log('Formulaire validé :', isFormValid);
        if (!isFormValid) return;

        //console.log('Données de la formation à soumettre :', this.newFormation);
        const updateResponse = await postFormation(this.newFormation);
        //console.log('Réponse du backend :', updateResponse);

        if (updateResponse) {
          if (this.newFormation.id) {
            //console.log(
            //  'Mise à jour de la formation avec ID :',
            //  this.newFormation.id
            //);
            const index = this.formationList.findIndex(
              (formation) => formation.id === this.newFormation.id
            );
            if (index !== -1) {
              this.formationList[index] = {
                id: this.newFormation.id,
                titre: this.newFormation.title,
                obtention: this.newFormation.date,
                organisme: this.newFormation.school,
                etude: this.newFormation.degree,
              };
              //console.log('Formation mise à jour :', this.formationList[index]);
            } else {
              console.warn(
                'Aucune formation trouvée avec cet ID :',
                this.newFormation.id
              );
            }
          } else {
            //console.log('Nouvelle formation ajoutée');
            this.formationList.push({
              id: updateResponse.id,
              titre: this.newFormation.title,
              obtention: this.newFormation.date,
              organisme: this.newFormation.school,
              etude: this.newFormation.degree,
            });
            //console.log(
            //  'Nouvelle formation ajoutée :',
            //  this.formationList[this.formationList.length - 1]
            //);
          }
          this.resetFormationForm();
          if (this.formationList.length <= 0) {
            this.notifications = 1;
          } else {
            this.notifications = 0;
          }
        } else {
          //console.error("Erreur lors de l'ajout de la formation");
          toaster.showInfoPopup("Erreur lors de l'ajout de la formation.");
        }
      },

      //  add a formation to the user list of formations
      addFormation() {
        if (this.isFormationInStaging)
          return toaster.showInfoPopup(
            'Veuillez enregistrer la formation en cours.'
          );
        this.isFormationInStaging = true;
        this.newFormation = {
          id: null,
          title: '',
          date: '',
          school: '',
          degree: '',
        };
      },

      // Delete a specific formation
      async deleteThisFormation(id, index) {
        //console.log('Suppression de la formation ID :', id);
        const confirmDelete = confirm(
          'Êtes-vous sûr de vouloir supprimer cette formation ?'
        );
        if (confirmDelete) {
          const deleteResponse = await deleteFormation(id);
          //console.log('Réponse de suppression :', deleteResponse);
          if (deleteResponse) {
            this.formationList.splice(index, 1);
            //console.log(
            //  'Formation supprimée, liste mise à jour :',
            //  this.formationList
            //);
            if (this.formationList.length <= 0) {
              this.notifications = 1;
            }
          } else {
            //console.error('Erreur lors de la suppression de la formation');
            toaster.showInfoPopup(
              'Erreur lors de la suppression de la formation.'
            );
          }
        }
      },

      //  form validation with active rules
      async formValidation() {
        //console.log('Validation des champs:', this.newFormation);
        if (this.newFormation['degree'] == null)
          return toaster.showInfoPopup(
            'Vous devez sélectionner un niveau de formation.'
          );
        if (this.newFormation['date'] == null)
          return toaster.showInfoPopup(
            "Vous devez sélectionner une date d'obtention."
          );
        if (!this.newFormation.title)
          return toaster.showInfoPopup(
            'Vous devez saisir le titre de la formation.'
          );
        if (!this.newFormation.school)
          return toaster.showInfoPopup(
            "Vous devez saisir le nom de l'organisme."
          );

        const validate = await this.$refs.form.validate();
        //console.log('Résultat de la validation:', validate);

        if (validate.valid) return true;
        else {
          toaster.showInfoPopup(
            'Les informations sur le formulaire sont incomplètes.'
          );
          return false;
        }
      },
    },
  };
</script>

<style scoped>
  /* section container & layout */
  .section-container {
    background-color: var(--white-200);
    width: 100%;
    border-radius: 2px;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid black;
    cursor: pointer;
  }
  .header-container h5 {
    display: flex;
    gap: 10px;
    align-items: center;
  }

  .notification-span {
    background-color: rgba(254, 85, 85, 0.403);
    border: solid 1px red;
    border-radius: 10px;
    padding: 0 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
  }
  .bg-color {
    background-color: var(--secondary-2b2);
    border-bottom: none;
  }

  .content {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: fit-content;
    padding: 24px;
  }

  /* buttons */
  .btn-row {
    display: flex;
    justify-content: end;
    gap: 5px;
  }

  .custom-btn {
    border-radius: 5px;
    border: 1px solid rgba(246, 179, 55, 1);
    height: 40px;
  }

  /* inputs */
  .input-field,
  .input-field2 {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .date-input-field {
    width: 150px;
  }

  /* content */
  .formations-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .title-date {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 10px;
  }

  .school-degree {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 10px;
  }

  .delete-btn {
    display: flex;
    justify-content: end;
  }

  @media screen and (min-width: 992px) {
    .input-field {
      width: 49%;
    }

    .input-field2 {
      width: 40%;
    }

    .title-date {
      flex-direction: row;
      justify-content: space-between;
      gap: 0px;
    }

    .school-degree {
      flex-direction: row;
      justify-content: space-between;
      gap: 10px;
    }
  }
</style>
