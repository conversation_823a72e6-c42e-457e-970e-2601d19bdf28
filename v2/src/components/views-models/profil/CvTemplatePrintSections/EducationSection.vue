<template>
  <div v-if="user.formation && user.formation.length">
    <div
      v-for="edu in user.formation.slice(0, 2)"
      :key="edu.titre"
      class="education-item"
    >
      <h3>
        {{ edu.titre }}
        <span class="date">({{ parseDates(edu.obtention) }})</span>
      </h3>
      <p class="lieu">chez {{ edu.organisme }}</p>
      <p class="description">{{ edu.etude }}</p>
    </div>
  </div>
  <p v-else>Aucune éducation spécifiée</p>
</template>

<script>
  export default {
    name: 'EducationSection',

    props: {
      user: {
        type: Object,
        required: true,
        default: () => ({
          formation: [],
        }),
      },
      parseDates: {
        type: Function,
        required: true,
      },
    },
  };
</script>
