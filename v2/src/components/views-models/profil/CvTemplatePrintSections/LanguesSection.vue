<template>
  <ul v-if="user.langue && user.langue.length" class="languages-list">
    <li v-for="langue in user.langue.slice(0, 3)" :key="langue.langue">
      • {{ langue.langue }}
    </li>
  </ul>
  <p v-else>Aucune langue spécifiée</p>
</template>

<script>
  export default {
    name: 'LanguesSection',

    props: {
      user: {
        type: Object,
        required: true,
        default: () => ({
          langue: [],
        }),
      },
    },
  };
</script>
