<template>
  <ul v-if="user.skill && user.skill.length" class="skills-list">
    <li v-for="skill in user.skill.slice(0, 5)" :key="skill">
      • {{ skill.nom }}
    </li>
  </ul>
  <p v-else>Aucune compétence spécifiée</p>
</template>

<script>
  export default {
    name: 'CompetencesSection',

    props: {
      user: {
        type: Object,
        required: true,
        default: () => ({
          skill: [],
        }),
      },
    },
  };
</script>
