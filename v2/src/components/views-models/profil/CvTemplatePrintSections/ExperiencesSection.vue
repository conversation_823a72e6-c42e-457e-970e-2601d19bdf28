<template>
  <div v-if="user.experience && user.experience.length">
    <div
      v-for="job in user.experience.slice(0, 2)"
      :key="job.position"
      class="experience-item"
    >
      <h3>
        {{ job.position }}
        <span class="date">
          ({{ parseDates(job.debut) }} -
          {{ job.fin ? parseDates(job.fin) : 'En cours' }})
        </span>
      </h3>
      <p class="lieu">chez {{ job.company }}</p>
      <p class="description">{{ job.exp_detail }}</p>
    </div>
  </div>
  <p v-else>Aucune expérience spécifiée</p>
</template>

<script>
  export default {
    name: 'ExperiencesSection',

    props: {
      user: {
        type: Object,
        required: true,
        default: () => ({
          experience: [],
        }),
      },
      parseDates: {
        type: Function,
        required: true,
      },
    },
  };
</script>
