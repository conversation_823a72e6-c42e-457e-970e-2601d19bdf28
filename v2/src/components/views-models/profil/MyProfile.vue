<template>
  <section id="my-profile" class="section-container">
    <!-- container header band -->
    <div
      class="header-container bg-color"
      @click="$emit('toggleSection', 'my-profile')"
    >
      <h5>
        {{
          user.type_user === 'applicant'
            ? 'Mon profil candidat'
            : 'Votre profil de membre'
        }}
        <!-- <span v-if="notifications > 0" class="notification-span">
          {{
            notifications +
            (notifications > 1
              ? ' informations manquantes !'
              : ' information manquante !')
          }}
        </span> -->
        <span
          v-for="(item, index) in array"
          :key="index"
          class="notification-span"
        >
          {{ getErrorMessage(item) }}
        </span>
      </h5>

      <img
        v-if="isPanelOpen"
        src="@/assets/icons/arrow-top.svg"
        alt="flèche en bas"
      />
      <img v-else src="@/assets/icons/arrow-bottom.svg" alt="flèche en bas" />
    </div>

    <!-- container content -->
    <div v-show="isPanelOpen" class="content">
      <div class="avatar-title">
        <div class="img-wrapper">
          <div class="image-container">
            <img
              v-if="formData.photo || previewImage"
              :src="previewImage || getFullImageUrl()"
              @click="openFileInput"
              class="avatar-icon"
              alt="avatar image"
            />
            <img
              v-else
              src="@/assets/icons/avatar.png"
              alt="avatar image"
              class="avatar-icon"
              @click="openFileInput"
            />

            <!-- Icone de modification par défaut -->

            <!-- Icône de statut (affichée uniquement lorsqu'une image est valide) -->
            <v-icon
              v-if="showSuccessIcon"
              class="img-state-icon"
              color="rgb(var(--v-theme-success))"
            >
              mdi-check-circle
            </v-icon>
            <!-- Icône d'erreur -->
            <v-icon
              v-if="showErrorIcon"
              class="img-state-icon"
              color="rgb(var(--v-theme-error))"
            >
              mdi-alert-circle
            </v-icon>
          </div>

          <!-- Message d'erreur affiché dynamiquement -->
          <p v-if="fileError" class="error-message">{{ fileError }}</p>

          <v-file-input
            ref="fileInput"
            :rules="rulesSize"
            accept="image/*"
            @change="handlePhotoChange"
            class="hidden"
          ></v-file-input>
        </div>

        <div class="title">
          <h5>Métier</h5>
          <v-text-field
            v-model="formData.metier"
            label="Mon métier"
            type="text"
            :rules="[...nameRules, ...notEmptyRules]"
          />
          <span v-if="!formData.photo && !previewImage" class="add-photo-text">
            Ajoutez votre photo
          </span>
        </div>
      </div>

      <div class="name">
        <div class="input">
          <h5>Nom</h5>
          <v-text-field
            v-model="formData.last_name"
            label="Mon nom"
            type="text"
            :rules="[...nameRules, ...notEmptyRules]"
          />
        </div>

        <div class="input">
          <h5>Prénom</h5>
          <v-text-field
            v-model="formData.first_name"
            label="Mon prénom"
            type="text"
            :rules="[...nameRules, ...notEmptyRules]"
          />
        </div>
      </div>

      <div class="contact">
        <div class="input">
          <h5>E-mail</h5>
          <v-text-field
            v-model="formData.email"
            label="<EMAIL>"
            type="text"
            :rules="[...emailRules, ...notEmptyRules]"
          />
        </div>

        <div class="input">
          <h5>Téléphone</h5>
          <v-text-field
            v-model="formData.numberPhone"
            label="0X XX XX XX XX"
            type="text"
            :rules="mobileRules"
          />
        </div>
      </div>

      <div class="location">
        <h5>Localisation</h5>
        <v-text-field
          v-model="formData.ville"
          label="Tapez votre ville"
          type="text"
        />
      </div>

      <div class="aboutme">
        <h5>A propos de moi</h5>
        <v-textarea
          v-model="formData.about"
          label="Décris-toi en quelques phrases ici"
          type="text"
          :rules="rules500"
          counter
          fastfail
        />
      </div>

      <div class="websites">
        <div class="input">
          <h5>Site internet</h5>
          <v-text-field
            v-model="formData.site_url"
            label="Saisi l'url de ton site ici"
            type="text"
            :rules="websiteRules"
            fastfail
          />
        </div>
        <div class="input">
          <h5>Compte linkedin</h5>
          <v-text-field
            v-model="formData.linkedin"
            label="Saisi l'url de ton compte ici"
            type="text"
            :rules="websiteRules"
            fastfail
          />
        </div>
      </div>
      <div class="websites">
        <div class="input">
          <h5>Instagram</h5>
          <v-text-field
            v-model="formData.instagram"
            label="Saisi l'url de ton Instagram ici"
            type="text"
            :rules="websiteRules"
            fastfail
          />
        </div>
        <div class="input">
          <h5>Facebook</h5>
          <v-text-field
            v-model="formData.facebook"
            label="Saisi l'url de ton compte Facebook ici"
            type="text"
            :rules="websiteRules"
            fastfail
          />
        </div>
        <div class="input">
          <h5>TikTok</h5>
          <v-text-field
            v-model="formData.tiktok"
            label="Saisi l'url de ton compte Tiktok ici"
            type="text"
            :rules="websiteRules"
            fastfail
          />
        </div>
      </div>

      <div class="websites2">
        <div class="input">
          <h5>Portfolio</h5>
          <v-text-field
            v-model="formData.porfolio_url"
            label="Saisis l'url de ton portfolio ici"
            type="text"
            :rules="websiteRules"
            fastfail
          />
        </div>

        <div class="input">
          <h5>Autre site</h5>
          <v-text-field
            v-model="formData.autre_url"
            label="Saisis l'url de ton site ici"
            type="text"
            :rules="websiteRules"
            fastfail
          />
        </div>
      </div>

      <div class="btn-row">
        <PrimaryNormalButton textContent="Enregistrer" @click="$emit('save')" />
      </div>
    </div>
  </section>
</template>

<script>
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import LocationInput from '@/components/inputs/LocationInput.vue';
  import { updateUserInformations } from '@/services/profile.service';
  import { takeUserNotifications } from '@/utils/userUtilities';
  import { takeUserNotifications1 } from '@/utils/userUtilities';
  import { baseUrl } from '../../../services/axios';
  import {
    validateEmail,
    validateMobile,
    validateName,
    validateNoSpecialChar,
    validateNotEmpty,
    validateWebsite,
  } from '../../../utils/validationRules';
  export default {
    name: 'MyProfile',

    components: {
      PrimaryNormalButton,
      LocationInput,
    },
    props: {
      user: {
        type: Object,
        required: true,
      },
      activeSection: String,
    },
    computed: {
      isPanelOpen() {
        return this.activeSection === 'my-profile';
      },
    },

    data() {
      return {
        isPanelOn: false, //  state of panel
        formData: {
          about: '',
          photo: null,
        }, //  datas sent
        notifications: 0,
        array: [],
        //userInfos: {},      //  user datas
        previewImage: '', //  string for the user photo

        /* input rules */
        emailRules: [(v) => validateEmail(v) || true],
        nameRules: [(v) => validateName(v) || true],
        notEmptyRules: [(v) => validateNotEmpty(v) || true],
        mobileRules: [(v) => validateMobile(v) || true],
        websiteRules: [(v) => validateWebsite(v) || true],
        noSpecialCharRules: [
          (v) =>
            validateNoSpecialChar(v) ||
            'Ne doit pas contenir de caractères spéciaux',
        ],
        rules500: [
          (v) => v?.length <= 500 || 'ne doit pas dépasser 500 caractères',
        ],
        showErrorIcon: false,
        showSuccessIcon: false,
        fileError: '', // Stocke le message d'erreur
        rulesSize: [
          (value) =>
            !value ||
            !value.length ||
            value[0].size < 1000000 ||
            'Ne doit pas avoir plus de 1 mo',
        ],
      };
    },

    mounted() {
      this.formData = {
        metier: this.user.metier,
        first_name: this.user.first_name,
        last_name: this.user.last_name,
        email: this.user.email,
        numberPhone: this.user.numberPhone,
        about: this.user.about,
        site_url: this.user.site_url,
        linkedin: this.user.linkedin,
        porfolio_url: this.user.porfolio_url,
        autre_url: this.user.autre_url,
        photo: this.user.photo,
        ville: this.user.ville,
        facebook: this.user.facebook,
        instagram: this.user.instagram,
        tiktok: this.user.tiktok,
      };
      this.notifications = takeUserNotifications(this.user, [
        'about',
        'ville',
        'photo',
        'numberPhone',
      ]);
      this.array = takeUserNotifications1(this.user, [
        'about',
        'ville',
        'photo',
        'numberPhone',
      ]);
      if (this.notifications > 0) {
        this.togglePanel();
      }
    },

    methods: {
      formatURL(url) {
        if (url && !url.startsWith('http://') && !url.startsWith('https://')) {
          return 'https://' + url; // Ajoute https:// si nécessaire
        }
        return url;
      },

      togglePanel() {
        this.isPanelOn = !this.isPanelOn;
      },
      async updateUserDatas() {
        this.formData.site_url = this.formatURL(this.formData.site_url) || '';
        this.formData.linkedin = this.formatURL(this.formData.linkedin) || '';
        this.formData.porfolio_url =
          this.formatURL(this.formData.porfolio_url) || '';
        this.formData.autre_url = this.formatURL(this.formData.autre_url) || '';
        this.formData.instagram = this.formatURL(this.formData.instagram) || '';
        this.formData.tiktok = this.formatURL(this.formData.tiktok) || '';
        this.formData.facebook = this.formatURL(this.formData.facebook) || '';
        //console.log({ 'currentFormData:': this.formData });

        await updateUserInformations(this.formData);
        const updatedUser = { ...this.user, ...this.formData };
        this.$store.dispatch('handleUserChange', {
          type: null,
          payload: updatedUser,
        });
        this.notifications = takeUserNotifications(this.formData, [
          'about',
          'ville',
          'photo',
          'numberPhone',
          'first_name',
          'last_name',
          'email',
        ]);
      },

      //  get city and postal code value and bind them to formData
      getCityAndPostalCodeValue(cityAndPostalCode) {
        if (cityAndPostalCode) {
          this.formData['ville'] = cityAndPostalCode[0];
          this.formData['code_postal'] = cityAndPostalCode[1];
        }
      },

      //  handle user photo changes
      // handle user photo changes
      handlePhotoChange(event) {
        let file = event.target.files[0];

        if (file) {
          if (file.size < 1000000) {
            this.showErrorIcon = false;
            this.showSuccessIcon = true; // Taille valide
            this.fileError = '';
          } else {
            this.showErrorIcon = true; // Taille trop grande
            this.showSuccessIcon = false;
            this.fileError = 'La taille du fichier ne doit pas dépasser 1 Mo.';
          }

          // Gestion du fichier valide
          const newFileName = `user_avatar_${this.user.id}_${new Date().getTime()}.${file.type.split('/')[1]}`;

          const renamedFile = new File([file], newFileName, {
            type: file.type,
          });

          this.formData.photo = renamedFile;

          const reader = new FileReader();
          reader.onload = () => {
            this.previewImage = reader.result;
          };
          reader.readAsDataURL(renamedFile);
        }
      },
      handlePhotoChange_old(event) {
        const file = event.target.files[0];
        if (file) {
          //this.userInfos.photo = file;
          this.formData.photo = file;
          const reader = new FileReader();
          reader.onload = () => {
            this.previewImage = reader.result;
          };
          reader.readAsDataURL(file);
        }
      },

      //  trigger hidden input to upload photo
      openFileInput() {
        this.$refs.fileInput.click();
      },

      //  get photo url
      getFullImageUrl() {
        if (this.formData.photo) {
          if (this.formData.photo.name) {
            if (typeof this.formData.photo.name === 'string') {
              return URL.createObjectURL(this.formData.photo);
            }
          }
          return baseUrl + this.formData.photo;
        } else if (this.previewImage) {
          return this.previewImage;
        } else {
          return '';
        }
      },

      getErrorMessage(value) {
        if (!value || typeof value !== 'string') return '';
        if (value == 'about') return 'A propos de moi manquante !';
        if (value == 'photo') return 'Photo manquante !';
        if (value == 'numberPhone') return 'Numéro de téléphone manquant !';
        if (value == 'ville') return 'Ville manquante !';
      },
    },
  };
</script>

<style scoped>
  .section-container {
    background-color: var(--white-200);
    width: 100%;
    border-radius: 2px;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid black;
    cursor: pointer;
  }
  .header-container h5 {
    display: flex;
    gap: 10px;
    align-items: center;
  }

  .notification-span {
    background-color: rgba(254, 85, 85, 0.403);
    border: solid 1px red;
    border-radius: 10px;
    padding: 0 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
  }
  .bg-color {
    background-color: var(--secondary-2b2);
    border-bottom: none;
  }
  .content {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: fit-content;
    padding: 24px;
  }

  .avatar-icon {
    width: 92px;
    height: 92px;
    cursor: pointer;
  }

  .btn-row {
    display: flex;
    justify-content: end;
  }

  /* input content */
  .input {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .avatar-title {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
  }

  .image-container {
    position: relative;
    display: inline-block;
  }

  .img-wrapper {
    width: 100%;
    position: relative;
  }
  .img-wrapper img {
    border-radius: 5px;
    object-fit: cover;
  }
  .img-wrapper .error-message {
    color: rgb(var(--v-theme-error));
    font-size: 12px;
    margin-top: 5px;
    width: 400%;
  }

  .img-state-icon {
    position: absolute;
    bottom: 8px;
    right: 8px;
    font-size: 22px;
    /*background-color: var(--surface-bg-5);
    border-radius: 50%;
    padding: 16px;*/
    cursor: pointer;
  }

  .title {
    width: 100%;
  }

  .name {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 20px;
  }

  .contact {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 20px;
  }

  .location {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .aboutme {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .websites {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 20px;
  }

  .websites2 {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 20px;
  }

  .hidden {
    display: none;
  }

  @media screen and (min-width: 992px) {
    .avatar-title {
      flex-direction: row;
    }

    .img-wrapper {
      width: 10%;
    }

    .input {
      width: 49%;
    }

    .title {
      width: 90%;
    }

    .name {
      flex-direction: row;
      justify-content: space-between;
    }

    .contact {
      flex-direction: row;
      justify-content: space-between;
    }

    .location {
      width: 49%;
    }

    .websites {
      flex-direction: row;
      justify-content: space-between;
    }

    .websites2 {
      flex-direction: row;
      justify-content: space-between;
    }
    /* Aligner l'élément "Ajoutez votre photo" à gauche sous l'avatar */
    .add-photo-text {
      text-align: left; /* Aligner à gauche */
      font-size: 14px;
      color: black; /* Change cette couleur selon ton design */
      margin-top: -10px;
      display: block; /* Permet à l'élément de prendre toute la largeur disponible */
      width: 100%;
      margin-left: -110px; /* Décaler le texte plus à gauche */
    }

    /* Ajouter un peu d'espace sous l'avatar */
  }
</style>
