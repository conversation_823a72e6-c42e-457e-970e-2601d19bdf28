<template>
  <section id="my-competences" class="section-container">
    <!-- container header band -->
    <div
      class="header-container bg-color"
      @click="$emit('toggleSection', 'my-competences')"
    >
      <h5>
        {{
          user.type_user === 'applicant' ? 'Mes compétences' : 'Vos compétences'
        }}
        <span v-if="notifications > 0" class="notification-span">
          {{
            user.type_user === 'applicant'
              ? 'Ajoute tes compétences !'
              : 'Ajoutez vos compétences !'
          }}</span
        >
      </h5>
      <img
        v-if="isPanelOpen"
        src="@/assets/icons/arrow-top.svg"
        alt="flèche en bas"
      />
      <img v-else src="@/assets/icons/arrow-bottom.svg" alt="flèche en bas" />
    </div>

    <!-- container content -->
    <div v-show="isPanelOpen" class="content">
      <div class="informations">
        <p>
          Renseignez vos compétences techniques, langues maîtrisées et moyen de
          mobilité pour mettre en valeur votre profil auprès des recruteurs !
        </p>
      </div>

      <!-- skills -->
      <div class="skills">
        <h5>Compétences</h5>
        <v-autocomplete
          v-model="formData.skill"
          label="Clicker sur entrée après la saisie de chaque compétence"
          :items="[]"
          multiple
          chips
          clearable
          closable-chips
          :hide-no-data="true"
          :hide-selected="true"
          :clear-on-select="true"
          v-model:search="search"
          @change="addCustomSkill"
          @keydown.enter="addCustomSkill"
        ></v-autocomplete>
      </div>

      <!-- languages -->
      <div class="langage">
        <h5>Langues</h5>
        <v-select
          v-model="formData.langue"
          :items="langues"
          label="Sélectionne les langues que tu maitrises"
          variant="underlined"
          multiple
          chips
          closable-chips
          hide-selected
          clearable
          :clear-on-select="true"
        ></v-select>
      </div>

      <!-- permit and mobility -->
      <div class="permit-mobility">
        <div class="input-field">
          <h5>Permis</h5>
          <v-select
            v-model="formData.permis"
            label="Sélectionne tes permis de conduite"
            :items="permits"
            variant="underlined"
            multiple
            chips
            closable-chips
            hide-selected
            clearable
          ></v-select>
        </div>

        <div class="input-field">
          <h5>Mobilité</h5>
          <v-select
            v-model="formData.mobilité"
            label="Sélectionne tes moyens de locomotions"
            :items="mobilityOptions"
            variant="underlined"
            multiple
            chips
            closable-chips
            hide-selected
            clearable
          ></v-select>
        </div>
      </div>

      <!-- update user -->
      <div class="btn-row" v-if="isPanelOpen">
        <PrimaryNormalButton textContent="Enregistrer" @click="$emit('save')" />
      </div>
    </div>
  </section>
</template>

<script>
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import { takeUserNotifications } from '@/utils/userUtilities';
  import iso6391 from 'iso-639-1';

  import { getAllSkills, updateUserSkills } from '@/services/profile.service';

  export default {
    name: 'MyCompetences',
    components: {
      PrimaryNormalButton,
    },
    props: {
      user: {
        type: Object,
        required: true,
      },
      activeSection: String,
    },
    computed: {
      isPanelOpen() {
        return this.activeSection === 'my-competences';
      },
    },
    data() {
      return {
        notifications: 0,
        search: '',
        skills: [],
        isPanelOn: false, // State of panel
        formData: {
          skill: [],
          langue: [],
          permis: [],
          mobilité: [],
        }, // Data to be sent
        langues: [], // List of languages
        permits: [
          'Permis A (Moto)',
          'Permis B (Voiture)',
          'Permis C (Camion)',
          'Permis D (Bus)',
          'Permis E (Remorque)',
        ],
        mobilityOptions: [
          'Voiture',
          'Moto',
          'Vélo',
          'Transport en commun',
          'Trottinette',
          'À pied',
        ],
        dataBaseSkills: [],
      };
    },
    created() {
      this.langues = iso6391.getAllCodes().map((code) => {
        const name = iso6391.getName(code);
        switch (name) {
          case 'French':
            return 'Français';
          case 'English':
            return 'Anglais';
          case 'German':
            return 'Allemand';
          case 'Spanish':
            return 'Espagnol';
          case 'Italian':
            return 'Italien';
          case 'Portuguese':
            return 'Portugais';
          case 'Dutch':
            return 'Néerlandais';
          case 'Russian':
            return 'Russe';
          case 'Chinese':
            return 'Chinois';
          case 'Japanese':
            return 'Japonais';
          case 'Korean':
            return 'Coréen';
          case 'Arabic':
            return 'Arabe';
          case 'Hindi':
            return 'Hindi';
          case 'Urdu':
            return 'Urdu';
          case 'Punjabi':
            return 'Punjabi';
          case 'Bengali':
            return 'Bengali';
          case 'Polish':
            return 'Polonais';
          case 'Romanian':
            return 'Roumain';
          case 'Czech':
            return 'Tchèque';
          case 'Slovak':
            return 'Slovaque';
          case 'Hungarian':
            return 'Hongrois';
          case 'Greek':
            return 'Grec';
          case 'Turkish':
            return 'Turc';
          case 'Vietnamese':
            return 'Vietnamien';
          case 'Thai':
            return 'Thaï';
          case 'Indonesian':
            return 'Indonésien';
          case 'Malay':
            return 'Malais';
          case 'Filipino':
            return 'Philippin';
          case 'Swedish':
            return 'Suédois';
          case 'Norwegian':
            return 'Norvégien';
          case 'Danish':
            return 'Danois';
          case 'Finnish':
            return 'Finnois';
          default:
            return name;
        }
      });
    },
    mounted() {
      this.loadUserFormData();
      this.fillSkillsArrayFromDataBase();
    },
    methods: {
      // TODO : apres cette fonction sera reemplacee par une fonction qui va chercher les skills dans SQLLite dans le front
      async fillSkillsArrayFromDataBase() {
        this.dataBaseSkills = await getAllSkills();
        // Extraction des noms des skill
        this.skills = this.dataBaseSkills?.map((skill) => skill.nom) || [];
      },
      loadUserFormData() {
        try {
          const userInfos = this.$store.getters.getUser;
          this.formData.langue = userInfos.langue
            ? [...new Set(userInfos.langue.map((item) => item.langue))]
            : [];
          // TODO : apres getUser aura seulement le nom des skills donc cet sprit ne sera plus necessaire
          console.log('skills');
          console.log(userInfos.skill);
          this.formData.skill = userInfos.skill
            ? userInfos.skill.map((item) => item.nom)
            : [];

          this.formData.mobilité = userInfos.mobilité || [];
          this.formData.permis = userInfos.permis || [];

          this.notifications = takeUserNotifications(this.formData);
          if (this.notifications > 0) {
            this.togglePanel();
          }
        } catch (error) {
          //console.error(
          //  'Erreur lors de la récupération des données utilisateur.',
          //  error
          //);
        }
      },
      togglePanel() {
        this.isPanelOn = !this.isPanelOn;
      },
      updateUserInfos() {
        // const selectedSkills = this.dataBaseSkills.filter((skill) =>
        //   this.formData.skill.includes(skill.nom)
        // );
        this.formData = {
          ...this.formData,
          updatedSkills: this.formData.skill,
        };
        console.log(this.formData);
        try {
          updateUserSkills(this.formData);
          this.notifications = takeUserNotifications(this.formData);
        } catch (error) {
          //console.error(
          //  'Erreur lors de la mise à jour des informations utilisateur.',
          //  error
          //);
        }
      },
      addCustomSkill() {
        //console.log(this.search);
        const trimmed = this.search?.trim();
        if (
          trimmed &&
          !this.skills.includes(trimmed) &&
          !this.formData.skill.includes(trimmed)
        ) {
          this.skills.push(trimmed); // on l’ajoute à la liste d’items
          this.formData.skill.push(trimmed); // et aussi à la sélection actuelle
        }
        this.search = ''; // réinitialise le champ de recherche
      },
    },
  };
</script>

<style scoped>
  /* section container & layout */
  .section-container {
    background-color: var(--white-200);
    width: 100%;
    border-radius: 2px;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid black;
    cursor: pointer;
  }
  .header-container h5 {
    display: flex;
    gap: 10px;
    align-items: center;
  }

  .notification-span {
    background-color: rgba(254, 85, 85, 0.403);
    border: solid 1px red;
    border-radius: 10px;
    padding: 0 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
  }
  .bg-color {
    background-color: var(--secondary-2b2);
    border-bottom: none;
  }

  .content {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: fit-content;
    padding: 24px;
  }

  /* buttons */
  .btn-row {
    display: flex;
    justify-content: end;
  }

  /* inputs */
  .input-field {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  /* content */
  .informations {
    border-radius: 5px;
    padding-inline: 8px;
    padding-block: 18px;
    background-color: rgba(88, 160, 150, 0.2);
  }

  .skills,
  .langage,
  .permit-mobility {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 10px;
  }

  .permit-mobility {
    gap: 24px;
  }

  @media screen and (min-width: 992px) {
    .input-field {
      width: 49%;
    }
    .permit-mobility {
      flex-direction: row;
      justify-content: space-between;
      gap: 0px;
    }
  }
</style>
