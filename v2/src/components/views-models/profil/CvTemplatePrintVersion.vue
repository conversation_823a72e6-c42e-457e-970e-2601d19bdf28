<template>
  <div ref="cvContainer" class="cv-container">
    <!-- Header Section -->
    <header class="header">
      <div class="download-button-container">
        <button class="download-button" @click="$emit('download-cv')">
          <v-tooltip location="top" activator="parent">
            <span>Télécharger le CV Thanks-Boss</span>
          </v-tooltip>
          <img src="@/assets/icons/download.svg" alt="download" />
        </button>
      </div>
      <div class="profile-section">
        <UserAvatar :user="user" :width="120" />
      </div>
      <div class="metier-title-container">
        <p class="user-name">{{ user.last_name + ' ' + user.first_name }}</p>
        <h2 class="job-title">{{ user.metier }}</h2>
      </div>
    </header>

    <div class="content">
      <!-- Left Sidebar -->
      <aside class="sidebar">
        <section
          v-for="section in sidebarSections"
          :key="section.id"
          class="section"
        >
          <h3>{{ section.title }}</h3>
          <!-- N'afficher la section CONTACT que si l'utilisateur est un ami -->
          <component
            v-if="section.id !== 'contact' || isFriend"
            :is="section.component"
            :user="user"
            :clearText="clearText"
            :parseDates="parseDates"
          ></component>
        </section>
        <div class="logo-thanks-boss-container">
          <img src="@/assets/logo-tb.svg" alt="logo thanks boss" />
          <!-- <a href="https://frontpreprod.thanks-boss.com/" target="_blank"
            >Conçu par Thanks-Boss</a
          > -->
          <p>Conçu par Thanks-Boss</p>
        </div>
      </aside>

      <!-- ===============  MAIN CONTENT (Right side) =============== -->
      <main class="main-content">
        <section
          v-for="section in mainSections"
          :key="section.id"
          class="section"
        >
          <div class="section-title">
            <h2 class="title-edit-icon">{{ section.title }}</h2>
            <div class="title-line"></div>
          </div>
          <component
            :is="section.component"
            :user="user"
            :clearText="clearText"
            :parseDates="parseDates"
          ></component>
        </section>
      </main>
    </div>
  </div>
</template>

<script>
  import getImgPath from '@/utils/imgpath.js';
  import { parsePhoneNumber } from '@/utils/userUtilities.js';
  import UserAvatar from '@/components/views-models/profil/UserAvatar.vue';
  import CompetencesSection from '@/components/views-models/profil/CvTemplatePrintSections/CompetencesSection.vue';
  import LanguesSection from '@/components/views-models/profil/CvTemplatePrintSections/LanguesSection.vue';
  import MobiliteSection from '@/components/views-models/profil/CvTemplatePrintSections/MobiliteSection.vue';
  import ContactSection from '@/components/views-models/profil/CvTemplatePrintSections/ContactSection.vue';
  import ExperiencesSection from '@/components/views-models/profil/CvTemplatePrintSections/ExperiencesSection.vue';
  import EducationSection from '@/components/views-models/profil/CvTemplatePrintSections/EducationSection.vue';
  import AboutSection from '@/components/views-models/profil/CvTemplatePrintSections/AboutSection.vue';

  export default {
    name: 'CvTemplatePrintedVersion',

    props: {
      user: {
        type: Object,
        required: true,
        default: () => ({
          skill: [],
          langue: [],
          permis: [],
          mobilité: [],
          ville: '',
          email: '',
          numberPhone: '',
          experience: [],
          formation: [],
          about: '',
          cvperso: {
            sections_order: [],
            theme: 0,
          },
        }),
      },
      isFriend: {
        type: Boolean,
        default: false,
      },
    },

    components: {
      UserAvatar,
      CompetencesSection,
      LanguesSection,
      MobiliteSection,
      ContactSection,
      ExperiencesSection,
      EducationSection,
      AboutSection,
    },

    data() {
      return {
        themes: {
          0: {
            '--header-bg': '#32394c',
            '--section-h2-color': '#829dbc',
            '--sidebar-bg':
              'linear-gradient(to bottom, rgba(130, 157, 188, 100), rgba(130, 157, 188, 0))',
          },
          1: {
            '--header-bg': '#C2B092',
            '--section-h2-color': '#C2B092',
            '--sidebar-bg':
              'linear-gradient(to bottom, rgba(232, 218, 213, 100), rgba(232, 218, 213, 0))',
          },
          2: {
            '--header-bg': '#668B86',
            '--section-h2-color': '#668B86',
            '--sidebar-bg':
              'linear-gradient(to bottom, rgba(222, 236, 234, 100), rgba(222, 236, 234, 0))',
          },
          3: {
            '--header-bg': '#829DBC',
            '--section-h2-color': '#829DBC',
            '--sidebar-bg':
              'linear-gradient(to bottom, rgba(196, 211, 228, 100), rgba(196, 211, 228, 0))',
          },
          4: {
            '--header-bg': '#94869D',
            '--section-h2-color': '#94869D',
            '--sidebar-bg':
              'linear-gradient(to bottom, rgba(210, 195, 228, 100), rgba(210, 195, 228, 0))',
          },
        },
        sectionsData: {
          competences: {
            title: 'COMPÉTENCES',
            component: 'CompetencesSection',
          },
          langues: { title: 'LANGUES', component: 'LanguesSection' },
          mobilite: { title: 'MOBILITÉ', component: 'MobiliteSection' },
          contact: { title: 'CONTACT', component: 'ContactSection' },
          experience: { title: 'Expériences', component: 'ExperiencesSection' },
          formation: { title: 'Education', component: 'EducationSection' },
          about: { title: 'A propos', component: 'AboutSection' },
        },
        sidebarSections: [],
        mainSections: [],
        defaultCvPerso: {
          theme: 0,
          sections_order: [
            'competences',
            'langues',
            'mobilite',
            'contact',
            'experience',
            'formation',
            'about',
          ],
        },
        cvperso: {},
      };
    },

    mounted() {
      this.initializeCvPerso();
      this.applyUserPreferences();

      // Ajouter un watcher pour isFriend pour réappliquer les préférences si la valeur change
      this.$watch('isFriend', () => {
        this.applyUserPreferences();
      });
    },

    methods: {
      initializeCvPerso() {
        // Vérifie si `user.cvperso` existe, sinon utilise les valeurs par défaut.
        this.cvperso = this.user.cvperso
          ? { ...this.user.cvperso }
          : { ...this.defaultCvPerso };

        // Vérifier `sections_order`
        this.cvperso.sections_order = this.cvperso.sections_order?.length
          ? [...this.cvperso.sections_order]
          : [...this.defaultCvPerso.sections_order];

        // Vérifier `theme`
        this.cvperso.theme =
          typeof this.cvperso.theme === 'number'
            ? this.cvperso.theme
            : this.defaultCvPerso.theme;
      },

      // Applique le thème et l'ordre des sections de l'utilisateur.
      applyUserPreferences() {
        this.applyTheme(this.cvperso.theme);

        // Filtrer les sections de la sidebar en fonction de si l'utilisateur est un ami
        let sidebarSectionsIds = ['competences', 'langues', 'mobilite'];

        // N'inclure la section CONTACT que si l'utilisateur est un ami
        if (this.isFriend) {
          sidebarSectionsIds.push('contact');
        }

        // Filtrer les sections de la sidebar
        const filteredSections = this.cvperso.sections_order.filter((id) => {
          return sidebarSectionsIds.includes(id);
        });

        // Mapper les sections filtrées
        this.sidebarSections = filteredSections.map((id) => {
          return { id, ...this.sectionsData[id] };
        });

        this.mainSections = this.cvperso.sections_order
          .filter((id) => ['experience', 'formation', 'about'].includes(id))
          .map((id) => ({ id, ...this.sectionsData[id] }));
      },

      // Applique le thème sélectionné par l'utilisateur.
      applyTheme(themeId) {
        const root = document.documentElement;
        const colors = this.themes[themeId] || {};

        Object.keys(colors).forEach((key) => {
          root.style.setProperty(key, colors[key]);
        });
      },

      parsePhoneNumber,
      getImgPath,
      clearText(text) {
        return text ? String(text).replace(/[\[\]"]/g, '') : '';
      },
      parseDates(date) {
        return date ? new Date(date).toLocaleDateString('fr-FR') : '';
      },
    },
  };
</script>

<style>
  main {
    background: inherit;
  }

  .cv-container.pdf-mode {
    border-radius: 0 !important;
    height: 1056px !important;
    max-height: 1056px !important;
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
    page-break-before: avoid;
    page-break-after: avoid;
    page-break-inside: avoid;
  }
  .cv-container.pdf-mode .header {
    border-radius: 0 !important;
  }
  .cv-container.pdf-mode .download-button-container {
    display: none !important;
  }

  .cv-container {
    width: 100%;
    height: 1123px; /* Taille standard d'une page A4 */
    margin: 0 auto;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .header {
    width: 100%;
    background: #32394c;
    padding: 2rem 0rem 2rem 0rem;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    border-radius: 10px 10px 0 0;
    position: relative;
  }
  .header h2 {
    color: var(--surface-bg);
  }

  .download-button-container {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 10;
  }

  .download-button {
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }

  .download-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }

  .download-button img {
    width: 24px;
    height: 24px;
    filter: brightness(0) invert(1);
  }
  .metier-title-container {
    text-align: left;
    margin-left: 2rem;
  }
  .header .user-name {
    color: var(--surface-bg);
    font-size: 35px;
    text-transform: uppercase;
    margin-top: 1rem;
    margin-bottom: -0.5rem;
  }
  .header-infos-container {
    display: flex;
    width: 100%;
    gap: 10px;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
  }
  .header-infos-container > div {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .ref-contact-container-mobile {
    display: none;
  }

  .profile-section {
    position: relative;
    display: inline-block;
    flex-direction: column;
    align-items: center;
    margin-left: 2rem;
  }

  .content {
    display: flex;
    flex: 1;
    width: 100%;
    flex-wrap: wrap;
  }

  .sidebar .section,
  .main-content .section {
    margin-bottom: 12mm;
  }

  .main-content {
    width: 70%;
    padding: 6mm 6mm 6mm 10mm;
  }

  .section-title {
    display: flex;
  }

  .title-edit-icon {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
  }

  .drag-handle {
    cursor: grab;
    user-select: none;
    margin: 0 0.75rem 1rem 0;
    opacity: 0; /* Caché par défaut */
    filter: none;
    transition: opacity 0.3s ease-in-out;
  }

  .edit-icon {
    margin: 0 0 1rem 0;
    cursor: pointer;
    filter: none;
  }

  .edit-icon svg path {
    stroke: #32394c;
  }

  .edit-icon-profile {
    margin: 0 0 1rem 0;
    cursor: pointer;
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
    color: #fff;
  }

  .section:hover .drag-handle {
    opacity: 1; /* Devient visible au survol */
  }

  .section {
    margin-bottom: 5mm;
  }

  .section h2 {
    font-size: 2em;
    margin: 0 0 1rem 0;
    color: #829dbc;
  }
  .title-line {
    width: 300px;
    height: 3px;
    margin: 1.5rem -6mm 0 2rem;
    background: linear-gradient(
      to right,
      rgba(50, 57, 76, 0),
      rgba(50, 57, 76, 100)
    );
  }

  .sidebar {
    width: 30%;
    color: #32394c;
    background: linear-gradient(
      to bottom,
      rgba(130, 157, 188, 100),
      rgba(130, 157, 188, 0)
    );
    padding: 6mm 6mm 6mm 10mm;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    height: 100%;
  }

  .sidebar a,
  .ref-contact-container-mobile a {
    color: #32394c;
    text-decoration: none;
    display: inline-block;
    max-width: 80%;
    overflow-wrap: break-word;
    word-break: break-word;
    white-space: normal;
  }
  .sidebar a:hover,
  .ref-contact-container-mobile a:hover {
    opacity: 0.6;
  }

  .sidebar .section h3 {
    font-size: 1.1em;
    margin: 0 0 1rem 0;
    color: #32394c;
  }

  .mobility-info,
  .contact-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .mobility-item-container,
  .contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95em;
  }
  .mobility-item-container p,
  .contact-item p {
    max-width: 80%;
    font-size: 0.95em;
  }

  .skills-list,
  .languages-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .skills-list li,
  .languages-list li {
    margin-bottom: 1mm;
    font-size: 0.95em;
  }

  .experience-item,
  .education-item {
    margin-bottom: 5mm;
  }
  .experience-item h3,
  .education-item h3,
  .experience-item .lieu,
  .education-item .lieu {
    color: #32394c;
  }
  .experience-item .date,
  .education-item .date {
    color: #32394c;
    font-size: 1rem;
  }
  .about-me,
  .experience-item .description,
  .education-item .description {
    color: #626161;
  }
  .experience-item .description {
    margin-top: 2mm;
  }

  .owner-empty-item-message {
    /* color: red; */
    color: var(--text-1) !important;
    border: solid 1px red;
    padding: 5px;
    border-radius: 10px;
    font-size: 0.95em;
    background-color: rgba(248, 141, 141, 0.347);
  }



  .logo-thanks-boss-container {
    position: absolute;
    bottom: 5px;
    left: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
  }
  .logo-thanks-boss-container img {
    width: 30px;
    height: 30px;
  }
  .logo-thanks-boss-container a {
    text-decoration: none;
    font-size: 0.8rem;
  }

  @media print {
    @page {
      size: A4;
      margin: 0; /* Supprime les marges imprimantes */
    }

    body {
      margin: 0;
      padding: 0;
    }

    .cv-container {
      width: 100%;
      height: 1056px !important;
      max-height: 1056px !important;
      overflow: hidden;
      position: relative;
    }

    .cv-container * {
      box-shadow: none !important;
    }

    .download-button-container {
      display: none !important;
    }
  }

  @media (max-width: 768px) {
    .content {
      flex-wrap: wrap;
    }
    .header {
      flex-direction: column;
      align-items: center;
      padding: 2rem;
      margin-left: 0;
    }
    .header h2 {
      font-size: 1.2rem;
    }
    .profile-section {
      margin-left: 0;
    }
    .ref-contact-container-desktop {
      display: none;
    }
    .ref-contact-container-mobile {
      display: block;
      width: 100%;
      padding: 32px;
      background-color: var(--yellow-100);
    }
    aside.sidebar {
      width: 100%;
    }
    .download-button-container {
      top: 10px;
      right: 10px;
    }
    .download-button img {
      width: 20px;
      height: 20px;
    }
  }
</style>
