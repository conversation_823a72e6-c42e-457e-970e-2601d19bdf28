<template>
  <div class="card">
    <div class="card-title">
      <div class="title-actions">
        <h2>Autres liens</h2>
        <div class="actions">
          <img
            v-if="isProfilePage || isRecruiterProfilePage"
            class="edit-icon"
            src="@/assets/icons/edit-icon-new.svg"
            alt="Edit"
            @click="goToSection('my-profile')"
          />
        </div>
      </div>
    </div>

    <div class="link-grid">
      <div class="link-container" v-for="link in links" :key="link">
        <img :src="setLinkIcon(link)" alt="icon du lien" />
        <a
          :href="link"
          class="link-item"
          target="_blank"
          rel="noopener noreferrer"
        >
          {{ getDomainName(link) }}
        </a>
      </div>
    </div>
  </div>
</template>

<script>
  import facebookIcon from '@/assets/icons/facebook-icon.svg';
  import githubIcon from '@/assets/icons/github-icon.svg';
  import instagramIcon from '@/assets/icons/instagram-icon.svg';
  import linkedinIcon from '@/assets/icons/linkedin-icon.svg';
  import tiktokIcon from '@/assets/icons/tiktok-icon.svg';
  import defaultIcon from '@/assets/icons/web-icon.svg';

  export default {
    name: 'MyContactLinks',
    props: {
      links: {
        type: Array,
        required: true,
        default: () => [],
      },
    },
    computed: {
      isProfilePage() {
        return (
          this.$route.path === '/profil' ||
          this.$route.path === '/recruteur/profil'
        );
      },
    },
    methods: {
      // On peut ajouter plusieurs domains et les donner un icon...
      setLinkIcon(link) {
        if (!link || typeof link !== 'string') {
          return defaultIcon;
        }

        const iconMap = {
          'linkedin.com': linkedinIcon,
          'facebook.com': facebookIcon,
          'instagram.com': instagramIcon,
          'tiktok.com': tiktokIcon,
          'github.com': githubIcon,
        };

        for (const domain in iconMap) {
          if (link.includes(domain)) {
            return iconMap[domain];
          }
        }

        return defaultIcon;
      },
      getDomainName(link) {
        try {
          const url = new URL(link); // Crea un objeto URL para el enlace
          return url.hostname; // Retorna el dominio, por ejemplo 'tiktok.com'
        } catch (error) {
          return link; // Si hay un error, devolver el enlace completo
        }
      },
      goToSection(sectionId) {
        this.$router
          .push({
            path: '/profil/edition',
            query: { section: sectionId },
          })
          .then(() => {
            this.$nextTick(() => {
              this.$emit('toggleSection', sectionId); // Ouvre la bonne section

              setTimeout(() => {
                const element = document.getElementById(sectionId);
                if (element) {
                  const navbarHeight =
                    document.querySelector('.navbar')?.offsetHeight || 80;
                  const elementPosition =
                    element.getBoundingClientRect().top + window.scrollY;
                  window.scrollTo({
                    top: elementPosition - navbarHeight - 20,
                    behavior: 'smooth',
                  });
                }
              }, 300); // Attend que le menu s'affiche avant de scroller
            });
          });
      },
    },
  };
</script>

<style scoped>
  .card {
    background-color: var(--white-200);
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .card-title {
    border-bottom: 1.5px solid var(--surface-bg);
    margin-bottom: 10px;
  }

  .title-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  .actions {
    display: flex;
    flex-grow: 1;
    justify-content: space-between;
  }
  .edit-icon {
    margin: 0 0 0 1rem;
    cursor: pointer;
    filter: none;
  }
  .edit-icon svg path {
    stroke: #32394c;
  }
  .edit-icon:hover {
    opacity: 0.6;
  }

  .link-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  .link-container {
    display: flex;
    max-width: 500px;
    overflow: hidden;
  }
  .link-grid a {
    color: black;
  }
  .link-grid img {
    width: 20px;
  }
  .link-item {
    /* color: #000; */
    text-decoration: none;
    padding: 0.75rem;
    border-radius: 4px;
    transition: background-color 0.2s;
  }

  .link-item:hover {
    background-color: #f5f5f5;
  }
  @media screen and (max-width: 500px) {
    .link-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
