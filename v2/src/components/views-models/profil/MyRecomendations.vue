<template>
  <div class="avis-container">
    <header class="avis-header">
      <h2>Recommandations</h2>
      <!-- Bouton pour ouvrir la modal -->
      <button
        v-if="!isUserOwner"
        class="create-recommandation"
        @click="openModal"
      >
        +
      </button>

      <!-- Modal pour créer une recommandation -->
      <CreateRecommandation
        :isVisible="isModalVisible"
        :userId="userId"
        @close="closeModal"
        @review-submitted="handleReviewSubmitted"
      />
    </header>
    <div class="recomendations-list">
      <p class="no-recommendations" v-if="recommendations.length === 0">
        Aucune recommandation trouvée.
      </p>
      <div v-else>
        <div
          v-for="(recommendation, index) in sortedRecommentations"
          :key="index"
          class="recommendation-item"
        >
          <!--<img :src="recomendation.image" alt="User Image" class="user-image" />-->
          <div class="creator-infos">
            <UserAvatar :user="recommendation.creator" :width="40" />
            <p>
              {{ recommendation.creator.first_name }}
              {{ recommendation.creator.last_name }}
            </p>
          </div>

          <div class="recomendation-content">
            <!-- Affichage des étoiles en fonction de la note -->
            <div class="stars-rating">
              <img
                v-for="starIndex in 5"
                :key="starIndex"
                :src="
                  starIndex <= recommendation.note
                    ? require('@/assets/icons/star-filled.svg')
                    : require('@/assets/icons/star-empty.svg')
                "
                alt="Star"
                class="star-icon"
              />
              <span class="date">{{
                formatDate(recommendation.date_creation)
              }}</span>
            </div>
            <p class="comment">{{ recommendation.commentaire }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import CreateRecommandation from '@/components/views-models/profil/CreateRecommandation.vue';
  import UserAvatar from '@/components/views-models/profil/UserAvatar.vue';

  import getImgPath from '@/utils/imgpath.js';
  import {
    getCompanyReviews,
    postReview,
  } from '../../../services/profile.service';

  export default {
    props: {
      user: {
        type: Object,
        required: true,
      },
    },
    components: {
      CreateRecommandation,
      UserAvatar,
    },
    data() {
      return {
        recommendations: [],
        isModalVisible: false,
        isUserOwner: this.user.id === this.$store.getters.getUser.id,
      };
    },
    methods: {
      getImgPath,
      async fetchRecommendations() {
        try {
          this.userId = this.user.id;
          this.recommendations = await getCompanyReviews(this.userId);
          return this.recommendations;
        } catch (error) {
          //console.error('Erreur lors de la récupération des avis:', error);
        }
      },
      handleReviewSubmitted(response) {
        this.recommendations.unshift(response);
      },
      openModal() {
        this.isModalVisible = true;
      },
      closeModal() {
        this.isModalVisible = false;
      },
      getStars(rating) {
        return new Array(Math.min(5, Math.floor(rating))).fill(1);
      },
      getEmptyStars(rating) {
        return new Array(Math.max(0, 5 - Math.floor(rating))).fill(1);
      },
      formatDate(date) {
        const d = new Date(date);
        const day = String(d.getDate()).padStart(2, '0');
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const year = d.getFullYear();
        return `${day}/${month}/${year}`;
      },
    },
    computed: {
      sortedRecommentations() {
        return this.recommendations.slice().sort((a, b) => {
          if (this.sortKey === 'name') {
            return a.title.localeCompare(b.title);
          } else if (this.sortKey === 'date') {
            return new Date(b.date_creation) - new Date(a.date_creation);
          } else if (this.sortKey === 'rating') {
            return b.note - a.note;
          }
        });
      },
    },
    async mounted() {
      await this.fetchRecommendations();
    },
  };
</script>

<style>
  .avis-header {
    display: flex;
    align-items: center;
    gap: 10px;
    justify-content: space-between;
    border-bottom: 1.5px solid var(--surface-bg);
  }
  .avis-container {
    position: relative;
    background: var(--surface-bg-2);
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .create-recommandation {
    background-color: var(--yellow-100);
    color: #000;
    height: 32px;
    width: 32px;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    text-align: center;
    align-items: center;
  }
  .recomendations-list {
    height: 100%;
    overflow-y: auto;
    flex: 1;
  }
  .recomendations-list p.no-recommendations {
    text-align: center;
    margin-top: 20px;
    font-size: 1.2rem;
    color: var(--text-1);
  }

  .recommendation-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding-top: 16px;
    padding-bottom: 24px;
    border-bottom: 1px solid black;
  }

  .recommendation-item:last-child {
    border-bottom: none; /* Pas de séparateur sur le dernier élément */
  }

  .creator-infos {
    display: flex;
    /* width: 72px; */
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-right: 24px;
    gap: 4px;
  }
  .creator-infos p {
    text-wrap: nowrap;
    font-weight: 600;
  }
  .stars-rating {
    display: flex;
    align-items: flex-start;
    margin-bottom: 5px;
  }
  .date {
    margin-left: 10px;
    font-size: 0.9rem;
    color: gray;
  }

  .comment {
    margin: 0;
    text-wrap: balance;
  }
</style>
