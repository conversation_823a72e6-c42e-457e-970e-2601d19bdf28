<template>
  <v-progress-linear
    v-if="progressBar < 100"
    class="custom-progress"
    color="var(--primary-1b)"
    :model-value="progressBar"
    height="35"
    rounded
    ><p>
      Complété à
      {{ progressBar }}%
    </p></v-progress-linear
  >
  <div v-else class="profile-complete-message">
    <p>🎉 Profil complété à 100% ! Félicitations !</p>
  </div>
</template>

<script>
  import {
    getProgressBarCompletion,
    requiredFieldsCandidate,
    requiredFieldsRecruteur,
  } from '@/utils/userUtilities';

  export default {
    name: 'ProgressProfileBar',
    props: {
      user: {
        type: Object,
        required: true,
      },
    },
    data() {
      return {
        progressBar: 0, //  progression of user's profil completion
        requiredFields:
          this.user.type_user === 'recruiter'
            ? requiredFieldsRecruteur
            : requiredFieldsCandidate,
      };
    },
    methods: {},
    mounted() {
      this.progressBar = getProgressBarCompletion(
        this.user,
        this.requiredFields
      );
    },
  };
</script>

<style scoped>
  .custom-progress {
    border-radius: 20px;
  }

  .custom-progress p {
    color: var(--text-1);
    font-weight: 600;
    font-size: 14px;
  }

  .profile-complete-message {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 15px 20px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  }

  .profile-complete-message p {
    margin: 0;
    font-weight: 600;
    font-size: 16px;
  }
</style>
