<template>
  <div v-if="isVisible" class="modal-overlay" @click="closeModal">
    <div class="modal-content" @click.stop>
      <h2>Ajouter une Recommandation</h2>
      <form @submit.prevent="submitReview">
        <!-- Système de notation par étoiles -->
        <div>
          <label for="note">Note</label>
          <div class="star-rating">
            <img
              v-for="index in 5"
              :key="index"
              :src="
                index <= note
                  ? require('@/assets/icons/star-filled.svg')
                  : require('@/assets/icons/star-empty.svg')
              "
              alt="Star"
              class="star"
              @click="setRating(index)"
            />
          </div>
        </div>
        <div class="commentaire">
          <label for="commentaire">Commentaire :</label>
          <textarea v-model="commentaire" required></textarea>
        </div>
        <div class="act-buttons">
          <PrimaryRoundedButton textContent="Envoyer" @click="submitReview" />
          <PrimaryRoundedButton
            textContent="Annuler"
            btnColor="secondary"
            @click="closeModal"
          />
        </div>
      </form>
    </div>
  </div>
</template>

<script>
  import { postReview } from '../../../services/profile.service';
  import PrimaryRoundedButton from '../../buttons/PrimaryRoundedButton.vue';

  export default {
    components: {
      PrimaryRoundedButton,
    },
    props: {
      isVisible: {
        type: Boolean,
        required: true,
      },
      userId: {
        type: Number,
        required: true,
      },
    },
    data() {
      return {
        note: 1,
        commentaire: '',
      };
    },
    methods: {
      async submitReview() {
        try {
          const reviewData = {
            note: this.note,
            commentaire: this.commentaire,
          };
          //console.log(reviewData);

          const response = await postReview(this.userId, reviewData);
          this.$emit('review-submitted', response); // Émet un événement pour informer le parent
          this.closeModal();
        } catch (error) {
          //console.error("Erreur lors de l'ajout de la recommandation :", error);
        }
      },
      closeModal() {
        this.$emit('close'); // Émet un événement pour fermer la modal
      },
      setRating(index) {
        this.note = index;
      },
    },
  };
</script>

<style scoped>
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .modal-content {
    background-color: var(--surface-bg-2);
    padding: 20px;
    border-radius: 8px;
    width: 90%;
  }

  .commentaire {
    display: flex;
    flex-direction: column;
  }

  .act-buttons {
    justify-self: flex-end;
  }

  button {
    margin: 10px;
  }

  textarea {
    background-color: var(--primary-1b2);
  }
</style>
