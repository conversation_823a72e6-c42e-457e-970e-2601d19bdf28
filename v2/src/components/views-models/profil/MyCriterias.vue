<template>
  <section id="my-criterias" class="section-container">
    <!-- container header band -->
    <div
      class="header-container bg-color"
      @click="$emit('toggleSection', 'my-criterias')"
    >
      <h5>
        {{
          user.type_user === 'applicant'
            ? 'Mes critères de recherche'
            : 'Vos critères de recherche'
        }}
        <span v-if="notifications > 0" class="notification-span"
          >{{
            notifications +
            (notifications > 1
              ? ' informations manquantes'
              : ' information manquante')
          }}
        </span>
      </h5>
      <img
        v-if="isPanelOpen"
        src="@/assets/icons/arrow-top.svg"
        alt="flèche en bas"
      />
      <img v-else src="@/assets/icons/arrow-bottom.svg" alt="flèche en bas" />
    </div>

    <!-- container content -->
    <div v-show="isPanelOpen" class="content">
      <div class="job-title-container">
        <div class="input-field">
          <h5>Poste recherché</h5>
          <v-text-field
            v-model="formData.wanted_job"
            label="Saisis l'intitulé du poste recherché"
            type="text"
            :rules="jobTitleRules"
          />
        </div>
      </div>

      <div class="contract-and-remote-options-container">
        <div class="input-field">
          <h5>Contrat</h5>
          <div class="subgrid">
            <CustomCheckbox
              field="contrat"
              :fields="contractOptionsInputList"
              :cValue="formData.contrat"
              @checkbox-stringarray="getFormDatas"
            />
          </div>
        </div>
        <div class="input-field2">
          <h5>Type de travail</h5>
          <CustomCheckbox
            field="teletravail"
            :fields="remoteOptionsInputList"
            :cValue="formData.teletravail"
            @checkbox-stringarray="getFormDatas"
          />
        </div>
      </div>

      <div class="activity-sector-container">
        <h5>Secteur d'activité</h5>
        <v-select
          v-model="formData.secteur"
          label="Choisis ton secteur d'activité professionnelle"
          :items="activitySectorInputList"
          variant="underlined"
          clearable
        ></v-select>
      </div>

      <div class="exp-and-salary-container">
        <div class="input-field">
          <h5>Expérience</h5>
          <div class="subgrid">
            <CustomRadio
              field="experiences"
              :fields="experienceInputList"
              :cValue="formData.experiences"
              @radio-selection="getFormDatas"
            />
          </div>
        </div>
        <div class="input-field-salaire">
          <h5>Salaire brut annuel en euros</h5>
          <v-text-field
            v-model="formData.salaire_souhaite"
            label="Saisis le montant de ton salaire idéal ici"
            type="text"
            :rules="salaryRules"
          />
        </div>
      </div>

      <!-- post most recent uploaded cv to db -->
      <div class="btn-row">
        <PrimaryNormalButton
          textContent="Enregistrer"
          @click="updateUserInfos"
        />
      </div>
    </div>
  </section>
</template>

<script>
  import CustomCheckbox from '@/components/buttons/CustomCheckbox.vue';
  import CustomRadio from '@/components/buttons/CustomRadio.vue';
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import { updateUserCriterias } from '@/services/profile.service';
  import { ACTIVITY_FIELDS } from '@/utils/base/activity_sector.js';
  import { CONTRACT_FIELDS } from '@/utils/base/contract.js';
  import { EXPERIENCE_FIELDS } from '@/utils/base/experience.js';
  import { TELETRAVAIL_FIELDS } from '@/utils/base/teletravail.js';
  import { takeUserNotifications } from '@/utils/userUtilities';
  import {
    validateJobTitleRules,
    validateSalaryRules,
  } from '@/utils/validationRules';

  export default {
    name: 'MyCriterias',
    props: {
      user: {
        type: Object,
        required: true,
      },
      activeSection: String,
    },
    components: {
      PrimaryNormalButton,
      CustomCheckbox,
      CustomRadio,
    },

    computed: {
      isPanelOpen() {
        return this.activeSection === 'my-criterias';
      },
    },

    data() {
      return {
        isPanelOn: false, //  state of panelx
        formData: {}, //  datas sent
        activitySectorInputList: [], //  select options for activity sector input
        remoteOptionsInputList: [], //  select options for remotes options input
        experienceInputList: [], //  select options for experience input
        contractOptionsInputList: [], //  select options for contract input
        notifications: 0,
        /* input rules */
        jobTitleRules: [(v) => validateJobTitleRules(v) || true],
        salaryRules: [(v) => validateSalaryRules(v) || true],
      };
    },

    mounted() {
      this.formData = {
        wanted_job: this.user.wanted_job,
        contrat: this.user.contrat,
        teletravail: this.user.teletravail,
        secteur: this.user.secteur,
        experiences: this.user.experiences,
        salaire_souhaite: this.user.salaire_souhaite,
      };
      this.notifications = takeUserNotifications(this.user, [
        'experiences',
        'salaire_souhaite',
        'wanted_job',
        'contrat',
        'secteur',
        'teletravail',
      ]);
      if (this.notifications > 0) {
        this.togglePanel();
      }
      this.activitySectorInputList = this.generateList(ACTIVITY_FIELDS, 'nom');
      this.remoteOptionsInputList = this.generateList(
        TELETRAVAIL_FIELDS,
        'teletravail'
      );
      this.experienceInputList = this.generateList(
        EXPERIENCE_FIELDS,
        'experience_job'
      );
      this.contractOptionsInputList = this.generateList(
        CONTRACT_FIELDS,
        'contrat'
      );
    },

    methods: {
      //  bind list to variable for input select options
      generateList(objectList, fieldName) {
        let list = [];
        for (let i = 0; i < objectList.length; i++) {
          list.push(objectList[i][fieldName]);
        }
        return list;
      },

      //  toggle section panel visibility
      togglePanel() {
        this.isPanelOn = !this.isPanelOn;
      },

      //  get datas from alert fields and hook them to formData
      getFormDatas(field, datas) {
        this.formData[field] = datas;
      },

      async updateUserInfos() {
        // Vérifier si 'contract' est un tableau et le convertir en chaîne de caractères
        if (Array.isArray(this.formData.contrat)) {
          this.formData.contrat = this.formData.contrat.join(', ');
        }

        if (Array.isArray(this.formData.teletravail)) {
          this.formData.teletravail = this.formData.teletravail.join(', ');
        }
        await updateUserCriterias(this.formData);
        const updatedUser = { ...this.user, ...this.formData };
        this.$store.dispatch('handleUserChange', {
          type: null,
          payload: updatedUser,
        });
        this.notifications = takeUserNotifications(this.formData, [
          'experiences',
          'salaire_souhaite',
          'wanted_job',
          'contrat',
          'secteur',
          'teletravail',
        ]);
      },
    },
  };
</script>

<style scoped>
  /* section container & layout */
  .section-container {
    background-color: var(--white-200);
    width: 100%;
    border-radius: 2px;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid black;
    cursor: pointer;
  }
  .header-container h5 {
    display: flex;
    gap: 10px;
    align-items: center;
  }

  .notification-span {
    background-color: rgba(254, 85, 85, 0.403);
    border: solid 1px red;
    border-radius: 10px;
    padding: 0 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
  }

  .bg-color {
    background-color: rgba(246, 179, 55, 0.2);
    border-bottom: none;
  }

  .content {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: fit-content;
    padding: 24px;
  }

  /* buttons */
  .btn-row {
    display: flex;
    justify-content: end;
  }

  /* inputs */
  .input-field {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .input-field2 {
    display: flex;
    flex-direction: column;
    width: 49%;
  }

  .input-field-salaire {
    width: 49%;
  }

  /* content */
  .job-title-container {
    display: flex;
    width: 100%;
  }

  .contract-and-remote-options-container {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .subgrid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 40px;
    margin-bottom: 30px;
  }

  .activity-sector-container {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .exp-and-salary-container {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  @media screen and (min-width: 992px) {
    .input-field {
      width: 49%;
    }

    .input-field2 {
      width: 40%;
    }

    .contract-and-remote-options-container {
      flex-direction: row;
      justify-content: space-between;
    }

    .exp-and-salary-container {
      flex-direction: row;
      justify-content: space-between;
    }
  }
</style>
