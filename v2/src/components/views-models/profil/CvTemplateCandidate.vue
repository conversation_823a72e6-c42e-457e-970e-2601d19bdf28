<template>
  <div class="color-buttons">
    <button @click="changeTheme(0)">
      <svg
        width="28"
        height="27"
        viewBox="0 0 28 27"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M0 0H27V27H0V0Z" fill="#829DBC" />
        <path
          d="M27.0313 0.0268872L0.0312484 27L0.00347443 0.0278313L27.0313 0.0268872Z"
          fill="#32394C"
        />
      </svg>
    </button>
    <button @click="changeTheme(1)">
      <svg
        width="28"
        height="27"
        viewBox="0 0 28 27"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M0 0H27V27H0V0Z" fill="#E8DAD6" />
        <path
          d="M27.0313 0.0268872L0.0312484 27L0.00347443 0.0278313L27.0313 0.0268872Z"
          fill="#C2B092"
        />
      </svg>
    </button>
    <button @click="changeTheme(2)">
      <svg
        width="28"
        height="27"
        viewBox="0 0 28 27"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M0 0H27V27H0V0Z" fill="#DEECEA" />
        <path
          d="M27.0313 0.0268872L0.0312484 27L0.00347443 0.0278313L27.0313 0.0268872Z"
          fill="#668B86"
        />
      </svg>
    </button>
    <button @click="changeTheme(3)">
      <svg
        width="28"
        height="27"
        viewBox="0 0 28 27"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M4.57466e-06 0H27V27H4.57466e-06V0Z" fill="#C4D3E5" />
        <path
          d="M27.0278 0.0268872L0.027774 27L0 0.0278313L27.0278 0.0268872Z"
          fill="#829DBC"
        />
      </svg>
    </button>
    <button @click="changeTheme(4)">
      <svg
        width="28"
        height="27"
        viewBox="0 0 28 27"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M0.996526 0H27.9965V27H0.996526V0Z" fill="#D2C3E4" />
        <path
          d="M27.9966 0.0268872L0.996526 27L0.96875 0.0278313L27.9966 0.0268872Z"
          fill="#94869D"
        />
      </svg>
    </button>
  </div>

  <!-- <div class="cv-wrapper"> -->
  <div ref="cvContainer" class="cv-container">
    <!-- Header Section -->
    <section class="header">
      <div class="download-button-container">
        <button class="download-button" @click="$emit('download-cv')">
          <v-tooltip location="top" activator="parent">
            <span>Télécharger le CV Thanks-Boss</span>
          </v-tooltip>
          <img src="@/assets/icons/download.svg" alt="download" />
        </button>
      </div>
      <div class="profile-section">
        <UserAvatar :user="user" :width="120" />
        <svg
          class="edit-icon-profile"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          @click="goToSection('my-profile')"
        >
          <path
            d="M13.8031 19.5517H19.8031M4.20312 19.5517L8.56911 18.672C8.80089 18.6253 9.0137 18.5111 9.18084 18.3439L18.9545 8.56486C19.4231 8.096 19.4228 7.33602 18.9538 6.86755L16.8834 4.79948C16.4146 4.33121 15.655 4.33153 15.1866 4.80019L5.41189 14.5802C5.24507 14.7471 5.13117 14.9595 5.08443 15.1908L4.20312 19.5517Z"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
      <div class="metier-title-container">
        <p class="user-name">{{ user.last_name + ' ' + user.first_name }}</p>
        <h2 class="job-title">{{ user.metier }}</h2>
      </div>
    </section>

    <div class="content">
      <!-- Left Sidebar -->
      <aside class="sidebar">
        <draggable
          v-if="sidebarSections.length"
          v-model="sidebarSections"
          group="sections"
          handle=".drag-handle"
          animation="200"
          itemKey="id"
          @update:modelValue="saveCvPerso"
        >
          <template #item="{ element }">
            <section class="section">
              <div class="section-title">
                <img
                  class="drag-handle"
                  src="@/assets/icons/drag-icon.svg"
                  alt="drag"
                />
                <div class="title-edit-icon">
                  <h3>{{ element.title }}</h3>
                  <div class="actions">
                    <img
                      class="edit-icon"
                      src="@/assets/icons/edit-icon-new.svg"
                      alt="Edit"
                      @click="goToSection(element.sectionId)"
                    />
                  </div>
                </div>
              </div>

              <!-- ============== COMPETENCES ============== -->
              <div v-if="element.id === 'competences'">
                <ul
                  v-if="user && user.skill && user.skill.length"
                  class="skills-list"
                >
                  <li v-for="skill in user.skill.slice(0, 5)" :key="skill">
                    • {{ skill.nom }}
                  </li>
                </ul>
                <p v-else :class="{ 'owner-empty-item-message': owner }">
                  {{
                    owner
                      ? user.type_user === 'applicant'
                        ? 'Ajoute tes compétences pour rendre ton profil encore plus attractif pour les recruteurs !'
                        : user.type_user === 'recruiter'
                          ? 'Ajoutez vos compétences !'
                          : 'Aucune compétence spécifiée'
                      : 'Aucune compétence spécifiée'
                  }}
                </p>
              </div>

              <!-- ============== LANGUES ============== -->
              <div v-else-if="element.id === 'langues'">
                <ul
                  v-if="user && user.langue && user.langue.length"
                  class="languages-list"
                >
                  <li
                    v-for="langue in user.langue.slice(0, 3)"
                    :key="langue.langue"
                  >
                    • {{ langue.langue }}
                  </li>
                </ul>
                <p v-else :class="{ 'owner-empty-item-message': owner }">
                  {{
                    owner
                      ? user.type_user === 'applicant'
                        ? 'Ajoute tes langues !'
                        : user.type_user === 'recruiter'
                          ? 'Ajoutez vos langues !'
                          : 'Aucune langue spécifiée'
                      : 'Aucune langue spécifiée'
                  }}
                </p>
              </div>

              <!-- ============== MOBILITÉ ============== -->
              <div v-else-if="element.id === 'mobilite'" class="mobility-info">
                <div class="mobility-item-container">
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M2 12.61H19M19 17.43V10.28C19 7.3 18.24 6.55 15.22 6.55H5.78003C5.50003 6.55 5.24 6.56 5 6.57M19 17.43C18.97 20.28 18.19 21 15.22 21H5.78003C2.76003 21 2 20.25 2 17.27V10.28C2 7.58 2.63 6.71 5 6.57M19 17.43C21.37 17.29 22 16.42 22 13.72V6.73C22 3.75 21.24 3 18.22 3H8.78003C5.81003 3 5.03 3.72 5 6.57M5.25 17.81H6.96997M9.10999 17.81H12.55"
                      stroke="currentColor"
                      stroke-width="1.5"
                      stroke-miterlimit="10"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <p
                    :class="{ 'owner-empty-item-message': owner }"
                    v-if="!user || !user.permis || user.permis.length === 0"
                  >
                    {{
                      owner
                        ? user.type_user === 'applicant'
                          ? 'Ajoute tes permis !'
                          : user.type_user === 'recruiter'
                            ? 'Ajoutez vos permis !'
                            : 'Aucun permis spécifié'
                        : 'Aucun permis spécifié'
                    }}
                  </p>
                  {{ clearText(user.permis) }}
                </div>
                <div class="mobility-item-container">
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M3.99876 7.99996H2.99876M20.9988 7.99996H19.9988M11.9988 2.99996V4.99996M10.4988 4.99996H13.4988M5.99876 15H8.99876M14.9988 15H17.9988M15.5088 2.82996H8.48876C5.99876 2.82996 5.44876 4.06996 5.12876 5.58996L3.99876 11H19.9988L18.8688 5.58996C18.5488 4.06996 17.9988 2.82996 15.5088 2.82996ZM21.9888 19.82C22.0988 20.99 21.1588 22 19.9588 22H18.0788C16.9988 22 16.8488 21.54 16.6588 20.97L16.4588 20.37C16.1788 19.55 15.9988 19 14.5588 19H9.43876C7.99876 19 7.78876 19.62 7.53876 20.37L7.33876 20.97C7.14876 21.54 6.99876 22 5.91876 22H4.03876C2.83876 22 1.89876 20.99 2.00876 19.82L2.56876 13.73C2.70876 12.23 2.99876 11 5.61876 11H18.3788C20.9988 11 21.2888 12.23 21.4288 13.73L21.9888 19.82Z"
                      stroke="currentColor"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <p
                    :class="{ 'owner-empty-item-message': owner }"
                    v-if="!user || !user.mobilité || user.mobilité.length === 0"
                  >
                    {{
                      owner
                        ? user.type_user === 'applicant'
                          ? 'Ajoute tes mobilités !'
                          : user.type_user === 'recruiter'
                            ? 'Ajoutez vos mobilités !'
                            : 'Aucune mobilité spécifiée'
                        : 'Aucune mobilité spécifiée'
                    }}
                  </p>
                  {{ clearText(user.mobilité) }}
                </div>
              </div>

              <!-- ============== CONTACT ============== -->
              <div v-else-if="element.id === 'contact'" class="contact-info">
                <!-- N'afficher la section CONTACT que si l'utilisateur est un ami ou le propriétaire -->
                <div v-if="isFriend || owner">
                  <div class="contact-item">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M11.9978 13.43C13.7209 13.43 15.1178 12.0331 15.1178 10.31C15.1178 8.58687 13.7209 7.19 11.9978 7.19C10.2746 7.19 8.87776 8.58687 8.87776 10.31C8.87776 12.0331 10.2746 13.43 11.9978 13.43Z"
                        stroke="currentColor"
                        stroke-width="1.5"
                      />
                      <path
                        d="M3.61776 8.49C5.58776 -0.169998 18.4178 -0.159997 20.3778 8.5C21.5278 13.58 18.3678 17.88 15.5978 20.54C13.5878 22.48 10.4078 22.48 8.38776 20.54C5.62776 17.88 2.46776 13.57 3.61776 8.49Z"
                        stroke="currentColor"
                        stroke-width="1.5"
                      />
                    </svg>
                    <p
                      :class="{ 'owner-empty-item-message': owner }"
                      v-if="!user || !user.ville || user.ville.length === 0"
                    >
                      {{
                        owner
                          ? user.type_user === 'applicant'
                            ? 'Ajoute ta localisation !'
                            : user.type_user === 'recruiter'
                              ? 'Ajoutez votre localisation !'
                              : 'Aucune localisation spécifiée'
                          : 'Aucune localisation spécifiée'
                      }}
                    </p>
                    {{ user.ville }}
                  </div>

                  <div class="contact-item">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M17 9L13.87 11.5C12.84 12.32 11.15 12.32 10.12 11.5L7 9M17 20.5H7C4 20.5 2 19 2 15.5V8.5C2 5 4 3.5 7 3.5H17C20 3.5 22 5 22 8.5V15.5C22 19 20 20.5 17 20.5Z"
                        stroke="currentColor"
                        stroke-width="1.5"
                        stroke-miterlimit="10"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                    <a
                      v-if="user && user.email && user.email.length"
                      target="_blank"
                      href="mailto:user.email"
                      >{{ user.email }}</a
                    >
                    <p v-else :class="{ 'owner-empty-item-message': owner }">
                      {{
                        owner
                          ? user.type_user === 'applicant'
                            ? 'Ajoute ton email !'
                            : user.type_user === 'recruiter'
                              ? 'Ajoutez votre email !'
                              : 'Aucun mail spécifié'
                          : 'Aucun mail spécifié'
                      }}
                    </p>
                  </div>

                  <div class="contact-item">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M21.97 18.33C21.97 18.69 21.89 19.06 21.72 19.42C21.55 19.78 21.33 20.12 21.04 20.44C20.55 20.98 20.01 21.37 19.4 21.62C18.8 21.87 18.15 22 17.45 22C16.43 22 15.34 21.76 14.19 21.27C13.04 20.78 11.89 20.12 10.75 19.29C9.6 18.45 8.51 17.52 7.47 16.49C6.44 15.45 5.51 14.36 4.68 13.22C3.86 12.08 3.2 10.94 2.72 9.81C2.24 8.67 2 7.58 2 6.54C2 5.86 2.12 5.21 2.36 4.61C2.6 4 2.98 3.44 3.51 2.94C4.15 2.31 4.85 2 5.59 2C5.87 2 6.15 2.06 6.4 2.18C6.66 2.3 6.89 2.48 7.07 2.74L9.39 6.01C9.57 6.26 9.7 6.49 9.79 6.71C9.88 6.92 9.93 7.13 9.93 7.32C9.93 7.56 9.86 7.8 9.72 8.03C9.59 8.26 9.4 8.5 9.16 8.74L8.4 9.53C8.29 9.64 8.24 9.77 8.24 9.93C8.24 10.01 8.25 10.08 8.27 10.16C8.3 10.24 8.33 10.3 8.35 10.36C8.53 10.69 8.84 11.12 9.28 11.64C9.73 12.16 10.21 12.69 10.73 13.22C11.27 13.75 11.79 14.24 12.32 14.69C12.84 15.13 13.27 15.43 13.61 15.61C13.66 15.63 13.72 15.66 13.79 15.69C13.87 15.72 13.95 15.73 14.04 15.73C14.21 15.73 14.34 15.67 14.45 15.56L15.21 14.81C15.46 14.56 15.7 14.37 15.93 14.25C16.16 14.11 16.39 14.04 16.64 14.04C16.83 14.04 17.03 14.08 17.25 14.17C17.47 14.26 17.7 14.39 17.95 14.56L21.26 16.91C21.52 17.09 21.7 17.3 21.81 17.55C21.91 17.8 21.97 18.05 21.97 18.33Z"
                        stroke="currentColor"
                        stroke-width="1.5"
                        stroke-miterlimit="10"
                      />
                    </svg>
                    <p
                      :class="{ 'owner-empty-item-message': owner }"
                      v-if="
                        !user ||
                        !user.numberPhone ||
                        user.numberPhone.length === 0
                      "
                    >
                      {{
                        owner
                          ? user.type_user === 'applicant'
                            ? 'Ajoute ton numéro de téléphone !'
                            : user.type_user === 'recruiter'
                              ? 'Ajoutez votre numéro de téléphone !'
                              : 'Aucun numéro de téléphone spécifié'
                          : 'Aucun numéro de téléphone spécifié'
                      }}
                    </p>
                    {{ parsePhoneNumber(user.numberPhone) }}
                  </div>
                </div>
              </div>
            </section>
          </template>
        </draggable>
      </aside>

      <!-- ===============  MAIN CONTENT (Right side) =============== -->
      <main class="main-content">
        <draggable
          v-if="mainSections.length"
          v-model="mainSections"
          group="sections"
          handle=".drag-handle"
          animation="200"
          itemKey="id"
          @update:modelValue="saveCvPerso"
        >
          <template #item="{ element }">
            <section class="section">
              <div class="section-title">
                <img
                  class="drag-handle"
                  src="@/assets/icons/drag-icon.svg"
                  alt="drag"
                />
                <div class="title-edit-icon">
                  <h2>{{ element.title }}</h2>
                  <div class="actions">
                    <img
                      class="edit-icon"
                      src="@/assets/icons/edit-icon-new.svg"
                      alt="Edit"
                      @click="goToSection(element.sectionId)"
                    />
                  </div>
                </div>
                <div class="title-line"></div>
              </div>

              <!-- ============== EXPERIENCES ============== -->
              <div v-if="element.id === 'experience'">
                <div v-if="user.experience && user.experience.length">
                  <div
                    v-for="job in user.experience.slice(0, 2)"
                    :key="job.position"
                    class="experience-item"
                  >
                    <h3>
                      {{ job.position }}
                      <span class="date">
                        - {{ parseDates(job.debut) }} -
                        {{ job.fin ? parseDates(job.fin) : 'En cours' }}</span
                      >
                    </h3>
                    <p class="lieu">chez {{ job.company }}</p>
                    <p class="description">{{ job.exp_detail }}</p>
                  </div>
                </div>
                <p v-else :class="{ 'owner-empty-item-message': owner }">
                  {{
                    owner
                      ? user.type_user === 'applicant'
                        ? 'Ajoute tes expériences professionnelles pour rendre ton profil encore plus attractif pour les recruteurs !'
                        : user.type_user === 'recruiter'
                          ? 'Ajoutez vos expériences professionnelles !'
                          : 'Aucune expérience spécifiée'
                      : 'Aucune expérience spécifiée'
                  }}
                </p>
              </div>

              <!-- ============== EDUCATION ============== -->
              <div v-else-if="element.id === 'formation'">
                <div v-if="user.formation && user.formation.length">
                  <div
                    v-for="edu in user.formation.slice(0, 2)"
                    :key="edu.titre"
                    class="education-item"
                  >
                    <h3>
                      {{ edu.titre }}
                      <span class="date">
                        - {{ parseDates(edu.obtention) }}</span
                      >
                    </h3>
                    <p class="lieu">à {{ edu.organisme }}</p>
                    <p class="description">{{ edu.etude }}</p>
                  </div>
                </div>
                <p v-else :class="{ 'owner-empty-item-message': owner }">
                  {{
                    owner
                      ? user.type_user === 'applicant'
                        ? 'Ajoute tes études pour rendre ton profil encore plus attractif pour les recruteurs !'
                        : user.type_user === 'recruiter'
                          ? 'Ajoutez vos études professionnelles !'
                          : 'Aucune étude spécifiée'
                      : 'Aucune étude spécifiée'
                  }}
                </p>
              </div>

              <!-- ============== A PROPOS ============== -->
              <div v-else-if="element.id === 'about'" class="about-me">
                <p
                  v-if="!user.about"
                  :class="{ 'owner-empty-item-message': owner }"
                >
                  {{
                    owner
                      ? user.type_user === 'applicant'
                        ? 'Ajoute une brève description de toi pour rendre ton profil encore plus attractif pour les recruteurs !'
                        : user.type_user === 'recruiter'
                          ? 'Ajoutez une brève description de vous !'
                          : 'Aucune description spécifiée'
                      : 'Aucune description spécifiée'
                  }}
                </p>
                <p v-else>{{ user.about }}</p>
              </div>
            </section>
          </template>
        </draggable>
      </main>
    </div>
  </div>
  <!-- </div> -->
</template>

<script>
  import UserAvatar from '@/components/views-models/profil/UserAvatar.vue';
  import getImgPath from '@/utils/imgpath.js';
  import { parsePhoneNumber } from '@/utils/userUtilities.js';
  import draggable from 'vuedraggable';
  import { postCvPerso } from '@/services/profile.service';

  export default {
    name: 'CvTemplateCandidate',

    props: {
      owner: {
        type: Boolean,
        required: false,
      },
      user: {
        type: Object,
        required: true,
        default: () => ({
          skill: [],
          langue: [],
          permis: [],
          mobilité: [],
          ville: '',
          email: '',
          numberPhone: '',
          experience: [],
          formation: [],
          about: '',
          cvperso: {},
        }),
      },
      isFriend: {
        type: Boolean,
        default: false,
      },
    },

    components: {
      UserAvatar,
      draggable,
    },

    data() {
      return {
        themes: {
          0: {
            '--header-bg': '#32394c',
            '--header-user-name': 'var(--surface-bg)',
            '--sidebar-bg':
              'linear-gradient(to bottom, rgba(130, 157, 188, 100), rgba(130, 157, 188, 0))',
            '--section-h3-color': '#32394c',
            '--section-h2-color': '#829dbc',
            '--sidebar-text-color': '#32394c',
            '--experience-title-color': '#32394c',
            '--experience-date-color': '#32394c',
            '--title-line-bg':
              'linear-gradient(to right, rgba(50, 57, 76, 0), rgba(50, 57, 76, 100))',
            '--edit-icon-stroke': '#32394c',
          },
          1: {
            '--header-bg': '#C2B092',
            '--header-user-name': '#392502',
            '--sidebar-bg':
              'linear-gradient(to bottom, rgba(232, 218, 213, 100), rgba(232, 218, 213, 0))',
            '--section-h3-color': '#392502',
            '--section-h2-color': '#C2B092',
            '--sidebar-text-color': '#392502',
            '--experience-title-color': '#392502',
            '--experience-date-color': '#392502',
            '--title-line-bg':
              'linear-gradient(to right, rgba(232, 218, 213, 0), rgba(232, 218, 213, 100))',
            '--edit-icon-stroke': '#C2B092',
          },
          2: {
            '--header-bg': '#668B86',
            '--header-user-name': '',
            '--sidebar-bg':
              'linear-gradient(to bottom, rgba(222, 236, 234, 100), rgba(222, 236, 234, 0))',
            '--section-h3-color': '#26282B',
            '--section-h2-color': '#668B86',
            '--sidebar-text-color': '#26282B',
            '--experience-title-color': '#26282B',
            '--experience-date-color': '#26282B',
            '--title-line-bg':
              'linear-gradient(to right, rgba(222, 236, 234, 0), rgba(222, 236, 234, 100))',
            '--edit-icon-stroke': '#26282B',
          },
          3: {
            '--header-bg': '#829DBC',
            '--header-user-name': '',
            '--sidebar-bg':
              'linear-gradient(to bottom, rgba(196, 211, 228, 100), rgba(196, 211, 228, 0))',
            '--section-h3-color': '#32394C',
            '--section-h2-color': '#829DBC',
            '--sidebar-text-color': '#32394C',
            '--experience-title-color': '#32394C',
            '--experience-date-color': '#32394C',
            '--title-line-bg':
              'linear-gradient(to right, rgba(196, 211, 228, 0), rgba(196, 211, 228, 100))',
            '--edit-icon-stroke': '#32394C',
          },
          4: {
            '--header-bg': '#94869D',
            '--header-user-name': '',
            '--sidebar-bg':
              'linear-gradient(to bottom, rgba(210, 195, 228, 100), rgba(210, 195, 228, 0))',
            '--section-h3-color': '#1A0E28',
            '--section-h2-color': '#94869D',
            '--sidebar-text-color': '#1A0E28',
            '--experience-title-color': '#1A0E28',
            '--experience-date-color': '#1A0E28',
            '--title-line-bg':
              'linear-gradient(to right, rgba(210, 195, 228, 0), rgba(210, 195, 228, 100))',
            '--edit-icon-stroke': '#1A0E28',
          },
        },
        selectedTheme: 0,
        sidebarSections: [
          {
            id: 'competences',
            title: 'COMPETENCES',
            sectionId: 'my-competences',
          },
          { id: 'langues', title: 'LANGUES', sectionId: 'my-competences' },
          { id: 'mobilite', title: 'MOBILITÉ', sectionId: 'my-competences' },
          { id: 'contact', title: 'CONTACT', sectionId: 'my-profile' },
        ],
        mainSections: [
          {
            id: 'experience',
            title: 'Expériences',
            sectionId: 'my-experiences',
          },
          { id: 'formation', title: 'Educations', sectionId: 'my-formations' },
          { id: 'about', title: 'A propos', sectionId: 'my-profile' },
        ],
        defaultCvPerso: {
          theme: 0,
          sections_order: [
            'competences',
            'langues',
            'mobilite',
            'contact',
            'experience',
            'formation',
            'about',
          ],
        },
      };
    },

    mounted() {
      this.loadCvPerso();

      // Ajouter un watcher pour isFriend pour réappliquer les préférences si la valeur change
      this.$watch('isFriend', () => {
        this.applySectionsOrder();
      });
    },

    methods: {
      loadCvPerso() {
        // Récupérer les préférences du localStorage ou de user.cvperso
        const storedData = JSON.parse(localStorage.getItem('cvPerso'));

        this.cvperso = storedData || { ...this.defaultCvPerso };

        if (
          !this.cvperso.sections_order ||
          !this.cvperso.sections_order.length
        ) {
          this.cvperso.sections_order = [...this.defaultCvPerso.sections_order];
        }

        if (typeof this.cvperso.theme !== 'number') {
          this.cvperso.theme = this.defaultCvPerso.theme;
        }

        this.applySectionsOrder();
        this.applyTheme();
      },

      applySectionsOrder() {
        const sectionMapping = {
          competences: 'my-competences',
          langues: 'my-competences',
          mobilite: 'my-competences',
          contact: 'my-profile',
          experience: 'my-experiences',
          formation: 'my-formations',
          about: 'my-profile',
        };

        const sectionTitles = {
          competences: 'COMPETENCES',
          langues: 'LANGUES',
          mobilite: 'MOBILITÉ',
          contact: 'CONTACT',
          experience: 'Expériences',
          formation: 'Education',
          about: 'A propos',
        };

        // Filtrer les sections de la sidebar en fonction de si l'utilisateur est un ami
        let sidebarSectionsIds = ['competences', 'langues', 'mobilite'];

        // N'inclure la section CONTACT que si l'utilisateur est un ami ou si c'est le propriétaire du profil
        if (this.isFriend || this.owner) {
          sidebarSectionsIds.push('contact');
        }

        // Filtrer les sections de la sidebar
        const filteredSections = this.cvperso.sections_order.filter((id) => {
          return sidebarSectionsIds.includes(id);
        });

        // Mapper les sections filtrées
        this.sidebarSections = filteredSections.map((id) => {
          return {
            id,
            title: sectionTitles[id],
            sectionId: sectionMapping[id],
          };
        });

        this.mainSections = this.cvperso.sections_order
          .filter((id) => ['experience', 'formation', 'about'].includes(id))
          .map((id) => ({
            id,
            title: sectionTitles[id],
            sectionId: sectionMapping[id],
          }));
      },

      applyTheme() {
        const theme = this.themes[this.cvperso.theme] || this.themes[0];

        Object.keys(theme).forEach((key) => {
          document.documentElement.style.setProperty(key, theme[key]);
        });
      },

      async saveCvPerso() {
        const sectionsOrder = [
          ...this.sidebarSections.map((s) => s.id),
          ...this.mainSections.map((s) => s.id),
        ];

        this.cvperso.sections_order = sectionsOrder;

        localStorage.setItem('cvPerso', JSON.stringify(this.cvperso));

        try {
          await postCvPerso(sectionsOrder, this.cvperso.theme);
        } catch (error) {
          //console.error('Erreur de mise à jour du cvPerso', error);
        }
      },

      changeTheme(themeId) {
        if (themeId < 0 || themeId > 4) return;

        this.cvperso.theme = themeId;
        this.applyTheme();
        this.saveCvPerso();
      },

      goToSection(sectionId) {
        this.$router
          .push({
            path: '/profil/edition',
            query: { section: sectionId },
          })
          .then(() => {
            this.$nextTick(() => {
              this.$emit('toggleSection', sectionId); // Ouvre la bonne section

              setTimeout(() => {
                const element = document.getElementById(sectionId);
                if (element) {
                  const navbarHeight =
                    document.querySelector('.navbar')?.offsetHeight || 80;
                  const elementPosition =
                    element.getBoundingClientRect().top + window.scrollY;
                  window.scrollTo({
                    top: elementPosition - navbarHeight - 20,
                    behavior: 'smooth',
                  });
                }
              }, 300); // Attend que le menu s'affiche avant de scroller
            });
          });
      },

      parsePhoneNumber,
      getImgPath,
      clearText(text) {
        return text ? String(text).replace(/[\[\]"]/g, '') : '';
      },
      parseDates(date) {
        return date ? new Date(date).toLocaleDateString('fr-FR') : '';
      },
    },
  };
</script>

<style scoped>
  main {
    background: inherit;
  }

  .color-buttons {
    display: flex;
    gap: 20px;
    margin-bottom: -40px;
  }
  .color-buttons button:hover {
    opacity: 0.6;
  }

  .btn-wrapper {
    width: fit-content;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
  .btn-wrapper button {
    width: fit-content;
  }

  .cv-container.pdf-mode {
    border-radius: 0 !important;
    height: 1056px !important;
    max-height: 1056px !important;
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
    page-break-before: avoid;
    page-break-after: avoid;
    page-break-inside: avoid;
  }
  .cv-container.pdf-mode .header {
    border-radius: 0 !important;
  }
  .cv-container.pdf-mode .download-button-container {
    display: none !important;
  }

  .cv-container {
    width: 100%;
    height: 1123px; /* Taille standard d'une page A4 */
    margin: 0 auto;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    overflow: hidden;
  }

  .header {
    width: 100%;
    background: var(--header-bg, #32394c);
    padding: 2rem 0rem 2rem 0rem;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    border-radius: 10px 10px 0 0;
    position: relative;
  }
  .header h2 {
    color: var(--surface-bg);
  }

  .download-button-container {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 10;
  }

  .download-button {
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }

  .download-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }

  .download-button img {
    width: 24px;
    height: 24px;
    filter: brightness(0) invert(1);
  }
  .metier-title-container {
    text-align: left;
    margin-left: 2rem;
  }
  .header .user-name {
    color: var(--header-user-name, var(--surface-bg));
    font-size: 35px;
    text-transform: uppercase;
    margin-top: 1rem;
    margin-bottom: -0.5rem;
  }
  .header-infos-container {
    display: flex;
    width: 100%;
    gap: 10px;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
  }
  .header-infos-container > div {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .ref-contact-container-mobile {
    display: none;
  }

  .profile-section {
    position: relative;
    display: inline-block;
    flex-direction: column;
    align-items: center;
    margin-left: 2rem;
  }

  .content {
    display: flex;
    width: 100%;
    flex-wrap: wrap;
  }

  .sidebar .section,
  .main-content .section {
    margin-bottom: 12mm;
  }

  .main-content {
    width: 70%;
    padding: 6mm 6mm 6mm 10mm;
  }

  .section-title {
    display: flex;
    margin-left: -2rem;
  }

  .title-edit-icon {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
  }

  .drag-handle {
    cursor: grab;
    user-select: none;
    margin: 0 0.75rem 1rem 0;
    opacity: 0; /* Caché par défaut */
    filter: none;
    transition: opacity 0.3s ease-in-out;
  }

  .edit-icon {
    margin: 0 0 1rem 0;
    cursor: pointer;
    filter: none;
  }
  .edit-icon:hover {
    opacity: 0.6;
  }

  .edit-icon svg path {
    stroke: var(--edit-icon-stroke, #32394c);
  }

  .edit-icon-profile {
    margin: 0 0 1rem 0;
    cursor: pointer;
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
    color: #fff;
  }
  .edit-icon-profile:hover {
    opacity: 0.6;
  }

  .section:hover .drag-handle {
    opacity: 1; /* Devient visible au survol */
  }

  .section {
    margin-bottom: 5mm;
  }

  .section h2 {
    font-size: 2em;
    margin: 0 0 1rem 0;
    color: var(--section-h2-color, #829dbc);
  }
  .title-line {
    width: 300px;
    height: 3px;
    margin: 1.5rem -6mm 0 2rem;
    background: var(
      --title-line-bg,
      linear-gradient(to right, rgba(50, 57, 76, 0), rgba(50, 57, 76, 100))
    );
  }

  .sidebar {
    width: 30%;
    color: var(--sidebar-text-color, #32394c);
    background: var(
      --sidebar-bg,
      linear-gradient(
        to bottom,
        rgba(130, 157, 188, 100),
        rgba(130, 157, 188, 0)
      )
    );
    padding: 6mm 6mm 6mm 10mm;
  }

  .sidebar a,
  .ref-contact-container-mobile a {
    color: var(--sidebar-text-color, #32394c);
    text-decoration: none;
    display: inline-block;
    max-width: 80%;
    overflow-wrap: break-word;
    word-break: break-word;
    white-space: normal;
  }
  .sidebar a:hover,
  .ref-contact-container-mobile a:hover {
    opacity: 0.6;
  }

  .sidebar .section h3 {
    font-size: 1.1em;
    margin: 0 0 1rem 0;
    color: var(--section-h3-color, #32394c);
  }

  .mobility-info,
  .contact-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .mobility-item-container,
  .contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95em;
  }

  .skills-list,
  .languages-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .skills-list li,
  .languages-list li {
    margin-bottom: 1mm;
    font-size: 0.95em;
  }

  .experience-item,
  .education-item {
    margin-bottom: 5mm;
  }
  .experience-item h3,
  .education-item h3,
  .experience-item .lieu,
  .education-item .lieu {
    color: var(--experience-title-color, #32394c);
  }
  .experience-item .date,
  .education-item .date {
    color: var(--experience-date-color, #32394c);
    font-size: 1rem;
  }
  .about-me p,
  .experience-item .description,
  .education-item .description {
    color: #626161;
  }
  .experience-item .description {
    margin-top: 2mm;
  }

  .owner-empty-item-message {
    /* color: red; */
    color: var(--text-1) !important;
    border: solid 1px red;
    padding: 5px;
    border-radius: 10px;
    font-size: 0.95em;
    background-color: rgba(248, 141, 141, 0.347);
    width: fit-content;
  }



  @media print {
    @page {
      size: A4;
      margin: 0; /* Supprime les marges imprimantes */
    }

    body {
      margin: 0;
      padding: 0;
    }

    .cv-container {
      width: 100%;
      height: 1056px !important;
      max-height: 1056px !important;
      overflow: hidden;
      position: relative;
    }

    .cv-container * {
      box-shadow: none !important;
    }

    .download-button-container {
      display: none !important;
    }

    .edit-icon-profile {
      display: none !important;
    }
  }

  @media (max-width: 768px) {
    .content {
      flex-wrap: wrap;
    }
    .header {
      flex-direction: column;
      align-items: center;
      padding: 2rem;
      margin-left: 0;
    }
    .header h2 {
      font-size: 1.2rem;
    }
    .profile-section {
      margin-left: 0;
    }
    .ref-contact-container-desktop {
      display: none;
    }
    .ref-contact-container-mobile {
      display: block;
      width: 100%;
      padding: 32px;
      background-color: var(--yellow-100);
    }
    aside.sidebar {
      width: 100%;
    }
    .download-button-container {
      top: 10px;
      right: 10px;
    }
    .download-button img {
      width: 20px;
      height: 20px;
    }
  }
</style>
