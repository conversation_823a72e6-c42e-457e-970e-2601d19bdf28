<template>
  <section id="my-experiences" class="section-container">
    <!-- container header band -->
    <div
      class="header-container bg-color"
      @click="$emit('toggleSection', 'my-experiences')"
    >
      <h5>
        {{
          user.type_user === 'applicant' ? 'Mes expériences' : 'Vos expériences'
        }}
        <span v-if="notifications > 0" class="notification-span"
          >{{
            user.type_user === 'applicant'
              ? 'Ajoute tes expériences professionnelles !'
              : 'Ajoutez vos expériences professionnelles !'
          }}
        </span>
      </h5>
      <img
        v-if="isPanelOpen"
        src="@/assets/icons/arrow-top.svg"
        alt="flèche en bas"
      />
      <img v-else src="@/assets/icons/arrow-bottom.svg" alt="flèche en bas" />
    </div>

    <!-- container content -->
    <div v-show="isPanelOpen" class="content">
      <!-- form to add experience -->
      <v-form
        v-if="isExperienceInStaging"
        class="formations-container"
        ref="form"
      >
        <div class="title-date">
          <div class="input-field">
            <h5>Titre du poste</h5>
            <v-text-field
              v-model="newExperience.position"
              label="Saisis l'intitulé du poste"
              type="text"
              :rules="stringRules"
              fast-fail
            />
          </div>
          <div class="input-field">
            <h5>Dates</h5>

            <div class="date-field">
              <label for="date-debut">De</label>
              <input
                type="date"
                v-model="newExperience.debut"
                class="date-input-field"
                id="date-debut"
                name="date-debut"
              />

              <label for="date-fin">à</label>
              <input
                type="date"
                v-model="newExperience.fin"
                :disabled="newExperience.isOngoing"
                class="date-input-field"
                id="date-fin"
                name="date-fin"
              />

              <!-- Checkbox "En cours" -->
              <label>
                <input
                  type="checkbox"
                  v-model="newExperience.isOngoing"
                  @change="handleOngoingChange"
                />
                En cours
              </label>
            </div>
          </div>
        </div>

        <div class="company-location">
          <div class="input-field">
            <h5>Entreprise</h5>
            <v-text-field
              v-model="newExperience.company"
              label="Saisis le nom de l'entreprise"
              type="text"
              :rules="stringRules"
              fast-fail
            />
          </div>
          <div class="input-field">
            <h5>Lieu</h5>
            <v-text-field
              v-model="newExperience.place"
              label="Saisis la localisation de l'entreprise"
              type="text"
              :rules="stringRules"
              fast-fail
            />
          </div>
        </div>

        <div>
          <h5>Détails</h5>
          <v-text-field
            v-model="newExperience.exp_detail"
            label="Description de votre expérience"
            type="text"
            :rules="largeStringRules"
            fast-fail
          />
        </div>

        <!-- update user -->
        <div class="btn-row">
          <PrimaryNormalButton
            textContent="Enregistrer"
            @click="postThisExperience"
          />
        </div>
      </v-form>

      <!-- btn: add an experience -->
      <div v-else class="add-btn">
        <PrimaryNormalButton
          textContent="Ajouter une expérience"
          btnColor="secondary"
          @click="addExperience"
          add
        />
      </div>

      <!-- list of all experience -->
      <div
        v-for="(experience, index) in experienceList"
        :key="experience.updated_at"
        class="formations-container"
      >
        <div v-if="newExperience.id !== experience.id">
          <div class="title-date">
            <div class="input-field">
              <h5>Titre du poste</h5>
              <v-text-field
                v-model="experience.position"
                type="text"
                disabled
              />
            </div>
            <div class="input-field">
              <h5>Dates</h5>

              <div class="date-field">
                <label for="date-debut">De</label>
                <input
                  type="date"
                  v-model="experience.debut"
                  class="date-input-field"
                  disabled
                />

                <label v-if="experience.fin" for="date-fin">à</label>
                <input
                  type="date"
                  v-model="experience.fin"
                  class="date-input-field"
                  disabled
                  v-if="experience.fin"
                />
                <label v-else for="date-fin">à aujourd'hui</label>
              </div>
            </div>
          </div>

          <div class="company-location">
            <div class="input-field">
              <h5>Entreprise</h5>
              <v-text-field v-model="experience.company" type="text" disabled />
            </div>
            <div class="input-field">
              <h5>Lieu</h5>
              <v-text-field v-model="experience.place" type="text" disabled />
            </div>
          </div>

          <div>
            <h5>Détails</h5>
            <v-text-field
              v-model="experience.exp_detail"
              type="text"
              disabled
            />
          </div>

          <div class="btn-row">
            <PrimaryNormalButton
              textContent="Modifier"
              btnColor="primary"
              @click="editExperience(experience)"
            />
            <v-btn
              flat
              class="custom-btn"
              append-icon="mdi-trash-can-outline"
              @click="deleteThisExperience(experience.id, index)"
            >
              Supprimer
            </v-btn>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import {
    deleteExperience,
    getExperienceList,
    postExperience,
  } from '@/services/profile.service.js';
  import { toaster } from '@/utils/toast/toast.js';
  import {
    validateLargeStringRules,
    validateStringRules,
  } from '../../../utils/validationRules';

  export default {
    name: 'MyCompetences',

    components: {
      PrimaryNormalButton,
    },
    props: {
      user: {
        type: Object,
        required: true,
      },
      activeSection: String,
    },
    computed: {
      isPanelOpen() {
        return this.activeSection === 'my-experiences';
      },
    },

    data() {
      return {
        notifications: 0,
        isPanelOn: false,
        experienceList: [],
        isExperienceInStaging: false,
        newExperience: {
          id: null,
          position: '', // titre du poste
          company: '',
          place: '', // localisation
          debut: '', // date de début
          fin: '', // date de fin
          isOngoing: false,
          exp_detail: '',
        },

        /* input rules */
        stringRules: [(v) => validateStringRules(v) || true],

        largeStringRules: [(v) => validateLargeStringRules(v) || true],
      };
    },

    async mounted() {
      try {
        const experienceListData = await getExperienceList({
          timestamp: new Date().getTime(),
        });
        if (experienceListData.length <= 0) {
          this.notifications = 1;
          this.togglePanel();
        }

        this.experienceList = experienceListData.map((exp) => ({
          id: exp.id,
          created_at: exp.created_at,
          updated_at: exp.updated_at,
          position: exp.position,
          company: exp.company,
          place: exp.place,
          debut: exp.debut,
          fin: exp.fin,
          exp_detail: exp.exp_detail,
          user: exp.user,
          resume: exp.resume,
        }));

        this.$emit('progress-bar', this.calculateCompletion());
      } catch (error) {
        //console.error(
        //  'Erreur lors de la récupération des experiences :',
        //  error
        //);
        toaster.showInfoPopup('Impossible de récupérer les experiences.');
      }
    },

    methods: {
      //  toggle section panel visibility
      togglePanel() {
        this.isPanelOn = !this.isPanelOn;
      },

      calculateCompletion() {
        let completedFields = 0;
        this.experienceList.forEach((exp) => {
          if (exp.position) completedFields++;
          if (exp.company) completedFields++;
          if (exp.place) completedFields++;
          if (exp.debut) completedFields++;
          if (exp.fin || exp.isOngoing) completedFields++;
          if (exp.exp_detail) completedFields++;
        });
        return completedFields;
      },

      handleOngoingChange() {
        if (this.newExperience.isOngoing) {
          this.newExperience.fin = null; // Efface la date de fin si "En cours" est sélectionné
        }
      },

      validateDates() {
        const today = new Date();
        const startDate = new Date(this.newExperience.debut);
        const endDate = this.newExperience.fin
          ? new Date(this.newExperience.fin)
          : null;

        if (startDate > today) {
          toaster.showInfoPopup(
            'La date de début ne peut pas être dans le futur.'
          );
          return false;
        }

        if (!this.newExperience.isOngoing && endDate) {
          if (endDate > today) {
            toaster.showInfoPopup(
              'La date de fin ne peut pas être dans le futur.'
            );
            return false;
          }
          if (startDate > endDate) {
            toaster.showInfoPopup(
              'La date de début doit être antérieure à la date de fin.'
            );
            return false;
          }
        }

        return true;
      },

      // Reset the formation form
      resetExperienceForm() {
        this.newExperience = {
          id: null,
          position: '', // titre du poste
          company: '',
          place: '', // localisation
          debut: '', // date de début
          fin: '', // date de fin
          isOngoing: false,
          exp_detail: '', // détails de l'expérience
        };
        this.isExperienceInStaging = false;
      },

      editExperience(experience) {
        if (this.isExperienceInStaging) {
          console.warn('Modification déjà en cours');
          toaster.showInfoPopup(
            "Veuillez terminer la modification en cours avant d'en commencer une autre."
          );
          return;
        }
        this.isExperienceInStaging = true;

        this.newExperience = {
          id: experience.id,
          position: experience.position, // titre du poste
          company: experience.company,
          place: experience.place, // localisation
          debut: experience.debut, // date de début
          fin: experience.fin, // date de fin
          exp_detail: experience.exp_detail, // détails de l'expérience
        };
      },

      async postThisExperience() {
        if (!this.validateDates()) return;
        if (!(await this.formValidation())) return;

        const experienceToSave = {
          id: this.newExperience.id,
          position: this.newExperience.position,
          company: this.newExperience.company,
          place: this.newExperience.place,
          debut: this.newExperience.debut,
          fin: this.newExperience.isOngoing ? null : this.newExperience.fin,
          exp_detail: this.newExperience.exp_detail,
        };
        const updateUserInformationsSuccess =
          await postExperience(experienceToSave);

        if (updateUserInformationsSuccess) {
          //console.log('Réponse du serveur :', updateUserInformationsSuccess);

          if (this.newExperience.id) {
            const index = this.experienceList.findIndex(
              (exp) => exp.id === this.newExperience.id
            );
            if (index !== -1) {
              this.$set(this.experienceList, index, {
                ...experienceToSave,
                updated_at: new Date().toISOString(),
              });
            }
          } else {
            this.experienceList.push({
              ...updateUserInformationsSuccess,
              updated_at: new Date().toISOString(),
            });
          }

          this.resetExperienceForm();
          this.$emit('progress-bar', this.calculateCompletion());
          this.notifications = this.experienceList.length > 0 ? 0 : 1;
        } else {
          toaster.showInfoPopup(
            "Erreur lors de l'ajout ou de la mise à jour de l'expérience."
          );
        }
      },
      // Méthode pour rafraîchir la liste
      async refreshExperienceList() {
        try {
          const experienceListData = await getExperienceList({
            timestamp: new Date().getTime(),
          });
          this.experienceList = experienceListData.map((exp) => ({
            id: exp.id,
            created_at: exp.created_at,
            updated_at: exp.updated_at,
            position: exp.position,
            company: exp.company,
            place: exp.place,
            debut: exp.debut,
            fin: exp.fin,
            exp_detail: exp.exp_detail,
            user: exp.user,
            resume: exp.resume,
          }));
        } catch (error) {
          //console.error(
          //  'Erreur lors de la mise à jour des expériences :',
          //  error
          //);
          toaster.showInfoPopup('Impossible de mettre à jour les expériences.');
        }
      },

      addExperience() {
        if (this.isExperienceInStaging)
          return toaster.showInfoPopup(
            "Veuillez enregistrer l'expérience en cours."
          );
        this.isExperienceInStaging = true;
        this.newExperience = {
          id: null,
          position: '', // titre du poste
          company: '',
          place: '', // localisation
          debut: '', // date de début
          fin: '', // date de fin
          exp_detail: '', // détails de l'expérience
        };
        this.$emit('progress-bar', this.calculateCompletion());
        if (this.experienceList.length <= 0) {
          this.notifications = 1;
        } else {
          this.notifications = 0;
        }
      },

      async deleteThisExperience(experienceId, experienceIndex) {
        const deleteSuccess = await deleteExperience(experienceId);
        if (deleteSuccess) {
          this.experienceList.splice(experienceIndex, 1);
          this.$emit('progress-bar', this.calculateCompletion());
          if (this.experienceList.length <= 0) {
            this.notifications = 1;
          }
        }
      },

      async formValidation() {
        if (this.newExperience.debut == null) {
          return toaster.showInfoPopup(
            'Vous devez indiquer des dates pour valider cette expérience.'
          );
        }

        const validate = await this.$refs.form.validate();

        if (validate.valid) return true;
        else {
          toaster.showInfoPopup(
            'Les informations sur le formulaire sont incomplètes.'
          );
          return false;
        }
      },
    },
  };
</script>

<style scoped>
  .section-container {
    background-color: var(--white-200);
    width: 100%;
    border-radius: 2px;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid black;
    cursor: pointer;
  }
  .header-container h5 {
    display: flex;
    gap: 10px;
    align-items: center;
  }

  .notification-span {
    background-color: rgba(254, 85, 85, 0.403);
    border: solid 1px red;
    border-radius: 10px;
    padding: 0 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
  }
  .bg-color {
    background-color: var(--secondary-2b2);
    border-bottom: none;
  }

  .content {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: fit-content;
    padding: 24px;
  }

  /* buttons */
  .btn-row {
    display: flex;
    justify-content: end;
    gap: 5px;
  }

  .custom-btn {
    border-radius: 5px;
    border: 1px solid rgba(246, 179, 55, 1);
    height: 40px;
  }

  /* inputs */
  .input-field,
  .input-field2 {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .date-input-field {
    width: fit-content;
  }

  /* content */
  .formations-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .title-date {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 10px;
  }

  .company-location {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 10px;
  }

  .date-field {
    display: flex;
    align-items: end;
    gap: 10px;
  }

  .delete-btn {
    display: flex;
    justify-content: end;
  }
  @media screen and (max-width: 778px) {
    .date-field {
      display: flex;
      align-items: flex-start;
      gap: 10px;
      flex-direction: column;
    }
  }
  @media screen and (min-width: 992px) {
    .input-field {
      width: 49%;
    }

    .input-field2 {
      width: 40%;
    }

    .title-date {
      flex-direction: row;
      justify-content: space-between;
      gap: 0px;
    }

    .company-location {
      flex-direction: row;
      justify-content: space-between;
      gap: 10px;
    }

    .date-field {
      padding-top: 10px;
    }
  }
</style>
