<!-- Tabs Component -->
<template>
    <div class="container tabs-container">
      <!-- Tabs -->
      <div class="tabs-toggle-container">
        <v-btn-toggle v-model="selectedTab" class="tabs-toggle" rounded="0" color="transparent" group mandatory>
          <v-btn value="all" :ripple="false" @click="selectTab('Tous')">Tous</v-btn>
          <v-btn value="published" :ripple="false" @click="selectTab('Posts publiés')">Posts publiés</v-btn>
          <v-btn value="comments" :ripple="false" @click="selectTab('Commentaires')">Commentaires</v-btn>
          <v-btn value="shares" :ripple="false" @click="selectTab('Partages')">Partages</v-btn>
          <v-btn value="likes" :ripple="false" @click="selectTab('Likes')">Likes</v-btn>
        </v-btn-toggle>
      </div>
    </div>
  </template>

  <script>
  export default {
    name: 'Tabs',
    data() {
      return {
        selectedTab: 'Tous',
      };
    },
    methods: {
      selectTab(tab) {
        this.selectedTab = tab;
        this.$emit('tab-selected', tab); // Emit an event to the parent component
      },
    },
  };
  </script>


  <style scoped>
  /* Main container */
  .tabs-container {
    width: 100%;
    margin-top: 10px;
  }

  /* Tab toggle container */
  .tabs-toggle-container {
    width: 100%;
    margin-bottom: 20px;
    overflow: hidden;
  }

  .tabs-toggle {
    width: 100%;
    display: flex;
  }

  .tabs-toggle button {
    flex: 1;
    height: 48px;
    border-bottom: 1px solid var(--surface-bg-4);
    color: var(--text-3);
    background-color: transparent;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    border-bottom: 1px solid var(--surface-bg-4);
  }

  .tabs-toggle button:hover {
    color: var(--text-1) !important;
    background-color: var(--surface-bg-4);
    border: 1px solid var(--gray-100);
    border-bottom: 1px solid var(--surface-bg-4);
    cursor: pointer;
  }

  .tabs-toggle button.v-btn--active {
    border: 1px solid var(--primary-1);
    border-bottom: 2px solid var(--primary-1);
  }

  .tabs-toggle-container .v-btn__content {
  justify-content: center;
  white-space: normal !important;
}

  @media (max-width: 480px) {
    .tabs-container {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    width: 100%;
    margin-top: 30px;
  }

  .tabs-toggle button {
    font-size: 14px;
    padding: 8px;
  }
}


  </style>
