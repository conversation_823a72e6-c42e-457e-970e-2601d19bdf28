<template>
  <div class="calendar-container">
    <!-- Header with Month Navigation -->
    <div class="calendar-header">
      <div v-if="viewMode === 'week'" class="month-navigation">
        <button @click="resetToMonthView" class="nav-button">
          <i class="fas fa-chevron-left"></i>
        </button>
        <div class="date-range">
          {{ formattedMonth }}
        </div>
      </div>
      <template v-else>
        <button @click="previous" class="nav-button">
          <i class="fas fa-chevron-left"></i>
        </button>
        <div class="date-range">
          {{ formattedMonth }}
        </div>
        <button @click="next" class="nav-button">
          <i class="fas fa-chevron-right"></i>
        </button>
      </template>
    </div>

    <!-- Weekday Headers -->
    <div class="weekdays-header">
      <div v-for="day in dayShortNames" :key="day" class="weekday">
        {{ day }}
      </div>
    </div>

    <!-- MONTH VIEW -->
    <div class="month-grid" v-if="viewMode === 'month'">
      <div
        v-for="(day, index) in monthDays"
        :key="index"
        class="day-box"
        @click="selectDay(day.formatted)"
      >
        <div
          v-if="day.date"
          class="day-circle"
          :class="[
            getDayColor(day.formatted),
            selectedDay === day.formatted ? 'active' : '',
          ]"
        >
          {{ day.date }}
        </div>
      </div>
    </div>

    <!-- WEEK VIEW -->
    <div class="week-grid" v-if="viewMode === 'week'">
      <div
        v-for="(day, index) in visibleDays"
        :key="index"
        class="day-box"
        @click="selectDay(day.formatted)"
      >
        <div
          class="day-circle"
          :class="[
            getDayColor(day.formatted),
            selectedDay === day.formatted ? 'active' : '',
          ]"
        >
          {{ day.date }}
        </div>
      </div>
    </div>

    <!-- EXPANDED FULL-DAY SCHEDULE VIEW -->

    <!-- Selected Date Display -->
    <div v-if="selectedDay" class="selected-date-header">
      <span class="selected-date">{{ formattedSelectedDay }}</span>
    </div>
    <div v-if="selectedDay" class="vertical-schedule">
      <div class="schedule-grid">
        <div v-for="hour in hours" :key="hour" class="schedule-hour">
          <span class="hour-label">{{ hour }}:00</span>
          <div class="event-container">
            <div
              v-for="entretien in getScheduledConfirmed(hour)"
              :key="'c' + entretien.id"
              class="event incoming-entretien-event"
              :style="{ top: entretien.topOffset, position: 'absolute' }"
            >
              <template v-if="userRole === 'recruiter'">
                {{ entretien.starttime.slice(11, 16) }} -
                {{ entretien.candidat }} - {{ entretien.job }}
              </template>
              <template v-else>
                {{ entretien.starttime.slice(11, 16) }} -
                {{ entretien.recruteur }} - {{ entretien.job }}
              </template>
            </div>

            <!-- Créneaux en attente -->
            <div
              v-for="pending in getPendingSlotsForHour(hour)"
              :key="'p' + pending.id"
              class="event pending-background-only"
              :style="{
                top: pending.minute === 30 ? '20px' : '0',
                position: 'absolute',
                zIndex: 1,
                marginLeft: '10px',
                width: '100%',
              }"
            ></div>
          </div>
        </div>
        <div
          v-if="currentHourLineStyle"
          class="current-hour-line"
          :style="currentHourLineStyle"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
  import {
    format,
    startOfWeek,
    addDays,
    subMonths,
    addMonths,
    startOfMonth,
    endOfMonth,
    getDay,
  } from 'date-fns';
  import fr from 'date-fns/locale/fr';

  export default {
    name: 'ExpandableCalendar',

    props: {
      postulations: Array,
      todayDate: String,
      userRole: String,
      entretiens: Array,
    },

    data() {
      return {
        currentDate: new Date(),
        selectedDay: null,
        viewMode: 'month',
        hours: Array.from({ length: 12 }, (_, i) =>
          (i + 8).toString().padStart(2, '0')
        ), // 08 → 19
        dayShortNames: ['LU', 'MA', 'ME', 'JE', 'VE', 'SA', 'DI'],
      };
    },

    computed: {
      formattedMonth() {
        return format(this.currentDate, 'MMMM ', {
          locale: fr,
        }).toUpperCase();
      },
      formattedSelectedDay() {
        if (!this.selectedDay) return '';
        return new Date(this.selectedDay).toLocaleDateString('fr-FR', {
          weekday: 'long',
          day: '2-digit',
          month: 'long',
          year: 'numeric',
        });
      },
      monthDays() {
        const start = startOfMonth(this.currentDate);
        const end = endOfMonth(this.currentDate);
        const days = [];
        const firstDayIndex = getDay(start) === 0 ? 7 : getDay(start);

        for (let i = 1; i < firstDayIndex; i++) {
          days.push({ date: '', formatted: '', status: 'empty' });
        }

        for (let i = 1; i <= end.getDate(); i++) {
          const date = new Date(start.getFullYear(), start.getMonth(), i);
          const dateStr = format(date, 'yyyy-MM-dd');

          let status = 'normal';
          const match = this.entretiens.find((e) => e.date.startsWith(dateStr));

          if (this.userRole === 'recruiter') {
            if (match?.accepted === true) status = 'accepted';
            else if (match?.accepted === false) status = 'pending';
          } else if (this.userRole === 'applicant') {
            if (match?.accepted === false) status = 'pending';
            else status = 'disabled';
          }

          days.push({ date: format(date, 'd'), formatted: dateStr, status });
        }
        return days;
      },
      visibleDays() {
        const baseDate = this.selectedDay
          ? new Date(this.selectedDay)
          : new Date();
        const start = startOfWeek(baseDate, { weekStartsOn: 1 });
        return Array.from({ length: 7 }, (_, i) => {
          const date = addDays(start, i);
          return {
            date: format(date, 'd', { locale: fr }),
            formatted: format(date, 'yyyy-MM-dd', { locale: fr }),
          };
        });
      },
      currentHourLineStyle() {
        if (!this.selectedDay) return null;

        const now = new Date();
        const todayStr = format(now, 'yyyy-MM-dd');
        if (todayStr !== this.selectedDay) return null;

        const hour = now.getHours();
        const minutes = now.getMinutes();
        const top = (hour - 8) * 40 + (minutes / 60) * 40; // 40px par heure

        return {
          top: `${top}px`,
        };
      },
    },

    methods: {
      selectDay(dayString) {
        if (dayString) {
          this.selectedDay = dayString;
          this.viewMode = 'week';
        }
      },
      previous() {
        this.currentDate =
          this.viewMode === 'month'
            ? subMonths(this.currentDate, 1)
            : new Date(this.selectedDay);
      },
      next() {
        this.currentDate =
          this.viewMode === 'month'
            ? addMonths(this.currentDate, 1)
            : new Date(this.selectedDay);
      },
      resetToMonthView() {
        this.viewMode = 'month';
        this.selectedDay = null;
      },

      getDayColor(date) {
        if (this.userRole === 'recruiter') {
          const hasConfirmed = this.entretiens?.some((e) => {
            return (
              e.date &&
              e.accepted === true &&
              format(new Date(e.date), 'yyyy-MM-dd') === date
            );
          });

          if (hasConfirmed) return 'entretien-next'; // priorité au jaune

          const hasPending = this.entretiens?.some((e) => {
            return (
              e.date &&
              e.accepted === false &&
              format(new Date(e.date), 'yyyy-MM-dd') === date
            );
          });

          if (hasPending) return 'entretien-pending'; // sinon bleu
        }

        const match = this.postulations?.filter((p) => {
          if (!p.availability?.date) return false;
          return format(new Date(p.availability.date), 'yyyy-MM-dd') === date;
        });

        if (!match?.length) return 'entretien-passed';

        const hasConfirmed = match.some(
          (p) => p.availability?.accepted === true
        );
        return hasConfirmed ? 'entretien-next' : 'entretien-pending';
      },

      getScheduledConfirmed(hour) {
        if (!this.selectedDay) return [];

        return this.postulations
          .filter((p) => {
            const date = p.availability?.date;
            const accepted = p.availability?.accepted;
            return (
              date &&
              accepted === true &&
              format(new Date(date), 'yyyy-MM-dd') === this.selectedDay &&
              parseInt(p.availability?.starttime?.slice(11, 13)) ===
                parseInt(hour)
            );
          })
          .map((p) => {
            const minute = parseInt(p.availability?.starttime?.slice(14, 16));
            const info = {
              id: p.availability?.id,
              starttime: p.availability?.starttime,
              topOffset: minute >= 30 ? '20px' : '0px',
            };
            if (this.userRole === 'recruiter') {
              info.candidat = p.user_first_name + ' ' + p.user_last_name;
              info.job = p.jobOffer?.title;
            } else {
              info.recruteur = p.job?.nom_recruteur;
              info.job = p.job?.title;
            }
            return info;
          });
      },

      getPendingSlotsForHour(hour) {
        if (!this.selectedDay || this.userRole !== 'recruiter') return [];

        return this.entretiens
          .filter((e) => {
            const isSameDay =
              e.date &&
              format(new Date(e.date), 'yyyy-MM-dd') === this.selectedDay;

            if (!isSameDay || e.accepted !== false) return false;

            const h = parseInt(e.starttime?.slice(11, 13));
            const m = parseInt(e.starttime?.slice(14, 16));

            return h === parseInt(hour) && (m === 0 || m === 30);
          })
          .map((e) => ({
            id: e.id,
            minute: parseInt(e.starttime?.slice(14, 16)), // 0 ou 30
          }));
      },
    },
  };
</script>

<style scoped>
  .calendar-container {
    background: white;
    padding: 20px;
    border-radius: 30px;
    display: flex;
    flex-direction: column;
    flex: 1;
    width: 100%;
    height: 100%;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
  }

  .calendar-header {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 60px 40px 60px;
  }

  .month-navigation {
    display: flex;
    position: absolute;
    left: 0;
    top: 0;
  }
  .month-navigation button {
    margin-right: 20px;
  }
  .date-range {
    font-weight: 500;
  }
  .nav-button {
    cursor: pointer;
  }

  .weekdays-header {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    font-weight: bold;
    font-size: 14px;
    gap: 1px;
  }
  .weekday {
    flex: 1;
    text-align: center;
    font-size: 12px;
  }
  .month-grid,
  .week-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
  }

  .day-box {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3px;
  }
  .day-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    background-color: var(--white-100);
    cursor: pointer;
  }
  .day-circle:hover {
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
  }
  .day-circle.entretien-next {
    background-color: var(--primary-1);
  }
  .day-circle.entretien-pending {
    background-color: var(--secondary-2b2);
  }
  /* .day-circle.entretien-passed {
    background-color: #cccccc;
  } */
  .event-container {
    position: relative;
    width: 100%;
    align-items: center;
  }
  .event {
    position: relative;
    z-index: 3;
    width: 100%;
    height: 20px;
    padding: 0 6px;
    font-size: 12px;
    margin-left: 10px;
    white-space: nowrap;
  }
  .event.incoming-entretien-event {
    background-color: var(--primary-1);
    z-index: 5;
  }
  .event.pending-background-only {
    background-color: var(--secondary-2b2);
    height: 20px;
  }
  .event.passed-entretien-event {
    background-color: #cccccc;
  }
  .vertical-schedule {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 95%;
    overflow-y: hidden;
  }
  .schedule-grid {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow: hidden;
    max-height: calc(100vh - 200px);
  }
  .schedule-hour {
    width: 100%;
    display: flex;
    align-items: center;
    height: 40px;
    position: relative;
  }
  .schedule-hour::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 1px;
    margin-left: 40px;
    background-color: #ddd;
    transform: translateY(-50%);
    z-index: 0;
  }
  .hour-label {
    font-weight: 300;
    font-size: 12px;
    color: #c4c4c4;
  }

  .selected-date-header {
    width: 100%;
    margin-top: 30px;
    color: black;
    text-align: center;
    padding: 10px 0;
    font-size: 18px;
    font-weight: medium;
    text-transform: capitalize;
    border-bottom: 1px black; /* Slightly darker bottom border for effect */
  }

  .selected-date {
    display: inline-block;
  }
  .current-hour-line {
    position: absolute;
    left: 40px;
    right: 0;
    height: 2px;
    background-color: var(--primary-1);
    z-index: 10;
  }
</style>
