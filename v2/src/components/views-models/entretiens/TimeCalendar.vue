<template>
  <div class="calendar-container">
    <!-- Fond sombre -->
    <div
      v-if="showConfirmModal"
      class="overlay"
      @click="showConfirmModal = false"
    ></div>
    <div class="calendar-layout">
      <!-- Cale<PERSON><PERSON> du mois -->
      <div class="calendar-grid">
        <div class="calendar-header">
          <button @click="previous" class="nav-button">
            <i class="fas fa-chevron-left"></i>
          </button>
          <p class="date-range">{{ formattedMonth }}</p>
          <button @click="next" class="nav-button">
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
        <div class="weekdays-header">
          <p v-for="day in dayShortNames" :key="day" class="weekday">
            {{ day }}
          </p>
        </div>
        <div class="month-grid">
          <div
            v-for="(day, index) in monthDays"
            :key="index"
            class="day-box"
            @click="day.status !== 'disabled' && selectDay(day.formatted)"
          >
            <p
              v-if="day.date"
              class="day-circle"
              :class="{
                active: selectedDay === day.formatted,
                'day-accepted': day.status === 'accepted',
                'day-pending': day.status === 'pending',
                'day-disabled': day.status === 'disabled',
                'day-has-selection': hasSelectedSlotsForDay(day.formatted),
              }"
            >
              {{ day.date }}
            </p>
          </div>
        </div>
      </div>

      <!-- Liste des heures alignée à droite -->
      <div v-if="selectedDay" class="schedule-panel">
        <div class="top-schedule-panel">
          <p class="day-selected">{{ formatDate(selectedDay) }}</p>

          <!-- Toggle Switch -->
          <div class="hour-bulk-select" v-if="userRole === 'recruiter'">
            <p>9h à 17h</p>
            <CustomSwitch
              v-model="isDayRangeChecked"
              label=""
              value="1"
              hide-details
              @change="toggleDayRange"
            ></CustomSwitch>
          </div>
        </div>

        <div class="hours-list">
          <div class="hour-grid">
            <div
              v-for="i in timeSlots.length / 2"
              :key="i"
              class="hour-row"
            >
              <!-- Slot gauche -->
              <button
                class="hour-item"
                :class="getSlotClass(timeSlots[i * 2 - 2])"
                @click="selectSlot(timeSlots[i * 2 - 2])"
                :disabled="isSlotDisabled(timeSlots[i * 2 - 2])"
              >
                {{ timeSlots[i * 2 - 2] }}
                <span
                  v-if="userRole === 'recruiter' && isSlotSelected(timeSlots[i * 2 - 2])"
                  class="remove-btn-inline"
                  @click.stop="removeSlotByTime(timeSlots[i * 2 - 2])"
                >
                  ✕
                </span>
              </button>

              <!-- Slot droit -->
              <button
                class="hour-item"
                :class="getSlotClass(timeSlots[i * 2 - 1])"
                @click="selectSlot(timeSlots[i * 2 - 1])"
                :disabled="isSlotDisabled(timeSlots[i * 2 - 1])"
              >
                {{ timeSlots[i * 2 - 1] }}
                <span
                  v-if="userRole === 'recruiter' && isSlotSelected(timeSlots[i * 2 - 1])"
                  class="remove-btn-inline"
                  @click.stop="removeSlotByTime(timeSlots[i * 2 - 1])"
                >
                  ✕
                </span>
              </button>
            </div>
          </div>
        </div>

        <!-- Nouveau bouton Suivant en bas -->
        <div v-if="userRole === 'applicant' && selectedSlots.length" class="calendar-actions">
          <PrimaryRoundedButton
            textContent="Suivant"
            @click="confirmSlot"
          />
        </div>

        <!-- Bouton Enregistrer pour le recruteur -->
        <div v-if="userRole === 'recruiter' && selectedSlots.length" class="calendar-actions">
          <PrimaryRoundedButton
            v-if="userRole === 'recruiter'"
            textContent="Enregistrer"
            @click="saveChanges"
          />
        </div>
      </div>
    </div>

    <ConfirmationModalEntretienCandidat
      class="entretien-modal"
      v-if="showConfirmModal"
      :recruiterName="modalInfos.name"
      :recruiterPicture="modalInfos.picture"
      :selectedSlot="modalInfos.selectedSlot"
      :userRole="userRole"
      @close="showConfirmModal = false"
      @confirm="validateConfirmedSlot"
    />
  </div>
</template>

<script>
  import {
    format,
    startOfMonth,
    endOfMonth,
    getDay,
    subMonths,
    addMonths,
  } from 'date-fns';
  import fr from 'date-fns/locale/fr';
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import {
    createEntretien,
    acceptEntretien,
  } from '@/services/entretiens.service.js';
  import { toaster } from '@/utils/toast/toast.js';
  import CustomSwitch from '@/components/switch/CustomSwitch.vue';
  import ConfirmationModalEntretienCandidat from '../../modal/confirmation/ConfirmationModalEntretienCandidat.vue';
  import getImgPath from '@/utils/imgpath.js';

  const toISOStringNoMs = (date) =>
    new Date(date).toISOString().replace(/\.\d{3}Z$/, 'Z');

  export default {
    name: 'CalendarTime',

    components: {
      PrimaryRoundedButton,
      CustomSwitch,
      ConfirmationModalEntretienCandidat,
    },

    props: {
      postulations: {
        type: Array,
        required: true,
      },
      todayDate: String,
      userRole: String,
      entretien: Object,
      entretiens: {
        type: Array,
        default: () => [],
      },
      getUser: {
        type: Object,
        required: true,
      },
    },

    data() {
      return {
        currentDate: new Date(),
        selectedDay: null,
        selectedSlots: [],
        timeSlots: Array.from({ length: 24 }, (_, i) => {
          const hour = Math.floor(i / 2) + 8;
          const minutes = i % 2 === 0 ? '00' : '30';
          return `${hour.toString().padStart(1, '0')}:${minutes}`;
        }),
        dayShortNames: ['LU', 'MA', 'ME', 'JE', 'VE', 'SA', 'DI'],
        isDayRangeChecked: false,
        showConfirmModal: false,
        modalInfos: {
          name: '',
          picture: '',
        },
      };
    },

    computed: {
      formattedMonth() {
        return format(this.currentDate, 'MMMM', {
          locale: fr,
        }).toUpperCase();
      },
      monthDays() {
        const start = startOfMonth(this.currentDate);
        const end = endOfMonth(this.currentDate);
        const days = [];
        const firstDayIndex = getDay(start) === 0 ? 7 : getDay(start);

        for (let i = 1; i < firstDayIndex; i++) {
          days.push({ date: '', formatted: '', status: 'empty' });
        }

        for (let i = 1; i <= end.getDate(); i++) {
          const date = new Date(start.getFullYear(), start.getMonth(), i);
          const dateStr = format(date, 'yyyy-MM-dd');

          let status = 'normal';
          const match = this.entretiens.find((e) => e.date.startsWith(dateStr));

          if (this.userRole === 'recruiter') {
            if (match?.accepted === true) status = 'accepted';
            else if (match?.accepted === false) status = 'pending';
          } else if (this.userRole === 'applicant') {
            if (match?.accepted === false) status = 'pending';
            else if (match?.accepted === true) status = 'pending';
            else status = 'disabled';
          }

          days.push({ date: format(date, 'd'), formatted: dateStr, status });
        }
        return days;
      },
      timeSlotPairs() {
        const pairs = [];
        for (let i = 0; i < this.timeSlots.length; i += 2) {
          pairs.push(this.timeSlots.slice(i, i + 2));
        }
        return pairs;
      },
    },

    methods: {
      getImgPath,

      toggleDayRange() {
        const rangeToSelect = this.timeSlots.filter((slot) => {
          const hour = parseInt(slot.split(':')[0]);
          return (hour >= 9 && hour < 12) || (hour >= 14 && hour < 17);
        });

        if (this.isDayRangeChecked && this.selectedDay) {
          const newSlots = rangeToSelect.map((time) => ({
            date: this.selectedDay,
            time,
          }));

          // Ajout sans doublon
          const unique = new Map(
            [...this.selectedSlots, ...newSlots].map((s) => [
              `${s.date}-${this.normalizeTime(s.time)}`,
              s,
            ])
          );
          this.selectedSlots = [...unique.values()];
        } else {
          // On retire les créneaux 9h-12h et 14h-17h
          this.selectedSlots = this.selectedSlots.filter((s) => {
            const hour = parseInt(s.time.split(':')[0]);
            return !(
              s.date === this.selectedDay &&
              ((hour >= 9 && hour < 12) || (hour >= 14 && hour < 17))
            );
          });
        }
      },

      removeSlotByTime(slot) {
        const normalized = this.normalizeTime(slot);
        this.selectedSlots = this.selectedSlots.filter(
          (s) =>
            !(
              this.normalizeTime(s.time) === normalized &&
              s.date === this.selectedDay
            )
        );
      },

      getDateStatus(dateStr) {
        if (this.userRole === 'recruiter') {
          const hasAccepted = this.entretiens.some(
            (e) => e.accepted === true && e.date.startsWith(dateStr)
          );
          const hasPending = this.entretiens.some(
            (e) => e.accepted === false && e.date.startsWith(dateStr)
          );

          if (hasAccepted) return true;
          if (hasPending) return false;
          return null;
        }

        if (!this.entretien || !this.entretien.dates) return null;

        const entry = Object.values(this.entretien.dates).find((d) =>
          d.date.startsWith(dateStr)
        );
        return entry ? entry.accepted : null;
      },

      getSlotClass(slot) {
        const iso = `${this.selectedDay}T${this.normalizeTime(slot)}:00Z`;
        const match = this.entretiens?.find((e) => e.date === iso);
        const isSelected = this.isSlotSelected(slot);

        if (match?.accepted === true && this.userRole === 'recruiter')
          return 'hour-item booked-slot';

        if (match?.accepted === false) {
          if (this.userRole === 'recruiter') {
            return isSelected
              ? 'hour-item selected-slot'
              : 'hour-item pending-slot';
          } else {
            return isSelected ? 'hour-item selected-slot' : 'hour-item';
          }
        }

        if (this.userRole === 'applicant') return 'hour-item disabled-slot';

        return ['hour-item', isSelected ? 'selected-slot' : ''].join(' ');
      },

      normalizeTime(time) {
        const [hour, minute] = time.split(':');
        return `${hour.padStart(2, '0')}:${minute}`;
      },

      // Vérifier si un créneau est désactivé (si il est occupé ou déjà sélectionné)
      isSlotDisabled(slot) {
        const iso = `${this.selectedDay}T${this.normalizeTime(slot)}:00Z`;

        if (this.userRole === 'applicant') {
          const match = this.entretiens.find(
            (e) => e.date === iso && e.accepted === false
          );
          return !match; // autorisé uniquement si match pending
        }

        return false;
      },

      formatDate(date) {
        return format(new Date(date), 'EEEE d MMMM', { locale: fr });
      },
      selectDay(dayString) {
        this.selectedDay = dayString;
        this.isDayRangeChecked = false;
      },
      hasSelectedSlotsForDay(date) {
        return this.selectedSlots.some((slot) => slot.date === date);
      },
      selectSlot(slot) {
        if (!this.selectedDay) return;

        const iso = `${this.selectedDay}T${this.normalizeTime(slot)}:00Z`;

        if (this.userRole === 'applicant') {
          const match = this.entretiens.find(
            (e) => e.date === iso && e.accepted === false
          );
          if (match) {
            this.selectedSlots = [{ date: this.selectedDay, time: slot }];
          }
        } else {
          const already = this.selectedSlots.find(
            (s) => s.date === this.selectedDay && s.time === slot
          );
          if (!already) {
            this.selectedSlots.push({ date: this.selectedDay, time: slot });
          }
        }
      },
      isSelected(date) {
        return this.selectedSlots.some((slot) => slot.date === date);
      },
      isSlotSelected(slot) {
        return this.selectedSlots.some(
          (selected) =>
            this.normalizeTime(selected.time) === this.normalizeTime(slot) &&
            selected.date === this.selectedDay
        );
      },
      removeSlot(index) {
        this.selectedSlots.splice(index, 1);
      },

      async saveChanges() {
        if (!this.selectedSlots.length) return;

        const dates = this.selectedSlots.map((slot) => {
          const start = new Date(
            `${slot.date}T${this.normalizeTime(slot.time)}:00Z`
          );
          const end = new Date(start.getTime() + 30 * 60 * 1000); // +30min

          return {
            date: toISOStringNoMs(start),
            starttime: toISOStringNoMs(start),
            endtime: toISOStringNoMs(end),
          };
        });

        const payload = { dates };

        try {
          const response = await createEntretien(payload);
          toaster.showSuccessPopup('Calendrier créé avec succès !');

          this.$emit('update:showRecruiterCalendar', false);
          this.$emit('refreshData');
        } catch (error) {
          //console.error('❌ Erreur création calendrier :', error);
          toaster.showErrorPopup(`Erreur lors de la création du calendrier`);
        }
      },
      confirmSlot() {
        if (!this.selectedSlots.length) return;

        const slot = this.selectedSlots[0];
        const isoDate = `${slot.date}T${this.normalizeTime(slot.time)}:00Z`;

        const matched = this.entretiens.find(
          (e) => e.date === isoDate && e.accepted === false
        );

        if (!matched) {
          toaster.showErrorPopup('Créneau introuvable.');
          return;
        }

        // Ouvre la modale avec les infos du recruteur
        this.modalInfos = {
          name: this.postulations[0]?.job?.nom_recruteur ?? 'Nom Recruteur',
          picture: getImgPath(this.postulations[0]?.job?.logo_url ?? ''),
          selectedSlot: { date: slot.date, time: slot.time },
        };

        this.showConfirmModal = true;
      },

      async validateConfirmedSlot() {
        const slot = this.selectedSlots[0];
        const isoDate = `${slot.date}T${this.normalizeTime(slot.time)}:00Z`;

        const matched = this.entretiens.find(
          (e) => e.date === isoDate && e.accepted === false
        );

        if (!matched) {
          toaster.showErrorPopup('Créneau introuvable.');
          return;
        }

        const payload = {
          jobs: this.postulations[0]?.job?.id,
          candidats: this.getUser.id,
        };

        try {
          await acceptEntretien(matched.id, payload);
          toaster.showSuccessPopup('Créneau confirmé avec succès !');
          this.$emit('update:showRecruiterCalendar', false);
          this.$emit('refreshData');
          this.$emit('closeCalendar');
          this.showConfirmModal = false;
        } catch (e) {
          //console.error('❌ Erreur confirmation créneau :', e);
          toaster.showErrorPopup(`Erreur lors de l'acceptation d'une date`);
        }
      }
    },
  };
</script>

<style scoped>
  .calendar-container {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .calendar-layout {
    width: 100%;
    display: flex;
    justify-content: space-between;
    gap: 20px;
    min-height: 550px;
    align-items: flex-start;
  }
  .calendar-grid {
    background: white;
    padding: 50px;
    border-radius: 30px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
  }
  .calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 60px 40px 60px;
  }
  .date-range {
    font-weight: 500;
  }
  .nav-button {
    cursor: pointer;
  }

  .weekdays-header,
  .month-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
    text-align: center;
  }
  .day-box {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px;
  }
  .day-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    background-color: var(--white-100);
  }
  .day-circle:hover {
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
  }
  .day-circle.active {
    background-color: var(--secondary-2) !important;
    color: white;
  }
  .day-circle.day-has-selection {
    background-color: var(--secondary-2b2);
  }

  .schedule-panel {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-height: 500px;
    overflow-y: auto;
  }
  .top-schedule-panel {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .day-selected {
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
  }
  .hour-bulk-select {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  .hour-bulk-select p {
    font-size: 14px;
  }
  .hours-list {
    display: flex;
    flex-direction: column;
  }
  .hour-grid {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 10px;
  }

  .hour-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
  }

  .hour-item {
    position: relative;
    border: 1px solid #ccc;
    border-radius: 10px;
    padding: 8px;
    text-align: center;
    cursor: pointer;
    width: 100%;
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
  }
  .hour-item:hover {
    opacity: 0.7;
  }
  .selected-slots {
    margin-top: 20px;
  }
  .slot-bubbles {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
  .slot-bubble {
    background: var(--secondary-2b2);
    padding: 8px 12px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
  }
  .remove-btn {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
  }
  .selected-day {
    background-color: var(--secondary-2b2);
  }
  .selected-slot {
    border: 2px solid var(--secondary-2) !important;
    background-color: var(--secondary-2b2) !important;
    pointer-events: none;
  }

  .day-circle.day-accepted {
    background-color: #fde8c6;
  }
  .day-circle.day-pending {
    background-color: var(--secondary-2b2);
  }
  .day-circle.day-disabled {
    background-color: #ddd;
    color: #aaa;
    pointer-events: none;
  }

  .hour-item.pending-slot {
    background-color: var(--secondary-2b2);
  }
  .hour-item.booked-slot {
    background-color: #fde8c6;
    color: #aaa;
    pointer-events: none;
  }
  .hour-item.disabled-slot {
    background-color: #ddd;
    color: #aaa;
    pointer-events: none;
  }
  .hour-item.selected-blue-slot {
    background-color: var(--secondary-2);
    color: white;
    position: relative;
  }

  .remove-btn-inline {
    position: absolute;
    top: 2px;
    right: 6px;
    font-size: 14px;
    pointer-events: auto;
    z-index: 2;
  }

  .calendar-actions {
    margin-top: 20px;
    padding: 16px;
    display: flex;
    justify-content: center;
    border-top: 1px solid #eee;
  }

  .schedule-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .hours-list {
    flex: 1;
    overflow-y: auto;
  }

  /* ----------- MODALE ----------- */
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Couleur semi-transparente */
    z-index: 999; /* Juste en dessous de la modale */
  }
  .entretien-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
  }
</style>
