<template>
  <div class="container-generale">
    <!-- Fond sombre -->
    <div
      v-if="showConfirmationModal || showRecruiterCalendar"
      class="overlay"
      @click="
        showConfirmationModal = false;
        showRecruiterCalendar = false;
      "
    ></div>
    <div class="top-container">
      <!-- Sous-titre dynamique indiquant le nombre d'entretiens ou de candidatures à venir -->
      <div class="title-container">
        <h4 v-if="entretiensDuJour.length > 0">
          Vous avez {{ entretiensDuJour.length }}
          {{ entretiensDuJour.length === 1 ? 'entretien' : 'entretiens' }}
          aujourd'hui !
        </h4>
        <h4 v-else>Aucun entretien aujourd'hui</h4>
        <p>
          <img src="@/assets/icons/lightBulb.svg" alt="" />En cas d'imprévu,
          prevenez votre
          {{ userRole === 'applicant' ? 'recruteur' : 'candidat' }} par message
        </p>
        <ul v-if="entretiensNext.length > 0">
          <li>
            <i>
              <span>
                {{ entretiensNext.length }}
                {{
                  entretiensNext.length === 1
                    ? 'Nouvel Entretien'
                    : 'Nouveaux Entretiens'
                }}</span
              >
              {{ entretiensNext.length === 1 ? ' ajouté' : ' ajoutés' }}
              à votre liste “prochainement”.
            </i>
          </li>
        </ul>
      </div>

      <!-- Sous-titre indiquant les nouvelles demandes d'entretien -->
      <div
        v-if="entretiensNewPending.length > 0 && userRole === 'applicant'"
        class="carousel-container"
      >
        <p class="carousel-title">
          {{
            entretiensNewPending.length === 1
              ? "Nouvelle demande d'entretien"
              : "Nouvelles demandes d'entretien"
          }}
        </p>
        <!-- Carrousel de navigation -->
        <div class="carousel">
          <button
            v-if="entretiensNewPending && entretiensNewPending.length > 1"
            class="nav-button left"
            @click="prevSlide"
          >
            <i class="fas fa-chevron-left"></i>
          </button>

          <div class="carousel-item">
            <img
              v-if="
                entretiensNewPending[currentIndex] &&
                entretiensNewPending[currentIndex].job
              "
              :src="
                getImgPath(
                  entretiensNewPending[currentIndex].job.logo_url || ''
                )
              "
              class="profile-img"
              alt="Profil"
            />

            <div class="carousel-text">
              <h6>{{ entretiensNewPending[currentIndex].job.title }}</h6>
              <p>
                {{ entretiensNewPending[currentIndex].job.nom_recruteur }}
              </p>
              <PrimaryRoundedButton
                textContent="Regarder"
                class="watch-button"
              />
            </div>
          </div>

          <button
            v-if="entretiensNewPending && entretiensNewPending.length > 1"
            class="nav-button right"
            @click="nextSlide"
          >
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>

      <PrimaryRoundedButton
        v-if="userRole === 'recruiter'"
        textContent="Modifier mes disponibilités"
        modify
        btnColor="blue"
        @click="handleRecruiterCalendar()"
      />
    </div>

    <!-- Conteneur des onglets -->
    <div class="tabs-container">
      <div class="tabs-wrapper">
        <!-- Onglets de navigation des statuts -->
        <div class="tabs">
          <button
            :class="{ active: activeTab === 'next' }"
            @click="activeTab = 'next'"
          >
            Prochainement
            <span
              v-if="entretiensNext.length > 0"
              class="pending-circle"
            ></span>
          </button>
          <button
            :class="{ active: activeTab === 'pending' }"
            @click="activeTab = 'pending'"
          >
            En Attente
            <span
              v-if="entretiensNewPending.length > 0"
              class="pending-circle"
            ></span>
          </button>
          <button
            :class="{ active: activeTab === 'passed' }"
            @click="activeTab = 'passed'"
          >
            Passé
          </button>
          <button
            :class="{ active: activeTab === 'canceled' }"
            @click="activeTab = 'canceled'"
          >
            Annulé
          </button>
        </div>
      </div>
    </div>

    <main class="main">
      <div
        v-if="postulationsAvecEntretiens.length > 0"
        class="entretiens-section"
      >
        <div
          class="card-section"
          v-for="(entretiens, month) in groupedEntretiens"
          :key="month"
        >
          <!-- Affichage du titre du mois -->
          <h3 class="mois-titre">{{ month }}</h3>

          <div
            v-if="isLoggedIn && userRole === 'applicant'"
            class="card-section"
          >
            <EntretiensCandidateCard
              v-for="postulation in entretiens"
              :key="postulation.id"
              :postulation="postulation"
              :logoTb="logoTb"
              :todayDate="todayDate"
              :availability="postulation.availability"
              :canJoin="canJoinEntretien(null, postulation)"
              @refuser-entretien="confirmationRefuserEntretien"
              @show-availability="showAvailability"
            />
          </div>
          <div
            v-if="isLoggedIn && userRole === 'recruiter'"
            class="card-section"
          >
            <EntretiensRecruiterCard
              v-for="postulation in entretiens"
              :key="postulation.id"
              :postulation="postulation"
              :imageAvatar="imageAvatar"
              :todayDate="todayDate"
              :availability="postulation.availability"
              :canJoin="canJoinEntretien(null, postulation)"
              @refuser-entretien="confirmationRefuserEntretien"
            />
          </div>
        </div>
      </div>
      <div v-else class="entretiens-section-else">
        <p>
          <img src="@/assets/icons/lightBulb.svg" alt="" />
          Complétez votre profil afin de vous mettre en avant auprès des
          recruteurs.
          <PrimaryRoundedButton textContent="Modifier" modify />
        </p>
        <p>
          <img src="@/assets/icons/lightBulb.svg" alt="" />
          Postulez à des offres !
          <PrimaryRoundedButton textContent="Voir les offres" eye />
        </p>
      </div>

      <ConfirmationModal
        v-if="showConfirmationModal"
        class="confirmation-modal"
        title=""
        description="Êtes-vous sûr.e de vouloir refuser cet entretien ?"
        @close="showConfirmationModal = false"
        @confirm="refuserEntretien"
      />

      <!-- Calendrier interactif -->
      <div class="calendar-container">
        <ExpandableCalendar
          :postulations="postulationsAvecEntretiens"
          :todayDate="todayDate"
          :userRole="userRole"
          :entretiens="entretiens"
        />
      </div>
      <RecruiterCalendar
        v-if="showRecruiterCalendar"
        class="calendar-modal"
        :postulations="postulationsAvecEntretiens"
        :showRecruiterCalendar="showRecruiterCalendar"
        @update:showRecruiterCalendar="showRecruiterCalendar = false"
        :userRole="userRole"
        :entretiens="entretiens"
        :getUser="getUser"
        @refreshData="refreshData"
      />
    </main>
  </div>
</template>

<script>
  import { format } from 'date-fns';
  import { fr } from 'date-fns/locale';
  import { mapGetters, mapActions } from 'vuex';
  import { getEntretiensListById } from '@/services/entretiens.service.js';
  import { cancelCandidateStatus } from '@/services/search.service.js';
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import ConfirmationModal from '../../../components/modal/confirmation/ConfirmationModal.vue';
  import EntretiensCandidateCard from '@/components/cards/entretiens/EntretiensCandidateCard.vue';
  import EntretiensRecruiterCard from '@/components/cards/entretiens/EntretiensRecruiterCard.vue';
  import getImgPath from '@/utils/imgpath.js';
  import logoTb from '@/assets/icons/tb-logo-borderblack.svg';
  import imageAvatar from '@/assets/Avatar.png';
  import ExpandableCalendar from './ExpandableCalendar.vue';
  import gotoPage from '@/utils/router.js';
  import RecruiterCalendar from './RecruiterCalendar.vue';

  export default {
    name: 'EntretienPage',

    components: {
      PrimaryRoundedButton,
      ConfirmationModal,
      EntretiensCandidateCard,
      EntretiensRecruiterCard,
      ExpandableCalendar,
      RecruiterCalendar,
    },

    data() {
      return {
        activeTab: 'next',
        currentIndex: 0,
        entretiens: [],
        currentDate: new Date(), // Stocke la date actuelle
        showConfirmationModal: false,
        logoTb,
        imageAvatar,
        showRecruiterCalendar: false,
      };
    },

    mounted() {
      //console.log(
      //  'this.postulationsAvecEntretiens',
      //  this.postulationsAvecEntretiens
      //);
      //console.log('Résultat entretiensNewPending:', this.entretiensNewPending);
    },

    computed: {
      ...mapGetters(['isLoggedIn', 'getUser', 'userRole', 'getJobOffers']),

      postulationsAvecEntretiens() {
        if (this.userRole === 'recruiter') {
          const all = [];

          this.getJobOffers.forEach((job) => {
            if (!job.postulants) return;

            job.postulants.forEach((postulant) => {
              const postulation = postulant;
              if (!postulant || postulant.user_accepter !== true) return;

              const userId = postulant.user_id;
              const matched = this.entretiens.find(
                (e) => e.accepted && e.jobs === job.id && e.candidats === userId
              );

              all.push({
                ...postulation,
                jobOffer: job,
                availability: matched || null,
                status: this.resolveStatut(matched),
              });
            });
          });

          return all;
        }

        if (this.userRole === 'applicant') {
          const all = this.getUser.postulation
            .filter((p) => p.accepter === true)
            .map((postulation) => {
              const job = postulation.job;
              const userId = this.getUser.id;
              const matched = this.entretiens.find(
                (e) => e.accepted && e.jobs === job.id && e.candidats === userId
              );

              return {
                ...postulation,
                availability: matched || null,
                status: this.resolveStatut(matched),
              };
            });

          return all;
        }

        return [];
      },

      entretiensDuJour() {
        return this.postulationsAvecEntretiens.filter((p) => {
          const slot = p.availability;
          return p.status === 'next' && slot?.date?.startsWith(this.todayDate);
        });
      },
      entretiensNext() {
        return this.postulationsAvecEntretiens.filter(
          (p) => p.status === 'next'
        );
      },
      entretiensNewPending() {
        if (this.userRole === 'recruiter') {
          return this.getJobOffers.flatMap((job) => {
            return (job.postulants || [])
              .filter((postulant) => postulant.user_accepter === true)
              .filter((postulant) => {
                const userId = postulant.user_id;
                const hasAcceptedEntretien = this.entretiens.some(
                  (e) =>
                    e.accepted && e.jobs === job.id && e.candidats === userId
                );
                return !hasAcceptedEntretien;
              })
              .map((postulant) => ({
                jobOffer: job,
                status: 'pending',
              }));
          });
        }

        if (this.userRole === 'applicant') {
          return this.getUser.postulation
            .filter((p) => p.accepter === true)
            .filter((p) => {
              const recruteurId = p.job?.author;
              const userId = this.getUser.id;
              const entretiensRecruteur = this.entretiens.filter(
                (e) =>
                  e.jobs === p.job.id && e.accepted && e.candidats === userId
              );
              return entretiensRecruteur.length === 0;
            })
            .map((p) => ({
              ...p,
              status: 'pending',
            }));
        }

        return [];
      },

      groupedEntretiens() {
        const groupes = {};
        const current = this.activeTab;
        const mois = [
          'janvier',
          'février',
          'mars',
          'avril',
          'mai',
          'juin',
          'juillet',
          'août',
          'septembre',
          'octobre',
          'novembre',
          'décembre',
        ];

        this.postulationsAvecEntretiens.forEach((p) => {
          if (p.status !== current) return;

          if (p.status === 'pending') {
            const title = p.job?.title || p.jobOffer?.title || 'Autre';
            groupes[title] = groupes[title] || [];
            groupes[title].push(p);
          } else {
            const date = new Date(p.availability?.starttime);
            const key = `${format(date, 'MMMM', { locale: fr })} ${format(date, 'yyyy')}`;
            groupes[key] = groupes[key] || [];
            groupes[key].push({ ...p, entretienDateTime: date });
          }
        });

        return Object.fromEntries(
          Object.entries(groupes)
            .sort(([a], [b]) => {
              const getIndex = (key) => mois.findIndex((m) => key.includes(m));
              const [ma, ya] = a.split(' ');
              const [mb, yb] = b.split(' ');
              return ya - yb || getIndex(ma) - getIndex(mb);
            })
            .map(([k, v]) => [
              k,
              v.sort((a, b) => a.entretienDateTime - b.entretienDateTime),
            ])
        );
      },

      todayDate() {
        return format(new Date(), 'yyyy-MM-dd');
      },
    },

    methods: {
      ...mapActions(['fetchUser']),
      getImgPath,
      gotoPage,

      resolveStatut(slot) {
        if (!slot || !slot.accepted || !slot.starttime || !slot.endtime)
          return 'pending';
        const now = new Date();
        const end = new Date(slot.endtime.replace(/Z$/, ''));
        return now > end ? 'passed' : 'next';
      },

      // Méthode pour formater la date
      formatDate(date) {
        const parsedDate = new Date(date);
        return {
          day: this.extractDay(parsedDate),
          month: this.extractMonth(parsedDate),
          week_day: this.extractWeekDay(parsedDate),
          time: this.extractTime(parsedDate),
        };
      },
      extractDay(date) {
        return format(date, 'dd');
      },
      extractMonth(date) {
        return format(date, 'MMMM', { locale: fr });
      },
      extractWeekDay(date) {
        return format(date, 'EEE', { locale: fr });
      },
      extractTime(date) {
        return format(date, 'HH:mm');
      },

      // Gère le slide du carousel d'images
      prevSlide() {
        if (this.entretiensNewPending.length > 0) {
          this.currentIndex =
            (this.currentIndex - 1 + this.entretiensNewPending.length) %
            this.entretiensNewPending.length;
        }
      },
      nextSlide() {
        if (this.entretiensNewPending.length > 0) {
          this.currentIndex =
            (this.currentIndex + 1) % this.entretiensNewPending.length;
        }
      },

      confirmationRefuserEntretien(postulation) {
        this.entretienSelectionne = postulation;
        this.showConfirmationModal = true;
      },
      async refuserEntretien() {
        if (!this.entretienSelectionne) return;

        try {
          const offerId = this.entretienSelectionne?.job?.id;
          const candidateId = this.getUser.id;

          // À METTRE UNE FOIS LE BACKEND COMPLÉTÉ
          //await cancelCandidateStatus(offerId, candidateId);

          this.entretienSelectionne.status = 'canceled';
          this.showConfirmationModal = false;
          this.entretienSelectionne = null;

          await this.refreshData();
        } catch (error) {
          //console.error('Erreur annulation :', error);
        }
      },

      showAvailability(entretien) {
        this.entretienSelectionne = entretien;
        this.showRecruiterCalendar = true;
      },
      handleRecruiterCalendar() {
        this.showRecruiterCalendar = true;
      },

      canJoinEntretien(_, postulation) {
        const slot = postulation?.availability;
        if (!slot || !slot.starttime || !slot.endtime) return false;

        const now = new Date();

        // Forcer lecture locale (supprime le "Z" si présent)
        const localStart = new Date(slot.starttime.replace(/Z$/, ''));
        const localEnd = new Date(slot.endtime.replace(/Z$/, ''));

        return now >= localStart && now <= localEnd;
      },

      async refreshData() {
        await this.fetchUser();

        if (this.userRole === 'recruiter') {
          const id = this.getUser.id;
          const data = await getEntretiensListById(id);
          this.entretiens = data.dates;
        } else {
          const ids = [
            ...new Set(this.getUser.postulation.map((p) => p.job?.author)),
          ];
          const all = [];
          for (const id of ids) {
            const data = await getEntretiensListById(id);
            all.push(...data.dates);
          }
          this.entretiens = all;
        }
      },
    },

    async created() {
      await this.fetchUser();

      if (this.userRole === 'recruiter') {
        const id = this.getUser.id;
        const data = await getEntretiensListById(id);
        this.entretiens = data.dates;
      } else {
        const ids = [
          ...new Set(this.getUser.postulation.map((p) => p.job?.author)),
        ];
        const all = [];
        for (const id of ids) {
          const data = await getEntretiensListById(id);
          all.push(...data.dates);
        }
        this.entretiens = all;
      }

      //console.log('this.getUser:', this.getUser);
      //console.log('this.getJobOffers:', this.getJobOffers);
      //console.log('this.entretiens:', this.entretiens);
    },
  };
</script>

<style scoped>
  /* ----------- STRUCTURE DE BASE ----------- */
  .container-generale {
    width: 100%;
    margin: 50px auto;
    padding: 20px;
    border-radius: 15px;
    background-color: var(--surface-bg-2);
  }

  .top-container {
    min-height: 150px;
    display: flex;
    justify-content: space-between;
    gap: 20px;
    align-items: center;
  }

  /* ----------- TITLE ----------- */
  .title-container {
    max-width: 80%;
  }
  .title-container h4 {
    font-size: 36px;
    font-weight: bold;
  }
  .title-container p {
    font-size: 16px;
    color: var(--text-3);
    display: flex;
    align-items: center;
  }
  .title-container p img {
    margin-right: 1rem;
  }
  .title-container li {
    list-style-type: disc;
    color: var(--text-3);
    margin-left: 2rem;
    margin-top: 1rem;
  }
  .title-container li::marker {
    color: var(--primary-1);
  }
  .title-container li span {
    color: var(--primary-1);
  }

  /* ----------- CAROUSSEL ----------- */
  .carousel-container {
    width: fit-content;
  }
  .carousel-title {
    color: var(--gray-100);
    font-weight: bold;
    text-align: center;
    margin-bottom: 20px;
  }
  .carousel {
    display: flex;
    align-items: center;
    gap: 10px;
    position: relative;
  }
  .nav-button {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    transition: transform 0.2s;
  }
  .nav-button:hover {
    transform: scale(1.2);
  }
  .carousel-item {
    display: flex;
    align-items: flex-start;
    background: #fff;
    padding: 20px;
    border-radius: 30px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.15);
    width: 340px;
    position: relative;
  }
  .carousel-item::before {
    content: '';
    position: absolute;
    top: -12px;
    right: -12px;
    width: 30px;
    height: 30px;
    background-color: var(--primary-1);
    border-radius: 50%;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.15);
    z-index: 500;
  }
  .profile-img {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 10px;
  }
  .carousel-text p {
    margin-left: 0.5rem;
  }
  .watch-button {
    position: absolute;
    bottom: 0;
    right: 0;
    transform: translate(-20px, -15px);
  }
  .carousel-text :deep(.v-btn) {
    font-size: 12px !important;
    height: 30px;
    border-radius: 15px;
    width: fit-content;
    padding-inline: 24px;
    padding-block: 0px;
  }

  /* ----------- TABS ----------- */
  .tabs {
    display: flex;
    gap: 15px;
  }
  .tabs button {
    position: relative;
    background-color: #f8f8f8;
    border: 1px solid transparent;
    padding: 5px 20px;
    cursor: pointer;
    border-radius: 10px;
    transition: all 0.3s ease;
  }
  .tabs button:hover {
    border-color: var(--gray-100);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  .tabs button.active {
    border-color: var(--primary-1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  .pending-circle {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 12px;
    height: 12px;
    background-color: var(--primary-1);
    border-radius: 50%;
    z-index: 500;
  }

  /* ----------- LAYOUT PRINCIPAL ----------- */
  .main {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    background-color: var(--surface-bg-2);
  }

  /* ----------- SECTION ENTRETIENS ----------- */
  .entretiens-section-else,
  .entretiens-section {
    border-top: 2px solid #c4c4c4;
    margin-top: 20px;
  }
  .entretiens-section-else p {
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 1rem;
    font-weight: 500;
    gap: 10px;
  }
  .entretiens-section-else p img {
    margin-right: 1rem;
    flex-shrink: 0;
  }
  .entretiens-section-else p .v-btn {
    margin-left: auto; /* Pousse le bouton à droite */
  }

  .card-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  .mois-titre {
    font-weight: 500;
    margin-top: 20px;
  }

  /* ----------- MODALE ----------- */
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Couleur semi-transparente */
    z-index: 999; /* Juste en dessous de la modale */
  }
  .confirmation-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    width: 100%;
    max-width: 500px;
    padding: 20px;
    text-align: center;
  }
  .calendar-modal {
    position: fixed;
    top: 45%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    width: 1080px;
    padding: 20px 100px;
  }
</style>
