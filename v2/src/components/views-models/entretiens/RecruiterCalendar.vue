<template>
  <div class="container">
    <button
      class="back-btn"
      @click="$emit('update:showRecruiterCalendar', false)"
    >
      <img src="@/assets/icons/arrow-back.svg" alt="" />
    </button>

    <div class="schedule-info" v-if="userRole === 'recruiter'">
      <h3 class="schedule-title">
        Quand êtes-vous disponibles pour passer des entretiens ?
      </h3>
      <p class="schedule-description">
        *Ceci est l'emploi du temps consacré aux entretiens vidéo avec les
        candidats. Il sera visible par toutes les personnes à qui vous proposez
        des entretiens sur la plateforme de Thanks-Boss.
      </p>
      <p class="schedule-subtitle">
        Choisissez vos dates et créneaux horaires :
      </p>
    </div>
    <div class="Info-container" v-else>
      <!-- Header Row with Image Icon and Text -->
      <div class="schedule-info">
        <h3 class="schedule-title">
          Entretien avec
          {{ postulations[0]?.job?.nom_recruteur || 'le recruteur' }} (30 min).
        </h3>
        <p class="schedule-description">
          Entretien vidéo via la plateforme Thanks-Boss.
        </p>
        <h6 class="schedule-description">
          Choisissez une date et un créneau horaire
        </h6>
      </div>
    </div>

    <div class="time-calendar-wrapper">
      <TimeCalendar
        :postulations="postulations"
        :todayDate="todayDate"
        :userRole="userRole"
        :entretiens="entretiens"
        :getUser="getUser"
        @update:showRecruiterCalendar="
          $emit('update:showRecruiterCalendar', false)
        "
        @refreshData="$emit('refreshData')"
        @closeCalendar="$emit('update:showRecruiterCalendar', false)"
      />
    </div>
  </div>
</template>

<script>
  import TimeCalendar from './TimeCalendar';

  export default {
    name: 'RecruiterCalendar',

    components: {
      TimeCalendar,
    },

    props: {
      showRecruiterCalendar: {
        type: Boolean,
        required: true,
      },
      postulations: Array,
      todayDate: String,
      userRole: String,
      entretiens: Array,
      getUser: {
        type: Object,
        required: true,
      },
    },
  };
</script>

<style scoped>
  .container {
    width: 100%;
    margin: 50px auto;
    padding: 20px;
    border-radius: 15px;
    background-color: var(--surface-bg-2);
    position: relative;
  }

  .back-btn {
    position: absolute;
    top: 0;
    left: 0;
    margin: 20px;
  }
  .schedule-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 20px;
  }
  .schedule-description {
    font-style: italic;
  }
  .schedule-subtitle {
    font-weight: 500;
  }

  .time-calendar-wrapper {
    max-height: 60vh;
  }
</style>
