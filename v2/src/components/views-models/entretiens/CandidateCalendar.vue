<template>
  <div class="container">
    <div class="Info-container">
      <!-- Header Row with Image Icon and Text -->
      <div class="schedule-info">
        <h3 class="schedule-title">Entretien avec ... (30 min).</h3>
        <p class="schedule-description">
          Entretien vidéo via la plateforme Thanks-Boss.
        </p>
        <h6 class="schedule-description">
          Choisissez une date et un créneau horaire
        </h6>
      </div>
    </div>

    <TimeCalendar
      :postulations="postulations"
      :todayDate="todayDate"
      :entretien="entretien"
      :userRole="userRole"
      :entretiens="entretiens"
    />
  </div>
</template>

<script>
  import TimeCalendar from './TimeCalendar';

  export default {
    name: 'CandidateCalendar',

    components: {
      TimeCalendar,
    },

    props: {
      postulations: Array,
      todayDate: String,
      entretien: Object,
      userRole: String,
      entretiens: Array,
    },

    data() {
      return {
        currentDate: new Date(),
        selectedDuration: '',
        selectedMonthYear: '',
        currentYear: new Date().getFullYear(),
        monthYearOptions: [],
        timeSlots: [],
        selectedDate: null,
        selectedSlots: {}, // Store selected slots for each date
        showPopup: false,
        activeNav: null,
        applyToAllWeeks: false,
      };
    },

    computed: {
      weekDays() {
        let days = [];

        // Start at the beginning of the selected month
        let firstDayOfMonth = new Date(
          this.currentDate.getFullYear(),
          this.currentDate.getMonth(),
          1
        );
        let firstMonday = new Date(firstDayOfMonth);

        // Find the first Monday of the selected month
        while (firstMonday.getDay() !== 1) {
          firstMonday.setDate(firstMonday.getDate() - 1);
        }

        // Populate exactly 7 days (from Monday to Sunday)
        for (let i = 0; i < 7; i++) {
          days.push({
            date: new Date(firstMonday),
            weekday: ['LU', 'MA', 'ME', 'JE', 'VE', 'SA', 'DI'][i],
          });
          firstMonday.setDate(firstMonday.getDate() + 1);
        }

        return days; // Ensuring no extra columns are added
      },
    },

    mounted() {
      this.generateMonthYearOptions();
      this.selectedMonthYear = `${this.getMonthLabel(new Date().getMonth())} ${this.currentYear}`;
      this.timeSlots = this.generateTimeSlots(1, 24);
      this.selectedDate = this.currentDate;
    },
    methods: {
      // Generate available months for the dropdown
      generateMonthYearOptions() {
        const months = [
          'Jan',
          'Fév',
          'Mar',
          'Avr',
          'Mai',
          'Juin',
          'Juil',
          'Août',
          'Sep',
          'Oct',
          'Nov',
          'Déc',
        ];
        this.monthYearOptions = months.map((month, index) => ({
          label: `${month} ${this.currentYear}`,
          value: `${index + 1}-${this.currentYear}`,
        }));
      },
      applyScheduleToAllWeeks() {
        if (Object.keys(this.selectedSlots).length === 0) {
          //console.log('No slots selected for the current week.');
          return;
        }

        const firstWeekKeys = Object.keys(this.selectedSlots);
        let baseDate = new Date(this.currentDate);
        let month = baseDate.getMonth();
        let year = baseDate.getFullYear();

        // Loop through the next weeks within the same month
        for (let i = 1; i <= 3; i++) {
          // Applying to next 3 weeks
          firstWeekKeys.forEach((dayKey) => {
            let originalDate = new Date(dayKey);
            let newDate = new Date(originalDate);
            newDate.setDate(originalDate.getDate() + i * 7); // Move forward by a week

            // Ensure the new date is within the same month
            if (
              newDate.getMonth() === month &&
              newDate.getFullYear() === year
            ) {
              const newDateKey = newDate.toDateString();

              // Ensure deep copy of selected slots to avoid reference issues
              if (!this.selectedSlots[newDateKey]) {
                this.selectedSlots[newDateKey] = [
                  ...this.selectedSlots[dayKey],
                ];
              }
            }
          });
        }

        this.selectedSlots = { ...this.selectedSlots }; // Trigger Vue reactivity
        //console.log(
          'Schedule applied to all weeks of the month.',
          this.selectedSlots
        );
      },

      // Get the month label from an index (0-11)
      getMonthLabel(monthIndex) {
        const months = [
          'Jan',
          'Fév',
          'Mar',
          'Avr',
          'Mai',
          'Juin',
          'Juil',
          'Août',
          'Sep',
          'Oct',
          'Nov',
          'Déc',
        ];
        return months[monthIndex];
      },

      saveChanges() {
        if (this.applyToAllWeeks) {
          this.applyScheduleToAllWeeks();
        }

        const savedData = {
          monthYear: this.selectedMonthYear,
          duration: this.selectedDuration,
          selectedSlots: this.selectedSlots,
          applyToAllWeeks: this.applyToAllWeeks,
        };

        //console.log('Saved Schedule:', savedData);
      },

      // Simulate clicking the dropdown when the icon is clicked
      toggleDropdown() {
        const dropdown = this.$refs.dropdownSelect;
        dropdown.disabled = false;
        dropdown.focus();
        dropdown.dispatchEvent(new Event('mousedown'));

        setTimeout(() => {
          dropdown.abled = true;
        }, 200);
      },

      // Update selected month-year when changed
      updateSelection() {
        //console.log('Selected Month-Year:', this.selectedMonthYear);

        let [month, year] = this.selectedMonthYear.split('-');
        let selectedDate = new Date(parseInt(year), parseInt(month) - 1, 1);

        // Update the calendar date while ensuring it reacts properly
        this.currentDate = selectedDate;
      },

      generateTimeSlots(startHour, endHour) {
        const slots = [];
        for (let hour = startHour; hour <= endHour; hour++) {
          slots.push(`${hour.toString().padStart(2, '0')}:00`);
        }
        return slots;
      },
      previousWeek() {
        let newDate = new Date(this.currentDate);
        newDate.setDate(newDate.getDate() - 7);
        this.currentDate = newDate;
      },
      nextWeek() {
        let newDate = new Date(this.currentDate);
        newDate.setDate(newDate.getDate() + 7);
        this.currentDate = newDate;
      },
      formatTime(time) {
        const [hour] = time.split(':');
        return `${hour.padStart(2, '0')}:00`;
      },

      isSelected(date) {
        return (
          this.selectedDate &&
          date.toDateString() === this.selectedDate.toDateString()
        );
      },
      selectDate(date) {
        this.selectedDate = date;
      },
      isSlotSelected(date, time) {
        const dateKey = date.toDateString();
        return (
          this.selectedSlots[dateKey] &&
          this.selectedSlots[dateKey].includes(time)
        );
      },
      selectSlot(date, time) {
        if (!this.selectedDate) {
          alert('Please select a date first!'); // Show an alert or disable interaction
          return;
        }

        if (!this.isSelected(date)) {
          return; // Prevent clicking on slots of unselected dates
        }

        const dateKey = date.toDateString();

        if (!this.selectedSlots[dateKey]) {
          this.selectedSlots[dateKey] = [];
        }

        const index = this.selectedSlots[dateKey].indexOf(time);

        if (index > -1) {
          this.selectedSlots[dateKey].splice(index, 1); // Deselect slot
        } else {
          this.selectedSlots[dateKey].push(time); // Select slot
        }

        // Ensure Vue detects the change
        this.selectedSlots = { ...this.selectedSlots };
      },
    },
  };
</script>

<style scoped>
  .container {
    width: 100%;
    margin: 0 auto;
    border-radius: 15px;
    background-color: var(--surface-bg-2);
  }

  .calendar-table-wrapper {
    display: flex;
    max-height: 600px;
    display: block;
    overflow: hidden;
  }

  .calendar-table {
    background: #fffdfc;
    max-height: 489px;

    border-collapse: collapse;
    overflow: hidden;
  }
  .calendar-header {
    position: sticky;
    top: 0;
    background: white;
    z-index: 10;
  }
  .calendar-table-container {
    max-height: 600px; /* Ensure it doesn't stretch too much */
    position: relative;
    margin-left: 10%;
    border-bottom: 2px solid darkgrey;
  }

  .calendar-table tbody {
    display: block;
    overflow: overlay;

    max-height: 600px; /* Scrollable body */
    padding-left: 6px;
  }

  /* Ensure table layout is consistent */
  .calendar-table thead,
  .calendar-table tbody tr {
    display: table;
    width: 100%;
    table-layout: fixed;
  }
  .calendar-table th,
  .calendar-table td {
    border: 1px solid grey;
    text-align: center;
    padding: 20px;
  }
  .time-slot {
    cursor: pointer;
    height: 50px;
    background: #fffdfc;
  }
  .selectedSlot {
    background: #58a09633;
  }
  .disabledSlot {
    background: #fffdfc;
    cursor: not-allowed;
  }
  .save-button-container {
    margin-top: 20px;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 20px;
  }
  .save-btn {
    padding: 10px 20px;
    background: orange;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 40px;
    margin-bottom: 30px;
  }

  .time-label {
    font-size: 10px;
    font-weight: 500;
    text-align: top; /* Aligns text to the left */
    padding-left: 5px;
    position: relative;
    top: -27px;
    left: 40px;
  }
  /* New row to align navigation and dropdown */
  /* Container for Navigation, Month-Year Selection, and Duration */
  .nav-selection-row {
    display: flex;
    align-items: center;
    justify-content: left;
    gap: 10px;
    margin-bottom: 10px;
    padding: 10px;
  }

  /* Navigation buttons */
  .navigation {
    display: flex;
    gap: 5px;
    border-right: 10px;
    margin-right: 30px;
  }

  .nav-button {
    width: 24px;
    height: 24px;
    padding: 5px 15px;
    cursor: pointer;

    border-radius: 4px;
  }

  /* Month-Year Dropdown */
  .dropdown-container {
    display: flex;
    align-items: center;
    width: 110px !important;
    padding-right: 10px;
    margin-right: 40px;
    height: 10px;
    margin-bottom: 60px;
  }

  /* Dropdown Icon */
  .dropdown-icon {
    width: 34px;
    height: 34px;
    margin-top: 30px;
    margin-right: -18px;

    cursor: pointer;
  }

  /* Duration Selection */
  .duration-container {
    display: flex;
    align-items: center;

    width: 140px;
    height: 36px;
    border-color: #58a09633;

    border: 2px solid #58a096; /* Add border around the select box */
    border-radius: 10px; /* Optional: Rounded corners */
    margin-left: -20px;
  }

  .duration-select {
    padding: 10px;
    border: none;

    font-size: 10px;
    background: transparent;
    cursor: pointer;
    font-weight: 500;

    appearance: auto;
    -webkit-appearance: auto;
    -moz-appearance: auto;
  }

  /* Style for the dropdown container */
  .dropdown-container {
    position: relative;
    display: inline-block;
    width: 250px;
  }

  /* Style the dropdown */
  .custom-dropdown {
    padding: 5px;
    max-width: 139px;
    height: 50px;
    padding: 10px;

    background-color: white;
    appearance: auto;

    border: none;
    border-bottom: 2px solid black;
    font-size: 19px;
    background-color: transparent;
    cursor: pointer;
  }

  /* Dropdown icon positioning */
  .dropdown-icon {
    position: absolute;
    top: 50%;
    right: 40px;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    cursor: pointer;
  }
  /* Make the border of the first column (time labels) invisible */
  .time-label {
    border-left: 2px solid transparent !important;

    border-top: 2px solid transparent !important;
    border-bottom: 2px solid transparent !important;

    background-color: inherit; /* Keeps the background the same as other cells */
  }

  /* Make the border of the first row (days and dates) invisible */
  .calendar-table thead th {
    border-top: 2px solid transparent !important; /* Hides the top border */
    background-color: inherit; /* Keeps the background unchanged */
  }

  .calendar-table thead {
    position: sticky;
    top: 0;
    background: white; /* Ensures visibility when scrolling */
    z-index: 2; /* Keeps it above other content */
  }
  /* Remove vertical borders between columns in the first row */
  .day-column {
    border-right: 1px solid transparent !important;
  }
  .time-column {
    border-left: 1px solid transparent !important;
    border-bottom: 2px solid transparent !important;
    border-right: 1px solid transparent !important;
  }

  .extra-info-row {
    height: 7px !important;

    line-height: 1px !important;

    background-color: #f9f9f9;
    border-bottom: 2px solid black; /* Ensure a bottom border */
    border-top: 2px solid black !important;
    padding-right: 50px;
    margin: 0;
  }

  .extra-info-cell {
    text-align: center;
    font-weight: bold;
    border: 1px solid grey; /* Make sure each cell has a border */
    font-size: 5px;
    padding: 0 !important;
    height: 10px !important;
  }

  table {
    border-collapse: collapse;
  }
  /* Keep first column (time column) aligned */
  .extra-time-column {
    width: 13px; /* Match time column width */
    height: 8px;
    border-right: 1px solid grey;
    border-left: none !important; /* Remove left border */
    overflow-y: hidden;
    font-size: 7px;

    text-align: right;
    font-weight: bold;
    font-size: 8px; /* Adjust size */
    white-space: nowrap; /* Prevents breaking if needed */
    line-height: 1; /* Adjust line height */
    vertical-align: middle; /* Centers text vertically */
  }

  .day-date-row {
    position: sticky;
    top: 0;
    background: white;
    z-index: 10;
  }
  .day-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 5px;
  }

  .day-text {
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 4px;
  }

  .date-circle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #eee;
    font-size: 14px;
    font-weight: 600;
    margin-top: 5px;
  }

  .highlighted {
    background-color: #58a096;
    color: white;
  }

  .calendar-table tbody::-webkit-scrollbar {
    width: 10px; /* Set scrollbar width */
    color: black;
    height: 150px;
  }

  .calendar-table tbody::-webkit-scrollbar-track {
    background: transparent;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3); /* Drop shadow effect */
  }

  .calendar-table tbody::-webkit-scrollbar-thumb {
    background: white; /* Scrollbar color */
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.5); /* Stronger drop shadow */
  }

  /* Displace scrollbar slightly to the right */
  .calendar-table-container {
    padding-right: 100px; /* Push scrollbar further to the right */
    margin-left: 10%;
    position: relative; /* Adjusts the position */
    border-bottom: 2px black;
  }

  .selected-date {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    border-radius: 50%;

    background-color: orange;
    font-size: 14px;
    font-weight: 600;
    margin-top: 5px;
  }
  .selectedSlot {
    background: #58a09633; /* Highlight selected slots */
    color: black;
    font-weight: bold;
  }

  /* Full-screen overlay to prevent background interaction */
  /* Full-screen overlay to prevent background interaction */
  .popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(123, 123, 123, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    pointer-events: auto; /* Ensures background is not clickable */
    backdrop-filter: blur(2px);
    transition: background 0.3s ease-in-out;
  }

  /* Centered popup */
  .popup {
    position: relative;
    background: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);

    min-width: 680px;
    height: 250px;
  }

  /* Close button (Top-right corner) */
  .close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: black;
  }

  /* Popup message */
  .popup-message {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 20px;
  }

  /* Retour button (Black) */
  .popup-btn {
    padding: 10px 20px;
    background: black;
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-size: 16px;
  }
  .nav-icon {
    filter: grayscale(100%) brightness(0); /* Default color (black/gray) */
    transition: filter 0.3s ease;
  }

  /* ✅ Change icon color when active */
  .nav-icon.active {
    filter: brightness(0) saturate(100%) invert(32%) sepia(59%) saturate(575%)
      hue-rotate(127deg) brightness(86%) contrast(86%);
  }

  .apply-to-all-weeks-container {
    display: flex;
    align-items: center;
    margin-top: 15px;
    font-size: 14px;
  }

  .apply-to-all-weeks-container input {
    margin-right: 10px;
    cursor: pointer;
  }
</style>
