<template>
  <div class="purchase-container-left">
    <div class="purchase-recap-container">
      <!-- Contenu de l'étape 2 : Informations de facturation -->

      <div class="purchase-recap-container">
        <div class="purchase-card-header">
          <div class="purchase-card-header-title">
            <div class="purchase-card-header-number-valid">1</div>
            <p>Récapitulatif</p>
            <p>/</p>
            <div class="purchase-card-header-number-valid">2</div>
            <p>Informations</p>
            <p>/</p>
            <div v-if="isApplicant" class="purchase-card-header-div">
              <div class="purchase-card-header-number">3</div>
              <p>Paiement</p>
            </div>
            <div v-else class="purchase-card-header-div">
              <div class="purchase-card-header-number">3</div>
              <p>Devis</p>
              <p>/</p>
              <div class="purchase-card-header-number">4</div>
              <p>Paiement</p>
            </div>
          </div>
        </div>
        <h5>Informations de facturation</h5>
        <hr />
        <div class="input-container">
          <label>Nom</label>
          <input
            type="text"
            placeholder="Votre nom"
            v-model="currentUser.last_name"
          />
        </div>
        <hr class="hr-white" />
        <div class="input-container">
          <label>Prénom</label>
          <input
            type="text"
            placeholder="Votre prénom"
            v-model="currentUser.first_name"
          />
        </div>
        <hr class="hr-white" />
        <div v-if="!isApplicant" class="input-container">
          <label>Entreprise</label>
          <input
            type="text"
            placeholder="Nom de votre entreprise"
            v-model="currentUser.company"
          />
        </div>
        <hr class="hr-white" />
        <div v-if="!isApplicant" class="input-container">
          <label>SIRET</label>
          <input
            type="text"
            placeholder="XXX XXX XXX XXXXX"
            v-model="currentUser.siret"
          />
        </div>
        <hr class="hr-white" />
        <div class="input-address">
          <label>Adresse de facturation</label>
          <div class="address">
            <input
              class="road"
              type="text"
              placeholder="Numéro, voie"
              v-model="currentUser.adress"
            />
            <div class="city">
              <input
                class="zip-code"
                type="text"
                placeholder="XXXXX"
                :value="cleanCodePostal"
                @input="currentUser.code_postal = $event.target.value"
              />
              <input
                class="city-name"
                type="text"
                placeholder="Ville"
                v-model="currentUser.ville"
              />
            </div>
          </div>
        </div>
        <hr class="hr-white" />
        <div class="input-container">
          <label>Mail</label>
          <input
            type="email"
            placeholder="<EMAIL>"
            v-model="currentUser.email"
          />
        </div>
        <hr class="hr-white" />
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    props: ['currentUser', 'isApplicant'],

    computed: {
      cleanCodePostal() {
        return this.currentUser.code_postal === 'undefined'
          ? ''
          : this.currentUser.code_postal;
      },
    },
  };
</script>

<style scoped>
  .container {
    display: flex;
    flex-direction: column;
  }

  .purchase-container {
    width: 90%;
    margin: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: flex-start;
  }

  .purchase-header {
    width: 75%;
    margin: 16px auto;
  }

  .purchase-container-left {
    width: 95%;
    display: flex;
    flex-direction: column;
    margin-top: 55px;
  }

  .purchase-recap-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
  }

  .purchase-card-header {
    display: none;
  }

  .purchase-card-header-div {
    display: flex;
    align-items: center;
  }

  form {
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .input-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  label {
    font-size: 16px;
    width: 25%;
  }

  .input-container input {
    margin-left: 16px;
    background-color: var(--surface-bg-4);
    width: 80%;
    margin-top: 8px;
    margin-bottom: 8px;
  }

  .input-address {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .input-address input {
    margin-left: 16px;
    background-color: var(--surface-bg-4);
    width: 96%;
    margin-top: 8px;
    margin-bottom: 8px;
  }

  .address {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }

  .city {
    display: flex;
    width: 100%;
  }

  .zip-code {
    width: 25% !important;
  }

  .city-name {
    width: 75% !important;
  }

  .hr-white {
    padding: 1px;
    background: var(--white-200);
    border: 0;
  }

  .purchase-container-right {
    width: 95%;
    height: fit-content;
    display: flex;
    flex-direction: column;
    margin-top: 55px;
  }

  .info-link,
  .next-step-btn {
    align-self: center;
  }

  @media screen and (min-width: 992px) {
    .container {
      width: 80%;
      margin-top: inherit;
      margin: auto;
    }

    .purchase-container {
      display: flex;
      flex-direction: row;
      justify-content: space-evenly;
    }

    .purchase-container-left {
      width: 45%;
    }

    .product-name {
      flex-direction: row;
    }

    .purchase-card-header {
      display: flex;
    }

    .purchase-card-header p,
    .purchase-card-header div {
      margin-right: 12px;
    }

    .purchase-card-header-title {
      margin: 8px;
      display: flex;
      align-items: center;
    }

    .purchase-card-header-number {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 8px;
      height: 24px;
      width: 24px;
      border: 1px solid black;
      border-radius: 50%;
      text-align: center;
    }

    .purchase-card-header-number-valid {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 8px;
      height: 24px;
      width: 24px;
      color: var(--white-200);
      background-color: var(--black-200);
      border-radius: 50%;
      text-align: center;
    }

    .purchase-container-right {
      width: 30%;
    }

    .info-link,
    .next-step-btn {
      align-self: flex-end;
      margin-right: 0;
      margin-top: 24px;
    }
  }

  /* * {
    border: 1px red solid;
  } */
</style>
