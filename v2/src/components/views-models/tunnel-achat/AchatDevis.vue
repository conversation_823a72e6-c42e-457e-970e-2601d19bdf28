<template>
  <div class="purchase-container-left">
    <div class="purchase-devis-container">
      <!-- Contenu de l'étape 3 : Devis -->

      <div class="purchase-devis-container">
        <div class="purchase-card-header">
          <div class="purchase-card-header-title">
            <div class="purchase-card-header-number-valid">1</div>
            <p>Récapitulatif</p>
            <p>/</p>
            <div class="purchase-card-header-number-valid">2</div>
            <p>Informations</p>
            <p>/</p>
            <div class="purchase-card-header-number-valid">3</div>
            <p>Devis</p>
            <p>/</p>
            <div class="purchase-card-header-number">4</div>
            <p>Paiement</p>
          </div>
        </div>
        <h5>Récapitulatif</h5>
        <hr />
        <div class="info-display">
          <p>Nom de l'entreprise</p>
          <h6>{{ currentUser.company }}</h6>
        </div>
        <hr class="hr-white" />
        <div class="info-display">
          <p>SIRET</p>
          <h6>{{ currentUser.siret }}</h6>
        </div>
        <hr class="hr-white" />
        <div class="info-display">
          <p>Email</p>
          <h6>{{ currentUser.email }}</h6>
        </div>
        <hr class="hr-white" />
        <hr class="hr-white" />
        <div class="modify-btn">
          <PrimaryRoundedButton
            textContent="Modifier"
            btnColor="light"
            @click="modify"
            modify
          />
        </div>
        <hr class="hr-white" />
        <h5>Devis</h5>
        <hr />
        <p>Vous allez recevoir votre devis par mail</p>
        <p>
          Vous pouvez également consulter votre devis sur la plateforme
          Thanks-Boss dans Compte / Abonnement / Historique de facturation.
        </p>
        <div class="devis-btn">
          <PrimaryRoundedButton
            textContent="Télécharger mon devis"
            btnColor="light"
            download
            @click="downloadPDF()"
          />
          <PrimaryRoundedButton
            textContent="Accéder à mon devis"
            btnColor="secondary"
            look
            @click="openPDF()"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import pdfMake from 'pdfmake/build/pdfmake';
  import pdfFonts from 'pdfmake/build/vfs_fonts';
  import PrimaryRoundedButton from '../../buttons/PrimaryRoundedButton.vue';

  // Set the fonts for pdfMake.
  pdfMake.vfs = pdfFonts.vfs;

  export default {
    components: {
      PrimaryRoundedButton,
    },
    props: {
      currentUser: Object,
      activeSubscription: Object,
      factures: {
        type: Array,
        default: () => [],
      },
    },

    computed: {
      latestFacture() {
        if (!this.factures.length) return null;
        // Optionnel : trier pour s'assurer de prendre la plus récente
        return [...this.factures].sort(
          (a, b) => new Date(b.date) - new Date(a.date)
        )[0];
      },
    },

    methods: {
      generateDevisPDF() {
        const {
          first_name,
          last_name,
          company,
          email,
          siret,
          adress,
          code_postal,
          ville,
        } = this.currentUser;
        const { prix_ht, prix_ttc, nom } = this.activeSubscription;

        const matchedFacture = this.factures.find(
          (facture) => facture.type_produit === this.activeSubscription.id
        );
        const devisNumber = matchedFacture?.number || 'N/A';

        const date = new Date().toLocaleDateString();
        // Définition du contenu du PDF
        return {
          content: [
            { text: `Devis n° : ${devisNumber}`, style: 'header' },
            { text: `Client : ${first_name} ${last_name}`, style: 'subheader' },
            { text: `Société : ${company}`, style: 'subheader' },
            { text: `SIRET : ${siret}`, style: 'subheader' },
            {
              text: `Adresse : ${adress}, ${code_postal} ${ville}`,
              style: 'subheader',
            },
            { text: `Email : ${email}`, style: 'subheader' },
            { text: `Date du devis : ${date}`, style: 'subheader' },
            { text: ' ', margin: [0, 10] }, // Espacement
            {
              table: {
                widths: ['*', '*'],
                body: [
                  ['Abonnement', `${nom}`],
                  ['Prix HT', `${prix_ht} €`],
                  ['TVA', `${(prix_ttc - prix_ht).toFixed(2)} €`],
                  ['Prix total', `${prix_ttc} €`],
                ],
              },
            },
          ],
          styles: {
            header: {
              fontSize: 18,
              bold: true,
            },
          },
          defaultStyle: {
            font: 'Roboto',
          },
        };
      },
      downloadPDF() {
        const devis = this.generateDevisPDF();
        pdfMake.createPdf(devis).download('devis.pdf');
      },
      openPDF() {
        const devis = this.generateDevisPDF();
        pdfMake.createPdf(devis).open();
      },
      modify() {
        this.$emit('modify');
      },
    },
  };
</script>

<style scoped>
  .container {
    display: flex;
    flex-direction: column;
  }

  .purchase-container {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: flex-start;
    width: 90%;
    margin: auto;
  }

  .purchase-container-left {
    width: 95%;
  }

  p {
    margin: 8px 0;
  }

  .purchase-devis-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .info-display {
    display: flex;
    justify-content: space-between;
  }

  .purchase-card-header {
    display: none;
  }

  .modify-btn {
    align-self: center;
    margin: 16px 0;
  }

  .devis-btn {
    height: 90px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin: 16px 0;
  }

  .hr-white {
    padding: 1px;
    background: var(--white-200);
    border: 0;
  }

  .purchase-container-left {
    width: 95%;
    display: flex;
    flex-direction: column;
    margin-top: 55px;
  }

  .info-link,
  .next-step-btn {
    align-self: center;
  }

  @media screen and (min-width: 992px) {
    .container {
      width: 80%;
      height: 100%;
      margin: auto;
    }

    .purchase-container {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-evenly;
    }

    .purchase-container-left {
      width: 45%;
    }

    .info-display {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }

    .devis-btn {
      flex-direction: row;
      justify-content: space-between;
      margin: 16px 0;
    }

    .modify-btn {
      align-self: flex-end;
    }

    .purchase-card-header {
      display: flex;
    }

    .purchase-card-header p,
    .purchase-card-header div {
      margin-right: 12px;
    }

    .purchase-card-header-title {
      margin: 8px;
      display: flex;
      align-items: center;
    }

    .purchase-card-header-number {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 8px;
      height: 24px;
      width: 24px;
      border: 1px solid black;
      border-radius: 50%;
      text-align: center;
    }

    .purchase-card-header-number-valid {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 8px;
      height: 24px;
      width: 24px;
      color: var(--white-200);
      background-color: var(--black-200);
      border-radius: 50%;
      text-align: center;
    }

    .purchase-container-right {
      width: 30%;
    }

    .info-link,
    .next-step-btn {
      align-self: flex-end;
      margin-right: 9%;
      margin-top: 24px;
    }
  }

  /* * {
    border: 1px red solid;
  } */
</style>
