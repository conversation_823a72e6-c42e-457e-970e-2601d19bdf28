<template>
  <div class="purchase-container-left">
    <div class="purchase-recap-container">
      <!-- Contenu de l'étape 1 : Récapitulatif de la commande -->

      <div class="purchase-card-header">
        <div class="purchase-card-header-title">
          <div class="purchase-card-header-number-valid">1</div>
          <p>Récapitulatif</p>
          <p>/</p>
          <div class="purchase-card-header-number">2</div>
          <p>Informations</p>
          <p>/</p>
          <div v-if="isApplicant" class="purchase-card-header-div">
            <div class="purchase-card-header-number">3</div>
            <p>Paiement</p>
          </div>
          <div v-else class="purchase-card-header-div">
            <div class="purchase-card-header-number">3</div>
            <p>Devis</p>
            <p>/</p>
            <div class="purchase-card-header-number">4</div>
            <p>Paiement</p>
          </div>
        </div>
      </div>

      <h5>Récapitulatif de ma commande</h5>
      <hr />
      <div class="product-name">
        <p>{{ activeSubscription.nom }}</p>
        <PrimaryRoundedButton
          btnColor="light"
          textContent="Modifier mon abonnement"
          @click="gotoPage(`/tarifs/`)"
          modify
        />
      </div>
      <hr class="hr-white" />
      <div class="info-display">
        <p>Options</p>
      </div>
      <hr class="hr-white" />
      <div class="info-display">
        <p>Type d'abonnement</p>
        <h6>Mensuel</h6>
      </div>
      <hr class="hr-white" />
      <div class="info-display">
        <p>Prélèvement mensuel à la date de paiement</p>
        <h6>{{ currentDateFormatted }}</h6>
      </div>
      <hr class="hr-white" />
    </div>
  </div>
</template>

<script>
  import gotoPage from '@/utils/router.js';
  import PrimaryRoundedButton from '../../../components/buttons/PrimaryRoundedButton.vue';

  export default {
    components: {
      PrimaryRoundedButton,
    },
    props: ['activeSubscription', 'currentDate', 'isApplicant'],
    computed: {
      currentDateFormatted() {
        const date = new Date();
        return date.toLocaleDateString('fr-FR', {
          day: 'numeric',
          month: 'long',
          year: 'numeric',
        });
      },
    },
    methods: {
      gotoPage,
    },
  };
</script>

<style scoped>
  .container {
    display: flex;
    flex-direction: column;
  }

  h6 {
    text-align: right;
  }

  p {
    line-height: 32px;
  }

  .purchase-container {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .purchase-container-left {
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .purchase-recap-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
  }

  .product-name {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .purchase-card-header {
    display: none;
  }

  hr {
    margin: 8px 0;
  }

  .info-display {
    display: flex;
    justify-content: space-between;
  }

  .hr-white {
    padding: 1px;
    background: var(--white-200);
    border: 0;
  }

  .purchase-container-right {
    width: 95%;
    display: flex;
    flex-direction: column;
    margin-top: 55px;
  }

  .info-link,
  .next-step-btn {
    align-self: center;
  }

  @media screen and (min-width: 992px) {
    .container {
      width: 80%;
      height: 100%;
      display: flex;
      flex-direction: column;
      margin-top: inherit;
      margin: auto;
    }

    .purchase-container {
      width: 90%;
      margin: auto;
      display: flex;
      flex-direction: row;
      justify-content: space-evenly;
      align-items: flex-start;
    }

    .purchase-header {
      width: 75%;
      margin: 16px auto;
    }

    .purchase-header-btn {
      align-self: flex-start;
    }

    .purchase-container-left {
      width: 45%;
    }

    .product-name {
      flex-direction: row;
    }

    .purchase-card-header {
      display: flex;
    }

    .purchase-card-header p,
    .purchase-card-header div {
      margin-right: 12px;
    }

    .purchase-card-header-div {
      display: flex;
      align-items: center;
    }

    .purchase-card-header-title {
      margin: 8px;
      display: flex;
      align-items: center;
    }

    .purchase-card-header-number {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 8px;
      height: 24px;
      width: 24px;
      border: 1px solid black;
      border-radius: 50%;
      text-align: center;
    }

    .purchase-card-header-number-valid {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 8px;
      height: 24px;
      width: 24px;
      color: var(--white-200);
      background-color: var(--black-200);
      border-radius: 50%;
      text-align: center;
    }

    .purchase-container-right {
      width: 30%;
    }

    .info-link,
    .next-step-btn {
      align-self: flex-end;
      margin-right: 0;
      margin-top: 24px;
    }
  }
</style>
