<template>
  <div class="purchase-container-left">
    <div class="purchase-devis-container">
      <!-- Contenu de l'étape 4 : Paiement -->
      <div class="purchase-devis-container">
        <div class="purchase-card-header">
          <div class="purchase-card-header-title">
            <div class="purchase-card-header-number-valid">1</div>
            <p>Récapitulatif</p>
            <p>/</p>
            <div class="purchase-card-header-number-valid">2</div>
            <p>Informations</p>
            <div v-if="isApplicant" class="purchase-card-header-div">
              <p>/</p>
              <div class="purchase-card-header-number-valid">3</div>
              <p>Paiement</p>
            </div>
            <div v-else class="purchase-card-header-div">
              <p>/</p>
              <div class="purchase-card-header-number-valid">3</div>
              <p>Devis</p>
              <p>/</p>
              <div class="purchase-card-header-number-valid">4</div>
              <p>Paiement</p>
            </div>
          </div>
        </div>
        <h5>Récapitulatif</h5>
        <hr />
        <div v-if="isApplicant">
          <div class="info-display">
            <p>Mon nom</p>
            <h6>
              {{ currentUser.last_name != null ? currentUser.last_name : '' }}
            </h6>
          </div>
          <hr class="hr-white" />
          <div class="info-display">
            <p>Mon prénom</p>
            <h6>
              {{ currentUser.first_name != null ? currentUser.first_name : '' }}
            </h6>
          </div>
          <hr class="hr-white" />
          <div class="info-display">
            <p>Mon mail</p>
            <h6>{{ currentUser.email != null ? currentUser.email : '' }}</h6>
          </div>
          <hr class="hr-white" />
        </div>
        <div v-else>
          <div class="info-display">
            <p>Nom de l'entreprise</p>
            <h6>
              {{ currentUser.company != null ? currentUser.company : '' }}
            </h6>
          </div>
          <hr class="hr-white" />
          <div class="info-display">
            <p>SIRET</p>
            <h6>{{ currentUser.siret != null ? currentUser.siret : '' }}</h6>
          </div>
          <hr class="hr-white" />
          <div class="info-display">
            <p>Email</p>
            <h6>{{ currentUser.email != null ? currentUser.email : '' }}</h6>
          </div>
          <hr class="hr-white" />
        </div>

        <div class="modify-btn">
          <PrimaryRoundedButton
            textContent="Modifier"
            btnColor="light"
            @click="modify"
            modify
          />
        </div>
        <p>Prélèvement mensuel à la date de paiement</p>
        <h5>Paiement</h5>
        <hr />
        <!-- <p>Choisissez votre mode de paiement</p> -->
        <div class="paiement-btn">
          <PrimaryRoundedButton textContent="Stripe" @click="submitStripe" />
          <!-- <PrimaryRoundedButton textContent="Paypal" @click="submitPaypal" /> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import PrimaryRoundedButton from '../../buttons/PrimaryRoundedButton.vue';

  export default {
    components: {
      PrimaryRoundedButton,
    },
    props: ['currentUser', 'isApplicant'],

    methods: {
      submitStripe() {
        this.$emit('submit-stripe');
      },
      submitPaypal() {
        this.$emit('submit-paypal');
      },
      modify() {
        // Émettre l'événement `previous-step` au parent
        this.$emit('modify');
      },
    },
  };
</script>

<style scoped>
  .container {
    display: flex;
    flex-direction: column;
  }

  .purchase-container {
    width: 90%;
    margin: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: flex-start;
  }

  .purchase-container-left {
    width: 95%;
    display: flex;
    flex-direction: column;
    margin-top: 55px;
  }

  h5 {
    margin: 16px 0;
  }

  p,
  h6 {
    margin: 8px 0;
  }

  .purchase-devis-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .purchase-card-header {
    display: none;
  }

  .modify-btn {
    align-self: center;
    margin-bottom: 40px;
    margin-top: 16px;
  }

  .paiement-btn {
    display: flex;
    justify-content: flex-start;
    margin: 16px 0;
  }

  .hr-white {
    padding: 1px;
    background: var(--white-200);
    border: 0;
  }

  .info-display {
    display: flex;
    justify-content: space-between;
  }

  .info-display h6 {
    align-self: center;
  }

  .info-link {
    align-self: center;
  }

  .next-step-btn .PrimaryRoundedButton {
    align-self: center;
  }

  @media screen and (min-width: 992px) {
    .container {
      width: 80%;
      display: flex;
      margin: auto;
    }

    .purchase-container {
      display: flex;
      flex-direction: row;
      justify-content: space-evenly;
    }

    .purchase-header {
      width: 75%;
      margin: 16px auto;
    }

    .purchase-header-btn {
      align-self: flex-start;
    }

    .purchase-container-left {
      width: 45%;
    }

    .purchase-card-header {
      display: flex;
    }

    .purchase-card-header-div {
      display: flex;
      align-items: center;
    }

    .purchase-card-header p,
    .purchase-card-header div {
      margin-right: 12px;
    }

    .purchase-card-header-title {
      margin: 8px;
      display: flex;
      align-items: center;
    }

    .purchase-card-header-number {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 8px;
      height: 24px;
      width: 24px;
      border: 1px solid black;
      border-radius: 50%;
      text-align: center;
    }

    .purchase-card-header-number-valid {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 8px;
      height: 24px;
      width: 24px;
      color: var(--white-200);
      background-color: var(--black-200);
      border-radius: 50%;
      text-align: center;
    }

    .devis-btn {
      flex-direction: row;
      justify-content: space-between;
      margin: 16px 0;
    }

    .modify-btn {
      align-self: flex-end;
      margin-bottom: 40px;
      margin-top: 16px;
    }

    .purchase-container-right {
      width: 30%;
    }

    .info-link {
      align-self: flex-end;
      margin-right: 9%;
      margin-top: 24px;
    }

    .next-step-btn {
      align-self: flex-end;
      margin-right: 0;
      margin-top: 24px;
    }
  }

  /* * {
    border: 1px red solid;
  } */
</style>
