<template>
  <div class="post-card">
    <div class="post-header">
      <div v-if="post.boost > 0" class="sponsorise">Sponsorisé</div>
      <PostMenuOptions
        :item="post"
        type="post"
        :currentUser="currentUser"
        :deleteAction="deleteThisPost"
      />
      <a :href="`/utilisateur/${post.user.id}`">
        <UserAvatar :user="post.user" :width="40" />
        <h3>{{ post.user.first_name }} {{ post.user.last_name }}</h3>
      </a>
    </div>

    <div class="post-content">
      <p v-html="formatText(post.text)"></p>
      <div v-if="post.image">
        <img :src="getImageUrl(post.image)" alt="post image" />
      </div>
      <div v-if="post.video">
        <iframe
          :src="getVideoUrl(post.video)"
          controls
          muted
          autoplay
          playsinline
          loop
          @play="handleVideoPlay($event)"
          class="post-video"
        ></iframe>
      </div>
    </div>
    <hr class="post-divider" />

    <div class="info">
      <div @click="showModal()">
        <span class="post-time">
          Publié il y a {{ timeElapsed(post.date_creation) }}</span
        >
        <span v-if="likedUsers.length === 1" class="liked-users">
          , {{ likedUsers[0].first_name }} {{ likedUsers[0].last_name }} a liké
        </span>

        <span v-else-if="likedUsers.length === 2" class="liked-users"
          >, {{ likedUsers[0].first_name }} {{ likedUsers[0].last_name }} et
          {{ likedUsers[1].first_name }} {{ likedUsers[1].last_name }} ont liké
        </span>

        <span v-else-if="likedUsers.length > 2" class="liked-users">
          , {{ likedUsers[likedUsers.length - 2].first_name }}
          {{ likedUsers[likedUsers.length - 2].last_name }},
          {{ likedUsers[likedUsers.length - 1].first_name }}
          {{ likedUsers[likedUsers.length - 1].last_name }} et
          {{ likedUsers.length - 2 }} autres ont liké
        </span>
      </div>

      <span class="post-comments" @click="toggleComments(postIndex)"
        >{{ comments.length }} commentaire(s)</span
      >
    </div>

    <div class="action-btn">
      <!-- Bouton pour afficher/masquer les commentaires -->
      <button class="comment" @click="toggleComments(postIndex)">
        <img
          src="@/assets/social/social-page-post-comment-icon.svg"
          alt="Icône de commentaire"
        />
        <v-tooltip activator="parent" location="top">Commentaire(s)</v-tooltip>
      </button>
      <LikeButton :post="post" :currentUser="currentUser" @like="likePost" />
    </div>

    <!-- Affichage des utilisateurs qui ont liké -->
    <div v-if="isModalVisible" class="modal">
      <ul>
        <li v-for="(liker, index) in likedUsers" :key="index">
          <a :href="`/recruiter/candidate/${liker.id}`"
            >{{ liker.first_name }} {{ liker.last_name }}</a
          >
        </li>
      </ul>
    </div>

    <!-- Accordéon pour afficher/masquer les commentaires -->
    <div v-if="isCommentsVisible" class="comments-section">
      <CommentSection
        :post="post"
        :comments="comments"
        :currentUser="currentUser"
        :isLoggedIn="isLoggedIn"
        @comments-loaded="emitCommentCount"
        @add-comment="addComment"
        @remove-comment="removeComment"
      />
    </div>
  </div>
</template>

<script>
  import LikeButton from '@/components/buttons/LikeButton.vue';
  import CommentSection from './CommentSection.vue';
  import { baseUrl } from '../../../services/axios';
  import {
    getCommentsForPost,
    deletePost,
    addLikePost,
    removeLikePost,
    getPostLikers,
  } from '../../../services/post.service';
  import { getUserById } from '../../../services/account.service';
  import PostMenuOptions from '../../buttons/PostMenuOptions.vue';
  import { mapActions } from 'vuex';
  import gotoPage from '@/utils/router.js';
  import UserAvatar from '../profil/UserAvatar.vue';

  export default {
    name: 'PostCard',
    props: {
      post: Object,
      currentUser: {
        type: Object,
        default: () => ({}), // Valeur par défaut si aucune donnée n'est transmise
      },
      commentCount: {
        type: Number,
        default: 0,
      },
      isLoggedIn: Boolean,
    },
    data() {
      return {
        observer: null, // Instance de l'IntersectionObserver
        comments: [],
        isCommentsVisible: false,
        likedUsers: [],
        isModalVisible: false,
      };
    },
    components: {
      LikeButton,
      CommentSection,
      PostMenuOptions,
      UserAvatar,
    },
    async created() {
      // Charge les utilisateurs qui ont liké dès la création du composant
      await this.loadLikedUsers();
    },
    watch: {
      // Observer les changements dans `post.likers` et mettre à jour la liste des utilisateurs qui ont liké
      'post.likers': 'loadLikedUsers',
      comments: {
        immediate: true,
        handler(newComments) {
          this.emitCommentCount();
        },
      },
    },
    methods: {
      ...mapActions(['addPost', 'removePost']),
      gotoPage,
      emitCommentCount() {
        this.$emit('update-comment-count', {
          postId: this.post.id,
          count: this.comments.length,
        });
      },
      toggleComments() {
        this.isCommentsVisible = !this.isCommentsVisible;
        this.fetchComments();
      },
      getImageUrl(photoUrl) {
        if (photoUrl) {
          return baseUrl + photoUrl;
        }
        return '/default-avatar.jpg'; // Image par défaut si aucune photo n'est présente
      },
      getVideoUrl(url) {
        const videoDetails = this.extractVideoDetails(url);
        if (videoDetails) {
          if (videoDetails.platform === 'youtube') {
            return `https://www.youtube.com/embed/${videoDetails.id}?autoplay=1&mute=1&controls=1&loop=1&playlist=${videoDetails.id}`;
          } else if (videoDetails.platform === 'vimeo') {
            return `https://player.vimeo.com/video/${videoDetails.id}?autoplay=1&muted=1&loop=1`;
          }
        }
        return ''; // Retourne une URL vide si l'ID de vidéo n'est pas trouvé
      },

      // Fonction pour extraire les détails de la vidéo en fonction de la plateforme
      extractVideoDetails(url) {
        const youtubeRegex =
          /(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=))([a-zA-Z0-9_-]{11})|(?:youtu\.be\/)([a-zA-Z0-9_-]{11})/;
        const vimeoRegex = /(?:vimeo\.com\/)(\d+)/;

        let match;

        // Vérifiez si c'est une vidéo YouTube
        if ((match = url.match(youtubeRegex))) {
          // Si l'URL est du type youtube.com/watch?v=XXX, l'ID est match[1]
          return { platform: 'youtube', id: match[1] || match[2] }; // Prend l'ID de la vidéo
        }

        // Vérifiez si c'est une vidéo Vimeo
        if ((match = url.match(vimeoRegex))) {
          return { platform: 'vimeo', id: match[1] };
        }

        return null; // Retourne null si la plateforme n'est pas reconnue
      },
      formatText(text) {
        // Vérifiez si 'text' est défini avant de tenter de le diviser
        if (!text) {
          return ''; // ou une chaîne vide pour éviter les erreurs
        }
        return text.replace(/(\r\n|\n|\r)/g, '<br/>');
      },
      handleIntersection(entries) {
        const video = this.$refs.video;

        const [entry] = entries;
        if (entry.isIntersecting) {
          video.play();
        } else {
          video.pause();
        }
      },
      async deleteThisPost(post) {
        const postId = post.id;

        try {
          // Appeler le service pour supprimer le post
          await deletePost(postId);
          // Émettre un événement pour informer le composant parent
          this.$emit('delete-post', postId);
          // Émettre un événement pour rafraîchir la liste des posts
          this.$emit('refresh-posts');
        } catch (error) {
          // Gérez les erreurs si la suppression échoue
          //console.error('Erreur lors de la suppression du post :', error);
        }
      },
      timeElapsed(dateString) {
        // Vérification de la date
        if (!dateString) {
          //console.error('Date invalide ou manquante :', dateString);
          return 'Date inconnue';
        }
        // Essai de création de la date
        const correctedDateString = dateString.replace(' ', 'T'); // ou essayez d'autres formats si nécessaire
        const date = new Date(correctedDateString);

        // Vérification si la date est valide
        if (isNaN(date.getTime())) {
          //console.error(
          //  'Date invalide après correction :',
          //  correctedDateString
          //);
          return 'Date invalide';
        }

        const now = new Date();
        const seconds = Math.floor((now - date) / 1000);

        let interval = Math.floor(seconds / 31536000);
        if (interval > 1) return interval + ' ans';
        interval = Math.floor(seconds / 2592000);
        if (interval > 1) return interval + ' mois';
        interval = Math.floor(seconds / 86400);
        if (interval > 1) return interval + ' jours';
        interval = Math.floor(seconds / 3600);
        if (interval > 1) return interval + ' heures';
        interval = Math.floor(seconds / 60);
        if (interval > 1) return interval + ' minutes';

        return seconds + ' secondes';
      },
      async loadLikedUsers() {
        try {
          if (this.post.id) {
            const likers = await getPostLikers(this.post.id);
            this.likedUsers = likers;
          }
        } catch (error) {
          //console.error('Erreur lors du chargement des likers :', error);
        }
      },
      async likePost() {
        try {
          const userHasLiked = this.post.likers.some(
            (liker) =>
              liker.id === this.currentUser.id || liker === this.currentUser.id
          );

          if (!userHasLiked) {
            // Logique pour ajouter le like
            await addLikePost(this.post.id);
            // Ajout du like de manière réactive
            if (this.currentUser.id) {
              this.post.likers.push(this.currentUser.id); // Utilise ID si seulement l'ID est stocké
            } else {
              this.post.likers.push(this.currentUser); // Si l'utilisateur est un objet complet
            }

            // Mettre à jour les utilisateurs qui ont liké de manière réactive
            await this.loadLikedUsers(); // Recharge la liste des utilisateurs qui ont liké
          } else {
            // Logique pour retirer le like
            await removeLikePost(this.post.id);
            // Utiliser Vue.set pour mettre à jour le tableau réactif de manière correcte
            this.post.likers = this.post.likers.filter(
              (liker) =>
                liker.id !== this.currentUser.id &&
                liker !== this.currentUser.id
            );

            // Mettre à jour les utilisateurs qui ont liké de manière réactive
            await this.loadLikedUsers(); // Recharge la liste des utilisateurs qui ont liké
          }
        } catch (error) {
          //console.error(
          //  'Erreur lors de la mise à jour du like :',
          //  error.message
          //);
        }
      },
      showModal() {
        this.isModalVisible = !this.isModalVisible;
      },
      addComment({ postId, newComment }) {
        this.$store.commit('addCommentInStore', { postId, newComment });
        this.fetchComments();
      },
      removeComment(comment) {
        this.comments = this.comments.filter((c) => c.id !== comment.id);

        // Appeler la mutation Vuex pour supprimer également dans le store
        this.$store.commit('removeCommentFromStore', comment.id);
      },
      async fetchComments() {
        try {
          const response = await getCommentsForPost(this.post.id); // Appel à la fonction pour récupérer les commentaires
          const commentsWithCreatorInfo = await Promise.all(
            response.data.map(async (comment) => {
              await this.fetchCreatorInfo(comment); // Ajouter les infos du créateur à chaque commentaire
              return comment;
            })
          );
          this.comments = commentsWithCreatorInfo; // Mettez à jour les commentaires
        } catch (error) {
          //console.error(
          //  'Erreur lors de la récupération des commentaires:',
          //  error
          //);
        }
      },
      async fetchCreatorInfo(comment) {
        try {
          const userInfo = await getUserById(comment.creator.id); // Utiliser l'ID du créateur pour récupérer ses infos
          comment.creator = userInfo; // Ajouter les infos de l'utilisateur au commentaire
        } catch (error) {
          //console.error(
          //  'Erreur lors de la récupération des informations du créateur:',
          //  error
          //);
        }
      },
      async fetchLikedUsers() {
        try {
          this.likedByUsers = await Promise.all(
            this.post.likers.map(async (liker) => {
              if (typeof liker === 'object') {
                return liker; // Si 'liker' est déjà un objet utilisateur
              } else {
                return await getUserById(liker); // Sinon, récupérez l'utilisateur via l'ID
              }
            })
          );
        } catch (error) {
          //console.error(
          //  'Erreur lors de la récupération des utilisateurs:',
          //  error.message
          //);
        }
      },
    },
    mounted() {
      this.fetchComments();

      this.fetchLikedUsers();

      const video = this.$refs.video;
      if (!video) return;

      // Liez correctement la fonction dans le contexte du composant
      this.observer = new IntersectionObserver(
        (entries) => this.handleIntersection(entries), // Utilisez une fonction fléchée
        { threshold: 0.5 } // La vidéo doit être visible à 50% pour déclencher
      );

      // Surveillez l'élément vidéo
      this.observer.observe(video);
    },
    beforeUnmount() {
      // Détruisez l'observateur pour éviter les fuites de mémoire
      if (this.observer) {
        this.observer.disconnect();
      }
    },
  };
</script>

<style scoped>
  a {
    color: black;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 24px;
  }

  .post-card {
    position: relative;
    background-color: var(--surface-bg-2);
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 4px var(--surface-bg-4);
    min-height: 11rem;
  }

  .post-header {
    display: flex;
    gap: 20px;
    align-items: center;
    margin-bottom: 15px;
  }

  .sponsorise {
    position: absolute;
    top: 10px;
    right: 50px;
    background-color: var(--primary-1b2);
    padding: 4px 8px;
    border-radius: 8px;
  }

  .info {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    cursor: pointer;
  }

  .action-btn {
    display: flex;
    justify-items: center;
  }

  .post-comments {
    font-size: 0.8rem;
    color: var(--text-3);
    cursor: pointer;
    text-align: right;
  }

  .post-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 100%;
  }

  .post-content img {
    max-width: 100%;
    margin-top: 24px;
  }

  iframe {
    border: none;
    display: block;
    margin-top: 24px;
    width: 100%;
    height: auto;
  }

  .post-divider {
    border: none;
    border-top: 1px solid var(--primary-3b);
    margin: 15px 0; /* Spacing uniformisé */
    width: 100%;
  }

  .liked-users {
    position: relative;
    cursor: pointer;
  }

  .modal {
    background: var(--primary-1b2);
    box-shadow: 0 2px 4px var(--surface-bg-4);
    border-radius: 5px;
    padding: 24px;
    z-index: 1000;
    width: fit-content;
    max-height: 200px; /* Pour éviter les modals trop longs */
    overflow-y: auto; /* Permet de scroller si trop d'éléments */
  }
  .modal li {
    list-style-type: none;
  }

  .modal a:hover {
    font-weight: bold;
  }

  @media screen and (min-width: 992px) {
    iframe {
      width: 800px;
      height: 500px;
      max-width: 800px;
    }
  }
</style>
