<template>
  <div class="comment-input">
    <img
      v-if="userPhoto"
      class="comment-avatar"
      :src="getImageUrl(currentUser.photo)"
      alt="Avatar de l'utilisateur"
    />
    <img
      v-else
      src="@/assets/social/social-page-post-avatar.svg"
      alt="avatar"
    />
    <input
      type="text"
      v-model="newComment"
      placeholder="Écrire un commentaire..."
      @keyup.enter="addComment()"
    />

    <!-- Send button -->
    <button @click="addComment()">
      <img src="@/assets/social/social-page-redact-post-letter.svg" />
      <v-tooltip activator="parent" location="top">Envoyer</v-tooltip>
    </button>
  </div>
</template>

<script>
import { baseUrl } from '../../../services/axios';

export default {
  name: 'CreateComment',
  props: {
    post: Object,
    currentUser: {
      type: Object,
      default: () => ({}), // Valeur par défaut si aucune donnée n'est transmise
    },
  },
  data() {
    return {
      newComment: "",
    };
  },
  computed: {
    userPhoto() {
      return this.currentUser.photo || ""; // Utilise `currentUser` pour l'avatar
    },
    isLoggedIn() {
      return this.$store.state.user !== null; 
    },
  },
  mounted() {
    //console.log("User state (mounted):", this.currentUser);
    //console.log("User photo (mounted):", this.userPhoto);
  },
  methods: {
    getImageUrl(photoUrl) {
      if (photoUrl) {
        return baseUrl + photoUrl;
      }
      return "/default-avatar.jpg"; // Image par défaut si aucune photo n'est présente
    },
    addComment() {
      // Émettre l'événement avec les données du commentaire et l'ID du post
      this.$emit("add-comment", this.newComment);
      this.newComment = ""; // Réinitialiser le champ de texte
    },
  }
}
</script>

<style scoped>
.comment-input {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 20px;
  background-color: var(--surface-bg-2);
  border-radius: 10px;
  box-shadow: 0 2px 4px var(--surface-bg-4);
  margin-bottom: 24px;
  position: relative;
}

.comment-input input {
  flex: 1;
  border-radius: 5px;
  background-color: var(--primary-1b2);
  height: 50px;
  margin: 0 20px;
  padding: 10px;
}


.comment-input img {
  width: 40px;
}

.comment-input button {
  display: flex;
  align-items: flex-end;
}
</style>