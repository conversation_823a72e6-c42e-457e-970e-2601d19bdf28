<template>
  <div
    class="create-post"
    :class="{ 'no-border-radius': imagePreview.length > 0 }"
  >
    <div class="create-post-main">
      <div class="create-post-text">
        <textarea
          placeholder="Clique pour commencer à écrire ton post"
          ref="textarea"
          @input="autoResize"
          :style="{ height: textareaHeight + 'px' }"
          v-model="formData.text"
        ></textarea>
        <!-- those fields were add because they are required in the API, but hidden -->
        <v-text-field v-model="formData.title" style="display: none" />
        <v-text-field v-model="formData.date_creation" style="display: none" />
        <v-text-field v-model="formData.date_update" style="display: none" />

        <!-- Champ pour URL vidéo -->
        <v-text-field
          v-if="showVideoInput"
          v-model="video"
          label="Ajouter un lien vidéo"
          placeholder="Entrez l'URL de la vidéo (YouTube, Vimeo)"
          @input="handleVideoUrl"
          dense
          outlined
        />
      </div>

      <div class="buttons">
        <!-- Bouton pour ajouter une image -->
        <label class="add-image">
          <input
            type="file"
            id="file-input"
            ref="fileInput"
            @change="handleFileUpload"
            accept="image/*"
            hidden
          />
          <img
            src="@/assets/social/social-page-redact-post-pj.svg"
            alt="Ajouter une pièce jointe"
          />
          <v-tooltip activator="parent" location="top"
            >Ajouter une pièce jointe</v-tooltip
          >
        </label>
        <label @click="toggleVideoInput" class="add-video">
          <img
            src="@/assets/icons/video-square-svgrepo-com.svg"
            alt="Ajouter une vidéo"
          />
          <v-tooltip activator="parent" location="top"
            >Ajouter une vidéo</v-tooltip
          >
        </label>

        <!-- Bouton pour envoyer le post -->
        <button class="send-post" @click="sendPost">
          <img
            src="@/assets/social/social-page-redact-post-letter.svg"
            alt="Envoyer le post"
          />
          <v-tooltip activator="parent" location="top">Envoyer</v-tooltip>
        </button>
      </div>
    </div>

    <!-- Section de prévisualisation de l'image -->
    <div v-if="imagePreview.length > 0" class="image-preview-container">
      <div
        class="image-preview"
        v-for="(image, index) in imagePreview"
        :key="index"
      >
        <div class="image-bg">
          <img :src="image.url" alt="Preview de l'image" />
        </div>
        <!-- Nouveau bouton pour supprimer l'image (croix rouge) -->
        <button class="delete-image" @click="removeImage(index)">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            fill="red"
            viewBox="0 0 24 24"
          >
            <line
              x1="18"
              y1="6"
              x2="6"
              y2="18"
              stroke="var(--primary-1)"
              stroke-width="2"
            />
            <line
              x1="6"
              y1="6"
              x2="18"
              y2="18"
              stroke="var(--primary-1)"
              stroke-width="2"
            />
          </svg>
        </button>
        <div class="file-name">{{ image.name }}</div>
      </div>
    </div>

    <!-- Section de prévisualisation de la vidéo -->
    <div v-if="videoPreview" class="video-preview">
      <iframe
        :src="videoPreview"
        frameborder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowfullscreen
      ></iframe>
      <button class="delete-video" @click="removeVideo">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          fill="red"
          viewBox="0 0 24 24"
        >
          <line
            x1="18"
            y1="6"
            x2="6"
            y2="18"
            stroke="var(--primary-1)"
            stroke-width="2"
          />
          <line
            x1="6"
            y1="6"
            x2="18"
            y2="18"
            stroke="var(--primary-1)"
            stroke-width="2"
          />
        </svg>
      </button>
    </div>
  </div>
</template>

<script>
  import { postPost } from '../../../services/post.service';
  import { mapActions } from 'vuex';

  export default {
    data() {
      return {
        video: null,
        videoPreview: null,
        showVideoInput: false,
        imagePreview: [],
        textareaHeight: 80, // Hauteur initiale minimale
        formData: {
          id: '',
          user: { id: '', name: '', avatar: '' },
          title: '',
          text: '',
          imageFile: null,
          video: '',
          time: '',
          comments: 0,
          commentList: [],
          date_creation: new Date().toISOString(), // Date d'aujourd'hui en format ISO,
          date_update: new Date().toISOString(), // Date d'aujourd'hui en format ISO
        },
      };
    },
    props: {
      currentUser: {
        type: Object,
        default: () => {},
      },
    },

    methods: {
      ...mapActions(['addPost', 'removePost']),
      resetForm() {
        this.formData.text = '';
        this.formData.title = '';
        this.formData.imageFile = null;
        this.formData.video = '';
        this.formData.date_creation = new Date().toISOString();
        this.formData.date_update = new Date().toISOString();
        this.imagePreview = [];
        this.videoPreview = null;
        this.video = '';
      },
      handleFileUpload(event) {
        const file = event.target.files[0];
        if (file) {
          const imageObject = {
            name: file.name,
            url: URL.createObjectURL(file),
          };
          this.imagePreview.push(imageObject);
        }
      },
      removeImage(index) {
        this.imagePreview.splice(index, 1);
      },
      handleVideoUrl() {
        if (this.validateVideoUrl(this.video)) {
          // L'URL complète est maintenant directement stockée dans formData.video
          this.videoPreview = this.transformVideoUrl(this.video); // Affichage de la vidéo sous forme d'iframe
          this.formData.video = this.video; // Stocke l'URL complète
        } else {
          this.videoPreview = null;
          this.formData.video = ''; // Réinitialiser si l'URL n'est pas valide
        }
      },
      validateVideoUrl(url) {
        // Validation simple pour YouTube ou Vimeo
        const youtubeRegex =
          /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/.+$/;
        const vimeoRegex = /^(https?:\/\/)?(www\.)?vimeo\.com\/.+$/;
        return youtubeRegex.test(url) || vimeoRegex.test(url);
      },
      transformVideoUrl(url) {
        // Convertit l'URL en un lien intégré (YouTube/Vimeo)
        if (url.includes('youtu.be') || url.includes('youtube.com')) {
          const videoId =
            url.split('v=')[1]?.split('&')[0] || url.split('youtu.be/')[1];
          return `https://www.youtube.com/embed/${videoId}`;
        } else if (url.includes('vimeo.com')) {
          const videoId = url.split('vimeo.com/')[1];
          return `https://player.vimeo.com/video/${videoId}`;
        }
        return null;
      },
      // La méthode handleVideoUrl se charge de valider l'URL et extraire l'ID vidéo
      handleVideoUrl() {
        if (this.validateVideoUrl(this.video)) {
          this.videoPreview = this.transformVideoUrl(this.video);
          this.formData.video = this.video;
        } else {
          this.videoPreview = null;
          this.formData.video = '';
          this.formData.video = null; // Réinitialiser si l'URL n'est pas valide
        }
      },
      removeVideo() {
        this.video = '';
        this.videoPreview = null;
        this.formData.video = '';
      },
      toggleVideoInput() {
        this.showVideoInput = !this.showVideoInput; // Bascule la visibilité
      },
      onFileChange(event) {
        const file = event.target.files[0]; // Récupérer le premier fichier
        if (file) {
          this.imageFile = file; // Stocker le fichier sélectionné
          const imageObject = {
            name: file.name,
            url: URL.createObjectURL(file),
          };
          this.imagePreview.push(imageObject);
        }
      },

      // Méthode pour déclencher le champ input de fichier
      triggerFileInput() {
        this.$refs.fileInput.click(); // Ouvrir le sélecteur de fichiers
      },

      //////////// CREATE POST ////////////
      async sendPost() {
        const formData = new FormData();
        const currentUser = this.currentUser;

        // Ajout des données requises du post
        formData.append('text', this.formData.text);
        formData.append('title', this.formData.title);
        formData.append('date_creation', this.formData.date_creation);
        formData.append('date_update', this.formData.date_update);
        //formData.append("image", this.imageFile);
        if (this.formData.video) {
          formData.append('video', this.formData.video);
        }

        if (currentUser.type_user === 'applicant') {
          formData.append('is_applicant', true);
        }
        if (currentUser.type_user === 'recruiter') {
          formData.append('is_recruteur', true);
        }

        // Ajout des images
        if (this.$refs.fileInput && this.$refs.fileInput.files.length > 0) {
          const files = this.$refs.fileInput.files;
          for (let i = 0; i < files.length; i++) {
            formData.append('image', files[i]);
          }
          this.$refs.fileInput.value = '';
        }

        // Log des données avant l'envoi
        for (let [key, value] of formData.entries()) {
          //console.log(key, value); // Affiche chaque champ et sa valeur
        }

        try {
          // Appel à la fonction postPost pour envoyer les données
          const response = await postPost(formData);

          // l'ID du post est ajouté au titre du post (champs requis)
          const newPostId = response.data.id;
          this.formData.title = `Post ID: ${newPostId}`;

          await this.addPost(response.data);
          this.$emit('post-added', response.data);
          this.resetForm();
          //console.log('Post créé avec succès:', response.data);

          // Réinitialiser le formulaire après l'envoi
          this.formData.title = '';
          this.formData.text = ''; // Réinitialiser le contenu
          this.imagePreview = []; // Réinitialiser les images
          this.videoPreview = null;
          this.video = '';
          this.formData.date_update = new Date().toISOString();
        } catch (error) {
          //console.error(
          //  "Erreur lors de l'envoi du post:",
          //  error.response ? error.response.data : error.message
          //);
        }
      },
      autoResize() {
        const textarea = this.$refs.textarea;
        textarea.style.height = 'auto'; // Réinitialiser la hauteur
        textarea.style.height = textarea.scrollHeight + 'px'; // Ajuster la hauteur
      },
    },
  };
</script>

<style scoped>
  /* Post Creation Section */
  .create-post {
    display: flex;
    flex-direction: column;
    width: 100%;
    align-items: center;
    padding: 20px;
    background-color: var(--surface-bg-2);
    border-radius: 10px;
    box-shadow: 0 2px 4px var(--surface-bg-4);
    margin-bottom: 24px;
    position: relative;
  }

  .create-post-main {
    display: flex;
    width: 100%;
  }

  .create-post.no-border-radius {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }

  .create-post-text {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .create-post-main textarea {
    width: 100%;
    padding: 15px;
    border-radius: 10px;
    background-color: var(--primary-1b2);
    border: none;
    outline: none;
    resize: none;
    font-size: 16px;
    line-height: 1.5;
  }

  .create-post-main textarea::placeholder {
    color: var(--text-3);
    text-align: left;
  }

  /* Button for adding image & sending post */
  .add-image,
  .send-post,
  .add-video {
    margin-left: 15px;
    border-radius: 20%;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .add-image,
  .add-video {
    margin-bottom: 4px;
  }
  .add-image input {
    display: none;
  }
  .add-image:hover,
  .add-video:hover {
    background-color: var(--primary-1);
    border-radius: 20%;
  }

  /* Image container and styling */
  .image-preview-container {
    display: flex;
    flex-wrap: wrap;
    background-color: var(--surface-bg-2);
    border-radius: 0 0 10px 10px;
    padding: 20px;
  }

  /* Image preview */
  .image-preview {
    position: relative;
    margin-right: 5px;
    margin-bottom: 5px;
    background-color: var(--surface-bg-2);
    border-radius: 5px;
    width: 100px;
    height: 100px;
    overflow: hidden;
    border: 1px solid #ddd;
  }

  /* Background behind the image */
  .image-bg {
    border-radius: 10px;
  }

  .image-preview img {
    max-width: 100px;
    max-height: 100px;
    border-radius: 10px;
    object-fit: cover;
    display: block;
  }

  /* Nouveau bouton pour supprimer l'image (croix rouge) */
  .delete-image {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: var(--surface-bg-5);
    border: none;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px;
    border-radius: 50%;
  }

  /* Icône croix rouge pour la suppression */
  .delete-image svg {
    fill: var(--primary-1);
    width: 20px;
    height: 20px;
  }

  /* Au survol, changer la couleur de la croix et le fond */
  .delete-image:hover svg {
    background-color: var(--surface-bg-2);
    border-radius: 50%;
  }

  .delete-image:hover {
    background-color: var(--primary-1);
  }

  /* File name banner on the image */
  .file-name {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: var(--surface-bg-5);
    color: var(--surface-bg);
    font-size: 0.75rem;
    padding: 3px;
    text-align: center;
    border-radius: 0 0 10px 10px;
  }

  .video-preview {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    background-color: var(--surface-bg-2);
    border-radius: 0 0 10px 10px;
    padding: 20px;
  }

  .video-preview iframe {
    width: 100%;
    height: 280px;
    border-radius: 10px;
  }

  .delete-video {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: red;
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    width: 20px;
    height: 20px;
  }

  .delete-video svg {
    width: 20px;
    height: 20px;
  }
</style>
