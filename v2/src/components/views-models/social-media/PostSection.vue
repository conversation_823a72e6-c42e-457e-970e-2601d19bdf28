<template>
  <div class="posts-container">
    <PostCard
      v-for="post in sortedPosts"
      :key="post.id"
      :post="post"
      :currentUser="currentUser"
      :isLoggedIn="isLoggedIn"
      :commentCount="commentCounts[post.id] || 0"
      @delete-post="handleDeletePost"
      @like-post="toggleLikePost"
      @toggle-comments="toggleComments"
      @refresh-posts="refreshPosts"
    />
  </div>
</template>

<script>
  import PostCard from './PostCard.vue';
  import { mapActions } from 'vuex';
  import { fetchCommentCounts } from '../../../services/post.service';

  export default {
    name: 'PostSection',
    components: { PostCard },
    props: {
      posts: {
        type: Array,
        required: true,
      },
      currentUser: {
        type: Object,
        default: () => ({}),
      },
      isLoggedIn: Boolean,
    },
    data() {
      return {
        commentCounts: {},
      };
    },
    computed: {
      sortedPosts() {
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Fixe l'heure du jour à 00:00:00 pour ne conserver que la date
        //console.log('posts :', this.posts);
        return [...this.posts].sort((a, b) => {
          const dateA = new Date(a.date_creation);
          const dateB = new Date(b.date_creation);

          // Vérification si les deux posts sont du jour
          const isTodayA = dateA >= today;
          const isTodayB = dateB >= today;

          // Si les deux posts sont du jour, on les trie par date du plus récent au plus ancien
          if (isTodayA && isTodayB) {
            return dateB - dateA; // Trie décroissant par date (plus récent en premier)
          }

          // Si l'un des posts est du jour et l'autre pas, celui du jour vient en premier
          if (isTodayA) return -1;
          if (isTodayB) return 1;

          // Si les deux posts ne sont pas du jour, on applique la logique existante (boost et likes/commentaires)
          const countA = this.commentCounts[a.id] || 0;
          const countB = this.commentCounts[b.id] || 0;

          // Tri d'abord par boost, ensuite par likes + commentaires
          if (a.boost !== b.boost) return b.boost - a.boost;
          const totalA = (a.likers.length || 0) + countA;
          const totalB = (b.likers.length || 0) + countB;
          return totalB - totalA;
        });
      },
    },
    async created() {
      try {
        const counts = await fetchCommentCounts();
        this.commentCounts = counts.reduce((acc, item) => {
          acc[item.id] = item.comment_count;
          return acc;
        }, {});
      } catch (error) {
        //console.error(
        //  'Erreur lors du chargement des comptes de commentaires:',
        //  error
        //);
      }
    },
    methods: {
      ...mapActions(['removePost']),
      async handleDeletePost(postId) {
        try {
          await this.removePost(postId); // Utiliser postId au lieu de this.postId
          this.$emit('post-removed');

          // Trouver l'index du post à supprimer en utilisant son ID
          const index = this.posts.findIndex(post => post.id === postId);
          if (index > -1) {
            // Créer une copie du tableau pour maintenir la réactivité
            const updatedPosts = [...this.posts];
            updatedPosts.splice(index, 1);
            // Mettre à jour les posts via une mutation Vuex ou un événement
            this.$emit('update-posts', updatedPosts);
          }
        } catch (error) {
          //console.error('Erreur lors de la suppression du post:', error);
        }
      },
      toggleLikePost(postId) {
        this.$emit('like-post', postId);
      },
      toggleComments(postId) {
        this.$emit('toggle-comments', postId);
      },
      refreshPosts() {
        // Émettre un événement pour demander au parent de rafraîchir la liste des posts
        this.$emit('post-removed');
      },
    },
  };
</script>

<style scoped>
  .posts-container {
    grid-column: 2 / 5;
    grid-row: 2;
    display: flex;
    flex-direction: column;
  }
</style>
