<template>
  <div class="comments">
    <div v-for="comment in comments" :key="comment.id">
      <hr class="post-divider" />
      <div class="comment">
        <div class="comment-header">
          <UserAvatar :user="comment.creator" :width="40" />
          <p>
            {{ comment.creator.first_name }} {{ comment.creator.last_name }}
          </p>
        </div>
        <p>{{ comment.commentaire }}</p>
        <PostMenuOptions
          v-if="comment.creator.id === currentUser.id"
          :item="comment"
          type="comment"
          :currentUser="currentUser"
          :deleteAction="deleteThisComment"
        />
      </div>
    </div>

    <CreateComment
      v-if="isLoggedIn"
      :post="post"
      :currentUser="currentUser"
      @add-comment="addComment"
    />
  </div>
</template>

<script>
  import { baseUrl } from '../../../services/axios';
  import CreateComment from './CreateComment.vue';
  import {
    sendCommentForPost,
    deleteComment,
  } from '../../../services/post.service';
  import { mapActions } from 'vuex';
  import PostMenuOptions from '../../buttons/PostMenuOptions.vue';
  import UserAvatar from '../profil/UserAvatar.vue';

  export default {
    name: 'CommentSection',
    components: {
      CreateComment,
      PostMenuOptions,
      UserAvatar,
    },
    props: {
      comments: Array,
      post: Object,
      currentUser: {
        type: Object,
        default: () => ({}),
      },
      isLoggedIn: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        newComment: '',
      };
    },
    methods: {
      ...mapActions(['addCommentInStore']),
      getImageUrl(photoUrl) {
        if (photoUrl) {
          return baseUrl + photoUrl;
        }
        return '/default-avatar.jpg'; // Image par défaut si aucune photo n'est présente
      },
      async addComment(newCommentText) {
        const post = this.post;

        if (!post || !post.id) {
          //console.error("Le post n'est pas défini ou n'a pas d'id.");
          return;
        }

        //console.log('post', post);

        if (newCommentText.trim() !== '') {
          const postId = post.id;
          const commentData = {
            creator: this.currentUser.id,
            commentaire: newCommentText,
          };

          //console.log('commentData', commentData);

          try {
            const response = await sendCommentForPost(postId, commentData);
            const newComment = response.data;

            //console.log('newComment', newComment);

            newComment.creatorName = this.getUser
              ? this.getUser.userType === 'recruiter'
                ? this.getUser.company || 'Default Company'
                : `${this.getUser.first_name || ''} ${
                    this.getUser.last_name || ''
                  }`
              : 'Default Name';

            newComment.creatorAvatar = this.getUser
              ? this.getImageUrl(this.getUser.photo) || 'default-avatar.jpg'
              : 'default-avatar.jpg';

            this.$emit('add-comment', { postId: this.post.id, newComment });

            this.newComment = '';
          } catch (error) {
            //console.error("Erreur lors de l'ajout du commentaire :", error);
          }
        }
      },

      async deleteThisComment(comment) {
        try {
          await deleteComment(comment.id);

          this.$emit('remove-comment', comment);

          //console.log('Commentaire supprimé avec succès');
        } catch (error) {
          //console.error('Erreur de suppression du commentaire:', error);
        }
      },
    },
  };
</script>

<style scoped>
  .comments {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .comments p {
    font-size: 16px;
  }

  .comments img {
    width: 40px;
  }

  .comment {
    width: 100%;
    display: flex;
    flex-direction: row;
    position: relative;
  }

  .comment-header {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    width: 10%;
    margin-right: 16px;
  }

  .comment-header p {
    font-size: 12px;
  }

  .like,
  .comment,
  .share {
    padding: 5px;
  }

  .post-divider {
    border: none;
    border-top: 1px solid var(--primary-3b);
    margin: 15px 0; /* Spacing uniformisé */
    width: 100%;
  }
</style>
