<template>
  <div class="candidate-card-container border-radius-5">
    <!-- header -->
    <div class="candidate-card-header">
      <div class="card-header-1">
        <!-- candidate profession  -->
        <div class="logo-wrapper">
          <UserAvatar :user="candidate" :width="125" />
        </div>
      </div>
    </div>

    <div class="candidate-card-body">
      <div class="title-wrapper">
        <h4>{{ formattedName }}</h4>
      </div>
      <!-- candidate name -->
      <div class="candidate-card-name d-flex a-center">
        <p>{{ formattedMetier }}</p>
      </div>
    </div>

    <!-- button -->
    <PrimaryRoundedButton
      textContent="Voir mon profil"
      @click="
        $router.push(
          candidate.type_user === 'recruiter' ? '/recruteur/profil' : '/profil'
        )
      "
    />
  </div>
</template>

<script>
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import getImgPath from '@/utils/imgpath.js';
  import UserAvatar from '../profil/UserAvatar.vue';

  export default {
    name: 'UserCard',

    components: {
      PrimaryRoundedButton,
      UserAvatar,
    },

    props: {
      candidate: {
        type: Object,
        default: () => {},
      },
    },

    async mounted() {
      await this.checkCandidateConnection();
    },

    computed: {
      // Formater le nom complet du candidat
      formattedName() {
        const firstName = this.candidate.first_name
          ? this.capitalizeFirstLetter(this.candidate.first_name)
          : '';
        const lastName = this.candidate.last_name
          ? this.capitalizeFirstLetter(this.candidate.last_name)
          : '';
        return `${firstName} ${lastName}`.trim();
      },
      // Formater la profession du candidat
      formattedMetier() {
        return this.candidate.metier
          ? this.capitalizeFirstLetter(this.candidate.metier)
          : '';
      },
    },

    methods: {
      getImgPath,
      async checkCandidateConnection() {
        this.candidateConnected = this.candidate.connected;
      },
      capitalizeFirstLetter(str) {
        if (!str) return str;
        return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
      },
    },
  };
</script>

<style scoped>
  /* container */
  .candidate-card-container {
    width: 250px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: var(--surface-bg-2);
    position: relative;
  }

  /* header */
  .candidate-card-header {
    width: 100%;
    display: flex;
    justify-content: center;
    position: relative;
  }

  .card-header-1 {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .logo-wrapper {
    width: 125px;
    height: 125px;
    border-radius: 5px;
    position: relative; /* Nécessaire pour positionner le cercle à l'intérieur */
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .title-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    margin-top: 10px;
  }

  .candidate-card-body {
    margin-top: 10px;
    margin-bottom: 20px;
  }

  /* name */
  .candidate-card-name {
    display: flex;
    justify-content: center; /* Centre le nom */
    align-items: center;
    width: 100%;
    text-align: center;
    margin-top: 5px;
  }

  @media screen and (max-width: 992px) {
    .candidate-card-container {
      display: none;
    }
  }
</style>
