<template>
  <article class="container-general">
    <div class="header-container bg-color">
      <h5>Aperçu du CV</h5>
      <div
        class="actions"
        :class="{ 'justify-end': !isProfilePage && !isRecruiterProfilePage }"
      >
        <img
          v-if="isProfilePage || isRecruiterProfilePage"
          class="edit-icon"
          src="@/assets/icons/edit-icon-new.svg"
          alt="Edit"
          @click="goToSection('my-cvs')"
        />
      </div>
    </div>

    <h5 v-if="allCvs.length > 1" class="cv-selection-title">
      Sélectionnez un CV parmi la liste ci-dessous :
    </h5>

    <!-- Liste des CVs -->
    <section v-if="allCvs.length >= 1" class="cv-selection-grid">
      <div
        v-for="cv in allCvs"
        :key="cv.id"
        class="cv-thumb"
        :class="{ selected: cv.id === selectedCvId }"
        @click="selectCv(cv)"
      >
        <p>{{ cv.title || 'CV sans titre' }}</p>

        <vue-pdf-embed
          width="250"
          :source="getImgPath(cv.file)"
          :page="1"
          @loaded="onDocumentLoaded"
          @error="onError"
          class="cv-preview-container"
        />
      </div>
    </section>

    <div v-if="loading" class="loading">Chargement du PDF...</div>
    <div v-if="error" class="error">
      Désolé, une erreur s'est produite lors du téléchargement du fichier
    </div>

    <div v-if="totalPages > 1" class="controls">
      <button @click="previousPage" :disabled="currentPage === 1">
        &#x276E;
        <!-- Code Unicode pour "◀" (flèche gauche) -->
      </button>

      <p>page {{ currentPage }} de {{ totalPages }}</p>

      <button @click="nextPage" :disabled="currentPage === totalPages">
        &#x276F;
        <!-- Code Unicode pour "▶" (flèche droite) -->
      </button>
    </div>
  </article>
</template>

<script>
  import VuePdfEmbed from 'vue-pdf-embed';
  import getImgPath from '@/utils/imgpath.js';

  export default {
    components: {
      VuePdfEmbed,
    },
    props: {
      user: Object,
      candidate: {
        type: Object,
        required: true,
      },
      pdfUrl: {
        type: String,
        required: true,
      },
      selectedCV: null,
    },
    data() {
      return {
        currentPage: 1,
        totalPages: 0,
        loading: true,
        error: null,
        selectedCvId: null,
      };
    },
    watch: {
      pdfUrl: {
        handler() {
          this.currentPage = 1;
          this.totalPages = 0;
          this.loading = true;
          this.error = null;
        },
        immediate: true,
      },
    },
    computed: {
      isProfilePage() {
        return (
          this.$route.path === '/profil' ||
          this.$route.path === '/recruteur/profil'
        );
      },
      allCvs() {
        //console.log(this.user?.cvs);
        return this.user?.cvs || []; // Adapte si nécessaire selon ta structure
      },
      selectedCvUrl() {
        const selected = this.allCvs.find((cv) => cv.id === this.selectedCvId);
        return selected?.cv || this.pdfUrl; // Si rien sélectionné, on garde celui par défaut
      },
    },
    methods: {
      getImgPath,
      previousPage() {
        if (this.currentPage > 1) {
          this.currentPage--;
        }
      },
      nextPage() {
        if (this.currentPage < this.totalPages) {
          this.currentPage++;
        }
      },
      onDocumentLoaded({ numPages }) {
        this.totalPages = numPages;
        this.loading = false;
      },
      onError(error) {
        this.loading = false;
        this.error = error.message || 'Erreur lors de le chargement du CV PDF';
        //console.error('Erreur lors de le chargement du CV PDF:', error);
      },
      goToSection(sectionId) {
        this.$router
          .push({
            path: '/profil/edition',
            query: { section: sectionId },
          })
          .then(() => {
            this.$nextTick(() => {
              this.$emit('toggleSection', sectionId); // Ouvre la bonne section

              setTimeout(() => {
                const element = document.getElementById(sectionId);
                if (element) {
                  const navbarHeight =
                    document.querySelector('.navbar')?.offsetHeight || 80;
                  const elementPosition =
                    element.getBoundingClientRect().top + window.scrollY;
                  window.scrollTo({
                    top: elementPosition - navbarHeight - 20,
                    behavior: 'smooth',
                  });
                }
              }, 300); // Attend que le menu s'affiche avant de scroller
            });
          });
      },
      async saveCV() {
        if (!this.selectedCvId) return;

        try {
          // 🔧 Appelle ton service backend ici, exemple :
          await this.$store.dispatch('updateDefaultCV', this.selectedCvId);

          this.$emit('cv-status-change'); // Pour notifier le parent si besoin
        } catch (error) {
          console.error(
            'Erreur lors de la sauvegarde du CV sélectionné :',
            error
          );
        }
      },

      async downloadPdf() {
        if (
          !this.pdfUrl ||
          !this.candidate ||
          !this.candidate.first_name ||
          !this.candidate.last_name
        ) {
          //console.error(
          //  'PDF indisponible ou informations candidat manquantes.'
          //);
          return;
        }

        try {
          // Définir le nom dynamique du fichier basé sur le candidat consulté
          const fileName = `CV-${this.candidate.first_name}-${this.candidate.last_name}.pdf`;

          // Télécharger le fichier sous forme de blob
          const response = await fetch(this.pdfUrl, { mode: 'cors' });
          const blob = await response.blob();

          // Créer un URL temporaire pour le fichier
          const blobUrl = window.URL.createObjectURL(blob);

          // Créer un lien de téléchargement
          const link = document.createElement('a');
          link.href = blobUrl;
          link.setAttribute('download', fileName); // Utilisation du nom dynamique
          document.body.appendChild(link);
          link.click();

          // Nettoyer l'URL temporaire
          window.URL.revokeObjectURL(blobUrl);
          document.body.removeChild(link);
        } catch (error) {
          //console.error('Erreur lors du téléchargement du PDF:', error);
        }
      },
      selectCv(cv) {
        this.selectedCvId = cv.id;
        this.currentPage = 1;
        this.totalPages = 0;
        this.error = null;
        this.$emit('submit_cv_infos', cv.id);
      },
      async updateUserDatas() {
        await updateUserInformations(this.formData);
        await updateUserCriterias({
          wanted_job: this.formData.wanted_job,
          contrat: this.formData.contrat,
          experiences: this.formData.experiences,
          salaire_souhaite: this.formData.salaire_souhaite,
          teletravail: this.formData.teletravail,
          secteur: this.formData.secteur,
        });
        const updatedUser = { ...this.user, ...this.formData };
        this.$store.dispatch('handleUserChange', {
          type: null,
          payload: updatedUser,
        });
        this.notifications = takeUserNotifications(this.formData, ['ville']);
      },

      loadCv() {
        console.log('CV lors du chargement :', this.selectedCV);
        if (this.selectCV) {
          this.selectedCvId = this.selectedCV;
        }
      },
    },
    mounted() {
      this.loadCv();
    },
  };
</script>

<style scoped>
  .container-general {
    background-color: var(--white-200);
    width: 100%;
    border-radius: 2px;
  }
  .cv-preview-container {
    height: 400px;
    overflow: auto;
    margin: 15px auto 0 auto;
    background: var(--surface-bg-2);
    background-color: var(--white-200);
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  @media screen and (max-width: 768px) {
    .cv-preview-container {
      height: 250px;
    }

    .cv-preview-container canvas {
      transform: scale(0.45);
    }
  }
  .header-container {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid black;
    cursor: pointer;
  }
  .bg-color {
    background-color: var(--secondary-2b2);
    border-bottom: none;
  }

  .header-container h5 {
    display: flex;
    gap: 10px;
    align-items: center;
  }

  .title-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  .actions {
    display: flex;
    flex-grow: 1;
    justify-content: space-between;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .edit-icon {
    margin: 0 0 0 1rem;
    cursor: pointer;
    filter: none;
  }
  .edit-icon svg path {
    stroke: #32394c;
  }
  .edit-icon:hover {
    opacity: 0.6;
  }
  button:hover {
    opacity: 0.6;
  }

  .controls {
    width: 100%;
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
  }

  .controls button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(
      --text-color,
      black
    ); /* Utilisation d'une variable CSS si disponible */
    transition: opacity 0.3s ease-in-out;
  }

  .controls button:disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }

  .controls button:hover:not(:disabled) {
    opacity: 0.6;
  }

  .loading,
  .error {
    text-align: center;
    padding: 2rem;
    font-size: 1.2rem;
    color: #4a5568;
  }

  .error {
    color: #e53e3e;
  }
  .cv-selection-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 1%;
    justify-content: center;
  }
  .cv-thumb {
    border: 2px solid transparent;
    cursor: pointer;
    padding: 0.5rem;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    transition: border-color 0.3s;
    width: 360px;
    box-sizing: border-box;
    margin-bottom: 1rem;
  }
  .cv-thumb.selected {
    border-color: #2a7de1;
  }
  .cv-thumb p {
    text-align: center;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }
  .cv-selection-title {
    display: flex;
    flex-direction: column;
    width: 100%;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: fit-content;
    padding: 24px;
  }
</style>
