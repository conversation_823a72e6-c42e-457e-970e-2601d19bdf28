<template>
  <section id="my-profile" class="section-container">
    <div class="header-container bg-color">
      <h5 v-if="user.type_user === 'applicant'">
        Formulaire
        <span
          v-for="(item, index) in array"
          :key="index"
          class="notification-span"
        >
          {{ getErrorMessage(item) }}
        </span>
      </h5>
    </div>

    <div class="content">
      <div class="top-container">
        <h5>Intitulé du poste</h5>
        <div class="name-display">
          <v-text-field
            v-model="formData.nom_job"
            label="Intitulé du poste"
            placeholder="Ex : Développeur web"
            outlined
            dense
            :rules="notEmptyRules"
          />
        </div>

        <h5>Années d'expérience</h5>
        <v-radio-group v-model="formData.experience">
          <div class="experience-columns">
            <div class="column">
              <v-radio label="Stagiaire/ Alternant" value="0" />
              <v-radio label="Junior: 0-2 ans" value="0-3" />
            </div>
            <div class="column">
              <v-radio label="Confirmé: 2-5 ans" value="3-5" />
              <v-radio label="Senior: > 5 ans" value="5+" />
            </div>
          </div>
        </v-radio-group>

        <h5>Type de contrat</h5>
        <v-select
          v-model="formData.contrat"
          :items="contractOptions"
          label="Sélectionnez un type de contrat"
          :rules="notEmptyRules"
        />

        <h5>Télétravail</h5>
        <v-select
          v-model="formData.presence"
          :items="remoteWorkOptions"
          label="Sélectionnez une option"
          :rules="notEmptyRules"
        />
      </div>
    </div>
  </section>
</template>

<script>
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import { updateUserCriterias } from '@/services/auto_postuler.service';
  import { takeUserNotifications } from '@/utils/userUtilities';
  import { baseUrl } from '../../../services/axios';
  import {
    validateNoSpecialChar,
    validateNotEmpty,
    validateWebsite,
  } from '../../../utils/validationRules';
  export default {
    name: 'MyForm',

    props: {
      user: {
        type: Object,
        required: true,
      },
      data: {
        type: Object,
        required: false,
      },
      activeSection: String,
    },

    data() {
      return {
        isPanelOn: false, //  state of panel
        formData: {
          nom_job: '',
          experience: '',
          contrat: '',
          presence: '',
        }, //  datas sent
        contractOptions: ['CDI', 'CDD', 'Alternance', 'Stage', 'Freelance'],
        remoteWorkOptions: [
          'Télétravail non autorisé',
          'Télétravail occasionnel',
          'Télétravail fréquent',
          'Télétravail total',
        ],
        notifications: 0,
        array: [],
        //userInfos: {},      //  user datas
        previewImage: '', //  string for the user photo

        /* input rules */
        notEmptyRules: [(v) => validateNotEmpty(v) || true],
        mobileRules: [(v) => validateMobile(v) || true],
        websiteRules: [(v) => validateWebsite(v) || true],
        noSpecialCharRules: [
          (v) =>
            validateNoSpecialChar(v) ||
            'Ne doit pas contenir de caractères spéciaux',
        ],
        rules500: [
          (v) => v?.length <= 500 || 'ne doit pas dépasser 500 caractères',
        ],
        showErrorIcon: false,
        showSuccessIcon: false,
        fileError: '', // Stocke le message d'erreur
        rulesSize: [
          (value) =>
            !value ||
            !value.length ||
            value[0].size < 1000000 ||
            'Ne doit pas avoir plus de 1 mo',
        ],
      };
    },

    mounted() {
      this.fetchdata();
      this.notifications = takeUserNotifications(this.user, ['ville']);
      this.array = takeUserNotifications(this.user, ['ville']);
      if (this.notifications > 0) {
        this.togglePanel();
      }
    },

    methods: {
      async fetchdata() {
        try {
          this.formData = {
            nom_job: this.data.nom_job || '',
            experience: this.data.experience || '',
            contrat: this.data.contrat || '',
            presence: this.data.presence || '',
          };
        } catch (error) {
          console.error('Erreur chargement candidatures', error);
        }
      },
      togglePanel() {
        this.isPanelOn = !this.isPanelOn;
      },
      async updateUserDatas() {
        this.$emit('submit_user_infos', {
          nom_job: this.formData.nom_job,
          experience: this.formData.experience,
          contrat: this.formData.contrat,
          presence: this.formData.presence,
        });
        // await updateUserInformations(this.formData);
        this.notifications = takeUserNotifications(this.formData, ['ville']);
      },

      getErrorMessage(value) {
        if (!value || typeof value !== 'string') return '';
        if (value == 'ville') return 'Ville manquante !';
      },
    },
  };
</script>

<style scoped>
  .section-container {
    background-color: var(--white-200);
    width: 100%;
    border-radius: 2px;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid black;
    cursor: pointer;
  }
  .header-container h5 {
    display: flex;
    gap: 10px;
    align-items: center;
  }

  .notification-span {
    background-color: rgba(254, 85, 85, 0.403);
    border: solid 1px red;
    border-radius: 10px;
    padding: 0 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
  }
  .bg-color {
    background-color: var(--secondary-2b2);
    border-bottom: none;
  }

  .btn-row {
    display: flex;
    justify-content: end;
  }

  .formulaire {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 20px;
  }

  .hidden {
    display: none;
  }

  @media screen and (min-width: 992px) {
    .avatar-title {
      flex-direction: row;
    }

    .img-wrapper {
      width: 10%;
    }

    .formulaire {
      flex-direction: row;
      justify-content: space-between;
    }

    .name-display {
      display: flex;
      align-items: center;
      font-size: 20px;
      font-weight: 600;
      color: black;
    }
    .websites {
      flex-direction: row;
      justify-content: space-between;
    }

    .websites2 {
      flex-direction: row;
      justify-content: space-between;
    }
  }
  .experience-columns {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
  }

  .experience-columns .column {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  .content {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: fit-content;
    padding: 24px;
  }
  .top-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 20px;
  }
</style>
