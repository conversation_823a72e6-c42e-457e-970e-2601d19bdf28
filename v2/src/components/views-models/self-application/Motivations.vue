<template>
  <section id="my-formData" class="section-container">
    <div class="header-container bg-color">
      <h5>Lettre de Motivation</h5>
    </div>

    <div class="content">
      <v-form ref="formDataForm" v-model="formIsValid" class="formData-form">
        <div class="top-container">
          <v-textarea
            v-model="formData.lettreMotivation"
            label="Expliquez pourquoi vous êtes un bon candidat"
            auto-grow
            rows="3"
            :rules="[(v) => !!v || 'Ce champ est requis']"
          />
        </div>
      </v-form>
    </div>
  </section>
</template>

<script>
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import { toaster } from '@/utils/toast/toast.js';
  import { submitLetter } from '@/services/letter.service.js';

  export default {
    name: 'MotivationSection',
    components: {
      PrimaryNormalButton,
    },
    props: {
      data: {
        type: Object,
        required: false,
      },
    },
    data() {
      return {
        formData: {
          lettreMotivation: '',
        },
        formIsValid: false,
      };
    },
    mounted() {
      console.log('aaa', this.data);
      this.loadMotivation();
      if (this.data) {
        this.formData.lettreMotivation = this.data.lm;
      }
    },
    methods: {
      async loadMotivation() {
        try {
          const existingLetter = await getMotivation();
          if (existingLetter?.lettreMotivation) {
            this.formData.lettreMotivation = existingLetter.lettreMotivation;
          }
        } catch (error) {
          console.error(
            'Erreur lors du chargement de la lettreMotivation de formData :',
            error
          );
        }
      },
      resetMotivationForm() {
        this.this.formData.lettreMotivation = '';
      },
      async submitMotivationForm() {
        const isValid = await this.$refs.formDataForm.validate();
        if (isValid) {
          try {
            this.$emit('submit_lm_infos', this.formData.lettreMotivation);
          } catch (error) {
            //console.error(
            //  "Erreur lors de l'enregistrement :",
            //  error.response?.data || error
            //);
            toaster.showErrorPopup("Erreur lors de l'enregistrement.");
          }
        } else {
          toaster.showErrorPopup('Veuillez corriger les erreurs.');
        }
      },
    },
  };
</script>

<style scoped>
  .section-container {
    background-color: var(--white-200);
    width: 100%;
    border-radius: 2px;
  }
  .header-container {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid black;
  }
  .bg-color {
    background-color: var(--secondary-2b2);
  }
  .content {
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding: 24px;
  }
  .top-container,
  .middle-container {
    width: 100%;
  }
  .disabled-field-wrapper {
    display: flex;
    align-items: center;
    gap: 15px;
  }
  .upload-btn {
    height: 55px;
    width: 55px;
  }
  .hidden-input {
    display: none;
  }
  .error-message {
    color: rgb(var(--v-theme-error));
    font-size: 12px;
  }
  .btn-row {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
  }
</style>
