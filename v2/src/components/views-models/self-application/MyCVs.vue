<template>
  <section id="my-cvs" class="section-container">
    <!-- container header band -->
    <div class="header-container bg-color">
      <h5 v-if="user.type_user === 'applicant'">
        CVs

        <span
          v-for="(item, index) in array"
          :key="index"
          class="notification-span"
        >
          {{ getErrorMessage(item) }}
        </span>
      </h5>
    </div>

    <div class="content">
      <v-form
        v-if="isCvInStaging"
        ref="cvForm"
        v-model="formIsValid"
        class="CV-container"
      >
        <!-- cv title and title input -->
        <div class="top-container">
          <h5>Ajouter un nouveau CV</h5>
          <v-text-field
            v-model="newCV['title']"
            label="Saisi le titre de ton CV"
            type="text"
            :rules="[...cvRules, ...noSpecialChar]"
            fastfail
          />
        </div>

        <!-- cv name and file input -->
        <div class="middle-container">
          <div class="disabled-field-wrapper">
            <PrimaryNormalButton
              btnColor="secondary"
              class="upload-btn"
              @click="triggerCvInput(index)"
              file
            />
            <v-text-field
              v-model="newCV['file']"
              label="Lien du CV"
              type="text"
              :rules="fileUrlRules"
              readonly
              :error="!!fileError"
              :class="{ 'error-field': !!fileError }"
            />
            <!-- hidden file input -->
            <input
              type="file"
              accept=".pdf"
              ref="fileInput"
              id="fileInput"
              class="hidden-input"
              @change="uploadCV"
            />
          </div>
          <p v-if="fileError" class="error-message">{{ fileError }}</p>
        </div>

        <!-- switch for using this cv by default -->
        <div class="bot-container">
          <p>Je choisis ce CV par défaut</p>
          <CustomSwitch
            v-model="newCV['defaultCV']"
            label=""
            value="1"
            hide-details
          ></CustomSwitch>
        </div>

        <!-- post most recent uploaded cv to db -->
        <div class="btn-row">
          <PrimaryNormalButton
            textContent="Annuler"
            btnColor="light"
            class="cancel-btn"
            @click="cancelCvCreation"
          />
          <PrimaryNormalButton textContent="Enregistrer" @click="submitForm" />
        </div>
      </v-form>

      <!-- btn: add a cv -->
      <div v-else class="add-btn">
        <PrimaryNormalButton
          textContent="Ajouter un CV"
          btnColor="secondary"
          @click="stageNewCV"
          add
        />
      </div>

      <div v-for="(cv, index) in cvList" :key="index" class="CV-container">
        <!-- cv title and title input -->
        <div class="top-container">
          <v-text-field
            v-model="cv['title']"
            label="Titre du CV"
            type="text"
            disabled
          />
        </div>

        <!-- cv name and file input -->
        <div class="middle-container">
          <div class="disabled-field-wrapper">
            <v-text-field
              v-model="cv['file']"
              label="Lien du CV"
              type="text"
              disabled
            />
          </div>
        </div>

        <!-- switch for using this cv by default -->
        <div class="bot-container custom-switch">
          <p>Je choisis ce CV par défaut</p>
          <CustomSwitch
            v-model="cv['default']"
            label=""
            :value="true"
            hide-details
            @change="updateDefaultCV(cv)"
          ></CustomSwitch>
        </div>

        <!-- btn to delete cv -->
        <div class="delete-btn-container">
          <v-btn
            flat
            class="custom-btn"
            append-icon="mdi-trash-can-outline"
            @click="deleteCvFromDB(cv['id'], index)"
          >
            Supprimer
          </v-btn>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import CustomSwitch from '@/components/switch/CustomSwitch.vue';
  import {
    deleteCV,
    getCvList,
    postCV,
    updateCVStatut,
  } from '@/services/profile.service';
  import { toaster } from '@/utils/toast/toast.js';
  import {
    validateCvRules,
    validateNoSpecialChar,
  } from '../../../utils/validationRules';

  export default {
    name: 'MyCVs',

    components: {
      CustomSwitch,
      PrimaryNormalButton,
    },
    props: {
      user: {
        type: Object,
        required: true,
      },
      activeSection: String,
    },
    computed: {
      isPanelOpen() {
        return this.activeSection === 'my-cvs';
      },
    },

    data() {
      return {
        isPanelOn: false, //  state of panel
        cvList: [], //  list of cv, index:[ title:string, name:string, default:boolean ]
        isCvInStaging: false, //  if a cv is already in staging, this help controlling user input
        newCV: {}, //  new cv currently in staging area
        notifications: 0,
        /* input rules */
        formIsValid: false,
        cvRules: [(v) => validateCvRules(v) || true],
        noSpecialChar: [(v) => validateNoSpecialChar(v) || true],
        fileUrlRules: [(v) => !!v || 'Une URL de fichier est requise'],
        fileError: null,
        postTitle: '',
        experienceRange: '',
      };
    },

    mounted() {
      //  bind formation list and display it in user interface
      this.cvList = getCvList();
      if (this.cvList?.length < 1) {
        this.notifications = 1;
      }
      if (this.notifications > 0) {
        this.togglePanel();
      }
    },

    methods: {
      //  toggle section panel visibility
      togglePanel() {
        this.isPanelOn = !this.isPanelOn;
      },

      //  stage a new CV if no cv is being created
      stageNewCV() {
        if (this.isCvInStaging)
          return toaster.showInfoPopup('Veuillez enregistrer le CV en cours.');

        this.isCvInStaging = true;

        this.newCV = {
          title: null,
          positionTitle: null,
          file: null,
          defaultCV: '0',
          cv: null,
          id: null,
        };
      },

      //  upload CV
      uploadCV(event) {
        const selectedFile = event.target.files[0];

        if (selectedFile) {
          this.newCV['file'] = `/mediam/cv/${selectedFile.name}`;
          const reader = new FileReader();
          reader.readAsDataURL(selectedFile);
          this.newCV['cv'] = selectedFile;
        }
        // Suppression de la limitation de taille pour les CV
        if (selectedFile) {
          this.fileError = null;
          this.newCV.fileObject = selectedFile;
          this.newCV.selectedFile = `/uploads/${selectedFile.name}`; // Simulez un chemin d’URL (à remplacer par le backend)
        }
      },

      submitForm() {
        const form = this.$refs.cvForm;

        if (form) {
          // Valider tous les champs du formulaire
          form.validate();

          if (this.formIsValid) {
            this.newCV.positionTitle = this.postTitle;
            this.newCV.experienceRange = this.experienceRange;
            this.postCvToDB(); // Appeler la méthode pour enregistrer le CV
          } else {
            toaster.showErrorPopup(
              'Veuillez corriger les erreurs avant de continuer.'
            );
          }
        }
      },

      //  send CV to db
      async postCvToDB() {
        const uploadSuccess = await postCV(this.newCV);

        if (uploadSuccess) {
          this.isCvInStaging = false;
          this.cvList.push(this.newCV);
          document.getElementById('fileInput').value = null;
          this.$emit('cv-status-change', true); // Emit event indicating a CV now exists
          this.notifications = 0;
        }
      },

      cancelCvCreation() {
        this.isCvInStaging = false; // Désactiver le mode ajout
        this.newCV = {
          title: '',
          file: '',
          defaultCV: false,
          fileObject: null,
        }; // Réinitialiser les champs
        this.fileError = null; // Réinitialiser les erreurs
        this.$refs.fileInput.value = ''; // Réinitialiser l'input fichier
      },

      async updateDefaultCV(cv) {
        try {
          // Si le CV est défini comme par défaut (c'est-à-dire qu'il a été sélectionné via le switch)
          if (cv.default) {
            // Désactiver le statut "par défaut" pour tous les autres CVs
            for (const otherCV of this.cvList) {
              if (otherCV.id !== cv.id) {
                otherCV.default = false;
                // Mettre à jour la base de données pour ces CVs
                await updateCVStatut(otherCV.id, false); // Désactive le statut "par défaut"
              }
            }

            // Ensuite, mettre à jour ce CV comme "par défaut" dans la base de données
            const updateSuccess = await updateCVStatut(cv.id, true); // Met ce CV à jour comme "par défaut"
            if (updateSuccess) {
              toaster.showSuccessPopup('Le CV a été mis à jour avec succès.');
            } else {
              toaster.showErrorPopup(
                'Une erreur est survenue lors de la mise à jour du CV.'
              );
              cv.default = false; // Remet le statut à `false` si l'update échoue
            }
          } else {
            // Si le CV n'est plus "par défaut", on le retire du statut "par défaut"
            const updateSuccess = await updateCVStatut(cv.id, false); // Enlever ce CV du statut "par défaut"
            if (!updateSuccess) {
              toaster.showErrorPopup(
                'Une erreur est survenue lors de la mise à jour du CV.'
              );
            }
          }
        } catch (error) {
          toaster.showErrorPopup(
            'Une erreur est survenue lors de la mise à jour du statut du CV.'
          );
          //console.error('Erreur lors de la mise à jour du CV:', error);
        }
      },

      // delete cv from database
      async deleteCvFromDB(cvId, cvIndex) {
        const deleteSuccess = await deleteCV(cvId);
        if (deleteSuccess) {
          //console.log(this.cvList);
          this.cvList.splice(cvIndex, 1);
          //console.log(this.cvList.length > 0);
          this.$emit('cv-status-change', this.cvList.length > 0); // Emit event based on whether any CVs remain
          if (this.cvList.length <= 0) {
            this.notifications = 1;
          }
        }
      },

      //  trigger the file input when the button is clicked
      triggerCvInput() {
        this.$refs.fileInput.click();
      },
    },
  };
</script>

<style scoped>
  /* section container & layout */
  .section-container {
    background-color: var(--white-200);
    width: 100%;
    border-radius: 2px;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid black;
    cursor: pointer;
  }
  .header-container h5 {
    display: flex;
    gap: 10px;
    align-items: center;
  }
  .bg-color {
    background-color: var(--secondary-2b2);
    border-bottom: none;
  }
  .notification-span {
    background-color: rgba(254, 85, 85, 0.403);
    border: solid 1px red;
    border-radius: 10px;
    padding: 0 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
  }
  .content {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: fit-content;
    padding: 24px;
  }

  /* buttons */
  .add-btn {
    display: flex;
  }

  .btn-row {
    display: flex;
    justify-content: end;
    gap: 15px;
  }

  .delete-btn-container {
    display: flex;
    justify-content: end;
  }

  /* buttons */
  .custom-btn {
    border-radius: 5px;
    border: 1px solid rgba(246, 179, 55, 1);
  }

  /* inputs */
  .hidden-input {
    display: none;
  }

  /* content */
  .input {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .CV-container {
    display: flex;
    flex-direction: column;
    border-top: 1px solid rgba(245, 242, 239, 1);
    border-bottom: 1px solid rgba(245, 242, 239, 1);
    padding-block: 16px;
  }

  .top-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 20px;
  }

  .middle-container {
    width: 100%;
  }

  .disabled-field-wrapper {
    width: 100%;
    display: flex;
  }

  .upload-btn {
    margin-right: 15px;
    height: 55px;
    width: 55px;
  }

  :deep(.disabled-field-wrapper .v-btn__append) {
    margin-inline: 0 !important;
  }

  .error-field .v-label {
    color: rgb(var(--v-theme-error));
  }

  .error-message {
    color: rgb(var(--v-theme-error));
    font-size: 12px;
    margin-top: -15px;
  }

  .bot-container {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .custom-switch {
    display: flex;
    gap: 20px;
    align-items: center;
  }

  @media screen and (min-width: 992px) {
    .input {
      width: 49%;
    }
  }
  .post-title-field {
    margin-bottom: 16px;
  }

  .v-radio-group {
    margin-top: -8px;
  }
</style>
