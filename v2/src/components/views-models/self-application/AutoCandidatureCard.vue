<template>
  <div class="card">
    <h3>{{ candidature.nom_job }}</h3>
    <p><strong>Poste :</strong> {{ candidature.contrat }}</p>
    <p><strong>Date :</strong> {{ formatDate(candidature.created_at) }}</p>

    <div class="actions">
      <!-- <button @click="$emit('edit', candidature)">Modifier</button>
      <button @click="$emit('delete', candidature.id)">Supprimer</button> -->
      <PrimaryNormalButton
        textContent="Modifier"
        @click="$emit('edit', candidature)"
        class="btn-save-all-changes"
      />
      <PrimaryNormalButton
        textContent="Supprimer"
        @click="$emit('delete', candidature.id)"
        btnColor="secondary"
        class="btn-save-all-changes"
      />
    </div>
  </div>
</template>

<script>
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  export default {
    name: 'AutoCandidatureCard',
    props: {
      candidature: Object,
    },
    methods: {
      formatDate(date) {
        return new Date(date).toLocaleDateString('fr-FR');
      },
    },
    components: {
      PrimaryNormalButton,
    },
  };
</script>

<style scoped>
  .card {
    background: #fff;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #ccc;
  }
  .actions {
    margin-top: 10px;
    display: flex;
    gap: 10px;
  }
</style>
