<template>
  <section class="hero-section">
    <div class="hero-content">
      <div class="content-wrapper">
        <h1>Recherchez des profils</h1>
        <p>
          Lorem ipsum dolor sit amet consectetur. Eget ac mauris integer aenean
          sed malesuada volutpat pulvinar auctor. Pellentesque id interdum
          adipiscing lectus magna quam amet risus. Eu tellus aliquet massa
          maecenas nunc pellentesque fringilla et nulla. Lectus tortor cras
          tortor auctor integer enim at pellentesque scelerisque.
        </p>
        <PrimaryRoundedButton
          textContent="Lancez votre recherche"
          @btn-click="gotoPage('/communaute')"
        />
      </div>
    </div>
  </section>
</template>

<script>
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import gotoPage from '@/utils/router';

  export default {
    name: 'SearchProfile',

    components: {
      PrimaryRoundedButton,
    },
    methods: {
      gotoPage,
    },
  };
</script>

<style scoped>
  .hero-section {
    width: 100vw;
    background-color: var(--surface-bg-2);
    display: flex;
    justify-content: center;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 30px;
  }

  .hero-content {
    width: 100%;
    max-width: 1440px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 8%;
  }

  .content-wrapper {
    padding-block: 40px;
    gap: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .content-wrapper p {
    width: 60%;
  }

  /* desktop */
  @media screen and (min-width: 992px) {
    .content-wrapper {
      width: 100%;
    }
  }

  /* large desktop */
  @media screen and (min-width: 1800px) {
    .content-wrapper {
      width: 80%;
      margin: 0 auto;
    }
  }

  /* x-large desktop */
  @media screen and (min-width: 2400px) {
    .content-wrapper {
      width: 70%;
      margin: 0 auto;
    }
  }
</style>
