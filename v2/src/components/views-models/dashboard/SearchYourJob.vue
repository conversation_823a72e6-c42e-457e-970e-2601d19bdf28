<template>
  <section class="hero-section">
    <div class="hero-content">
      <div class="content-wrapper padding-container">
        <h1>Recherche ton job</h1>
        <p>
          Découvrez des opportunités d'emploi sur mesure, adaptées à vos
          compétences et ambitions. Parcourez une large sélection d'offres dans
          divers secteurs. <br />
          Trouvez l'emploi qui vous correspond et postulez en quelques clics.
          Prenez le contrôle de votre carrière dès aujourd'hui.
        </p>

        <PrimaryRoundedButton
          textContent="Lancer ta recherche"
          @btn-click="gotoPage('recherche')"
        />
      </div>
    </div>
  </section>
</template>

<script>
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import gotoPage from '@/utils/router';

  export default {
    name: 'SearchYourJob',

    components: {
      PrimaryRoundedButton,
    },
    methods: {
      gotoPage,
    },
  };
</script>

<style scoped>
  .hero-section {
    background-color: var(--surface-bg-2);
    width: 100vw;
    display: flex;
    justify-content: center;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 3%;
  }

  .hero-content {
    width: 100%;
    max-width: 1440px;
    margin: 0 8%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .content-wrapper {
    padding-block: 40px;
    gap: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .content-wrapper h1 {
    text-wrap: balance;
  }

  /* desktop */
  @media screen and (min-width: 992px) {
    .content-wrapper {
      width: 100%;
    }
  }

  /* large desktop */
  @media screen and (min-width: 1800px) {
    .content-wrapper {
      width: 80%;
      margin: 0 auto;
    }
  }

  /* x-large desktop */
  @media screen and (min-width: 2400px) {
    .content-wrapper {
      width: 70%;
      margin: 0 auto;
    }
  }
</style>
