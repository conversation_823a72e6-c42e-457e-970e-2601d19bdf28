<template>
  <section class="hero-section">
    <div class="hero-content">
      <div class="content-wrapper">
        <h2>Candidatures récentes {{ userTitle ? `de ${userTitle}` : '' }}</h2>
        <div class="recent-jobs-card-wrapper">
          <CompactCandidateCard
            v-if="!mobile"
            v-for="candidate in candidates.slice(0, 3)"
            :key="candidate.id"
            :candidate="candidate"
            :jobOfferTitle="candidate.offer_title"
            :jobOfferId="candidate.offer_id"
          />

          <!-- carousel for mobile -->
          <v-carousel v-else :navigation="true">
            <v-carousel-item
              v-for="candidate in candidates.slice(0, 3)"
              :key="candidate.id"
            >
              <CompactCandidateCard
                :candidate="candidate"
                :jobOfferTitle="candidate.offer_title"
                :jobOfferId="candidate.offer_id"
              />
            </v-carousel-item>
          </v-carousel>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  import CompactCandidateCard from '@/components/cards/candidate-card/CompactCandidateCard.vue';

  export default {
    name: 'RecentCandidates',
    components: {
      CompactCandidateCard,
    },
    props: {
      candidates: {
        type: Array,
        required: true,
        default: () => [],
      },
      userTitle: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        mobile: window.innerWidth <= 799, // Initial mobile state based on screen width
      };
    },
    watch: {
      candidates: {
        handler(newValue) {
          //console.log('Candidates updated:', newValue);
        },
        deep: true,
      },
    },
    created() {
      // Initial mobile state check
      //console.log('Initial candidates:', this.candidates);
      //console.log('Is mobile:', this.mobile);
      window.addEventListener('resize', this.handleResize);
    },
    beforeDestroy() {
      // Remove the resize event listener when the component is destroyed
      window.removeEventListener('resize', this.handleResize);
    },
    methods: {
      handleResize() {
        // Update the mobile state based on screen width
        this.mobile = window.innerWidth <= 799;
        //console.log('Window resized, is mobile:', this.mobile);
      },
    },
  };
</script>

<style>
  .hero-section .v-responsive__content {
    display: flex;
    justify-content: space-around;
    align-items: center;
  }
</style>

<style scoped>
  .hero-section {
    width: 100vw;
    background-color: #f5e5cc;
    display: flex;
    justify-content: center;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
  }

  .hero-content {
    width: 100%;
    max-width: 1440px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 8%;
  }

  .content-wrapper {
    height: 100%;
    width: 100%;
    padding-block: 40px;
    gap: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .recent-jobs-card-wrapper {
    height: 100%;
    width: 100%;
    display: flex;
    gap: 16px;
    justify-content: center;
  }

  .v-carousel__prev,
  .v-carousel__next {
    display: flex !important;
    align-items: center;
    justify-content: center;
  }

  @media screen and (max-width: 1279px) {
    h2 {
      display: flex;
      justify-content: center;
    }

    .hero-section {
      height: fit-content;
    }

    .recent-jobs-card-wrapper {
      flex-direction: row;
    }
  }
</style>
