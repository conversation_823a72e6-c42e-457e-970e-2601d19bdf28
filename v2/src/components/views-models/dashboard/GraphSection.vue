<template>
  <section class="graph-dashboard-container">
    <div class="content-wrapper">
      <div class="graph-dashboard-box-main">
        <!--Première ligne-->
        <div class="graph-dashboard-box-main-top">
          <div
            class="graph-dashboard-box candidatures"
            @click="this.$router.push('/candidatures')"
          >
            <h5>Total des candidatures</h5>
            <h2>{{ user.postulation?.length || 0 }}</h2>
          </div>

          <div class="graph-dashboard-box cursor-pointer">
            <h5>Total des auto-postulations</h5>
            <!--<h2>{{ user.autoApply?.length || 0 }}</h2> -->
            <!--Is A boolean variant in the data ?? (potential problem)-->
            <h2 id="incoming">à venir</h2>
          </div>
        </div>

        <!--deuxième ligne-->
        <div class="graph-dashboard-box-main-bottom">
          <div
            class="graph-dashboard-box cursor-pointer"
            @click="this.$router.push('/favoris')"
          >
            <h5>Favoris</h5>
            <h2>{{ user.favorie_job?.length || 0 }}</h2>
          </div>

          <div
            class="graph-dashboard-box cursor-pointer"
            @click="this.$router.push('/alertes')"
          >
            <h5>Alertes</h5>
            <h2>{{ user.alerte?.length || 0 }}</h2>
          </div>

          <div
            class="graph-dashboard-box cursor-pointer"
            @click="this.$router.push('/reseau-social')"
          >
            <h5>Posts</h5>
            <h2>{{ posts }}</h2>
          </div>

          <!-- <div class="graph-dashboard-box auto-candidatures">
              <h5>Candidatures auto</h5>
              <h2>à venir</h2>
            </div>-->
          <!--   <div class="graph-dashboard-box">
            <h5>Publications</h5>
            <h2>à venir</h2>
            <h2>{{ user.posts.length }}</h2>
          </div> -->
        </div>
      </div>

      <!--Prodile card-->
      <div class="graph-dashboard-box-profile-infos">
        <div
          v-if="progressBar < 100"
          class="graph-dashboard-box graph-dashboard-box-profile-completed cursor-pointer"
          @click="this.$router.push('/profil')"
        >
          <h5>Mon profil</h5>
          <p>Profil complété à</p>
          <!-- progress bar -->
          <v-progress-circular
            :model-value="progressBar"
            :size="mobile ? 110 : 121"
            :width="mobile ? 8 : 10"
            rounded
            bg-color="var(--text-3)"
            color="var(--primary-1)"
          >
            <template v-slot:default> {{ progressBar }}% </template>
          </v-progress-circular>
        </div>
        <div
          v-else
          class="graph-dashboard-box graph-dashboard-box-profile-complete cursor-pointer"
          @click="this.$router.push('/profil')"
        >
          <h5>Mon profil</h5>
          <div class="profile-complete-celebration">
            <p>🎉</p>
            <p>Profil complet !</p>
          </div>
        </div>

        <!-- profile summary with graph and user infos -->
        <div class="graph-dashboard-box-profile-summary">
          <!-- header -->
          <div class="graph-dashboard-box-profile-summary-header">
            <div class="graph-dashboard-box-profile-summary-header-resume">
              <!-- user profile picture -->
              <UserAvatar :user="user" />
              <!-- user first name and last name -->
              <div class="d-flex flex-column">
                <p>{{ user.last_name }} {{ user.first_name }}</p>
                <p>{{ user.titre }}</p>
              </div>
            </div>
            <!-- settings ico -->
            <div
              class="graph-dashboard-box-profile-summary-header-icon"
              @click="this.$router.push('/parametres')"
            >
              <button>
                <img
                  src="@/assets/icons/settings.svg"
                  class="border-radius-5"
                  alt="Icône des paramètres"
                />
              </button>
            </div>
          </div>
          <!-- description -->
          <p class="graph-dashboard-box-profile-summary-description">
            {{ user.about }}
          </p>
          <!-- button -->
          <div class="graph-dashboard-box-profile-summary-button">
            <button
              class="border-radius-5"
              @click="this.$router.push('/profil')"
            >
              Voir mon profil
              <img
                src="@/assets/icons/yellow-eye.svg"
                alt="Icône d'œil jaune pour voir profil"
              />
            </button>
          </div>
        </div>
      </div>

      <!-- Nouvelle section pour les données de l'entreprise, en utilisant les mêmes classes de style -->
      <div v-if="user.companyName" class="graph-dashboard-box-profile-infos">
        <div class="graph-dashboard-box graph-dashboard-box-profile-summary">
          <div class="graph-dashboard-box-profile-summary-header">
            <div class="graph-dashboard-box-profile-summary-header-resume">
              <!-- Icône de l'entreprise (vous pouvez le personnaliser selon votre design) -->
              <img src="company-icon-placeholder.png" class="company-icon" />

              <!-- Informations sur l'entreprise -->
              <div class="d-flex flex-column">
                <p>{{ user.companyName }}</p>
                <p>Numéro SIRET: {{ user.siretNumber }}</p>
                <p>{{ user.companyOwner }}</p>
              </div>
            </div>
          </div>

          <!-- Description de l'entreprise -->
          <p class="graph-dashboard-box-profile-summary-description">
            {{ user.companyDescription }}
          </p>

          <!-- Bouton pour voir le profil de l'entreprise -->
          <div class="graph-dashboard-box-profile-summary-button">
            <button class="border-radius-5" @click="gotoCompanyProfile">
              Voir le profil
              <img
                src="@/assets/icons/yellow-eye.svg"
                alt="Icône d'œil jaune pour voir profil"
              />
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  import { getPostsByUser } from '@/services/post.service';
  import gotoPage from '@/utils/router';
  import {
    getProgressBarCompletion,
    requiredFieldsCandidate,
  } from '@/utils/userUtilities';
  import UserAvatar from '../profil/UserAvatar.vue';

  export default {
    name: 'DashboardPage',
    props: {
      user: {
        type: Object,
        required: true,
        default: () => {},
      },
      mobile: {
        type: Boolean,
      },
    },
    components: {
      UserAvatar,
    },
    data() {
      return {
        previewImage: '',
        totalApplications: 0,
        autoApply: 0,
        favorites: 0,
        alerts: 0,
        posts: 0,
        userCompleted: 0,
        progressBar: 0,
        fieldCompletion: {
          MyProfile: 0,
          MyCV: 0,
          MyCriterias: 0,
          MyCompetences: 0,
        },
      };
    },

    mounted() {
      this.getUserPosts();
      this.progressBar = getProgressBarCompletion(
        this.user,
        requiredFieldsCandidate
      );
    },

    methods: {
      gotoPage,
      async getUserPosts() {
        try {
          const response = await getPostsByUser(this.user.id);
          this.posts = Array.isArray(response.data) ? response.data.length : 0;
        } catch (error) {
          //console.error('Erreur lors de la recuperation des posts:', error);
          this.posts = 0;
        }
      },
      gotoCompanyProfile() {
        this.$router.push('/company-profile'); //Changer la route `/company-profile` par celle qui correspond.
      },
    },
  };
</script>

<style>
  .graph-dashboard-box-profile-completed .v-progress-circular {
    font-size: 24px;
    font-family: 'Anton';
    margin-top: 20px;
  }

  .graph-dashboard-box-profile-completed .v-progress-circular__underlay {
    color: var(--surface-bg-2) !important;
  }
  @media screen and (max-width: 1280px) {
    .graph-dashboard-box-profile-completed .v-progress-circular {
      margin-top: 30px;
    }
  }
</style>

<style scoped>
  h2 {
    color: var(--primary-1);
  }

  h5 {
    color: var(--surface-bg);
    display: flex;
    justify-content: center;
    text-align: center;
  }

  .graph-dashboard-container {
    margin-top: 50px;
  }

  .content-wrapper {
    display: flex;
  }

  .graph-dashboard-box-main {
    width: 50%;
  }

  .graph-dashboard-box-main > div {
    height: 127.31px;
  }

  /* applies and auto applies container */
  .graph-dashboard-box-main-top {
    gap: 16px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
  }

  /* favs, alerts, and posts container */
  .graph-dashboard-box-main-bottom {
    gap: 16px;
    margin-top: 20px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
  }

  /* user infos container */
  .graph-dashboard-box-profile-infos {
    width: 50%;
    gap: 20px;
    padding-left: 17px;
    display: flex;
  }

  /* round graph profil completed container */
  .graph-dashboard-box-profile-completed {
    width: 100%;
    height: 275px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    color: var(--surface-bg);
    font-size: 12px;
  }

  /* user infos box container */
  .graph-dashboard-box-profile-summary {
    width: 100%;
    height: 275px;
    padding: 15px;
    gap: 20px;
    border-radius: 25px;
    display: flex;
    flex-direction: column;
    background-color: var(--surface-bg-2);
    color: var(--text-1);
  }

  /* image, first name, last name and settings ico container */
  .graph-dashboard-box-profile-summary-header {
    height: 45%;
    display: flex;
    justify-content: space-between;
  }

  /* image, first name and last name container */
  .graph-dashboard-box-profile-summary-header-resume {
    display: flex;
    gap: 10px;
    padding-right: 4px;
  }

  /* user image */
  .graph-dashboard-box-profile-summary-header-resume img {
    height: 92px;
    width: 92px;
    border-radius: 50%;
    object-fit: cover;
  }

  /* user first name and last name */
  .graph-dashboard-box-profile-summary-header-resume div {
    display: flex;
    gap: 10px;
  }

  /* user title */
  .graph-dashboard-box-profile-summary-header-resume div > p:nth-child(2) {
    font-size: 12px;
  }

  /* settings ico container */
  .graph-dashboard-box-profile-summary-header-icon img {
    height: 32px;
    width: 32px;
    padding: 4px;
    background-color: var(--surface-bg);
  }

  .graph-dashboard-box-profile-summary-description {
    height: 40%;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4; /* edit this line to change the number of lines displayed */
    line-clamp: 4; /* Standard property for compatibility */
    line-height: 1.2;
  }

  .graph-dashboard-box-profile-summary-button {
    height: 25%;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
  }

  .graph-dashboard-box-profile-summary-button button {
    height: 32px;
    padding: 10px;
    gap: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--text-1);
    color: var(--surface-bg);
  }

  .graph-dashboard-box-profile-summary-button button > img {
    height: 24px;
    width: 24px;
  }

  /* template for boxs */
  .graph-dashboard-box {
    cursor: pointer;
    border: 1px solid black;
    padding: 10px;
    border-radius: 25px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: var(--text-1);
  }

  /* mobile */
  @media screen and (max-width: 288px) {
    #incoming {
      font-size: 25px;
    }

    h5 {
      font-size: 12px;
    }

    .graph-dashboard-box-profile-infos {
      padding-top: 81px;
      padding-left: 0px;
    }
  }

  @media screen and (min-width: 289px) and (max-width: 352px) {
    h5 {
      font-size: 14px;
    }

    .graph-dashboard-box-profile-infos {
      padding-top: 81px;
      padding-left: 0px;
    }
  }

  @media screen and (max-width: 393px),
    screen and (min-width: 394px) and (max-width: 437px),
    screen and (min-width: 438px) and (max-width: 491px),
    screen and (min-width: 492px) and (max-width: 528px),
    screen and (min-width: 529px) and (max-width: 536px),
    screen and (min-width: 537px) and (max-width: 612px),
    screen and (min-width: 613px) and (max-width: 740px),
    screen and (min-width: 740px) and (max-width: 766px) {
    .content-wrapper {
      flex-direction: column;
      align-items: center;
      gap: 20px;
    }

    .graph-dashboard-box-main,
    .graph-dashboard-box-profile-infos {
      width: 100%;
      padding-left: 0px;
    }

    .graph-dashboard-box-main-bottom {
      gap: 8px;
      margin-top: 20px;
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      grid-gap: 10px;
    }

    .graph-dashboard-box {
      padding: 15px;
      text-align: center;
    }

    .graph-dashboard-box-profile-summary-header-resume img {
      height: 62px;
      width: 62px;
    }

    .graph-dashboard-box-profile-summary-header-icon img {
      height: 30px;
      width: 30px;
    }

    .graph-dashboard-box-profile-completed,
    .graph-dashboard-box-profile-summary {
      height: auto;
      padding: 8px;
    }

    .graph-dashboard-box-profile-summary-header-resume {
      flex-direction: column;
      gap: 20px;
    }

    .graph-dashboard-box-profile-summary-description {
      height: auto;
      margin-block: 10px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      line-clamp: 3; /* Standard property for compatibility */
      line-height: 1.2;
    }
  }

  @media screen and (min-width: 768px) and (max-width: 864px),
    screen and (min-width: 865px) and (max-width: 932px),
    screen and (min-width: 933px) and (max-width: 993px) {
    .content-wrapper {
      flex-direction: column;
      align-items: center;
      gap: 20px;
    }

    .graph-dashboard-box-main,
    .graph-dashboard-box-profile-infos {
      width: 90%;
      padding-left: 0px;
    }

    .graph-dashboard-box-main-bottom {
      gap: 10px;
      margin-top: 20px;
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      grid-gap: 15px;
    }

    .graph-dashboard-box {
      padding: 20px;
      text-align: center;
    }

    .graph-dashboard-box-profile-summary-header-resume img {
      height: 80px;
      width: 80px;
    }

    .graph-dashboard-box-profile-summary-header-icon img {
      height: 35px;
      width: 35px;
    }

    .graph-dashboard-box-profile-completed,
    .graph-dashboard-box-profile-summary {
      height: auto;
      padding: 15px;
    }

    .graph-dashboard-box-profile-summary-header-resume {
      flex-direction: column;
      gap: 25px;
    }

    .graph-dashboard-box-profile-summary-description {
      height: auto;
      margin-block: 15px;
      -webkit-line-clamp: 4;
      line-clamp: 4; /* Standard property for compatibility */
      line-height: 1.4;
    }
  }

  @media screen and (min-width: 994px) and (max-width: 1104px) {
    .content-wrapper {
      width: 100%;
    }
  }

  @media screen and (min-width: 1105px) and (max-width: 1280px),
    screen and (min-width: 1281px),
    screen and (min-width: 1800px) {
    .content-wrapper {
      width: 100%;
    }
  }

  @media screen and (min-width: 1824px) {
    .graph-dashboard-box-main > div {
      height: 7vw;
    }

    .graph-dashboard-box-profile-completed {
      height: 15vw;
    }

    .graph-dashboard-box-profile-summary {
      height: 15vw;
    }
  }

  @media screen and (min-width: 2400px) {
    .content-wrapper {
      width: 100%;
      margin: 0 auto;
    }
  }

  /* Styles pour le profil complet */
  .graph-dashboard-box-profile-complete {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    border-radius: 15px;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  }

  .graph-dashboard-box-profile-complete h5 {
    color: white;
    margin-bottom: 10px;
  }

  .profile-complete-celebration p:first-child {
    font-size: 3rem;
    margin: 0;
    animation: bounce 2s infinite;
  }

  .profile-complete-celebration p:last-child {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 5px 0 0 0;
  }

  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    60% {
      transform: translateY(-5px);
    }
  }
</style>
