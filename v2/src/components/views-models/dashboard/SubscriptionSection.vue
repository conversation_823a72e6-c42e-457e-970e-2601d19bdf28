<template>
<section class="subscribe-section padding-container">

  <div class=content-wrapper>
    <h2>Les abonnements Thanks-Boss</h2>

    <div class="card-display">
        <SubscriptionCompactCard v-if="!mobile" v-for="subscription in subscriptionList" :key="subscription.id" :subscription="subscription"/>
      
        <!-- carousel for mobile -->
        <v-carousel v-else :navigation="true">
          <v-carousel-item v-for="item in subscriptionList" :key="item.id">
            <SubscriptionCompactCard :subscription="item" />
          </v-carousel-item>
        </v-carousel>
    </div>
  </div>

  <BackToTopArrow />
</section>
</template>

<script>
import SubscriptionCompactCard from '@/components/cards/subscription-cards/SubscriptionCompactCard.vue';
import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue'

export default {
  name: 'SubscribeSection',
  props: {
    subscriptionList: {
      type: Array,
      required: true,
      default: () => [],
    },
    mobile: {
      type: Boolean,
    }
  }, 
  components: {
    SubscriptionCompactCard,
    BackToTopArrow
  },
  methods: {
    // TODO implémenter les fonctionnalités des btns
    test() {
      //console.log("l-36 HowDoesItWork.vue => getting the click fron children children (main comp > card comp > btn comp)");
    },
  },
};
</script>

<style>
.subscribe-section .v-responsive__content {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
</style>

<style scoped>
.subscribe-section {
  background-color: var(--text-1);
  color: var(--surface-bg);
  width: 100%;
  display: flex;
  justify-content: center;
}

.content-wrapper {
  width: 100%;
  padding-top: 30px;
  padding-bottom: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.content-wrapper h2 {
  margin-bottom: 40px;
}

.card-display {
  height: 100%;
  width: 100%;

}

/* desktop */
@media screen and (min-width: 1280px) {
  .card-display {
    gap: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

/* large desktop */
@media screen and (min-width: 1800px) {
  .content-wrapper {
    width: 70%;
  }

  .card-display {
    gap: 80px;
  }
}

/* x-large desktop */
@media screen and (min-width: 2400px) {
  .content-wrapper {
    width: 60%;
  }
}
</style>