<template>
  <section class="hero-section">
    <div class="hero-content">
      <div class="content-wrapper">
        <h2>Annonces récentes {{ userTitle ? `de ${userTitle}` : '' }}</h2>
        <div class="recent-jobs-card-wrapper">
          <CompactJobCard
            v-if="!mobile"
            v-for="job in jobsToDisplay"
            :key="job.id"
            :job="job"
          />

          <!-- carousel for mobile -->
          <v-carousel v-else :navigation="true">
            <v-carousel-item v-for="job in jobsToDisplay" :key="job.id">
              <CompactJobCard :job="job" />
            </v-carousel-item>
          </v-carousel>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import CompactJobCard from '@/components/cards/job-cards/CompactJobCard.vue';
  import { getPublishedJobList } from '../../../services/job.service.js';

  export default {
    name: 'RecentJobs',
    components: {
      PrimaryRoundedButton,
      CompactJobCard,
    },
    props: {
      jobs: {
        type: Array,
        required: true,
        default: () => [],
      },
      mobile: {
        type: Boolean,
        default: false,
      },
      userTitle: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        jobsToDisplay: [], // Local data to store jobs to be displayed
      };
    },
    methods: {
      async fetchJobs() {
        try {
          const response = await getPublishedJobList();
          this.jobsToDisplay = response.jobs.slice(0, 3);
        } catch (error) {
          //console.error('Error fetching jobs:', error);
        }
      },
    },
    mounted() {
      this.fetchJobs();
    },
  };
</script>

<style>
  .hero-section .v-responsive__content {
    display: flex;
    justify-content: space-around;
    align-items: center;
  }
</style>

<style scoped>
  .hero-section {
    background-color: #f5e5cc;
    width: 100vw;
    height: 352px;
    display: flex;
    justify-content: center;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
  }

  .hero-content {
    width: 100%;
    max-width: 1440px;
    margin: 0 8%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  h2 {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .content-wrapper {
    height: 100%;
    width: 100%;
    padding-block: 40px;
    gap: 24px;
    display: flex;
    justify-content: center;
  }

  .recent-jobs-card-wrapper {
    height: 100%;
    width: 100%;
    gap: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* desktop */
  @media screen and (min-width: 992px) {
    .content-wrapper {
      width: 100%;
    }
  }

  @media screen and (max-width: 1279px) {
    h2 {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .hero-section {
      height: fit-content;
    }
    .content-wrapper {
      flex-direction: column;
    }
  }
  @media screen and (min-width: 1280px) {
    .recent-jobs-card-wrapper {
      margin-left: 7vw;
    }
  }

  /* large desktop */
  @media screen and (min-width: 1800px) {
    .content-wrapper {
      width: 80%;
      margin: 0 auto;
    }

    h2 {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  /* x-large desktop */
  @media screen and (min-width: 2400px) {
    .hero-section {
      height: 400px;
    }
    .content-wrapper {
      width: 70%;
      margin: 0;
      display: flex;
      flex-direction: column;
    }

    h2 {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .recent-jobs-card-wrapper {
      gap: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 0;
    }
  }
</style>
