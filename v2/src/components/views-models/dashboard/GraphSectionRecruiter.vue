<template>
  <section class="graph-dashboard-container">
    <div class="content-wrapper">
      <div class="graph-dashboard-box-main">
        <!--Première ligne-->
        <div class="graph-dashboard-box-main-top">
          <div
            class="graph-dashboard-box-top cursor-pointer"
            @click="this.$router.push('/recruteur/offres')"
          >
            <h5>Total des offres</h5>
            <h2>{{ recruiterJobOffers?.length || 0 }}</h2>
          </div>

          <div
            class="graph-dashboard-box-top cursor-pointer"
            @click="this.$router.push('/recruteur/offres')"
          >
            <h5>Total des candidatures</h5>
            <h2 class="applics-pointer">
              {{ totalCandidatesCount }}
            </h2>
          </div>
        </div>

        <!--Deuxième ligne-->
        <div class="graph-dashboard-box-main-bottom">
          <!--
          <div
            class="graph-dashboard-box-bottom cursor-pointer"
            @click="this.$router.push('/recruiter/alerts')"
          >
            <h5>Total des alertes</h5>
            <h2>{{ user.alerte.length }}</h2>
          </div>
          -->
          <div
            class="graph-dashboard-box-bottom cursor-pointer"
            @click="this.$router.push('/recruteur/favoris')"
          >
            <h5>Total des favoris</h5>
            <h2>{{ favorites }}</h2>
          </div>
          <div
            class="graph-dashboard-box-bottom cursor-pointer"
            @click="this.$router.push('/reseau-social')"
          >
            <h5>Total des posts</h5>
            <h2>{{ posts }}</h2>
          </div>
        </div>
      </div>

      <div class="graph-dashboard-box-profile-infos">
        <div
          class="graph-dashboard-box graph-dashboard-box-profile-completed cursor-pointer"
          @click="
            this.$router.push({ path: '/recruteur/profil', hash: '#avis' })
          "
        >
          <h5>Note globale</h5>
          <p>Avis laissé par les candidats</p>
          <div v-if="globalRating === 0">
            <p>Pas encore d'avis sur cette entreprise</p>
          </div>
          <v-progress-circular
            :model-value="progressBar"
            :size="mobile ? 110 : 121"
            :width="mobile ? 8 : 10"
            rounded
            bg-color="var(--text-3)"
            color="var(--primary-1)"
          >
            <template v-slot:default> {{ progressBar }}% </template>
          </v-progress-circular>
        </div>
        <div class="graph-dashboard-box-profile-summary">
          <div class="graph-dashboard-box-profile-summary-header">
            <div class="graph-dashboard-box-profile-summary-header-resume">
              <UserAvatar :user="user" />
              <div class="d-flex flex-column title-wrapper">
                <p class="company-name">{{ user.company }}</p>
                <div class="siret">
                  <p>{{ user.siret }}</p>
                </div>
              </div>
            </div>
            <div
              class="graph-dashboard-box-profile-summary-header-icon"
              @click="this.$router.push('/parametres')"
            >
              <button>
                <img
                  src="@/assets/icons/settings.svg"
                  class="border-radius-5"
                />
              </button>
            </div>
          </div>
          <p class="graph-dashboard-box-profile-summary-description">
            {{ user.about }}
          </p>
          <div class="graph-dashboard-box-profile-summary-button">
            <button
              class="border-radius-5"
              @click="gotoPage('/recruteur/profil')"
            >
              Voir le profil <img src="@/assets/icons/yellow-eye.svg" />
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  import {
    getCandidatesForOffer,
    getJobOffers,
  } from '@/services/search.service';
  import gotoPage from '@/utils/router';
  import {
    getProgressBarCompletion,
    requiredFieldsCandidate,
  } from '@/utils/userUtilities';
  import { getPostsByUser } from '../../../services/post.service';
  import UserAvatar from '../profil/UserAvatar.vue';
  export default {
    name: 'DashboardPage',
    components: {
      UserAvatar,
    },
    props: {
      user: {
        type: Object,
        required: true,
        default: () => {},
      },
      mobile: {
        type: Boolean,
      },
    },
    data() {
      return {
        previewImage: '',
        totalApplications: 0,
        autoApply: 0,
        favorites: this.user.recruteurFavorites?.length || 0,
        alerts: 0,
        posts: 0,
        userCompleted: 0,
        progressBar: 0,
        numberOfFieldNeededForCompletion: null,
        fieldCompletion: {
          MyProfile: 0,
          MyCV: 0,
          MyCriterias: 0,
          MyCompetences: 0,
        },
        totalCandidatesCount: 0,
        recruiterPosts: 0,
        globalRating: 0,
      };
    },
    mounted() {
      this.getOffersByRecruiter();
      this.getUserPosts();
      this.progressBar = getProgressBarCompletion(
        this.user,
        requiredFieldsCandidate
      );
    },
    methods: {
      gotoPage,
      async getUserPosts() {
        try {
          const response = await getPostsByUser(this.user.id);
          this.posts = Array.isArray(response.data) ? response.data.length : 0;
        } catch (error) {
          //console.error('Erreur lors de la recuperation des posts:', error);
          this.posts = 0;
        }
      },
      //  WORK IN PROGRESS update progress bar with datas from store
      // Nouveau méthode pour rediriger vers le profil de l'entreprise.
      gotoCompanyProfile() {
        this.$router.push('/company-profile'); //Changer la route `/company-profile` par celle qui correspond.
      },
      async getOffersByRecruiter() {
        try {
          const jobOffers = await getJobOffers();
          this.recruiterJobOffers = jobOffers.results.filter(
            (offer) => offer.author === this.user.id
          );

          let totalCandidatesCount = 0;

          // Récupérer le nombre de candidatures pour chaque offre
          for (const offer of this.recruiterJobOffers) {
            const candidates = await getCandidatesForOffer(offer.id);
            offer.candidatesCount = candidates.postulants.length; // Ajoutez le nombre de candidatures à l'objet offre
            totalCandidatesCount += offer.candidatesCount; // Accumulez le nombre de candidatures
          }

          this.totalCandidatesCount = totalCandidatesCount;
        } catch (error) {
          //console.error('Error in getOffersByRecruiter:', error);
        }
      },
    },
  };
</script>

<style>
  .graph-dashboard-box-profile-completed .v-progress-circular {
    font-size: 24px;
    font-family: 'Anton';
    margin-top: 20px;
  }

  .graph-dashboard-box-profile-completed .v-progress-circular__underlay {
    color: var(--surface-bg-2) !important;
  }
  @media screen and (max-width: 1280px) {
    .graph-dashboard-box-profile-completed .v-progress-circular {
      margin-top: 30px;
    }
  }
</style>

<style scoped>
  h2 {
    color: var(--primary-1);
  }

  h5 {
    color: var(--surface-bg);
    display: flex;
    justify-content: center;
    text-align: center;
  }

  .text-white {
    color: white;
  }

  .applics-pointer {
    position: relative;
    top: 0; /* Ajustez cette valeur selon vos besoins */
  }

  .graph-dashboard-container {
    margin-top: 30px;
    display: flex;
    justify-content: space-around;
    height: 80%;
  }

  .content-wrapper {
    width: 100%;
    display: flex;
  }

  .graph-dashboard-box-main {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    width: 100%;
  }

  .graph-dashboard-box-main-top {
    gap: 16px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
  }

  .graph-dashboard-box-main-bottom {
    gap: 16px;
    margin-top: 0px;
    justify-content: space-between;
    display: flex;
    grid-template-columns: repeat(3, 1fr);
  }

  .graph-dashboard-box-top,
  .graph-dashboard-box-bottom {
    padding: 12px;
    border-radius: 25px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 18vh;
    align-items: center;
    background-color: var(--text-1);
    box-sizing: border-box;
    overflow: hidden;
    width: calc(100% - 8px);
  }

  .graph-dashboard-box {
    padding: 24px;
    border-radius: 25px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: var(--text-1);
    box-sizing: border-box;
    overflow: hidden;
    width: calc(100% - 8px);
  }

  .graph-dashboard-box-main-top .graph-dashboard-box {
    height: 150px;
  }

  .graph-dashboard-box-main-bottom .graph-dashboard-box {
    height: 150px;
  }

  .graph-dashboard-box-profile-infos {
    width: 100%;
    gap: 20px;
    padding-left: 17px;
    display: flex;
  }

  .graph-dashboard-box-profile-completed {
    width: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    color: var(--surface-bg);
  }

  .graph-dashboard-box-profile-summary {
    width: 100%;
    max-width: 600px;
    padding: 15px;
    gap: 20px;
    border-radius: 25px;
    display: flex;
    flex-direction: column;
    background-color: var(--surface-bg-2);
    color: var(--text-1);
  }

  .graph-dashboard-box-profile-summary-header {
    width: 100%;
    height: 45%;
    display: flex;
    justify-content: space-between;
  }

  .graph-dashboard-box-profile-summary-header-resume {
    width: 100%;
    display: flex;
    gap: 10px;
    padding-right: 4px;
  }

  .graph-dashboard-box-profile-summary-header-resume img {
    height: 92px;
    width: 92px;
    border-radius: 50%;
    object-fit: cover;
  }

  .graph-dashboard-box-profile-summary-header-resume div {
    display: flex;
    gap: 10px;
    text-align: center;
  }

  .graph-dashboard-box-profile-summary-header-resume div > p:nth-child(2) {
    font-size: 12px;
  }

  .graph-dashboard-box-profile-summary-header-icon img {
    height: 32px;
    width: 32px;
    padding: 4px;
    background-color: var(--surface-bg);
  }

  .graph-dashboard-box-profile-summary-description {
    height: 40%;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4;
    line-clamp: 4;
    line-height: 1.2;
  }

  .graph-dashboard-box-profile-summary-button {
    height: 25%;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
  }

  .graph-dashboard-box-profile-summary-button button {
    height: 32px;
    padding: 10px;
    gap: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--text-1);
    color: var(--surface-bg);
  }

  .graph-dashboard-box-profile-summary-button button > img {
    height: 24px;
    width: 24px;
  }

  .company-info-box {
    background-color: white;
    color: black;
  }

  .text-black {
    color: black;
  }

  .title-wrapper {
    margin-left: 0.5rem;
  }

  @media screen and (max-width: 1280px) {
    .graph-dashboard-container {
      flex-direction: column;
      align-items: center;
      gap: 20px;
    }
    .graph-dashboard-box-main,
    .graph-dashboard-box-profile-infos {
      width: 100%;
      padding-inline: 3vw;
    }

    .graph-dashboard-box-profile-summary {
      align-items: center;
    }

    .graph-dashboard-box-profile-summary-header-resume img {
      height: 62px;
      width: 62px;
    }

    .graph-dashboard-box-profile-summary-header-icon img {
      height: 30px;
      width: 30px;
    }

    .graph-dashboard-box-profile-completed,
    .graph-dashboard-box-profile-summary {
      height: 300px;
      padding: 8px;
    }

    .graph-dashboard-box-profile-summary-header-resume {
      flex-direction: column;
      gap: 20px;
    }

    .graph-dashboard-box-profile-summary-header-resume div {
      gap: 10px;
    }

    .graph-dashboard-box-profile-summary-description {
      height: 12%;
      margin-block: 10px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      line-height: 1.2;
    }

    .graph-dashboard-box {
      border: 1px solid black;
      padding: 8px;
      border-radius: 25px;
      display: flex;
      flex-direction: column;
      align-items: center;
      background-color: var(--text-1);
    }

    .graph-dashboard-box-top,
    .graph-dashboard-box-bottom {
      height: 18vh;
    }
  }

  @media screen and (min-width: 1824px) {
    .graph-dashboard-box-main > div {
      height: 7vw;
    }
    .graph-dashboard-box-profile-completed {
      height: auto;
    }
    .graph-dashboard-box-profile-summary {
      height: auto;
    }
    .graph-dashboard-box-top,
    .graph-dashboard-box-bottom {
      height: 18vh;
    }
  }

  @media screen and (max-width: 768px) {
    .graph-dashboard-box-main-top,
    .graph-dashboard-box-main-bottom {
      flex-direction: column;
      align-items: center;
      width: 100%;
      margin-bottom: 16px;
    }

    .graph-dashboard-box-top,
    .graph-dashboard-box-bottom {
      height: 18vh;
    }

    .graph-dashboard-box-main-bottom {
      gap: 16px;
      margin-top: 0px;
      justify-content: space-around;
      display: flex;
    }

    .graph-dashboard-box {
      width: 100%;
      margin-bottom: 16px;
    }

    .graph-dashboard-box-profile-infos {
      flex-direction: column;
      align-items: center;
      width: 100%;
    }

    .company-info-box {
      background-color: white;
      color: black;
    }
    .graph-dashboard-box-profile-completed,
    .graph-dashboard-box-profile-summary {
      width: 100%;
      height: auto;
    }

    .graph-dashboard-box-profile-summary-header-resume {
      flex-direction: column;
      align-items: center;
    }

    .graph-dashboard-box-profile-summary-description {
      height: auto;
      -webkit-line-clamp: unset;
      line-clamp: unset;
    }
    .graph-dashboard-box-profile-completed .v-progress-circular {
      margin-top: -10px; /* Remonter un peu la progressBar */
    }
  }

  /* x-large desktop */
  @media screen and (min-width: 2400px) {
    .graph-dashboard-box-top,
    .graph-dashboard-box-bottom {
      height: 13vh;
    }
  }

  /* Small screens (300-325px) */
  @media screen and (max-width: 325px) {
    h2 {
      font-size: 1.5rem;
    }

    h5 {
      font-size: 1rem;
    }

    .graph-dashboard-box-top,
    .graph-dashboard-box-bottom {
      height: auto;
      padding: 10px;
    }

    .graph-dashboard-box-profile-completed {
      width: 100%;
      height: auto;
    }

    .graph-dashboard-box-profile-summary {
      width: 100%;
      height: auto;
      padding: 10px;
    }

    .graph-dashboard-box-profile-summary-header-resume img {
      height: 50px;
      width: 50px;
    }

    .graph-dashboard-box-profile-summary-header-icon img {
      height: 24px;
      width: 24px;
    }

    .graph-dashboard-box-profile-summary-description {
      font-size: 0.8rem;
    }

    .graph-dashboard-box-profile-completed .v-progress-circular {
      size: 80px;
      width: 6px;
      margin-top: -20px;
    }
  }

  /* Medium screens (769-1216px) */
  @media screen and (min-width: 769px) and (max-width: 1216px) {
    h2 {
      font-size: 1.8rem;
    }

    h5 {
      font-size: 1.2rem;
    }

    .graph-dashboard-box-top,
    .graph-dashboard-box-bottom {
      height: auto;
      padding: 15px;
    }

    .graph-dashboard-box-profile-completed {
      width: 100%;
      height: auto;
    }

    .graph-dashboard-box-profile-summary {
      width: 100%;
      height: auto;
      padding: 15px;
    }

    .graph-dashboard-box-profile-summary-header-resume img {
      height: 70px;
      width: 70px;
    }

    .graph-dashboard-box-profile-summary-header-icon img {
      height: 28px;
      width: 28px;
    }

    .graph-dashboard-box-profile-summary-description {
      font-size: 1rem;
    }

    .graph-dashboard-box-profile-completed .v-progress-circular {
      size: 100px;
      width: 8px;
      margin-top: -15px;
    }
  }

  /* Center the text of the global rating block */
  .graph-dashboard-box-profile-completed {
    text-align: center;
  }

  .graph-dashboard-box-profile-completed h5,
  .graph-dashboard-box-profile-completed p {
    margin: 0 auto;
  }
</style>
