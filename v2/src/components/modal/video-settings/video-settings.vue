<template>
    <div class="modal-overlay" v-if="isVisible">
      <div class="modal-content">
        <button class="confirmation-modal-close-button" @click="$emit('close')">
          <img src="@/assets/icons/cross-black.svg" />
        </button>
        <slot></slot>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    props: {
      isVisible: {
        type: Boolean,
        required: true,
      },
    },
    methods: {
      closeModal() {
        this.$emit('close');
      },
    },
  }
  </script>
  
  <style scoped>
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
  }
  .modal-content {
    background: white;
    border-radius: 8px;
    padding: 20px;
    width: 400px; /* Adjust width as needed */
    position: relative;
  }
  .confirmation-modal-close-button {
    position: absolute;
    top: 17px;
    right: 10px;
    border: none;
    background: transparent;
    cursor: pointer;
  }
  </style>
  