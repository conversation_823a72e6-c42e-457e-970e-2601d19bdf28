<template>
  <div class="confirmation-modal">
    <div class="confirmation-modal-container border-radius-20 reduced-size">
      <div class="confirmation-modal-header">
        <h3 class="modal-title">{{ title }}</h3>
        <button class="confirmation-modal-close-button" @click="$emit('close')">
          <img
            src="@/assets/icons/cross-black.svg"
            alt="Bouton de fermeture modale"
          />
        </button>
      </div>
      <p v-html="description" class="modal-description"></p>
      <div class="confirmation-modal-footer">
        <template v-if="actions.length > 0">
          <PrimaryRoundedButton
            v-for="(action, index) in actions"
            :key="index"
            @click="action.onClick"
            :textContent="action.label"
            :btnColor="action.color || 'primary'"
            class="margin"
          />
        </template>
        <template v-else>
          <PrimaryRoundedButton
            @click="$emit('close')"
            textContent="Annuler"
            btnColor="secondary"
            class="margin"
          />
          <PrimaryRoundedButton
            @click="$emit('confirm')"
            textContent="Valider"
            class="margin"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<script>
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';

  export default {
    name: 'ConfirmationModal',
    components: {
      PrimaryRoundedButton,
    },
    props: {
      //  title of modal
      title: {
        type: String,
        default: 'Confirmation',
      },
      // description of modal
      description: {
        type: String,
        default: 'Êtes-vous sûr de vouloir continuer ?',
      },
      actions: {
        type: Array,
        default: () => [],
      },
    },
  };
</script>

<style scoped>
  .margin {
    margin: 14px !important;
  }
  .confirmation-modal {
    margin-left: auto;
    margin-right: auto;
    width: fit-content;
  }
  .reduced-size {
    width: 736px !important;
    min-height: 280px;
    padding-block: 11px !important;
    padding-inline: 28px !important;
  }
  .modal-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 12px;
  }
  .modal-description {
    font-size: 1.4rem;
    line-height: 1.4;
    text-align: center;
    margin-bottom: 18px;
    font-weight: normal;
  }
  .bold-important {
    font-weight: bold;
    color: #eab308;
  }
  .confirmation-modal-container {
    gap: 18px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: var(--surface-bg);
    position: relative;
    z-index: 999;
    font-weight: normal !important;
  }
  .confirmation-modal-header {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: flex-start;
  }
  .confirmation-modal-header button {
    right: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
  }
  .confirmation-modal-header img {
    width: 1.4rem;
  }
  .confirmation-modal-footer {
    display: flex;
    justify-content: center;
  }
  @media screen and (max-width: 768px) {
    .reduced-size {
      width: 95vw !important;
      min-width: unset;
      padding-inline: 6px !important;
    }
    .modal-title {
      font-size: 1.1rem;
    }
    .modal-description {
      font-size: 0.9rem;
    }
    .confirmation-modal-footer {
      width: 95%;
    }
  }
</style>
