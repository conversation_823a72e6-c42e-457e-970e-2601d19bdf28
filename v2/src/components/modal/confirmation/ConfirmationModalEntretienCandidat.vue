<template>
  <div class="confirmation-modal">
    <div class="modal-box">
      <!-- Bouton fermer -->
      <button class="close-btn" @click="$emit('close')">
        <img src="@/assets/icons/cross-black.svg" alt="Fermer" />
      </button>

      <!-- Contenu principal -->
      <div class="content">
        <!-- Avatar + nom -->
        <img class="avatar" :src="recruiterPicture" alt="Avatar" />
        <p class="recruiter-name">{{ recruiterName }}</p>

        <!-- Titre -->
        <h3 class="main-title">Entretien vidéo</h3>

        <!-- Infos -->
        <ul class="info-list">
          <li>
            <img src="@/assets/icons/clock.svg" alt="Durée" />
            30 Minutes
          </li>
          <li>
            <img src="@/assets/icons/video.svg" alt="Type" />
            Conférence vidéo sur la plateforme Thanks-Boss
          </li>
          <li>
            <img src="@/assets/icons/calendar.svg" alt="Date" />
            {{ formattedDate }}
          </li>
          <li>
            <img src="@/assets/icons/world.svg" alt="Fuseau" />
            Fuseau horaire de l'Europe centrale
          </li>
        </ul>

        <!-- Bouton -->
        <PrimaryRoundedButton
          textContent="Programmer"
          @click="$emit('confirm')"
        />
      </div>
    </div>
  </div>
</template>

<script>
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import { format } from 'date-fns';
  import { fr } from 'date-fns/locale';

  export default {
    name: 'ConfirmationModalEntretienCandidat',
    components: {
      PrimaryRoundedButton,
    },
    props: {
      recruiterName: String,
      recruiterPicture: String,
      selectedSlot: Object,
    },

    computed: {
      formattedDate() {
        if (!this.selectedSlot) return '';

        const [hour, minute] = this.selectedSlot.time.split(':');
        const [year, month, day] = this.selectedSlot.date.split('-');

        // Crée une date locale (pas UTC)
        const start = new Date(year, month - 1, day, hour, minute);
        const end = new Date(start.getTime() + 30 * 60 * 1000);

        const timeRange = `${this.formatTime(start)} - ${this.formatTime(end)}`;
        const fullDate = format(start, 'EEEE d MMMM yyyy', { locale: fr });

        return `${timeRange}, ${fullDate}`;
      },
    },

    methods: {
      formatTime(date) {
        return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      },
    },
  };
</script>

<style scoped>
  .confirmation-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    background: rgba(255, 255, 255, 1);
    border-radius: 12px;
    padding: 32px;
    width: 480px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  }

  .modal-box {
    position: relative;
  }

  .close-btn {
    position: absolute;
    right: 16px;
    top: 16px;
    background: transparent;
    border: none;
    cursor: pointer;
  }

  .content {
    text-align: center;
  }

  .avatar {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 8px;
  }

  .recruiter-name {
    font-weight: 600;
    margin-bottom: 8px;
  }

  .main-title {
    font-size: 36px;
    font-weight: 500;
    margin: 16px 0;
  }

  .info-list {
    color: var(--text-1);
    list-style: none;
    padding: 0;
    margin: 24px 0;
    display: flex;
    flex-direction: column;
    gap: 12px;
    font-size: 14px;
    text-align: left;
  }

  .info-list li {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .info-list img {
    width: 16px;
    height: 16px;
  }
</style>
