<template>
  <div class="confirmation-modal">
    <div class="modal-box">
      <!-- Bouton fermer -->
      <button class="close-btn" @click="$emit('close')">
        <img src="@/assets/icons/cross-black.svg" alt="Fermer" />
      </button>

      <!-- Avatar + Titre -->
      <div class="modal-header">
        <img class="avatar" :src="candidatePicture" alt="Candidat" />
        <div class="modal-title">
          <h5>
            Proposer un entretien à <strong>{{ candidateName }}</strong> ?
          </h5>
          <p class="description">
            Pour cet entretien seulement, le candidat pourra choisir parmi les
            dates et horaires de disponibilités que vous avez prédéfinis dans
            votre calendrier.
          </p>
          <p class="lien">
            Gérez vos entretiens dans la partie :
            <a href="#" @click.prevent="redirectToEntretien">mes entretiens</a>
          </p>
        </div>
      </div>

      <!-- Boutons -->
      <div class="modal-actions">
        <PrimaryRoundedButton
          textContent="Confirmer"
          @click="$emit('confirm')"
        />
        <PrimaryRoundedButton
          textContent="Voir mon calendrier"
          btnColor="blue"
          @click="redirectToEntretien"
        />
      </div>
    </div>
  </div>
</template>

<script>
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';

  export default {
    name: 'ConfirmationModalEntretien',
    components: {
      PrimaryRoundedButton,
    },
    props: {
      candidateName: String,
      candidatePicture: String,
    },
    data() {
      return {
        iaActivated: false,
      };
    },
    methods: {
      redirectToEntretien() {
        this.$emit('close');
        this.$router.push({
          path: '/recruteur/offres',
          query: { entretien: 'true' },
        });
      },
    },
  };
</script>

<style scoped>
  .confirmation-modal {
    margin-left: auto;
    margin-right: auto;
    width: fit-content;
  }

  .modal-box {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    width: 580px;
    position: relative;
  }

  .close-btn {
    position: absolute;
    right: 16px;
    top: 16px;
    background: transparent;
    border: none;
    cursor: pointer;
  }

  .modal-header {
    display: flex;
    gap: 16px;
    align-items: center;
  }

  .avatar {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    object-fit: cover;
  }

  .modal-title {
    text-align: left;
  }

  .modal-title h5 {
    margin: 0;
    font-weight: bold;
  }

  .modal-title .description {
    color: var(--text-3);
    margin-top: 4px;
  }
  .modal-title .lien {
    font-size: 12px;
    color: var(--text-3);
    margin-top: 4px;
  }

  .modal-actions {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 30px;
  }
</style>
