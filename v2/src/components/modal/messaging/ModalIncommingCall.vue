<template>
  <div class="incoming-call-notification">
    <div class="incoming-call-modal">
      <div class="incoming-call-icon-container swing-icon">
        <div class="ripple-effect"></div>
        <IncomingCallIcon color="black" />
      </div>
      <h3>A<PERSON> entrant</h3>
      <div class="call-actions">
        <button @click="acceptCall" class="accept-btn">Accepter</button>
        <button @click="rejectCall" class="reject-btn">Refuser</button>
      </div>
    </div>
  </div>
</template>

<script></script>
<style scope>
  .incoming-call-notification {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
  }
  .incoming-call-modal {
    background-color: var(--surface-bg);
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 20px;
    padding: 30px;
  }
  .swing-icon {
    animation: swing 1s infinite ease-in-out;
  }

  @keyframes swing {
    0% {
      transform: rotate(0deg);
    }
    25% {
      transform: rotate(-10deg);
    }
    50% {
      transform: rotate(10deg);
    }
    75% {
      transform: rotate(-10deg);
    }
    100% {
      transform: rotate(0deg);
    }
  }

  .incoming-call-icon-container {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background-color: rgb(42, 186, 42);
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: visible;
  }

  .ripple-effect {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100px;
    height: 100px;
    border: 1px solid rgba(42, 186, 42, 0.8);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(1);
    animation: ripple-animation 1.5s infinite;
    opacity: 1;
  }

  @keyframes ripple-animation {
    0% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 1;
    }
    100% {
      transform: translate(-50%, -50%) scale(1.4);
      opacity: 0;
    }
  }
  .call-actions {
    display: flex;
    gap: 10px;
  }
  .accept-btn {
    background-color: var(--yellow-100);
    color: black;
    border: none;
    padding: 8px 16px;
    cursor: pointer;
    border-radius: 10px;
  }
  .reject-btn {
    background-color: #ccc;
    color: black;
    border: none;
    padding: 8px 16px;
    cursor: pointer;
    border-radius: 10px;
  }
</style>
