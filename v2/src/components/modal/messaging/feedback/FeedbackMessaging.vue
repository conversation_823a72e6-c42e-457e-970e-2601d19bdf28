<template><!-- chat history section -->
<div class="feedback-messaging-container">
  <div class="feedback-messaging-logo-container">
    <img src="@/assets/logo-tb2.png"/>
  </div>
  <div v-if="!messageSent" class="feedback-messaging-middle-container">
    <p v-if="raisonFeedback">Pourquoi la réponse ne te convient pas ?<br> Clique ci-dessous pour écrire ta réponse.</p>
    <p v-else>Quelle est la raison du signalement ?<br> Clique ci-dessous pour écrire ta réponse.</p>
    <div class="feedback-messaging-input-container">
      <v-text-field class="border-radius-5" variant="plain" :rules="notEmptyRules" v-model="feedbackMessage" @click:append="sendFeedback" hide-details>
        <template v-slot:append>
          <img :src="sendIco" class="cursor-pointer" @click="sendFeedback" />
        </template>
      </v-text-field>
    </div>
  </div>
  <div v-else class="feedback-messaging-middle-container">
    <p>Merci pour votre commentaire, il sera bien pris en compte.</p>
  </div>
  <p class="feedback-messaging-close-container" @click="close">X</p>
</div> 

</template>

<script>
import sendIco from "@/assets/icons/send-msg-transparent.svg";

export default {
  name: 'FeedbackMessaging',
  components: {
   
  },
  props: {
    messageSent: {
      type: Boolean,
      required: false,
    },
    raisonFeedback: {
      type: Boolean,
      required: false,
    },
  },   
  data() {
    return {
      notEmptyRules: [(v) => !!v || 'Champ requis'],
      feedbackMessage: '',
      sendIco,
    };
  },
  methods: {
    close() {
      this.$emit('close');
    },
    sendFeedback() {
      this.$emit('send-feedback', this.feedbackMessage);
      this.feedbackMessage = '';
    },
  },
}

</script>


<style>



.feedback-messaging-input-container .v-text-field {
  background-color: var(--text-1);
  color: var(--surface-bg-2);
}

.feedback-messaging-input-container .v-field__input {
  padding: inherit;
  padding-left: 5px;
  font-size: 14px;
}

.feedback-messaging-input-container .v-input__control {
  width: 100%;
}

.feedback-messaging-input-container .v-input__append {
  height: 100%;
  display: flex;
  align-items: center;
  padding-top: inherit !important;
}

</style>

<style scoped>
.feedback-messaging-container {
  width: 51%;
  max-width: 51%;

  margin-left: 10px;
  margin-bottom: 15px;
  padding: 10px;

  border-radius: 10px;
  border-top-left-radius: 0;

  display: flex;
  background-color: var(--primary-1);
}

.feedback-messaging-logo-container {
  width: 30px;
  display: flex;
  justify-content: center;
  cursor: pointer;
}

.feedback-messaging-logo-container img {
  height: 24px;
  width: 24px;
}

.feedback-messaging-middle-container {
  width: 100%;
  margin-inline: 10px;
}

.feedback-messaging-middle-container p {
  margin-top: 4px;
  margin-bottom: 8px;
}

.feedback-messaging-input-container {
  display: flex;
  align-items: center;
  
}

.feedback-messaging-input-container img {
  height: 24px;
  width: 24px;
  margin-right: 8px;
  text-align: center
}

.feedback-messaging-close-container {
  width: 30px;
  display: flex;
  justify-content: center;
  cursor: pointer;
}

</style>
