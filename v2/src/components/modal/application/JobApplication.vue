<template>
  <div class="modal-content">
    <!-- Fond sombre -->
    <div
      v-if="showConfirmationModal"
      class="overlay"
      @click="showConfirmationModal = false"
    ></div>

    <div class="modal-header">
      <h2>
        Postuler à {{ joboffer.title }}
        <!--chez {{ joboffer.company }}-->
      </h2>
      <button class="close-modal" @click="closeApplicationModal">
        <img src="@/assets/social/social-page-redact-post-close.svg" />
        <v-tooltip activator="parent" location="top">Fermer</v-tooltip>
      </button>
    </div>

    <div class="modal-sections">
      <section class="infos">
        <h5>Mes informations</h5>
        <div class="contact">
          <div class="input">
            <v-text-field
              v-model="formData.mail"
              label="<EMAIL>"
              type="text"
              :rules="[...emailRules, ...notEmptyRules]"
            />
          </div>
          <div class="input">
            <v-text-field
              v-model="formData.phone"
              label="0X XX XX XX XX"
              type="text"
              :rules="mobileRules"
            />
          </div>
        </div>

        <div v-if="defaultCv.length > 0" class="defaultCv">
          <!-- afficher le cv par défaut -->
          <div
            v-for="(cv, index) in defaultCv"
            :key="cv.id"
            class="CV-container"
          >
            <!-- cv name and file input -->
            <div class="disabled-field-wrapper">
              <a
                :href="`${baseUrl}${cv.file}`"
                target="_blank"
                rel="noopener noreferrer"
              >
                <img
                  src="@/assets/icons/eye-border-orange.svg"
                  alt="Visualiser CV"
                  class="eye-icon"
                />
              </a>
              <span class="cv-label">
                {{ `${cv.title} - ${getCvName(cv.file)}` }}
              </span>
            </div>
          </div>
        </div>

        <div v-else class="CV-container none-defaultCv">
          <p>
            Veuillez enregistrer un CV par défaut dans votre profil avant de
            postuler.
          </p>
        </div>
      </section>

      <section class="motivation">
        <h5>Pourquoi moi ?</h5>
        <!-- ajout d'une lettre de motivation -->
        <div class="file-input">
          <!-- upload du fichier -->
          <v-file-input
            ref="fileInput"
            v-model="formData.lettre_motivation"
            accept=".pdf,.doc,.docx"
            hide-input
            prepend-icon=""
            truncate-length="15"
          ></v-file-input>
          <!-- bouton pour ajout -->
          <PrimaryNormalButton
            textContent="Télécharger une lettre de motivation (facultative)"
            btnColor="secondary"
            add
            @click="triggerFileUpload"
          />
          <!-- affichage du fichier -->
          <div v-if="formData.lettre_motivation">
            <v-textarea
              :label="formData.lettre_motivation.name"
              type="text"
              disabled
              class="file-upload"
              auto-grow
              rows="1"
            />
          </div>
        </div>
      </section>

      <section class="message">
        <v-textarea
          v-model="formData.lettre"
          label="Expliquez en quelques mots"
          :rules="rules"
          counter
          clear-icon="mdi-close-circle"
          clearable
          auto-grow
        >
        </v-textarea>
      </section>

      <!-- J'AI AJOUTÉ DES QUESTIONS EN localJobOffer POUR TESTER L'AFFICHAGE
             FAUDRA REMETTRE joboffer.questions
             ET v-if="joboffer.questions" DANS LA DIV SECTION
            <section class="questions" >
                <h5>Questions supplémentaires</h5>
                <div v-for="(question, index) in localJoboffer.questions" class="question-wrapper">
                    <h6>{{ question.title }}</h6>
                    <v-text-field
                        v-model="question.answer"
                        label="Votre réponse ici."
                        type="text"
                        :rules="notEmptyRules"
                        fast-fail
                    />
                </div>

            </section>-->
    </div>

    <div class="modal-footer">
      <!-- Info legal -->
      <div class="legal-infos">
        <p>
          L'employeur recevra également une copie de ton profil Thanks-Boss.
        </p>
        <p>
          En soumettant ma candidature, j'accepte que mes données soient
          traitées pour les fins du recrutement.
        </p>
      </div>

      <!-- bouton -->
      <div class="btn-wrapper">
        <PrimaryNormalButton
          textContent="Postuler à l'offre"
          @click="openConfirmationModal"
        />
      </div>
    </div>
  </div>

  <!-- Boîte de confirmation personnalisée -->
  <ConfirmationModal
    v-if="showConfirmationModal"
    class="confirmation-modal"
    title="Confirmer l'envoi de votre candidature"
    description="Êtes-vous sûr que toutes les informations sont exactes ?"
    @close="showConfirmationModal = false"
    @confirm="onConfirmApplication"
  />
</template>

<script>
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import ConfirmationModal from '@/components/modal/confirmation/ConfirmationModal.vue';
  import { postApplicationToJob } from '@/services/job.service.js';
  import { getCvList } from '@/services/profile.service';
  import { toaster } from '@/utils/toast/toast.js';
  import { mapGetters } from 'vuex';
  import store from '../../../store';
  import {
    validateEmail,
    validateMobile,
    validateNoSpecialChar,
    validateNotEmpty,
  } from '../../../utils/validationRules';

  export default {
    name: 'JobApplication',

    props: {
      joboffer: {
        type: Object,
        required: true,
      },
    },

    components: {
      PrimaryNormalButton,
      ConfirmationModal,
    },

    data() {
      return {
        // J'AI AJOUTÉ DES QUESTIONS EN localJobOffer POUR TESTER L'AFFICHAGE
        localJoboffer: {
          questions: [
            {
              title: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit?',
            },
            { title: 'Ut enim ad minim veniam, quis nostrud exercitation?' },
            {
              title:
                'Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur?',
            },
            { title: 'Excepteur sint occaecat cupidatat non proident?' },
            {
              title:
                'Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium?',
            },
          ],
        },
        formData: {
          lettre: '',
        },
        cvList: [],
        emailRules: [(v) => validateEmail(v) || true],
        mobileRules: [(v) => validateMobile(v) || true],
        notEmptyRules: [(v) => validateNotEmpty(v) || true],
        noSpecialChar: [(v) => validateNoSpecialChar(v) || true],
        rules: [
          (v) => v.length <= 800 || 'ne doit pas dépasser 800 caractères',
        ],
        showConfirmationModal: false,
      };
    },

    mounted() {
      this.formData = {
        mail: store.getters.getUser.email,
        phone: store.getters.getUser.numberPhone,
        lettre_motivation: store.getters.getUser.lettre_motivation,
        lettre: store.getters.getUser.lettre,
      };
      this.cvList = getCvList();
      this.updateCoverLetter();
    },

    computed: {
      // On mappe les getters Vuex vers les computed properties
      ...mapGetters({
        isLoggedIn: 'isLoggedIn',
        getUser: 'getUser',
      }),
      // Filtrer seulement le CV par défaut
      defaultCv() {
        return this.cvList.filter((cv) => cv.default === true);
      },
    },

    watch: {
      getUser: {
        immediate: true,
      },
    },

    methods: {
      // Ouvrir la boîte de confirmation
      openConfirmationModal() {
        if (!this.hasDefaultCv()) {
          toaster.showErrorPopup(
            'Veuillez enregistrer un CV par défaut dans votre profil avant de postuler.'
          );
          return;
        }
        this.showConfirmationModal = true;
      },

      // Vérifie s'il y a un CV par défaut
      hasDefaultCv() {
        return this.defaultCv.length > 0;
      },

      // Application au poste après confirmation
      async postApplication(jobId) {
        const formData = new FormData();
        formData.append('lettre', this.formData.lettre || '');

        // Ajoutez le fichier lettre de motivation s'il existe
        if (this.formData.lettre_motivation) {
          formData.append('lettre_motivation', this.formData.lettre_motivation);
        }

        try {
          const response = await postApplicationToJob(jobId, formData);
          const currentUserData = this.getUser;
          const updatedUserData = {
            ...currentUserData,
            postulation: [
              ...(currentUserData.postulation || []),
              response.data,
            ],
          };
          store.dispatch('handleUserChange', {
            type: null,
            payload: updatedUserData,
          });
          toaster.showSuccessPopup('Candidature envoyée avec succès.');
          this.closeApplicationModal(true);
        } catch (error) {
          //console.error('Erreur lors de la candidature :', error);
        }
      },

      // Action confirmée par l'utilisateur
      onConfirmApplication() {
        this.showConfirmationModal = false; // Ferme la modal
        this.postApplication(this.joboffer.id); // Appelle la méthode pour envoyer la candidature
      },

      // Émettre un événement de fermeture
      closeApplicationModal(success) {
        this.$emit('close-modal', success);
      },

      //  return cv name
      getCvName(rawString) {
        let array = rawString.split('/');
        let str = array[array.length - 1];
        return str;
      },

      updateCoverLetter() {
        const jobTitle = this.joboffer.title || '[Titre du poste]';
        const jobCompany = this.joboffer.nom_recruteur || 'Thanks Boss';
        this.formData.lettre = `Madame, Monsieur,

            Je vous adresse ma candidature pour le poste de ${jobTitle} chez ${jobCompany}. Grâce à mes expériences antérieures et mes compétences, je suis convaincu(e) de pouvoir contribuer de manière positive et efficace à vos équipes.

            Je reste à votre disposition pour toute information complémentaire et vous prie d'agréer, Madame, Monsieur, mes salutations distinguées.`;
      },

      triggerFileUpload() {
        this.$refs.fileInput.$el.querySelector('input[type="file"]').click();
      },
    },
  };
</script>

<style scoped>
  /***** modal styles *****/
  .modal-content {
    background: var(--surface-bg-2);
    padding: 20px;
    border-radius: 10px;
    width: 60%;
    max-height: fit-content;
    height: 80%;
    max-width: 1200px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(246, 179, 55, 0.2);
  }
  .modal-header,
  .modal-footer {
    flex-shrink: 0;
  }

  .modal-sections {
    flex-grow: 1;
    overflow-y: auto;
  }
  .modal-sections h5 {
    margin: 30px 0;
    height: 50px;
    background-color: rgba(246, 179, 55, 0.2);
    border-radius: 5px;
    padding-left: 30px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .CV-container,
  .file-upload {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid var(--gray-10);
    border-radius: 5px;
  }
  .file-upload {
    margin-top: 10px;
  }
  .file-input :deep(.v-field--variant-filled .v-field__overlay) {
    background-color: transparent !important;
  }
  .file-input :deep(.v-field--variant-filled .v-field__outline::before) {
    border: none;
  }

  .disabled-field-wrapper {
    display: flex;
    align-items: center;
  }
  .eye-icon {
    cursor: pointer;
    margin: 10px;
  }
  .cv-label {
    margin: 16px 0;
    color: var(--text-3);
  }
  .none-defaultCv {
    color: #e74c3c;
    padding: 16px;
  }

  .modal-sections .message {
    padding-top: 30px;
  }

  .modal-footer .btn-wrapper {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-top: 30px;
    border-top: 1px solid rgba(246, 179, 55, 0.2);
  }
  .modal-footer .legal-infos {
    margin: 30px 0;
    color: var(--text-3);
  }

  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Couleur semi-transparente */
    z-index: 999; /* Juste en dessous de la modale */
  }
  .confirmation-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000; /* Assurez-vous que la modale est au-dessus du contenu */
    width: 100%; /* Cela peut être ajusté selon vos besoins */
    max-width: 500px; /* Ajustez selon la taille de votre modale */
    padding: 20px; /* Optionnel, pour ajouter du padding autour de la modale */
    text-align: center;
  }

  @media (max-width: 768px) {
    .modal-content {
      width: 90%;
      height: 90%;
    }
  }
</style>
