<template>
  <section class="alert-panel">
    <div class="content-wrapper">
      <div class="top-content">
        <h2>Nouvelle alerte</h2>

        <v-btn
          @click="$emit('close-alert-panel')"
          class="close-btn size-btn"
          flat
        >
          <img
            src="@\assets\search/search-page-alert-panel-close-icon.svg"
            class="close-img"
          />
        </v-btn>
      </div>
      <!-- name & mail -->
      <div class="input-row">
        <div class="input-field">
          <h5>Nom de l'alerte</h5>
          <v-text-field
            v-model="formData.nom_alerte"
            label="Saisi le nom de ton alerte"
            type="text"
            :rules="notEmptyRules"
            fast-fail
          />
        </div>

        <div class="input-field">
          <h5>E-mail</h5>
          <v-text-field
            v-model="formData.email"
            label="<EMAIL>"
            type="text"
            :rules="[...emailRules, ...notEmptyRules]"
            fast-fail
          />
        </div>
      </div>

      <!-- poste cherche -->
      <div class="input-row">
        <div class="input-field2">
          <h5>Poste</h5>
          <v-text-field
            v-model="formData.poste"
            label="Sélectionne le métier recherché"
            type="text"
            :rules="notEmptyRules"
            fast-fail
          />
        </div>
      </div>

      <!-- Frequency selection -->
      <div class="input-row">
        <div class="input-field">
          <h5>Fréquence</h5>
          <v-select
            v-model="formData.frequency"
            :items="frequencyOptions"
            label="Choisissez la fréquence de l'alerte"
          />
        </div>
      </div>

      <!-- location & remote option -->
      <div class="input-row">
        <div class="input-field">
          <h5>Localisation</h5>
          <!--LocationInput
            @city-and-postal-code="getCityAndPostalCodeValue"
            :textContent="formData.ville"
          /-->
        <v-text-field
          v-model="formData.ville"
          label="Entrez votre ville"
          type="text"
          :textContent="formData.ville"
        />
        </div>

        <div class="input-field">
          <CustomCheckbox
            field="teletravail"
            :fields="remoteOptionList"
            @checkbox-stringarray="getFormDatas"
            multiple
            :cValue="formData.teletravail"
          />
        </div>
      </div>

      <!-- contract type & activity sector -->
      <div class="input-row">
        <div class="input-field">
          <h5>Contrat</h5>
          <div class="subgrid">
            <CustomCheckbox
              field="contrat"
              :fields="contractTypeList"
              @checkbox-stringarray="getFormDatas"
              multiple
              :cValue="formData.contrat"
            />
          </div>
        </div>

        <div class="input-field">
          <h5>Secteur d'activité</h5>
          <v-select
            clearable
            v-model="formData.activity_sector"
            label="Choisis ton secteur d'activité professionnelle"
            :items="activitySectorList"
            multiple
            variant="underlined"
          ></v-select>
        </div>
      </div>

      <!-- experience & anual salary -->
      <div class="input-row">
        <div class="input-field">
          <h5>Expérience</h5>
          <div class="subgrid">
            <CustomRadio
              field="experience"
              :fields="experienceTypeList"
              @radio-selection="getFormDatas"
              :cValue="formData.experience"
            />
          </div>
        </div>

        <div class="input-field salary-bar">
          <h5>Salaire brut annuel en euros</h5>
          <v-tooltip activator="parent" location="start"
            >{{ salaryValue[0] }}€</v-tooltip
          >
          <v-tooltip activator="parent" location="end"
            >{{ salaryValue[1] }}€</v-tooltip
          >
          <v-range-slider
            v-model="formData.salaire"
            :min="0"
            :max="120000"
            :step="1000"
            color="#F6B337"
            thumb-label="true"
            strict
          />
        </div>
      </div>
      <!-- btn cancel & btn create alert -->
      <div v-if="!readOnly" class="btns-row">
        <div class="btns-wrapper">
          <div class="btn-wrapper" @click="$emit('close-alert-panel')">
            <PrimaryNormalButton textContent="Annuler" btnColor="secondary" />
          </div>
          <div class="btn-wrapper">
            <PrimaryNormalButton
              textContent="Créer mon alerte"
              @click="createAlert()"
            />
          </div>
        </div>
      </div>

      <!-- btn delete & modify -->
      <div v-else class="btns-row">
        <div class="btns-wrapper">
          <div class="btn-wrapper" @click="deleteThisAlert()">
            <PrimaryNormalButton textContent="Supprimer" btnColor="secondary" />
          </div>
          <div class="btn-wrapper" @click="modifyAlert()">
            <PrimaryNormalButton textContent="Modifier mon alerte" />
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  import CustomCheckbox from '@/components/buttons/CustomCheckbox.vue';
  import CustomRadio from '@/components/buttons/CustomRadio.vue';
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import LocationInput from '@/components/inputs/LocationInput.vue';
  import {
    deleteProfileAlert,
    modifyProfileAlert,
  } from '@/services/alert-recruiter.service.js';

  import { ACTIVITY_FIELDS } from '@/utils/base/activity_sector.js';
  import { CONTRACT_FIELDS } from '@/utils/base/contract.js';
  import { EXPERIENCE_FIELDS } from '@/utils/base/experience.js';
  import { PROFESSION_FIELDS } from '@/utils/base/profession.js';
  import { TELETRAVAIL_FIELDS } from '@/utils/base/teletravail.js';
  import gotoPage from '@/utils/router';
  import { validateEmail, validateNotEmpty } from '@/utils/validationRules';
  import { mapGetters } from 'vuex';
  import { createNewProfileAlert } from '@/services/alert-recruiter.service';

  export default {
    name: 'CreationAlertProfiles',

    components: {
      CustomCheckbox,
      CustomRadio,
      PrimaryNormalButton,
      LocationInput,
    },

    props: {
      alert: {
        type: Object,
        default: null,
      },

      //  alert index in the list
      alertIndex: {
        type: Number,
      },
    },

    data() {
      return {
        formData: {
          ville: '',
          teletravail: [], // Initialisation par défaut avec un tableau vide
          contrat: [], // Initialisation par défaut avec un tableau vide
          poste: [], // Initialisation par défaut avec un tableau vide
          activity_sector: [],
          experience: '',
          salaire: [0, 120000], // Initialisation par défaut pour le slider de salaire
          frequency: 'Jamais', // Changement ici
        },
        frequencyOptions: [
          'Jamais',
          'Tous les jours',
          'Tous les 7 jours',
          'Tous les mois',
        ],
        activitySectorList: [], //  list of select for activity sector input
        contractTypeList: [], //  list of select for contract input
        remoteOptionList: [], //  list of select for remote option input
        workTypeList: [], //  list of select for work type input
        experienceTypeList: [], //  list of select for experience type input
        salaryValue: [0, 120000], //  value of salary field
        skillList: [], //  list of skill
        softSkillList: [], //  list of soft skill
        readOnly: false, //  toggle read only
        notEmptyRules: [(v) => validateNotEmpty(v) || false],
        emailRules: [(v) => validateEmail(v) || true],
      };
    },

    computed: {
      ...mapGetters(['getUser']),
    },
    mounted() {
      this.formData.email = this.getUser.email;
    },
    beforeMount() {
      //  get all select input list
      this.activitySectorList = this.generateList(ACTIVITY_FIELDS, 'nom');
      this.contractTypeList = this.generateList(CONTRACT_FIELDS, 'contrat');
      this.remoteOptionList = this.generateList(
        TELETRAVAIL_FIELDS,
        'teletravail'
      );
      this.workTypeList = this.generateList(PROFESSION_FIELDS, 'nom');
      this.experienceTypeList = this.generateList(
        EXPERIENCE_FIELDS,
        'experience_job'
      );
      //  if an id is passed as props, hydrate alert with values
      if (!this.alert) return;
      this.readOnly = true;

      //  get value from alert object to form data to hydrate html
      this.formData = {
        nom_alerte: this.alert.nom_alerte, //  string
        email: this.alert.email, //  string
        poste: this.alert.poste, //  string
        ville: this.alert.ville, //  string
        teletravail: this.alert.teletravail?.split(',') || [], // Asegúrate de convertir en array
        contrat: this.alert.contrat?.split(',') || [], // Asegúrate de convertir en array
        activity_sector: this.alert.activity_sector?.split(',') || [], //  list
        experience: this.alert.experience, //  string
        salaire: this.alert.salaire?.split(',') || [], //  list
        frequency: this.alert.frequency || 'Jamais', // Mise à jour ici
      };
    },

    methods: {
      gotoPage,
      //  bind list to variable for input select options
      generateList(objectList, fieldName) {
        let list = [];
        for (let i = 0; i < objectList.length; i++) {
          list.push(objectList[i][fieldName]);
        }
        return list;
      },

      //  get datas from alert fields and hook them to formData
      getFormDatas(field, datas) {
        this.formData[field] = datas;
      },
      async createAlert() {
        const alertaData = {
          ...this.formData,
          activity_sector: this.formData.activity_sector.join(','),
          salaire: this.formData.salaire.join(','),
          contrat: this.formData.contrat.join(','),
          teletravail: this.formData.teletravail.join(','),
        };

        try {
          const response = await createNewProfileAlert(alertaData);
          if (response.statusText === 'Created') {
            // Actualiser le user dans le store
            const updatedUser = {
              ...this.getUser,
              alerte_profils: [...this.getUser.alerte_profils, response.data],
            };
            this.$store.dispatch('handleUserChange', {
              type: null,
              payload: updatedUser,
            });
          }
          this.$emit('close-alert-panel');
        } catch (error) {
          //console.error("Erreur lors de la création de l'alerte :", error);
        }
      },
      //  delete alert
      async deleteThisAlert() {
        await deleteProfileAlert(this.alert.id);
        // alctualiser le store de l'utilisateur
        const updatedUser = {
          ...this.getUser,
          alerte_profils: this.getUser.alerte_profils.filter(
            (alert) => alert.id !== this.alert.id
          ),
        };
        this.$store.dispatch('handleUserChange', {
          type: null,
          payload: updatedUser,
        });
        this.$emit('close-alert-panel');
      },

      //  modify alert
      async modifyAlert() {
        // parser les array du formulaire
        const alertData = {
          ...this.formData,
          activity_sector: this.formData.activity_sector.join(','),
          salaire: this.formData.salaire.join(','),
          contrat: this.formData.contrat.join(','),
          teletravail: this.formData.teletravail.join(','),
        };
        //console.log({ alertData });
        modifyProfileAlert(this.alert.id, alertData);
        //  update user alert
        const updatedUser = {
          ...this.getUser,
          alerte_profils: this.getUser.alerte_profils.map((alert) => {
            if (alert.id === this.alert.id) {
              return {
                ...alert,
                ...alertData,
              };
            }
            return alert;
          }),
        };
        this.$store.dispatch('handleUserChange', {
          type: null,
          payload: updatedUser,
        });
        this.$emit('close-alert-panel');
      },

      //  get city and postal code value and bind them to formData
      getCityAndPostalCodeValue(cityAndPostalCode) {
        if (cityAndPostalCode) {
          this.formData['ville'] = cityAndPostalCode[0];
          this.formData['postalCode'] = cityAndPostalCode[1];
        }
      },
    },
  };
</script>

<style scoped>
  h5 {
    margin-bottom: 7px;
  }
  .size-btn {
    padding: 0 !important;
    min-width: 4px !important;
  }

  .alert-panel {
    background-color: var(--surface-bg-2);
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-block: 50px;
    border-radius: 5px;
    margin-block: 50px;
  }

  .content-wrapper {
    width: 80%;
  }

  .top-content {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2rem;
  }

  .close-btn {
    position: absolute;
    left: 78%;
    background-color: var(--black-100);
  }

  .close-img {
    width: 31px;
    height: 23px;
  }

  .input-row {
    display: flex;
    flex-direction: column;
    width: 100%;
    justify-content: space-between;
    margin-bottom: 30px;
  }

  .input-field {
    width: 100%;
  }

  .input-field2 {
    width: 100%;
  }

  .subgrid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 40px;
    margin-bottom: 30px;
  }

  .btns-row {
    display: flex;
    justify-content: center;
  }

  .btns-wrapper {
    display: flex;
    width: 100%;
    justify-content: space-between;
  }

  .salary-bar {
    height: 100px;
  }

  @media screen and (min-width: 992px) {
    .input-row {
      flex-direction: row;
    }

    .input-field {
      width: 48%;
    }

    .btns-wrapper {
      width: 48%;
    }
  }
</style>
