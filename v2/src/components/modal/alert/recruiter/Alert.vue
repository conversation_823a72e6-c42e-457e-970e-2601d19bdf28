<template>
  <section class="alert-panel">
    <div class="content-wrapper">
      <div class="top-content">
        <h2>Mon alerte</h2>

        <v-btn
          @click="$emit('close-alert-panel')"
          class="close-btn size-btn"
          flat
        >
          <img
            src="@\assets\search/search-page-alert-panel-close-icon.svg"
            class="close-img"
          />
        </v-btn>
      </div>
      <!-- name & mail -->
      <div class="input-row">
        <div class="input-field">
          <h5>Nom de l'alerte</h5>
          <v-text-field
            v-model="formData.alertName"
            label="Saisi le nom de ton alerte"
            type="text"
            :rules="notEmptyRules"
            fast-fail
          />
        </div>

        <div class="input-field">
          <h5>E-mail</h5>
          <v-text-field
            v-model="formData.mail"
            label="<EMAIL>"
            type="text"
            :rules="[...emailRules, ...notEmptyRules]"
            fast-fail
          />
        </div>
      </div>

      <!-- job title -->
      <div class="input-row">
        <div class="input-field2">
          <h5>Poste</h5>
          <v-text-field
            v-model="formData.title"
            label="Sélectionne le métier recherché"
            type="text"
            :rules="noSpecialChar"
            fast-fail
          />
        </div>
      </div>

      <!-- location & remote option -->
      <div class="input-row">
        <div class="input-field">
          <h5>Localisation</h5>
          <!--LocationInput
            @city-and-postal-code="getCityAndPostalCodeValue"
            :textContent="formData.city"
          /-->
          <v-text-field
            v-model="formData.ville"
            label="Entrez votre ville"
            type="text"
            :textContent="formData.ville"
          />

          <v-tooltip activator="parent" location="start"
            >{{ radius }}km</v-tooltip
          >
          <v-slider
            v-model="formData.radius"
            :min="0"
            :max="50"
            :step="5"
            color="#F6B337"
            thumb-label
            strict
          />
        </div>

        <div class="input-field">
          <h5>Type de travail</h5>
          <CustomCheckbox
            field="remote"
            :fields="remoteOptionList"
            @checkbox-stringarray="getFormDatas"
            multiple
            :cValue="formData.remote"
          />
        </div>
      </div>

      <!-- contract type & activity sector -->
      <div class="input-row">
        <div class="input-field">
          <h5>Contrat</h5>
          <div class="subgrid">
            <CustomCheckbox
              field="contract"
              :fields="contractTypeList"
              @checkbox-stringarray="getFormDatas"
              multiple
              :cValue="formData.contract"
            />
          </div>
        </div>

        <div class="input-field">
          <h5>Secteur d'activité</h5>
          <v-select
            clearable
            v-model="formData.sector"
            label="Choisis ton secteur d'activité professionnelle"
            :items="activitySectorList"
            multiple
            variant="underlined"
          ></v-select>
        </div>
      </div>

      <!-- experience & anual salary -->
      <div class="input-row">
        <div class="input-field">
          <h5>Expérience</h5>
          <div class="subgrid">
            <CustomRadio
              field="experience"
              :fields="experienceTypeList"
              @radio-selection="getFormDatas"
              :cValue="formData.experience"
            />
          </div>
        </div>

        <div class="input-field salary-bar">
          <h5>Salaire brut annuel en euros</h5>
          <v-tooltip activator="parent" location="start"
            >{{ salaryValue[0] }}€</v-tooltip
          >
          <v-tooltip activator="parent" location="end"
            >{{ salaryValue[1] }}€</v-tooltip
          >
          <v-range-slider
            v-model="formData.salaryValue"
            :min="0"
            :max="120000"
            :step="1000"
            color="#F6B337"
            thumb-label="true"
            strict
            v-bind="props"
          />
        </div>
      </div>

      <!-- soft skills -->
      <!-- <div class="input-row">

                <div class="input-field2">
                    <h5>Savoirs-faire</h5>
                    <v-select
                        clearable
                        v-model="formData.skills"
                        label="Sélectionne tes savoir-faire"
                        :items="softSkillList"
                        multiple
                    ></v-select>
                </div>

            </div>

            <div class="input-row">

                <div class="input-field2">
                    <h5>Savoirs-être</h5>
                    <v-select
                        clearable
                        v-model="formData.softSkills"
                        label="Sélectionne tes savoir-être"
                        :items="skillList"
                        multiple
                    ></v-select>
                </div>

            </div> -->

      <!-- btn cancel & btn create alert -->
      <div v-if="!readOnly" class="btns-row">
        <div class="btns-wrapper">
          <div class="btn-wrapper" @click="$emit('close-alert-panel')">
            <PrimaryNormalButton textContent="Annuler" btnColor="secondary" />
          </div>
          <div class="btn-wrapper" @click="createAlert()">
            <PrimaryNormalButton textContent="Créer mon alerte" />
          </div>
        </div>
      </div>

      <!-- btn delete & modify -->
      <div v-else class="btns-row">
        <div class="btns-wrapper">
          <div class="btn-wrapper" @click="deleteThisJobAlert()">
            <PrimaryNormalButton textContent="Supprimer" btnColor="secondary" />
          </div>
          <div class="btn-wrapper" @click="modifyAlert()">
            <PrimaryNormalButton textContent="Modifier mon alerte" />
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  import CustomCheckbox from '@/components/buttons/CustomCheckbox.vue';
  import CustomRadio from '@/components/buttons/CustomRadio.vue';
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import LocationInput from '@/components/inputs/LocationInput.vue';
  import {
    createNewJobAlert,
    deleteJobAlert,
    getAlertById,
    getSkillList,
    getSoftSkillList,
    modifyJobAlert,
  } from '@/services/alert.service.js';
  import { ACTIVITY_FIELDS } from '@/utils/base/activity_sector.js';
  import { CONTRACT_FIELDS } from '@/utils/base/contract.js';
  import { EXPERIENCE_FIELDS } from '@/utils/base/experience.js';
  import { PROFESSION_FIELDS } from '@/utils/base/profession.js';
  import { TELETRAVAIL_FIELDS } from '@/utils/base/teletravail.js';
  import {
    validateEmail,
    validateNoSpecialChar,
    validateNotEmpty,
  } from '../../../../utils/validationRules';

  export default {
    name: 'AlertModal',

    components: {
      CustomCheckbox,
      CustomRadio,
      PrimaryNormalButton,
      LocationInput,
    },

    props: {
      alertId: {
        type: String,
        default: null,
      },

      //  alert index in the list
      alertIndex: {
        type: Number,
      },
    },

    data() {
      return {
        formData: {}, //  datas
        activitySectorList: [], //  list of select for activity sector input
        contractTypeList: [], //  list of select for contract input
        remoteOptionList: [], //  list of select for remote option input
        workTypeList: [], //  list of select for work type input
        experienceTypeList: [], //  list of select for experience type input
        radius: 0, //  value of location field
        salaryValue: [0, 120000], //  value of salary field
        alert: null, //  alert object if an alert id is passed as props
        skillList: [], //  list of skill
        softSkillList: [], //  list of soft skill
        readOnly: false, //  toggle read only
        noSpecialChar: [(v) => validateNoSpecialChar(v) || true],
        notEmptyRules: [(v) => validateNotEmpty(v) || true],
        emailRules: [(v) => validateEmail(v) || true],
      };
    },

    beforeMount() {
      //  if an id is passed as props, hydrate alert with values
      if (this.alertId && typeof this.alertId == 'number') {
        this.alert = getAlertById(this.alertId);
        if (!this.alert) return;
        //console.log('l-263', this.alert);
        this.readOnly = true;

        //  format string as list
        let teletravail, contrat, activite, salaire;
        //console.log('THIS LAERT ', this.alert.salaire, this.alert.radius);
        if (this.alert.teletravail != null)
          teletravail = this.alert.teletravail.split(',');
        if (this.alert.contrat != null) contrat = this.alert.contrat.split(',');
        if (this.alert.activite != null)
          activite = this.alert.activite.split(',');
        if (this.alert.salaire != null) salaire = this.alert.salaire.split(',');
        if (this.alert.radius != null) this.alert.radius = this.alert.radius;

        //  get value from alert object to form data to hydrate html
        this.formData = {
          alertName: this.alert.nom, //  string
          mail: this.alert.email, //  string
          title: this.alert.quoi, //  string
          city: this.alert.ville, //  string
          radius: this.alert.radius,
          remote: teletravail, //  list
          contract: contrat, //  list
          sector: activite, //  list
          experience: this.alert.experience, //  string
          salaryValue: salaire, //  list
          //   "skills": this.alert.savoir_pro,            //  list
          //  "softSkills": this.alert.savoir,            //  list
        };
      }

      //  get skill and soft skill lists
      // this.getSkillsLists();
      //  get all select input list
      this.activitySectorList = this.generateList(ACTIVITY_FIELDS, 'nom');
      this.contractTypeList = this.generateList(CONTRACT_FIELDS, 'contrat');
      this.remoteOptionList = this.generateList(
        TELETRAVAIL_FIELDS,
        'teletravail'
      );
      this.workTypeList = this.generateList(PROFESSION_FIELDS, 'nom');
      this.experienceTypeList = this.generateList(
        EXPERIENCE_FIELDS,
        'experience_job'
      );
    },

    methods: {
      //  bind skills lists
      async getSkillsLists() {
        this.skillList = await getSkillList();
        this.softSkillList = await getSoftSkillList();
        //console.log(this.skillList);
        //console.log(this.softSkillList);
      },

      //  bind list to variable for input select options
      generateList(objectList, fieldName) {
        let list = [];
        for (let i = 0; i < objectList.length; i++) {
          list.push(objectList[i][fieldName]);
        }
        return list;
      },

      //  get datas from alert fields and hook them to formData
      getFormDatas(field, datas) {
        this.formData[field] = datas;
      },

      //  create alert
      async createAlert() {
        const creationSuccessfull = await createNewJobAlert(
          this.formData,
          this.alertIndex
        );
        this.$emit('close-alert-panel');
      },

      //  delete alert
      async deleteThisJobAlert() {
        const deletionSuccessfull = await deleteJobAlert(
          this.alertId,
          this.alertIndex
        );
        this.$emit('close-alert-panel');
      },

      //  modify alert
      async modifyAlert() {
        const modificationSuccessfull = await modifyJobAlert(
          this.alertId,
          this.formData,
          this.alertIndex
        );
        if (modificationSuccessfull) this.$emit('close-alert-panel');
      },

      //  get city and postal code value and bind them to formData
      getCityAndPostalCodeValue(cityAndPostalCode) {
        //console.log(cityAndPostalCode);
        if (cityAndPostalCode) {
          this.formData['city'] = cityAndPostalCode[0];
          this.formData['postalCode'] = cityAndPostalCode[1];
        }
      },
    },
  };
</script>

<style scoped>
  h5 {
    margin-bottom: 7px;
  }
  .size-btn {
    padding: 0 !important;
    min-width: 4px !important;
  }

  .alert-panel {
    background-color: var(--surface-bg-2);
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-block: 50px;
    border-radius: 5px;
    margin-block: 50px;
  }

  .content-wrapper {
    width: 80%;
  }

  .top-content {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2rem;
  }

  .close-btn {
    position: relative;
    left: 45%;
    background-color: var(--black-100);
  }

  .close-img {
    width: 31px;
    height: 23px;
  }

  .input-row {
    display: flex;
    flex-direction: column;
    width: 100%;
    justify-content: space-between;
    margin-bottom: 30px;
  }

  .input-field {
    width: 100%;
  }

  .input-field2 {
    width: 100%;
  }

  .subgrid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 40px;
    margin-bottom: 30px;
  }

  .btns-row {
    display: flex;
    justify-content: center;
  }

  .btns-wrapper {
    display: flex;
    width: 100%;
    justify-content: space-between;
  }

  .salary-bar {
    height: 100px;
  }

  @media screen and (min-width: 992px) {
    .input-row {
      flex-direction: row;
    }

    .input-field {
      width: 48%;
    }

    .btns-wrapper {
      width: 48%;
    }
  }
</style>
