<template>
  <v-dialog
    :model-value="show"
    @update:modelValue="$emit('update:show', $event)"
    persistent
    max-width="900px"
    min-width="300px"
  >
    <v-card>
      <v-card-title>Amélioration de la description</v-card-title>
      <v-card-text>
        <!-- Afficher le loader pendant le chargement -->
        <div v-if="isLoading" class="loading-container">
          <v-progress-circular
            :size="50"
            :width="5"
            color="primary"
            indeterminate
            class="spinner-container"
          ></v-progress-circular>
          <p>L'IA travaille sur l'amélioration de votre description...</p>
        </div>

        <!-- Afficher le contenu seulement après le chargement -->
        <div v-else>
          <div class="ai-description" v-html="formattedMessage"></div>
        </div>
      </v-card-text>
      <div class="buttons">
        <PrimaryRoundedButton
          textContent="Retour"
          btnColor="secondary"
          @click="cancel"
        />
        <PrimaryRoundedButton
          textContent="Confirmer changement"
          @click="confirm"
        />
      </div>
    </v-card>
  </v-dialog>
</template>

<script>
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';

  export default {
    name: 'AiImprovementModal',

    components: {
      PrimaryRoundedButton,
    },

    props: ['show', 'isLoading', 'aiDescription'],
    emits: ['update:show', 'confirm'],

    computed: {
      formattedMessage() {
        if (!this.aiDescription) return '';

        let formattedText = this.aiDescription;

        // 🔹 Convertir "#### Sous-Titre" en <h4>
        formattedText = formattedText.replace(/####\s(.*)/g, (match, p1) => {
          return `<h4 style="text-decoration: underline; margin: 20px 0 10px 0;">${p1}</h4>`;
        });

        // 🔹 Convertir "### Titre" en <h3>
        formattedText = formattedText.replace(/###\s(.*)/g, (match, p1) => {
          return `<h3 style="font-weight: bold;">${p1}</h3>`;
        });

        // 🔹 Convertir "**Texte en gras**" en <b>
        formattedText = formattedText.replace(/\*\*(.*?)\*\*/g, (match, p1) => {
          return `<b>${p1}</b>`;
        });

        // 🔹 Convertir les numéros "1." en pastilles alignées
        formattedText = formattedText.replace(
          /(\d+)\.\s(.+)/g,
          (match, p1, p2) => {
            return `<div style="display: flex; align-items: center; margin: 10px 0;">
            <span style=" display: flex; justify-content: center; align-items: center; font-weight: bold; font-size: 16px; color: #f7bc53; width: 25px; height: 25px; border: 2px solid #f7bc53; border-radius: 50%; flex-shrink: 0; text-align: center; line-height: 30px;">${p1}</span>
            <span style="margin-left: 10px;">${p2}</span>
          </div>`;
          }
        );

        // 🔹 Convertir les listes non numérotées "- Texte" en puces "• Texte"
        formattedText = formattedText.replace(/^\s*-\s(.+)/gm, (match, p1) => {
          return `<div style="display: flex; align-items: flex-start; margin-left: 20px; margin-top: 10px;">
            <span style="font-size: 18px; color: black; margin-right: 10px;">•</span>
            <span>${p1}</span>
          </div>`;
        });

        // 🔹 Convertir les liens email Markdown [texte](mailto:email) en liens HTML
        formattedText = formattedText.replace(
          /\[([^\]]+)\]\(mailto:([^)]+)\)/g,
          (match, p1, p2) => {
            return `<a href="mailto:${p2}" style="color: #007bff; text-decoration: underline;">${p1}</a>`;
          }
        );

        // 🔹 Supprimer les <br> entre les <div> (évite les sauts de ligne entre éléments de liste)
        formattedText = formattedText.replace(
          /<\/div>\s*<br>\s*<div/g,
          '</div><div>'
        );

        // 🔹 Supprimer les <br> qui suivent immédiatement un <div> (évite les espaces vides)
        formattedText = formattedText.replace(/<div[^>]*>\s*<br>/g, '<div>');

        // 🔹 Convertir les retours à la ligne (\n) en <br>, sauf après <h4> et entre les <div>
        formattedText = formattedText.replace(/\n(?!<\/h4>|<\/div>)/g, '<br>');

        // 🔹 Nettoyer les <br> après <h4> pour éviter un saut de ligne supplémentaire
        formattedText = formattedText.replace(/<\/h4><br>/g, '</h4>');

        return formattedText;
      },
    },

    methods: {
      cancel() {
        this.$emit('update:show', false);
      },
      confirm() {
        this.$emit('confirm', this.aiDescription);
      },
    },
  };
</script>

<style scoped>
  .buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 20px;
  }

  .loading-container {
    height: 150px;
  }
  .loading-container p {
    text-align: center;
    margin-top: 20px;
  }
  .spinner-container {
    display: flex;
    flex-direction: column;
    margin: 0 auto;
  }

  .ai-description {
    background-color: rgb(240, 240, 240);
    padding: 20px 40px;
    height: 500px;
    overflow-y: scroll;
    text-align: left;
  }
</style>
