<template>
  <section class="chat-window">
    <!-- Cha<PERSON> -->
    <header class="chat-header" v-if="receiver && receiver.id">
      <div class="chat-header-info">
        <UserAvatar width="50" :user="receiver" />
        <p class="receiver-name">
          {{ receiver?.first_name || 'Sélectionner un destinataire' }}
        </p>
      </div>
      <div class="chat-header-actions">
        <button
          @click="startVideoCall"
          class="chat-action"
          aria-label="Commencer l'appel vidéo"
        >
          <img
            src="@/assets/viseo.svg"
            alt="Commencer l'appel vidéo"
          />
        </button>
        <button class="chat-action" aria-label="Commencer l'appel" @click="startVoiceCall">
          <img src="@/assets/call.svg" alt="Icone d'appel" />
        </button>
        <button @click="onClick" class="chat-action" aria-label="Appel actif">
          <img src="@/assets/icons/eye-border-orange.svg" alt="Voir le profil" />
        </button>
        <!-- button class="chat-action" aria-label="More Options">
          <img src="@/assets/dots-black.svg" alt="More Options Icon" />
        </button -->
      </div>
      <button
        v-if="isInLayout"
        class="toggle-chat"
        aria-label="Activer la visibilité du tchat"
      >
        <i
          @click="toggleChatOpen"
          :class="
            isChatWindowOpen ? 'fas fa-chevron-down' : 'fas fa-chevron-up'
          "
        ></i>
        <i @click="closeChatWindow" class="fas fa-times"></i>
      </button>

    </header>

    <header v-else class="chat-header">
      <div class="chat-header-info">
        <UserAvatar width="50" :user="receiver" />
        <p class="receiver-name">
          {{ receiver?.first_name || 'Sélectionner un destinataire' }}
        </p>
      </div>
    </header>


    <!-- Chat Messages -->
    <main v-if="isChatWindowOpen" class="chat-messages" ref="messagesContainer" >
      <div
        v-for="(message, index) in messages"
        :key="index"
        :class="[
          'chat-message',
          message.isCurrentUser ? 'sent' : 'received',
        ]"
      >
        <span class="message-info">
          {{ message.sender_name }} - {{ message.time }} {{ message.date }}
        </span>

        <!-- Afficher une image si le message est une image -->
        <template v-if="message.isImage">
          <img :src="message.message" class="message-image" alt="Image envoyée" />
          <br />
        </template>
        <template v-else>
          <p
            class="message-text"
            :style="{ fontFamily: message.fontFamily || 'inherit' }"
            v-html="formatMessageWithLinks(message.message)"
          ></p>


        </template>


      </div>
    </main>

    <!-- Chat Input -->
    <footer v-if="isChatWindowOpen && receiver.id" class="chat-input-section">
      <div class="textarea-container">
        <textarea
          v-model="messageInput"
          @keyup.enter="sendMessage"
          placeholder="Saisissez votre message"
          aria-label="Write your message"
          :style="{ fontFamily: selectedFont }"
        ></textarea>
      </div>

      <input
        type="file"
        ref="fileInput"
        style="display: none"
        @change="handleFileUpload"
      />

      <div class="input-actions">
        <div class="input-icons">
          <button
            v-for="icon in inputIcons"
            :key="icon.alt"
            class="input-icon"
            @click="icon.action"
            :aria-label="icon.alt"
          >
            <img :src="icon.src" :alt="icon.alt" />
          </button>
        </div>

        <emoji-picker
          v-if="showEmojiPicker"
          @emoji-click="addEmoji"
          class="emoji-popup"
        />

        <div v-if="showFontSelector" class="font-selector">
        <p>Choisissez une police :</p>
        <ul>
          <li
            v-for="font in availableFonts"
            :key="font"
            :style="{ fontFamily: font, cursor: 'pointer' }"
            @click="selectFont(font)"
          >
            {{ font }}
          </li>
        </ul>
      </div>


        <button
          class="send-button"
          @click="sendMessage"
          aria-label="Send Message"
        >
          <img
            src="@/assets/icons/icon-send-msg-primary1.svg"
            alt="Send Icon"
          />
        </button>

        <div v-if="showImagePreviewModal" class="image-preview-modal">
          <div class="image-preview-content">
            <p>Envoyer cette image ?</p>
            <img :src="previewImage" alt="Aperçu" class="preview-img" />
            <div class="preview-buttons">
              <button @click="confirmSendImage">Envoyer</button>
              <button @click="cancelSendImage">Annuler</button>
            </div>
          </div>
        </div>

      </div>
    </footer>
  </section>
</template>
<script>
  import axios from 'axios';
  import { axiosInstance } from '../../services/axios';
  import { initializeChatWebSocket } from '../../utils/webSocketUtils';
  import { getConversationHistory } from '../../services/conversation.service';
  import UserAvatar from '@/components/views-models/profil/UserAvatar.vue';
  import { addUnreadMessage, markUserMessagesAsRead } from '@/services/unread-messages.service';
  import { initNotificationSound, playNotificationSound } from '@/services/notification.service';
  import 'emoji-picker-element';
  export default {
    props: {
      receiver: {
        type: Object,
        required: true,
      },
      isInLayout: {
        type: Boolean,
        required: false,
        default: false,
      },
      isOpen: {
        type: Boolean,
        required: false,
        default: true,
      },
      lastConversation: {
        type: Object,
        required: false, // Peut être null si aucune conversation n'est sélectionnée
      },
    },
    components: {
      UserAvatar,
    },
    data() {
      return {
        sender: this.$store.getters.getUser,
        isChatWindowOpen: this.isOpen,
        messages: [],
        chatMessages: {},
        unreadMessages: {},
        previousMessages: [],
        conversations: [],
        messageInput: '',
        currentId: null,
        recipientId: null,
        // Événement personnalisé pour les notifications
        notificationEvent: new CustomEvent('new-message'),
        showEmojiPicker: false,
        previewImage: null,
        showImagePreviewModal: false,
        pendingFile: null,
        selectedFont: 'inherit',
        showFontSelector: false,
        availableFonts: ['inherit', 'Arial', 'Courier New', 'Georgia', 'Times New Roman', 'Verdana', 'Comic Sans MS'],
      };
    },
    // Les watchers sont définis plus bas dans le composant
    methods: {
      formatMessageWithLinks(text) {
        const urlRegex = /((https?:\/\/)?([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})(\/\S*)?)/g;
        return text.replace(urlRegex, (match) => {
          let url = match;
          if (!url.startsWith('http://') && !url.startsWith('https://')) {
            url = 'https://' + url;
          }
          return `<a href="${url}" target="_blank" rel="noopener noreferrer">${match}</a>`;
        });
      },

      insertLink() {
        let url = prompt("Entrez l'URL du lien :");
        if (url) {
          if (!url.startsWith('http://') && !url.startsWith('https://')) {
            url = 'https://' + url;
          }
          const link = `<a href="${url}" target="_blank" rel="noopener noreferrer">${url}</a>`;
          this.messageInput += ' ' + link;
        }
      },

      toggleFontSelector() {
        this.showFontSelector = !this.showFontSelector;
      },
      selectFont(font) {
        this.selectedFont = font;
        this.showFontSelector = false;
      },

      async handleFileUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();

        reader.onload = (e) => {
          const base64 = e.target.result;
          const isImage = file.type.startsWith('image/');

          if (isImage) {
            // Affiche la modale de prévisualisation
            this.previewImage = base64;
            this.pendingFile = {
              base64,
              file,
            };
            this.showImagePreviewModal = true;
          } else {
            // Si ce n’est pas une image, confirmation simple
            const confirmSend = window.confirm(`Voulez-vous envoyer le fichier "${file.name}" ?`);
            if (confirmSend) {
              this.sendFileAsMessage(base64, file);
            }
          }

          // Réinitialise l’input pour permettre un upload répété du même fichier
          event.target.value = '';
        };

        reader.readAsDataURL(file);
      },
      addEmoji(event) {
        this.messageInput += event.detail.unicode;
      },
      toggleEmojiPicker() {
        this.showEmojiPicker = !this.showEmojiPicker;
      },
      scrollToBottom() {
        this.$nextTick(() => {
          const container = this.$refs.messagesContainer;
          if (container) {
            container.scrollTop = container.scrollHeight;
          }
        });
      },
      onClick() {
        const id1 = this.sender.id;
        const idRecipient = this.receiver.id;
        //console.log('currentId:', id1, 'recipientId:', idRecipient);
        if (id1 && idRecipient) {
          this.$router.push(
            idRecipient === id1
              ? `/utilisateur/${id1}`
              : `/utilisateur/${idRecipient}`
          );
        } else {
          //console.error('Erreur : Les ID ne sont pas valides.');
        }
      },
      // Récupère l'historique des messages via API REST
      async fetchConversations(id1, id2) {
        try {
          //console.log('Récupération des messages via API REST initiée');

          if (!id1 || !id2) {
            //console.error('IDs manquants pour récupérer l\'historique des messages');
            return;
          }

          // Vider la liste des messages actuels pour éviter les doublons
          this.messages = [];

          // Récupérer l'historique des messages via l'API REST
          const messages = await getConversationHistory(id1, id2);
          //console.log('Messages récupérés via API REST:', messages);

          // Formater les messages pour l'affichage
          const formattedMessages = messages.map(msg => {
            // Vérifier si le message a le format attendu
            if (!msg.sender || !msg.receiver) {
              //console.error('Format de message invalide:', msg);
              return null;
            }

            return {
              id: msg.id,
              sender_id: msg.sender.id,
              sender_name: `${msg.sender.first_name} ${msg.sender.last_name}`,
              receiver_id: msg.receiver.id,
              receiver_name: `${msg.receiver.first_name} ${msg.receiver.last_name}`,
              message: msg.message,
              time: this.formatTime(msg.time),
              date: this.formatDate(msg.date),
              read: msg.read || false,
              // Comparer les IDs en tant que chaînes de caractères
              isCurrentUser: String(msg.sender.id) === String(this.sender.id),
              isImage: msg.message && msg.message.startsWith('data:image/')
            };
          }).filter(msg => msg !== null); // Filtrer les messages invalides

          // Mettre à jour la liste des messages
          this.messages = formattedMessages;

          // Faire défiler vers le bas pour afficher les nouveaux messages
          this.$nextTick(() => {
            this.scrollToBottom();
          });

          // Mettre à jour le compteur de messages non lus
          formattedMessages.forEach(message => {
            if (!message.read && !message.isCurrentUser) {
              const senderId = message.sender_id || (message.sender_name !== `${this.sender.first_name} ${this.sender.last_name}` ? this.receiver.id : null);
              if (senderId) {
                if (!this.unreadMessages[senderId]) {
                  this.unreadMessages[senderId] = 1;
                } else {
                  this.unreadMessages[senderId]++;
                }
              }
            }
          });
        } catch (error) {
          //console.error('Erreur lors de la récupération des messages via API REST:', error);
        }
      },
      areMessagesEqual(newMessages, oldMessages) {
        if (newMessages.length !== oldMessages.length) return false;

        // Créer une copie des messages locaux pour pouvoir les marquer comme traités
        const localMessages = [...oldMessages].filter(msg => msg.is_local);

        // Pour chaque nouveau message, vérifier s'il correspond à un message local
        for (let i = 0; i < newMessages.length; i++) {
          const newMsg = newMessages[i];

          // Vérifier d'abord si les messages à la même position sont identiques
          if (newMsg.message === oldMessages[i].message) {
            continue; // Les messages sont identiques, passer au suivant
          }

          // Sinon, chercher un message local correspondant
          const localMsgIndex = localMessages.findIndex(msg =>
            msg.message === newMsg.message &&
            (msg.temp_id === newMsg.temp_id || !newMsg.temp_id)
          );

          // Si aucun message local correspondant n'est trouvé, les listes sont différentes
          if (localMsgIndex === -1) {
            return false;
          }

          // Marquer le message local comme traité en le supprimant de la liste
          localMessages.splice(localMsgIndex, 1);
        }

        // Si tous les messages ont été traités, les listes sont équivalentes
        return localMessages.length === 0;
      },
      async markAsRead(message) {
        try {
          if (this.chatSocket && this.chatSocket.readyState === WebSocket.OPEN) {
            // Envoyer une demande de marquage comme lu via WebSocket
            const markAsReadRequest = {
              type: 'mark_as_read',
              sender_id: this.sender.id,
              recipient_id: this.receiver.id,
              id: this.receiver.id // Pour le routage dans le WebSocket global
            };

            this.chatSocket.send(JSON.stringify(markAsReadRequest));
            //console.log('Demande de marquage comme lu envoyée:', markAsReadRequest);

            // Mettre à jour localement
            const msg = this.messages.find((m) => m === message);
            if (msg) {
              msg.read = true;

              // Utiliser le service de messages non lus pour marquer le message comme lu
              if (message.sender_id) {
                markUserMessagesAsRead(message.sender_id);
              }

              delete this.unreadMessages[message.sender_name];
            }
          }
        } catch (error) {
          //console.error(
          //  'Erreur lors de la mise à jour du statut du message :',
          //  error
          //);
        }
      },
      initializeChatWebSocketConnection(senderId, receiverId) {
        // Vérifier que les IDs sont bien définis
        if (!senderId || !receiverId) {
          //console.error('Erreur: IDs d\'expéditeur ou de destinataire non définis', { senderId, receiverId });
          return;
        }

        //console.log('Initialisation du WebSocket privé pour l\'utilisateur:', senderId);

        // Importer dynamiquement le service WebSocket pour éviter les dépendances circulaires
        import('../../services/conversation-websocket.service').then(({ initializePrivateWebSocket }) => {
          // Initialisation du WebSocket privé pour l'utilisateur actuel
          const webSocket = initializePrivateWebSocket();
          this.chatSocket = webSocket;

          if (this.chatSocket) {
            //console.log('WebSocket privé initialisé avec succès');
          } else {
            //console.error('Échec de l\'initialisation du WebSocket privé');
          }
        }).catch(error => {
          //console.error('Erreur lors de l\'importation du service WebSocket:', error);
        });

        // Récupérer l'historique des messages via API REST
        this.fetchConversations(senderId, receiverId);

        if (!this.chatSocket) {
          //console.error('Erreur: WebSocket non initialisé');
          return;
        }

        this.chatSocket.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            //console.log('Message WebSocket reçu:', data);

            // Traitement des messages de chat individuels
            // Gérer à la fois 'chat' et 'chat_message' comme types de message
            if (data.type === 'chat' || data.type === 'chat_message') {
              //console.log('Message de chat reçu:', data);

              // Vérifier si le message contient les informations de l'expéditeur
              let senderName = '';
              let senderId = null;
              let receiverId = null;

              if (data.sender && typeof data.sender === 'object') {
                // Format avec objet sender complet
                senderName = `${data.sender.first_name} ${data.sender.last_name}`;
                senderId = data.sender.id;
              } else if (data.sender_id) {
                // Format avec sender_id uniquement
                senderId = data.sender_id;

                // Utiliser le nom de l'expéditeur si c'est nous-même
                if (senderId === this.sender.id) {
                  senderName = `${this.sender.first_name} ${this.sender.last_name}`;
                } else if (this.receiver && this.receiver.id === senderId) {
                  // Sinon, utiliser le nom du destinataire si c'est lui l'expéditeur
                  senderName = `${this.receiver.first_name} ${this.receiver.last_name}`;
                } else {
                  // Fallback si on ne peut pas déterminer le nom
                  senderName = `Utilisateur ${senderId}`;
                }
              }

              // Récupérer l'ID du destinataire
              if (data.recipient_id) {
                receiverId = data.recipient_id;
              } else if (data.receveir_id) { // Gérer la faute d'orthographe
                receiverId = data.receveir_id;
              }

              // Vérifier que le message appartient à la conversation actuellement affichée
              // Le message doit être entre l'utilisateur actuel et le destinataire sélectionné
              const isCurrentConversation = (
                // Soit l'utilisateur actuel est l'expéditeur et le destinataire sélectionné est le destinataire du message
                (senderId === this.sender.id && receiverId === this.receiver.id) ||
                // Soit l'utilisateur actuel est le destinataire et le destinataire sélectionné est l'expéditeur du message
                (senderId === this.receiver.id && receiverId === this.sender.id) ||
                // Soit le message est un message local de l'utilisateur actuel (pour les messages en attente de confirmation)
                (data.temp_id && senderId === this.sender.id)
              );

              // Si le message n'appartient pas à la conversation actuelle, l'ignorer
              if (!isCurrentConversation) {
                //console.log('Message ignoré car il n\'appartient pas à la conversation actuelle', {
                //  senderId,
                //  receiverId,
                //  currentSenderId: this.sender.id,
                //  currentReceiverId: this.receiver.id
                //});
                return;
              }

              //console.log('Message appartient à la conversation actuelle, traitement en cours...');

              // Vérifier si ce message est un message que nous avons déjà ajouté localement
              // en cherchant un message local avec le même contenu et le même expéditeur
              const isLocalMessage = this.messages.some(msg => {
                // Si le message a un identifiant temporaire et qu'il correspond à celui du message reçu
                if (msg.is_local && data.temp_id && msg.temp_id === data.temp_id) {
                  return true;
                }

                // Si le message a le même contenu, le même expéditeur et a été marqué comme local
                if (msg.is_local &&
                    msg.message === data.message &&
                    senderId === this.sender.id) {
                  // Mettre à jour le message local avec les informations du serveur
                  // comme l'horodatage et la date
                  if (data.time) msg.time = this.formatTime(data.time);
                  if (data.date) msg.date = this.formatDate(data.date);
                  // Marquer comme non local maintenant qu'il a été confirmé par le serveur
                  msg.is_local = false;
                  return true;
                }

                return false;
              });

              // Si ce n'est pas un message local, l'ajouter à la liste
              if (!isLocalMessage) {
                //console.log('Ajout d\'un nouveau message reçu du serveur');

                // Ajouter le message à la liste des messages
                this.messages.push({
                  sender_name: senderName,
                  message: data.message,
                  time: data.time ? this.formatTime(data.time) : this.formatTime(new Date()),
                  date: data.date ? this.formatDate(data.date) : null,
                });

                // Faire défiler vers le bas pour afficher le nouveau message
                this.$nextTick(() => {
                  this.scrollToBottom();
                });

                // Si le message vient de l'autre utilisateur
                if (senderId && senderId !== this.sender.id) {
                  // Mettre à jour le compteur de messages non lus
                  if (!this.unreadMessages[senderId]) {
                    this.unreadMessages[senderId] = 1;
                  } else {
                    this.unreadMessages[senderId]++;
                  }

                  // Utiliser le service de messages non lus
                  addUnreadMessage(senderId, data.message);

                  // Jouer le son de notification
                  playNotificationSound();

                  // Créer un événement personnalisé avec les détails du message
                  const notificationEvent = new CustomEvent('new-message', {
                    detail: {
                      senderId: senderId,
                      senderName: senderName,
                      message: data.message,
                      unreadCount: this.unreadMessages[senderId],
                    },
                  });

                  // Déclencher l'événement sur window pour qu'il soit accessible globalement
                  window.dispatchEvent(notificationEvent);
                }
              } else {
                //console.log('Message déjà présent localement, pas d\'ajout en double');
              }
            }
            // Traitement des messages de type 'messages' (historique de conversation)
            else if (data.type === 'messages' || data.message_type === 'messages') {
              //console.log('Historique de messages reçu via WebSocket:', data);

              // Vérifier que l'historique correspond à la conversation actuellement affichée
              // Récupérer les IDs de la conversation depuis les données ou les paramètres de la requête
              let conversationSenderId = data.sender_id;
              let conversationReceiverId = data.recipient_id || data.receveir_id;

              // Si les IDs ne sont pas dans les données, utiliser les IDs actuels
              if (!conversationSenderId || !conversationReceiverId) {
                //console.log('IDs de conversation non trouvés dans les données, utilisation des IDs actuels');
                conversationSenderId = this.sender.id;
                conversationReceiverId = this.receiver.id;
              }

              // Vérifier que l'historique appartient à la conversation actuellement affichée
              const isCurrentConversation = (
                // Soit les IDs correspondent directement
                (conversationSenderId === this.sender.id && conversationReceiverId === this.receiver.id) ||
                // Soit les IDs sont inversés (historique entre les mêmes personnes mais dans l'autre sens)
                (conversationSenderId === this.receiver.id && conversationReceiverId === this.sender.id)
              );

              // Si l'historique n'appartient pas à la conversation actuelle, l'ignorer
              if (!isCurrentConversation) {
                //console.log('Historique ignoré car il n\'appartient pas à la conversation actuelle', {
                //  conversationSenderId,
                //  conversationReceiverId,
                //  currentSenderId: this.sender.id,
                //  currentReceiverId: this.receiver.id
                //});
                return;
              }

              //console.log('Historique appartient à la conversation actuelle, traitement en cours...');

              // Extraire les messages selon le format
              const messagesData = data.messages || data.data || [];

              if (Array.isArray(messagesData) && messagesData.length > 0) {
                const newMessages = messagesData.map((msg) => {
                  // Vérifier si le message contient les informations de l'expéditeur
                  let senderName = '';

                  if (msg.sender && typeof msg.sender === 'object') {
                    // Format avec objet sender complet
                    senderName = `${msg.sender.first_name} ${msg.sender.last_name}`;
                  } else if (msg.sender_id) {
                    // Format avec sender_id uniquement
                    const senderId = msg.sender_id;

                    // Utiliser le nom de l'expéditeur si c'est nous-même
                    if (senderId === this.sender.id) {
                      senderName = `${this.sender.first_name} ${this.sender.last_name}`;
                    } else if (this.receiver && this.receiver.id === senderId) {
                      // Sinon, utiliser le nom du destinataire si c'est lui l'expéditeur
                      senderName = `${this.receiver.first_name} ${this.receiver.last_name}`;
                    } else {
                      // Fallback si on ne peut pas déterminer le nom
                      senderName = `Utilisateur ${senderId}`;
                    }
                  }

                  return {
                    sender_name: senderName,
                    message: msg.message,
                    time: this.formatTime(msg.time),
                    date: this.formatDate(msg.date),
                    read: msg.read,
                  };
                });

                const messagesHaveChanged = !this.areMessagesEqual(
                  newMessages,
                  this.messages
                );

                if (messagesHaveChanged) {
                  this.messages = newMessages;

                  // Faire défiler vers le bas pour afficher les nouveaux messages
                  this.$nextTick(() => {
                    this.scrollToBottom();
                  });

                  newMessages.forEach((message) => {
                    if (!message.read && message.sender_name !== this.currentId) {
                      this.unreadMessages[message.sender_name] = true;
                    }
                  });
                }
              }
            }
          } catch (error) {
            //console.error('Erreur lors du traitement du message WebSocket:', error);
          }
        };
      },
      formatTime(time) {
        try {
          if (!time) {
            // Si le temps n'est pas défini, utiliser l'heure actuelle
            const now = new Date();
            return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
          }

          if (time instanceof Date) {
            // Si c'est un objet Date
            const hours = time.getHours().toString().padStart(2, '0');
            const minutes = time.getMinutes().toString().padStart(2, '0');
            return `${hours}:${minutes}`;
          } else if (typeof time === 'string') {
            // Format "HH:MM:SS.mmm" ou "HH:MM"
            if (time.includes('.')) {
              // Format avec millisecondes (15:20:38.615617)
              const parts = time.split('.');
              const timeParts = parts[0].split(':');
              if (timeParts.length >= 2) {
                return `${timeParts[0]}:${timeParts[1]}`;
              }
            } else if (time.includes(':')) {
              // Format simple (15:20)
              const timeParts = time.split(':');
              if (timeParts.length >= 2) {
                return `${timeParts[0]}:${timeParts[1]}`;
              }
            }
          }

          // Si le format n'est pas reconnu, retourner le temps tel quel
          console.warn('Format de temps non reconnu:', time);
          return time;
        } catch (error) {
          //console.error('Erreur lors du formatage du temps:', error);
          return time; // Retourner le temps tel quel en cas d'erreur
        }
      },
      formatDate(date) {
        try {
          if (!date) {
            // Si la date n'est pas définie, utiliser la date actuelle
            const now = new Date();
            const day = now.getDate().toString().padStart(2, '0');
            const month = (now.getMonth() + 1).toString().padStart(2, '0');
            const year = now.getFullYear();
            return `${day}-${month}-${year}`;
          }

          // Format ISO "YYYY-MM-DD"
          if (typeof date === 'string' && date.includes('-')) {
            const parts = date.split('-');
            if (parts.length === 3) {
              const [year, month, day] = parts;
              return `${day}-${month}-${year}`;
            }
          }

          // Si le format n'est pas reconnu, retourner la date telle quelle
          console.warn('Format de date non reconnu:', date);
          return date;
        } catch (error) {
          //console.error('Erreur lors du formatage de la date:', error);
          return date; // Retourner la date telle quelle en cas d'erreur
        }
      },
      sendMessage() {
        if (this.chatSocket && this.chatSocket.readyState === WebSocket.OPEN && this.messageInput.trim()) {
          // Vérifier que le destinataire est bien défini
          if (!this.receiver || !this.receiver.id) {
            //console.error('Erreur: Destinataire non défini ou sans ID');
            return;
          }

          //console.log('Envoi de message au destinataire:', this.receiver);

          // Générer un identifiant temporaire pour le message
          const tempId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          const messageText = this.messageInput.trim();

          // Créer l'objet message au format exact attendu par le backend
          const message = {
            // Format simplifié pour le WebSocket privé
            message: messageText,
            receiver_id: parseInt(this.receiver.id)
          };

          this.chatSocket.send(JSON.stringify(message));
          //console.log('Message envoyé:', message);

          const now = new Date();
          const formattedTime = `${now
            .getHours()
            .toString()
            .padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;

          // Ajouter le message à notre liste locale avec l'identifiant temporaire
          const localMessage = {
            id: tempId,
            sender_id: this.sender.id,
            sender_name: `${this.sender.first_name} ${this.sender.last_name}`,
            receiver_id: this.receiver.id,
            receiver_name: `${this.receiver.first_name} ${this.receiver.last_name}`,
            message: messageText,
            time: this.formatTime(formattedTime),
            date: this.formatDate(new Date().toISOString().split('T')[0]),
            read: false,
            isCurrentUser: true,
            isImage: messageText.startsWith('data:image/'),
            temp_id: tempId, // Stocker l'identifiant temporaire
            is_local: true,   // Marquer comme message local
            fontFamily: this.selectedFont
          };

          this.messages.push(localMessage);

          // Réinitialiser le champ de saisie
          this.messageInput = '';

          // Faire défiler vers le bas pour afficher le nouveau message
          this.scrollToBottom();
        }
      },
      toggleChatOpen() {
        this.isChatWindowOpen = !this.isChatWindowOpen;
        this.$emit('toggleChatOpen', !this.isOpen);
      },
      closeChatWindow() {
        this.$emit('closeChatWindow', this.receiver.id);
      },
      startVideoCall() {
        this.$emit('start-video-call', this.receiver.id);
      },
      startVoiceCall() {
        this.$emit('start-voice-call', this.receiver.id);
      },

      /**
       * Gère les nouveaux messages reçus via WebSocket
       * @param {CustomEvent} event - L'événement contenant les détails du message
       */
      /**
       * Gère les messages de chat reçus via l'événement personnalisé chat-message-received
       * @param {CustomEvent} event - L'événement contenant les détails du message
       */
      handleChatMessageEvent(event) {
        try {
          // Récupérer les détails du message
          const data = event.detail;

          if (!data) {
            //console.error('%c[MESSAGERIE] Détails du message manquants dans l\'événement chat-message-received', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            return;
          }

          //console.log('%c[MESSAGERIE] Message de chat reçu via événement personnalisé:', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;', data);

          // Vérifier si le message contient les informations nécessaires
          if (!data.message) {
            //console.error('%c[MESSAGERIE] Message de chat invalide (pas de contenu)', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', data);
            return;
          }

          // Extraire les informations du message
          const senderId = data.sender_id;
          const receiverId = data.recipient_id || data.receiver_id;

          // Vérifier si le message appartient à la conversation actuelle
          const isForCurrentConversation =
            (senderId === this.sender.id && receiverId === this.receiver.id) ||
            (senderId === this.receiver.id && receiverId === this.sender.id);

          if (!isForCurrentConversation) {
            //console.log('%c[MESSAGERIE] Message ignoré car il n\'appartient pas à la conversation actuelle', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;', {
            //  senderId,
            //  receiverId,
            //  currentSenderId: this.sender.id,
            //  currentReceiverId: this.receiver.id
            //});
            return;
          }

          //console.log('%c[MESSAGERIE] Message appartient à la conversation actuelle, traitement en cours...', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');

          // Déterminer le nom de l'expéditeur
          let senderName = '';
          if (senderId === this.sender.id) {
            senderName = `${this.sender.first_name} ${this.sender.last_name}`;
          } else if (this.receiver && this.receiver.id === senderId) {
            senderName = `${this.receiver.first_name} ${this.receiver.last_name}`;
          } else {
            senderName = `Utilisateur ${senderId}`;
          }

          // Formater le message pour l'affichage
          const formattedMessage = {
            id: data.id || `msg_${Date.now()}`,
            sender_id: senderId,
            sender_name: senderName,
            receiver_id: receiverId,
            receiver_name: receiverId === this.sender.id ? `${this.sender.first_name} ${this.sender.last_name}` : `${this.receiver.first_name} ${this.receiver.last_name}`,
            message: data.message,
            time: this.formatTime(data.time || new Date()),
            date: this.formatDate(data.date || new Date().toISOString().split('T')[0]),
            read: data.read || false,
            isCurrentUser: senderId === this.sender.id,
            isImage: data.message && data.message.startsWith('data:image/')
          };

          // Vérifier si le message existe déjà dans la liste des messages
          const messageExists = this.messages.some(msg =>
            (msg.id && msg.id === formattedMessage.id) ||
            (msg.message === formattedMessage.message &&
             msg.sender_id === formattedMessage.sender_id &&
             msg.time === formattedMessage.time)
          );

          if (messageExists) {
            //console.log('%c[MESSAGERIE] Message déjà présent dans la conversation, ignoré', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');
            return;
          }

          // Ajouter le message à la liste des messages
          this.messages.push(formattedMessage);

          // Faire défiler vers le bas pour afficher le nouveau message
          this.$nextTick(() => {
            this.scrollToBottom();
          });

          // Si le message vient de l'autre utilisateur, le marquer comme lu
          if (!formattedMessage.isCurrentUser) {
            this.markAsRead(formattedMessage);
          }

          //console.log('%c[MESSAGERIE] Message ajouté avec succès à la conversation', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
        } catch (error) {
          //console.error('%c[MESSAGERIE] Erreur lors du traitement du message de chat:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
        }
      },

      handleNewMessage(event) {
        try {
          // Récupérer les détails du message
          const message = event.detail;

          if (!message) {
            //console.error('Détails du message manquants dans l\'événement new-message');
            return;
          }

          //console.log('Nouveau message reçu via WebSocket:', message);

          // Vérifier si le message appartient à la conversation actuelle
          const isForCurrentConversation =
            // Si l'expéditeur est l'utilisateur actuel et le destinataire est le destinataire actuel
            (message.sender_id === this.sender.id && message.receiver_id === this.receiver.id) ||
            // Ou si l'expéditeur est le destinataire actuel et le destinataire est l'utilisateur actuel
            (message.sender_id === this.receiver.id && message.receiver_id === this.sender.id);

          if (!isForCurrentConversation) {
            //console.log('Message ignoré car il n\'appartient pas à la conversation actuelle');
            return;
          }

          //console.log('Message ajouté à la conversation actuelle');

          // Vérifier si le message existe déjà dans la liste des messages
          const messageExists = this.messages.some(msg =>
            (msg.id && msg.id === message.id) || // Si les IDs correspondent
            (msg.message === message.message &&
             msg.sender_id === message.sender_id &&
             msg.time === message.time) // Si le contenu, l'expéditeur et l'heure correspondent
          );

          if (messageExists) {
            //console.log('Message déjà présent dans la conversation, ignoré');
            return;
          }

          // Formater le message pour l'affichage
          const formattedMessage = {
            id: message.id || `msg_${Date.now()}`,
            sender_id: message.sender_id,
            sender_name: message.sender_name,
            receiver_id: message.receiver_id,
            receiver_name: message.receiver_name,
            message: message.message,
            time: this.formatTime(message.time),
            date: this.formatDate(message.date),
            read: message.read || false,
            isCurrentUser: message.sender_id === this.sender.id,
            isImage: message.message && message.message.startsWith('data:image/')
          };

          // Ajouter le message à la liste des messages
          this.messages.push(formattedMessage);

          // Faire défiler vers le bas pour afficher le nouveau message
          this.$nextTick(() => {
            this.scrollToBottom();
          });

          // Si le message vient de l'autre utilisateur, le marquer comme lu
          if (!formattedMessage.isCurrentUser) {
            this.markAsRead(formattedMessage);
          }
        } catch (error) {
          //console.error('Erreur lors du traitement du nouveau message:', error);
        }
      },
      confirmSendImage() {
        if (this.pendingFile) {
          this.sendFileAsMessage(this.pendingFile.base64, this.pendingFile.file);
        }
        this.previewImage = null;
        this.pendingFile = null;
        this.showImagePreviewModal = false;
      },

      cancelSendImage() {
        this.previewImage = null;
        this.pendingFile = null;
        this.showImagePreviewModal = false;
      },

      sendFileAsMessage(base64, file) {
        const isImage = file.type.startsWith('image/');
        if (!this.chatSocket || this.chatSocket.readyState !== WebSocket.OPEN) return;

        const message = {
          message: base64,
          receiver_id: parseInt(this.receiver.id),
        };

        this.chatSocket.send(JSON.stringify(message));

        const tempId = `temp_${Date.now()}`;
        const now = new Date();
        this.messages.push({
          id: tempId,
          sender_id: this.sender.id,
          sender_name: `${this.sender.first_name} ${this.sender.last_name}`,
          receiver_id: this.receiver.id,
          receiver_name: `${this.receiver.first_name} ${this.receiver.last_name}`,
          message: base64,
          time: this.formatTime(now),
          date: this.formatDate(now.toISOString().split('T')[0]),
          read: false,
          isCurrentUser: true,
          isImage: isImage,
          temp_id: tempId,
          is_local: true,
        });

        this.scrollToBottom();
      },
    },
    mounted() {
      this.inputIcons = [
         /*{
          src: require('@/assets/text-black.svg'),
          alt: 'Police d\'écriture',
          action: () => this.toggleFontSelector(),
        }, */
        /* {
          src: require('@/assets/more-black.svg'),
          alt: 'More Options',
          action: () => {},
        }, */
        /*{
          src: require('@/assets/at-black.svg'),
          alt: 'Ajouter un lien cliquable',
          action: () => this.insertLink(),
        },*/
        {
          src: require('@/assets/pj-black.svg'),
          alt: 'Pièce jointe',
          action: () => this.$refs.fileInput.click(),
        },
        {
          src: require('@/assets/emoji-black.svg'),
          alt: 'Ajouter un émoji',
          action: () => this.toggleEmojiPicker()
        },
      ],

      //console.log(
      //  'Conversation reçue dans ChatWindow :',
      //  this.lastConversation
      //);

      // Initialiser le son de notification
      initNotificationSound();

      // Initialiser la connexion WebSocket et charger l'historique des messages
      this.initializeChatWebSocketConnection(this.sender.id, this.receiver.id);

      // Ajouter un écouteur d'événements pour les nouveaux messages
      window.addEventListener('new-message', this.handleNewMessage);

      // Ajouter un écouteur d'événements pour les messages de chat
      window.addEventListener('chat-message-received', this.handleChatMessageEvent);

      //console.log('%c[MESSAGERIE] Écouteurs d\'événements configurés', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
    },

    beforeDestroy() {
      // Supprimer les écouteurs d'événements pour éviter les fuites de mémoire
      window.removeEventListener('new-message', this.handleNewMessage);
      window.removeEventListener('chat-message-received', this.handleChatMessageEvent);

      //console.log('%c[MESSAGERIE] Écouteurs d\'événements supprimés', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');

      if (this.chatSocket) {
        this.chatSocket.close();
        //console.log('%c[MESSAGERIE] WebSocket fermé', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
      }
    },





    // Surveiller les changements de destinataire pour recharger l'historique des messages
    watch: {
      'receiver.id': {
        handler(newReceiverId, oldReceiverId) {
          if (newReceiverId && newReceiverId !== oldReceiverId) {
            //console.log('Destinataire changé, rechargement de l\'historique des messages');
            // Fermer l'ancienne connexion WebSocket si elle existe
            if (this.chatSocket) {
              this.chatSocket.close();
            }
            // Initialiser une nouvelle connexion WebSocket avec le nouveau destinataire
            this.initializeChatWebSocketConnection(this.sender.id, newReceiverId);
          }
        },
        immediate: true
      },
      messages() {
        this.scrollToBottom();
      }
    },

  };
</script>
<style scoped>


  .chat-window {
    width: 500px;
    max-height: 650px;
    display: flex;
    flex-direction: column;
    border: 1px solid #ccc;
    border-radius: 8px;
    overflow: hidden;
    background-color: #fff;
  }

  .chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #eee;
  }

  .chat-header-info {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  .toggle-chat {
    display: flex;
  }

  .receiver-avatar {
    width: 50px;
    height: 50px;
    border-radius: 13%;
    object-fit: cover;
  }

  .receiver-name {
    font-weight: bold;
    font-size: 1rem;
    color: #333;
  }

  .chat-header-actions {
    display: flex;
    margin-left: 7rem;
  }
  .fa-chevron-down:before {
    font-size: 20px;
    padding: 10px;
  }
  .fa-times:before {
    font-size: 20px;
    padding: 10px;
  }
  .fa-chevron-up:before {
    font-size: 20px;
    padding: 10px;
  }

  .chat-action {
    background: none;
    border: none;
    padding: 5px;
    cursor: pointer;
  }

  .chat-messages {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    min-height: 400px;
    background-color: white;
    min-height: 359px;
    max-height: 359px;
  }

  .chat-message {
    padding: 15px;
    margin-bottom: 10px;
    max-width: 70%;
  }

  .sent {
    align-self: flex-end;
    justify-self: flex-end;
    background-color: #eeeeee;
    border-radius: 10px 10px 0px 10px;
  }

  .received {
    align-self: flex-start;
    background-color: #f1f1f1;
    border-radius: 10px 10px 10px 0px;
  }

  .message-text {
    font-size: 0.9rem;
    margin-bottom: 4px;
    font-weight: 600;
    word-wrap: break-word;
  }

  .message-image {
    max-width: 100%;
    max-height: 200px;
    border-radius: 5px;
    margin-bottom: 4px;
  }

  .message-info {
    font-size: 0.75rem;
    color: #777;
  }

  .chat-input-section {
    padding: 16px;
    border-top: 1px solid #eee;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .textarea-container textarea {
    width: 100%;
    padding: 12px;
    border-radius: 5px;
    border: 1px solid var(--gray-300);
    background-color: #fdf5e6;
    font-size: 14px;
    resize: none;
    min-height: 80px;
  }

  .input-actions {
    display: flex;
    justify-content: space-between;
  }

  .input-icons {
    display: flex;
    gap: 8px;
  }

  .input-icon {
    background: none;
    border: none;
    cursor: pointer;
  }

  .send-button {
    border: none;
    padding: 8px 12px;
    color: #fff;
    border-radius: 8px;
    cursor: pointer;
  }
  @media only screen and (max-width: 768px) {
    .chat-window {
      width: 100%;
    }
    .chat-header-actions[data-v-c00ab182] {
      margin-left: 0rem;
    }
  }

  .emoji-popup {
  position: absolute;
  bottom: 60px;
  right: 710px;
  z-index: 1000;
  }

    .image-preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .image-preview-content {
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
  }

  .preview-img {
    max-width: 300px;
    max-height: 300px;
    margin: 15px 0;
    border-radius: 6px;
  }

  .preview-buttons button {
    margin: 0 10px;
    padding: 8px 14px;
    border: none;
    border-radius: 6px;
    background-color: #ffc107;
    cursor: pointer;
    font-weight: bold;
  }

  .font-selector {
    position: absolute;
    bottom: 60px;
    right: 580px;
    background: #fff;
    border: 1px solid #ccc;
    padding: 12px;
    border-radius: 8px;
    z-index: 1000;
  }
  .font-selector ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  .font-selector li:hover {
    background-color: #f0f0f0;
  }

  :deep(.message-text a) {
    color: #007bff;
    text-decoration: underline;
    word-break: break-word;
  }

  :deep(.message-text a:hover) {
    color: #0056b3;
  }


</style>
