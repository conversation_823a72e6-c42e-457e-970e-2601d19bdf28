<template>
  <!-- Sidebar pour l'historique des chats et utilisateurs -->
  <aside class="messaging-chat-history-section">

    <!-- Liste des utilisateurs -->
    <div class="user-list">
      <header>
        <span
          v-if="isInLayout"
          class="toggle-users-list"
          @click="toggleChatOpen"
        >
          <i v-if="isChatOpen" class="fas fa-chevron-down"></i>
          <i v-else class="fas fa-chevron-up"></i>
        </span>
        <h3>Mon Réseau</h3>
      </header>
      <ul
        v-show="isChatOpen"
        v-if="users.length > 0"
        class="user-list"
        ref="userList"
        @scroll="onScroll"
      >
        <v-text-field
              label="Rechercher un utilisateur"
              type="text"
              class="search-input"
              v-model="searchQuery"
        />
        <li
          v-for="user in filteredUsers"
          :key="user.id"
          @click="selectUser(user)"
          :class="{ selected: user.id === currentUser.id }"
          class="user-item"
        >
          <div
            class="connection-indicator"
            :class="{ online: user.connected, offline: !user.connected }"
            :title="user.connected ? 'Connecté' : 'Déconnecté'"
          ></div>
          <UserAvatar :width="48" :user="user" />

            <div class="flex">
              <div class="user-info">
                <span class="user-name title"
                  >{{ user.first_name }} {{ user.last_name }}</span
                >
                <span v-if="unreadMessages[user.id]" class="unread-badge">{{
                  unreadMessages[user.id]
                }}</span>
              </div>
              <div>
                <span class="user-job job">{{ user.metier }}</span>
              </div>
            </div>
          </li>
      </ul>
      <p v-if="isChatOpen && users.length === 0">Aucun utilisateur trouvé</p>
    </div>
  </aside>
</template>

<script>
  import UserAvatar from '@/components/views-models/profil/UserAvatar.vue';
  import LazyLoader from '@/components/utils/LazyLoader.vue';
  import { axiosInstance } from '../../services/axios';
  import { markUserMessagesAsRead, getUnreadMessagesByUser } from '@/services/unread-messages.service';
  import { useMessagerieStore } from '@/store/messagerie';
  import { storeToRefs } from 'pinia';

  export default {
    props: {
      // Spécifie si le composant appartient au layout ou si c'est le composant dans la page messagerie
      isInLayout: {
        type: Boolean,
        required: false,
        default: false,
      },
    },
    data() {
      return {
        // Si le composant n'appartient pas au layout, on affiche le chat ouvert par défaut, sinon on se base sur l'état du localStorage
        isChatOpen: this.isInLayout ? false : true,
        currentUser: this.$store.getters.getUser,
        searchQuery: '',
        selectedTab: 0,
        currentPage: 1,
        totalPages: 1,
        isLoading: false,
        unreadMessages: {}, // Stocke les compteurs de messages non lus par utilisateur
      };
    },
    setup() {
      const messagerieStore = useMessagerieStore();
      const { users } = storeToRefs(messagerieStore);
      return { messagerieStore, users };
    },
    components: {
      UserAvatar,
      LazyLoader,
    },
    methods: {
      toggleChatOpen() {
        this.isChatOpen = !this.isChatOpen;
        if (this.isInLayout) {
          this.$emit('toggleChatOpen', this.isChatOpen);
        }
      },
      async fetchUsers() {
        if (!this.messagerieStore.usersLoaded) {
          this.isLoading = true;
          await this.messagerieStore.loadUsers();
          this.isLoading = false;
        }
      },
      goToPage(page) {
        if (page > 0 && page <= this.totalPages) {
          // //console.log('Changement de page :', page);
          this.fetchUsers(page);
        } else {
          //console.log('Page invalide ou déjà à la dernière page');
        }
      },
      onScroll() {
        const list = this.$refs.userList;
        if (list.scrollTop + list.clientHeight >= list.scrollHeight - 10) {
          if (this.currentPage < this.totalPages) {
            this.fetchUsers(this.currentPage + 1);
          }
        }
      },
      selectUser(user) {
        //console.log('selectUser called from child: ', user);
        this.$emit('selectUser', user);
        this.$emit('lastConversation', user);

        // Réinitialiser le compteur de messages non lus pour cet utilisateur
        if (this.unreadMessages[user.id]) {
          this.unreadMessages[user.id] = 0;

          // Utiliser le service de messages non lus pour marquer les messages comme lus
          markUserMessagesAsRead(user.id);
        }
      },

      // Gérer les notifications de nouveaux messages
      handleNewMessage(event) {
        const { senderId, unreadCount } = event.detail;

        // Mettre à jour le compteur de messages non lus
        this.unreadMessages[senderId] = unreadCount;

        // Trouver l'utilisateur correspondant dans la liste
        const sender = this.users.find((user) => user.id === senderId);

        // Si l'utilisateur n'est pas dans la liste, on ne fait rien
        if (!sender) return;

        // Mettre à jour l'interface utilisateur pour refléter le nouveau tri
        this.$forceUpdate();

        //console.log('%c[MESSAGERIE] Nouveau message reçu, mise à jour du tri des contacts', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
      },

      // Gérer les mises à jour des messages non lus
      handleUnreadMessagesUpdated(event) {
        //console.log('%c[MESSAGERIE] Mise à jour des messages non lus reçue', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;', event.detail);

        // Récupérer les données des messages non lus
        const { byUser } = event.detail;

        // Mettre à jour le compteur local de messages non lus
        this.unreadMessages = { ...this.unreadMessages, ...byUser };

        // Mettre à jour l'interface utilisateur pour refléter le nouveau tri
        this.$forceUpdate();

        //console.log('%c[MESSAGERIE] Compteurs de messages non lus mis à jour, mise à jour du tri des contacts', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
      },
    },
    mounted() {
      this.fetchUsers();

      // Écouter les événements de nouveaux messages
      window.addEventListener('new-message', this.handleNewMessage);

      // Écouter les événements de mise à jour des messages non lus
      window.addEventListener('unread-messages-updated', this.handleUnreadMessagesUpdated);

      // Initialiser les compteurs de messages non lus
      this.unreadMessages = { ...this.unreadMessages, ...getUnreadMessagesByUser() };
    },

    beforeDestroy() {
      // Nettoyer les écouteurs d'événements
      window.removeEventListener('new-message', this.handleNewMessage);
      window.removeEventListener('unread-messages-updated', this.handleUnreadMessagesUpdated);
    },
    computed: {
    filteredUsers() {
      // Filtrer les utilisateurs en fonction de la recherche
      let filteredList = this.users;
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        filteredList = this.users.filter(user =>
          `${user.first_name} ${user.last_name}`.toLowerCase().includes(query)
        );
      }

      // Trier les utilisateurs : ceux avec des messages non lus en premier
      return filteredList.sort((a, b) => {
        // Récupérer le nombre de messages non lus pour chaque utilisateur
        const unreadA = this.unreadMessages[a.id] || 0;
        const unreadB = this.unreadMessages[b.id] || 0;

        // Si les deux utilisateurs ont des messages non lus, trier par nombre de messages non lus (décroissant)
        if (unreadA > 0 && unreadB > 0) {
          return unreadB - unreadA;
        }

        // Si seulement l'utilisateur A a des messages non lus, le placer en premier
        if (unreadA > 0) {
          return -1;
        }

        // Si seulement l'utilisateur B a des messages non lus, le placer en premier
        if (unreadB > 0) {
          return 1;
        }

        // Si aucun des deux n'a de messages non lus, conserver l'ordre actuel
        return 0;
      });
    },
  },
  };
</script>

<style scoped>
  .exit-fullscreen-button {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    cursor: pointer;
  }
  .exit-fullscreen-button img {
    width: 33px;
    height: 33pxx;
  }
  .fullscreen {
    width: 100%;
    height: 100%;
  }

  .fullscreen-button {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    cursor: pointer;
  }

  .fullscreen-button img {
    width: 33px;
    height: 33px;
  }

  .video-container {
    display: flex;
    flex-direction: column;
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  .video-controls button:hover {
    background-color: #33363a;
  }
  .video-controls button {
    padding: 6px 12px;
    border: none;
    border-radius: 5px;
    background-color: #26282b;
    color: #333;
    cursor: pointer;
  }
  .video-controls {
    position: relative;
    bottom: 10px;
    display: flex;
    gap: 10px;
    background: rgb(38 40 43);
    padding: 6px;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    align-items: center;
    justify-content: center;
  }

  .webcam-on {
    display: grid;
    grid-template-columns: 1fr 1fr !important;
    gap: 24px;
  }
  .webcam-video {
    border-radius: 8px;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .unread-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #ff5722; /* Couleur orange vif pour attirer l'attention */
    color: white;
    font-size: 12px;
    font-weight: bold;
    border-radius: 50%;
    width: 22px;
    height: 22px;
    margin-left: 8px;
    animation: pulse 1.5s infinite;
    box-shadow: 0 0 5px rgba(255, 87, 34, 0.7); /* Effet de lueur */
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }
  .job {
    color: #7f7272;
  }
  .title {
    font-size: 18px;
    font-weight: 600;
  }
  .flex {
    display: flex;
    flex-direction: column;
  }
  aside {
    width: 350px;
  }
  .fa-chevron-down:before {
    font-size: 20px;
  }
  .user-list {
    position: relative;
    width: 100%;
    background-color: #ffffff;
    border-right: 1px solid #ffffff;
    overflow-y: auto;
    margin-top: 10px;
  }
  .user-list header {
    border-bottom: 1px solid #ccc;
    min-height: 48px;
    padding: 15px;
    display: flex;
  }
  .search-input {
    padding:  10px 10px 0px 10px;
    font-size: 16px;
    width: 100%;
  }
  .toggle-users-list {
    cursor: pointer;
    position: absolute;
    right: 10px;
    top: 6px;
    transform: translateY(50%);
  }
  .fa-chevron-up:before {
    font-size: 20px;
  }
  .user-list h3 {
    font-size: 18px;
    font-weight: bold;
    color: black;
  }

  .user-list ul {
    list-style: none;
    padding: 0;
    height: 500px;
    overflow: auto;
  }

  .user-list li {
    display: flex;
    align-items: flex-start;
    padding: 7px;
    gap: 10px;
    cursor: pointer;
    transition: background-color 0.3s;
    flex-direction: row;
  }

  .user-list li:hover {
    background-color: #eee;
  }
  .connection-indicator {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    position: relative;
    left: 16px;
    top: 33px;
    box-shadow:
      0 2px 4px rgba(0, 0, 0, 0.3),
      inset 0 -2px 4px rgba(0, 0, 0, 0.5);
    background: radial-gradient(circle, #ffffff 10%, #e0e0e0 70%, #9e9e9e 100%);
  }
  .connection-indicator.online {
    background: radial-gradient(circle, #00ca10 10%, #4caf50 70%, #2e7d32 100%);
  }

  .connection-indicator.offline {
    background: radial-gradient(circle, #afafaf 10%, #9e9e9e 70%, #616161 100%);
  }
  .row {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .message-content {
    display: flex;
    flex-direction: column;
  }
  .message-username {
    font-weight: bold;
    margin-bottom: 4px;
  }

  .message-timestamp {
    font-size: 0.75em;
    color: gray;
    padding-left: 12px;
  }
</style>
