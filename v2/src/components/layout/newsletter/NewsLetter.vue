<template>

    <section class="container" id="newsletterSection">

        <div class="padding-container">
            <div class="newsletter-main-container">
                <div class="newsletter-text-container">
                    <h2 class="title">Inscris-toi à la sortie de <span style="color:var(--primary-1)">notre prochaine version !</span></h2>
                    
                    <p class="text">Saisis ta chance et rejoins la version bêta-test prochaine pour profiter de fonctionnalités avancées
                        comme
                        l'amélioration de ton CV et des recommandations avancées de jobs.
                        Seulement une centaine de places disponibles !⏱</p>
                    
                    <v-form class="row" @submit.prevent="submitForm" ref="form">
                    
                        <div class="input-wrapper" >
                            <v-text-field v-model="formData.email"  :rules="[...emailRules , ...notEmptyRules]"
                                label="Saisis ton adresse email" variant="solo" flat></v-text-field>
                        </div>
                    
                        <div class="btn-wrapper">
                            <PrimaryRoundedButton textContent="Je m'inscris" type="submit"/>
                        </div>
                    
                    </v-form>
                </div>
            </div>
           
            
        </div>

    </section>

</template>

<script>
import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
import { submitEmailNewsletter } from '../../../services/newsletter.service';
import {  validateEmail , validateNotEmpty } from "../../../utils/validationRules"; 

export default {
    name: 'NewsLetter',

    components: {
        PrimaryRoundedButton,
    },

    data() {
        return {
            formData: {},
            emailRules: [
            v => validateEmail(v) || true],
            notEmptyRules: [
            v => validateNotEmpty(v) || true],
        }
    },

    methods: {
        async submitForm (event) {
            event.preventDefault();
            const validate = await this.$refs.form.validate()
            if (validate.valid)  {
                try {
                    await submitEmailNewsletter(this.formData);
                    this.formData.email = '';  // Réinitialise le champ email après une soumission
                    this.$refs.form.resetValidation();  // Réinitialise la validation du formulaire
                } catch (error) {
                    //console.log(error);
                }
            }
        },
    }
}
</script>

<style scoped>
h2 {
    color: var(--surface-bg) !important;
}
.container {
    background-color: var(--black-100);
    display: flex;
    flex-direction: column;
    margin-top: inherit;
    padding: 20px 20px 30px 20px;
}

.padding-container {
    display: flex;
    flex-direction: column;
}

.newsletter-main-container {
    display: flex;
    justify-content: start;
    align-items: center;
}

.newsletter-text-container {
    display: flex;
    flex-direction: column;
    
}

.title {
    color: var(--yellow-100);
    text-align: start;
    padding: 20px 0 10px 0;
}

.text {
    color: var(--white-200);
    padding: 10px 0 20px 0;
}

.row {
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
}

.input-wrapper {
    width: 100%;
}

.btn-wrapper {
    margin-top: 10px;
}
@media screen and (min-width: 992px) {
    .row {
        flex-direction: row;
        align-items: start;
    }

    .title {
        text-align: initial;
    }
    .newsletter-main-container {
        flex-direction: row;
        align-items: inherit;
    }
}

@media screen and (min-width: 1800px) {

    .container {
        width: 100%;
        margin: auto;
    }   

    .padding-container {
        max-width: 80%; 
        margin: 0 auto;
    }
}
</style>
