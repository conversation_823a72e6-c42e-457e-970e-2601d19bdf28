<template>
  <nav>
    <div :class="['navbar', userTypeClass]">
      <div class="img-container" @click.stop="gotoPage('/')">
        <div class="img-container-circle">
          <img src="@/assets/logo-tb.svg" alt="logo" class="logo"/>
        </div>
        <div class="beta-section">
          <h2>Bêta</h2>
        </div>
      </div>

      <!-- switch visible sur homepage.com uniquement -->
      <div v-if="!isLoggedIn" class="view-switch">
        <button @click="toggleView">
          Voir comme {{ isViewingAsRecruiter ? 'Candidat' : 'Recruteur' }}
        </button>
      </div>

      <!-- mobile only -->
      <div id="mobile">
        <div class="hamburger-container" @click.stop="toggleMenu">
          <img
            v-if="isMenuOpen"
            src="@/assets/icons/close-btn.svg"
            alt="liste icône"
          />
          <img v-else src="@/assets/icons/hamburger.svg" alt="liste icône" />
        </div>
      </div>

      <!-- menu -->
      <ul :class="['menu', { hidden: isMobile && !isMenuOpen }]">
        <div class="overlay" @click.stop="handleSubMenuClose"></div>
        <!-- ########### Offres d'emploi ########### -->
        <li
          :class="{
            'li-with-submenu': activeSubMenu === 'offer',
            active: activePage === 'search',
          }"
          @mouseover="handleSubMenuMouseOver('offer')"
          @mouseleave="handleSubMenuMouseLeave('offer')"
        >
          <!-- Texte pour redirection -->
          <div class="menu-item" @click.stop="gotoPage('/recherche')">
            Emplois
          </div>
          <!-- Flèche pour déplier le sous-menu -->
          <img
            v-if="isLoggedIn && userRole === 'applicant'"
            class="arrows"
            :src="
              showSubMenuOffer
                ? require('@/assets/icons/arrow-up.svg')
                : require('@/assets/Icon-black-54.svg')
            "
            @click.stop="toggleSubMenu('offer')"
          />
          <!-- ############### submenu ############### -->
          <transition name="fade">
            <ul
              v-if="isLoggedIn && userRole === 'applicant' && showSubMenuOffer"
              class="submenu"
              :class="{ show: showSubMenuOffer }"
            >
              <li
                :class="{ active: activePage === 'search' }"
                @click.stop="gotoPage('/recherche')"
              >
                Offres d'emplois
              </li>
              <li
                :class="{ active: activePage === 'candidatures' }"
                @click.stop="gotoPage('/candidatures')"
              >
                Mes candidatures
              </li>
              <li
                :class="{ active: activePage === 'favorite' }"
                @click.stop="gotoPage('/favoris')"
              >
                Mes favoris
              </li>
              <li
                :class="{ active: activePage === 'Alerts' }"
                @click.stop="gotoPage('/alertes')"
              >
                Mes alertes
              </li>
            </ul>
          </transition>
        </li>

        <!-- ########### Chat IA emploi ########### -->
        <li
          v-if="isLoggedIn && userRole === 'applicant'"
          :class="{ active: activePage === 'messaging' }"
          @click.stop="gotoPage('/chat-ia')"
        >
          Chat IA emploi
        </li>

        <!-- ########### Simulateur d'entretien ########### -->
        <li
          v-if="isLoggedIn && userRole === 'applicant'"
          :class="{
            active:
              activePage === 'interviews-list' ||
              activePage === 'create-interview' ||
              activePage === 'candidate-interview' ||
              activePage === 'interview-report',
          }"
          @click.stop="gotoPage('/interviews')"
        >
          Simulateur d'entretien
        </li>

        <!-- ########### Réseau social ########### -->
        <li
          :class="{ active: activePage === 'social' }"
          @click.stop="gotoPage('/reseau-social')"
        >
          Réseau social
        </li>

        <!-- ########### Communauté ########### -->
        <li
          v-if="isLoggedIn"
          :class="{
            'li-with-submenu': activeSubMenu === 'communaute',
            active: activePage === 'recruiter-search',
          }"
          @mouseover="handleSubMenuMouseOver('communaute')"
          @mouseleave="handleSubMenuMouseLeave('communaute')"
        >
          <div
            class="badge-container"
            :class="{ 'with-badge': receivedInvitationsCount > 0 }"
          >
            <!-- Texte pour redirection -->
            <div class="menu-item" @click.stop="gotoPage('/communaute')">
              Communauté
            </div>

            <!-- Affichage du badge avec le nombre d'invitations reçues -->
            <span v-if="receivedInvitationsCount > 0" class="badge">
              {{ receivedInvitationsCount }}
            </span>
          </div>
          <!-- Flèche pour déplier le sous-menu -->
          <img
            class="arrows"
            :src="
              showSubMenuCommunaute
                ? require('@/assets/icons/arrow-up.svg')
                : require('@/assets/Icon-black-54.svg')
            "
            @click.stop="toggleSubMenu('communaute')"
          />
          <!-- ############ submenu ############ -->
          <transition name="fade">
            <ul
              v-if="showSubMenuCommunaute"
              class="submenu"
              :class="{ show: showSubMenuCommunaute }"
            >
              <li
                :class="{ active: activePage === 'recruiter-search' }"
                @click.stop="gotoPage('/communaute')"
              >
                Réseaux
              </li>
              <li
                :class="{ active: activePage === 'friends' }"
                @click.stop="gotoPage('/amis')"
              >
                <div
                  class="badge-container"
                  :class="{ 'with-badge': receivedInvitationsCount > 0 }"
                >
                  Mon réseau
                  <span v-if="receivedInvitationsCount > 0" class="badge">
                    {{ receivedInvitationsCount }}
                  </span>
                </div>
              </li>
              <li
                v-if="userRole === 'recruiter'"
                :class="{ active: activePage === 'recruiter-favorite' }"
                @click.stop="gotoPage('/recruteur/favoris')"
              >
                Mes favoris
              </li>

              <li
                v-if="userRole === 'recruiter'"
                :class="{ active: activePage === 'recruiter-messaging' }"
                @click.stop="gotoPage('/recruteur/messagerie')"
              >
                <div
                  class="badge-container"
                  :class="{ 'with-badge': unreadMessagesCount > 0 }"
                >
                  Messagerie
                  <span v-if="unreadMessagesCount > 0" class="badge">
                    {{ unreadMessagesCount }}
                  </span>
                </div>
              </li>
              <li
                v-if="userRole === 'applicant'"
                :class="{ active: activePage === 'call' }"
                @click.stop="gotoPage('/messagerie')"
              >
                <div
                  class="badge-container"
                  :class="{ 'with-badge': unreadMessagesCount > 0 }"
                >
                  Messagerie
                  <span v-if="unreadMessagesCount > 0" class="badge">
                    {{ unreadMessagesCount }}
                  </span>
                </div>
              </li>
            </ul>
          </transition>
        </li>

        <!-- ########### Actualités ########### -->
        <li
          :class="{ active: activePage === 'blog' }"
          @click.stop="gotoPage('/actualites')"
        >
          Actualités
        </li>

        <!-- ########### Tarifs ########### -->
        <!--li
          :class="{ active: activePage === 'prices' }"
          @click.stop="gotoPage('/tarifs')"
        >
          Tarifs
        </li-->
        <li
          v-if="!isLoggedIn"
          @click.stop="gotoPage('/connexion')"
        >
          S'inscrire / Se connecter
        </li>
        <li
          v-else
          :class="{
            'li-with-submenu': activeSubMenu === 'profil',
            active:
              activePage ===
              (userRole === 'recruiter' ? 'profil-recruiter' : 'profil'),
            avatar: true,
          }"
          @mouseover="handleSubMenuMouseOver('profil')"
          @mouseleave="handleSubMenuMouseLeave('profil')"
        >
          <div class="avatar-img">
            <UserAvatar v-if="user" :user="user" :width="40" />
          </div>
          <!-- Texte pour redirection -->
          <div
            class="menu-item"
            @click.stop="
              gotoPage(
                userRole === 'recruiter' ? '/recruteur/profil' : '/profil'
              )
            "
          >
            Profil
          </div>

          <!-- Flèche pour déplier le sous-menu -->
          <img
            class="arrows"
            :src="
              showSubMenuProfil
                ? require('@/assets/icons/arrow-up.svg')
                : require('@/assets/Icon-black-54.svg')
            "
            @click.stop="toggleSubMenu('profil')"
          />
          <!-- ############ submenu ############ -->
          <transition name="fade">
            <ul
              v-if="showSubMenuProfil"
              class="submenu submenu-up"
              :class="{ show: showSubMenuProfil }"
            >
              <!-- rajoute de block pour acceder a mon profil 1 cas -->
              <!-- <li
                :class="{ active: activePage === 'profil' }"
                @click.stop="gotoPage('/profil')"
              >
                Mon profil
              </li> -->

              <!-- rajoute de block pour acceder a mon profil edition  deuxieme cas-->
              <li
                :class="{ active: activePage === 'profile-edition' }"
                @click.stop="gotoPage('/profil/edition')"
              >
                Mon profil
              </li>

              <li
                v-if="userRole === 'recruiter'"
                :class="{ active: activePage === 'recruiter-dashboard' }"
                @click.stop="gotoPage('/recruteur/tableau-de-bord')"
              >
                Tableau de bord
              </li>
              <li
                v-else
                :class="{ active: activePage === 'dashboard' }"
                @click.stop="gotoPage('/tableau-de-bord')"
              >
                Tableau de bord
              </li>

              <li
                v-if="userRole === 'recruiter'"
                :class="{ active: activePage === 'recruiter-offer' }"
                @click.stop="gotoPage('/recruteur/offres')"
              >
                Mes offres
              </li>
              <li
                v-if="isGoogle === false"
                :class="{ active: activePage === 'settings' }"
                @click.stop="gotoPage('/parametres')"
              >
                Paramètres
              </li>
              <li @click.stop="handleLogout">Se déconnecter</li>
            </ul>
          </transition>
        </li>
      </ul>
    </div>
  </nav>
</template>

<script>
  import { logout } from '@/services/account.service';
  import gotoPage from '@/utils/router';
  import { mapGetters, mapState } from 'vuex';
  import UserAvatar from '../../views-models/profil/UserAvatar.vue';
  import { getTotalUnreadCount } from '@/services/unread-messages.service';

  export default {
    name: 'NavbarApp',
    components: {
      UserAvatar,
    },
    data() {
      return {
        user: this.getUser || {},
        activePage: this.$route.name || 'home', // Set active page for lasting hover effect ("breadcrumb")
        isMenuOpen: false, // Toggle state of navbar for navigation
        showSubMenuProfil: false,
        showSubMenuCommunaute: false,
        showSubMenuOffer: false,
        activeSubMenu: null,
        isViewingAsRecruiter: false, // état pour le switch
        unreadMessagesCount: 0, // Compteur de messages non lus
        isMobile: false,
      };
    },
    async mounted() {
      try {
        this.photoUrl = this.getUser.photo;

        // Initialiser le compteur de messages non lus
        this.unreadMessagesCount = getTotalUnreadCount();

        // Écouter les événements de mise à jour des messages non lus
        window.addEventListener(
          'unread-messages-updated',
          this.handleUnreadMessagesUpdated
        );

        this.checkMobile();
        window.addEventListener('resize', this.checkMobile);
      } catch (error) {
        //console.error('Error fetching profile section data:', error);
      }
    },

    beforeUnmount() {
      // Supprimer l'écouteur d'événements
      window.removeEventListener(
        'unread-messages-updated',
        this.handleUnreadMessagesUpdated
      );
      window.removeEventListener('resize', this.checkMobile);
    },
    computed: {
      ...mapGetters([
        'isLoggedIn',
        'isGoogle',
        'getUser',
        'userRole',
        'receivedInvitationsCount',
      ]),
      ...mapState(['receivedInvitationsCount']),

      userTypeClass() {
        if (this.isLoggedIn) {
          return this.userRole === 'recruiter' ? 'navbar-recruiter' : 'navbar';
        }
        return this.isViewingAsRecruiter ? 'navbar-recruiter' : 'navbar';
      },
    },
    watch: {
      '$route.name'(newName) {
        this.activePage = newName;
      },
      getUser: {
        immediate: true,
        handler(newUser) {
          this.user = newUser || {};
        },
      },
    },
    methods: {
      handleLogout() {
        try {
          logout();
        } catch (error) {
          //console.log(error);
        }
        this.closeOverlay();
      },

      // Set active page for breadcrumb and use vue router to switch page
      gotoPage(page) {
        // Fermer tous les sous-menus
        this.showSubMenuProfil = false;
        this.showSubMenuCommunaute = false;
        this.showSubMenuOffer = false;
        this.closeOverlay();
        this.isMenuOpen = false;
        document.body.classList.remove('no-scroll');

        // Définir la page active
        this.activePage = page;

        // Appeler la fonction de navigation
        gotoPage(page);
      },
      // basculer entre le mode recruteur et candidat
      toggleView() {
        this.isViewingAsRecruiter = !this.isViewingAsRecruiter;
        this.$emit('view-switched', this.isViewingAsRecruiter);
      },

      // Fonction pour ouvrir/fermer le sous-menu
      handleSubMenuMouseOver(submenu) {
        if (window.innerWidth >= 768) {
          if (submenu === 'offer') {
            this.showSubMenuOffer = true;
          } else if (submenu === 'communaute') {
            this.showSubMenuCommunaute = true;
          } else if (submenu === 'profil') {
            this.showSubMenuProfil = true;
          }
        }

        // Gestion de l'overlay
        const overlay = document.querySelector('.overlay');

        if (
          this[
            `showSubMenu${submenu.charAt(0).toUpperCase() + submenu.slice(1)}`
          ]
        ) {
          overlay?.classList.add('active'); // Active l'overlay si le sous-menu est visible
        } else {
          overlay?.classList.remove('active'); // Désactive l'overlay si aucun sous-menu n'est visible
        }
      },

      handleSubMenuMouseLeave(submenu) {
        if (window.innerWidth >= 768) {
          if (submenu === 'offer') {
            this.showSubMenuOffer = false;
          } else if (submenu === 'communaute') {
            this.showSubMenuCommunaute = false;
          } else if (submenu === 'profil') {
            this.showSubMenuProfil = false;
          }
        }
      },

      // Fonction pour basculer la visibilité du sous-menu sélectionné
      toggleSubMenu(submenu) {
        // Vérifiez si le sous-menu est déjà actif
        if (this.activeSubMenu === submenu) {
          // Fermez le sous-menu si vous cliquez à nouveau
          this.activeSubMenu = null;
        } else {
          // Sinon, définissez le sous-menu comme actif
          this.activeSubMenu = submenu;
        }

        // Basculez la visibilité du sous-menu
        if (submenu === 'offer') {
          this.showSubMenuOffer = !this.showSubMenuOffer;
        } else if (submenu === 'communaute') {
          this.showSubMenuCommunaute = !this.showSubMenuCommunaute;
        } else if (submenu === 'profil') {
          this.showSubMenuProfil = !this.showSubMenuProfil;
        }

        // Mettez à jour la visibilité de l'overlay
        this.updateOverlayVisibility();
      },

      toggleMenu() {
        this.isMenuOpen = !this.isMenuOpen;
        if (this.isMenuOpen && this.isMobile) {
          document.body.classList.add('no-scroll');
        } else {
          document.body.classList.remove('no-scroll');
        }
        if (this.isMenuOpen) {
          this.showSubMenuProfil = false;
          this.showSubMenuCommunaute = false;
          this.showSubMenuOffer = false;
          this.closeOverlay();
        }
      },

      closeOverlay() {
        const overlay = document.querySelector('.overlay');
        overlay?.classList.remove('active');
      },

      handleSubMenuClose() {
        this.showSubMenuProfil = false;
        this.showSubMenuCommunaute = false;
        this.showSubMenuOffer = false;
        this.closeOverlay();
        this.isMenuOpen = false;
        document.body.classList.remove('no-scroll');
      },

      // Mettre à jour la visibilité de l'overlay
      updateOverlayVisibility() {
        const overlay = document.querySelector('.overlay');
        if (
          this.showSubMenuOffer ||
          this.showSubMenuCommunaute ||
          this.showSubMenuProfil
        ) {
          overlay?.classList.add('active'); // Affiche l'overlay si un sous-menu est ouvert
        } else {
          overlay?.classList.remove('active'); // Cache l'overlay si aucun sous-menu n'est ouvert
        }
      },

      /**
       * Gère les mises à jour des messages non lus
       * @param {CustomEvent} event - Événement contenant les détails des messages non lus
       */
      handleUnreadMessagesUpdated(event) {
        if (event && event.detail) {
          this.unreadMessagesCount = event.detail.totalCount;
        }
      },

      checkMobile() {
        this.isMobile = window.innerWidth < 768;
        if (!this.isMobile) {
          this.isMenuOpen = false;
          document.body.classList.remove('no-scroll');
        }
      },
    },
  };
</script>

<style scoped>
  .beta-section h2 {
    width: -1rem;
    color: var(--primary-1);
    font-size: 20px;
  }

  nav {
    position: sticky;
    top: 0;
    left: 0;
    width: 100vw;
    margin: 0;
    padding: 0;
    z-index: 6000;
    background: var(--navbar-bg-color);
  }

  .overlay {
    display: none; /* Par défaut, caché partout */
    z-index: 5500;
  }

  .overlay.active {
    display: block; /* S'affiche uniquement lorsqu'il est activé */
  }

  .menu li {
    position: relative; /* Permet de positionner le sous-menu par rapport à ce <li> */
  }

  /* Animation Fade */
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease-in-out;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }

  .fade-enter-to,
  .fade-leave-from {
    opacity: 1;
  }

  .submenu {
    display: none; /* Cache le sous-menu par défaut */
    position: absolute; /* Positionne le sous-menu en dehors du flux normal */
    top: 100%; /* Place le sous-menu juste en dessous de l'élément parent */
    left: 0; /* Aligne le sous-menu avec le côté gauche de l'élément parent */
    background-color: var(--navbar-bg-color); /* Couleur de fond */
    z-index: 6000; /* Assurez-vous que le sous-menu est au-dessus d'autres éléments */
    color: var(--navbar-font-color);
    height: fit-content;
  }

  .submenu li {
    padding: 10px; /* Espacement des items dans le sous-menu */
    background-color: var(--navbar-bg-color);
    cursor: pointer;
    width: 100%;
    height: 56px;
  }
  .submenu li.active {
    background-color: var(--primary-1b2);
  }

  /* Affiche les sous-menus quand l'état `showSubMenu` est vrai */
  .submenu.show {
    display: block;
  }

  .submenu-up {
    top: auto !important;
    bottom: 100%;
    left: 0;
    transform: translateY(-8px);
    /* Optionnel : petite ombre pour la lisibilité */
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.08);
  }

  .menu {
    background-color: var(--navbar-bg-color);
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 6000;
  }

  .menu.hidden {
    display: none;
  }

  ul {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    list-style-type: none;
    justify-content: space-around;
  }

  li {
    width: 100%;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease-out;
    font-size: var(--navbar-font-size);
  }

  li.active {
    background-color: var(--primary-1b2);
  }

  li:hover {
    background-color: var(--gray-light);
  }

  .navbar {
    position: relative;
    min-height: 70px;
    max-height: 100vh;
    width: 100vw;
    left: 0;
    right: 0;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    background-color: var(--navbar-bg-color);
    color: var(--navbar-font-color);
    overflow: visible;
    z-index: 6001;
  }

  .navbar-recruiter .img-container-circle {
    background-color: var(--yellow-100);
    border-radius: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 64px;
    width: 64px;
    z-index: 6001;
  }

  .img-container {
    height: 100%;
    min-width: 205px;
    display: flex;
    align-items: flex-end;
    padding-left: 15px;
    cursor: pointer;
    z-index: 6001;
  }

  .avatar img:first-child {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30px;
    width: 30px;
    border-radius: 50%;
  }

  .avatar img {
    height: 30px;
    width: 30px;
  }

  .avatar-img {
    border-radius: 50%;
    border: 2px solid var(--gray-light);
    margin-right: 10px;
  }

  .grey-bg {
    background-color: var(--gray-light);
    justify-content: center;
    align-items: center;
    text-align: center;
  }

  /* hamburger & avatar */
  .hamburger-container {
    position: absolute;
    top: -5px;
    right: 10px;
    cursor: pointer;
    z-index: 6100;
  }
  .hamburger-container img {
    height: 50px;
  }

  #mobile {
    position: absolute;
    top: 20px;
    right: 0px;
    display: flex;
    gap: 5px;
  }

  #avatar {
    display: flex;
    height: 30px;
  }

  .hidden {
    display: none;
  }

  .badge {
    position: absolute;
    top: -5px; /* Positionnement en haut */
    right: -20px; /* Décalage vers la droite */
    background-color: red;
    color: white; /* Texte blanc pour le contraste */
    border-radius: 50%; /* Forme circulaire */
    width: 18px; /* Taille fixe */
    height: 18px;
    font-size: 12px; /* Taille de police ajustée */
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2); /* Ombre légère */
    z-index: 10; /* Pour s'assurer qu'il est au-dessus du reste */
  }

  .badge-container {
    position: relative; /* Permet d'aligner le badge */
    display: inline-block; /* S'adapte à la taille du texte */
  }
  .with-badge {
    margin-right: 20px;
  }

  .arrows {
    height: 30px;
    width: 30px;
  }

  .view-switch {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-left: auto;
    margin-right: 10px;
  }
  .view-switch button {
    background-color: var(--primary-1b2);
    font-size: var(--navbar-font-size) !important;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    cursor: pointer;
    border-radius: 5px;
    transition:
      background-color 0.3s ease,
      transform 0.2s ease;
  }
  .view-switch button:hover {
    background-color: var(--primary-hover);
    transform: scale(1.05);
  }
  .view-switch button:active {
    background-color: var(--primary-active);
    transform: scale(0.95);
  }

  @media screen and (max-width: 768px) {
    .view-switch {
      margin-right: 5px;
    }
    .view-switch button {
      padding: 0.3rem 0.7rem;
      font-size: 0.8rem;
    }

    .overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      backdrop-filter: blur(1px);
      z-index: 5500;
      display: none; /* Par défaut, masqué */
      transition: opacity 0.3s ease;
    }

    .overlay.active {
      display: block; /* Affiché lorsque l'overlay est actif */
    }

    .menu .li-with-submenu {
      z-index: 5500;
    }

    .menu {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      flex-direction: column;
      background: var(--navbar-bg-color);
      padding-top: 80px;
      max-height: 100vh;
      transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      overflow: hidden;
      display: flex;
      z-index: 6000;
    }
    .menu.hidden {
      max-height: 0;
      padding-top: 0;
      display: flex;
    }
    body.no-scroll {
      overflow: hidden;
    }

    .hamburger-container {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 7000;
      background: var(--navbar-bg-color);
      border-radius: 50%;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      padding: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .hamburger-container img {
      height: 40px;
      width: 40px;
    }

    .navbar {
      width: 100vw;
      min-width: 0;
      max-width: 100vw;
      left: 0;
      right: 0;
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    nav {
      width: 100vw;
      left: 0;
      right: 0;
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
  }

  @media screen and (min-width: 769px) {
    .menu {
      position: initial;
      display: flex !important;
      flex-direction: row;
      height: fit-content;
      min-height: 80px;
      max-height: none;
      padding-top: 0;
      box-shadow: none;
      overflow: visible;
    }
    .menu.hidden {
      display: flex !important;
      max-height: none;
      padding-top: 0;
    }
    li.avatar {
      display: flex !important;
      visibility: visible !important;
      opacity: 1 !important;
      height: 56px !important;
      min-width: 120px !important;
      max-width: 220px;
      align-items: center !important;
      justify-content: center !important;
      position: relative !important;
      z-index: 7000 !important;
    }
    .avatar-img {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }
    /* Correction du sous-menu profil */
    .submenu.submenu-up {
      display: block !important;
      top: 100% !important;
      bottom: auto !important;
      left: 0;
      transform: none;
      min-width: 180px;
      background: var(--navbar-bg-color);
      z-index: 8000 !important;
      box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    }
    .submenu.submenu-up.show {
      display: block !important;
    }
  }

  @media screen and (min-width: 992px) {
    ul {
      flex-direction: row;
    }

    .navbar {
      flex-direction: row;
      align-items: center;
      padding-top: 0px;
      z-index: 6001;
    }

    .menu {
      position: initial;
      display: flex;
      flex-direction: row;
      height: fit-content;
      min-height: 80px;
    }

    .submenu {
      top: 100%; /* Toujours en dessous du parent */
      left: 0;
      height: fit-content;
    }

    .hamburger-container {
      display: none;
    }

    .img-container {
      justify-content: center;
      padding-left: 0px;
      z-index: 6001;
    }

    #mobile {
      display: none;
    }
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .navbar {
      padding: 0 10%;
      margin: 0 auto;
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .navbar {
      padding: 0 20%;
      margin: 0 auto;
    }
  }
</style>
