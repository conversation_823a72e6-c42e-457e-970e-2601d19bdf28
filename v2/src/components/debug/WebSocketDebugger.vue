<template>
  <div class="websocket-debugger">
    <button @click="toggleDebugger" class="debug-button">
      {{ isActive ? 'Désactiver' : 'Activer' }} le débogage WebSocket
    </button>
    <div v-if="isActive" class="debug-status">
      Débogage WebSocket actif
    </div>
  </div>
</template>

<script>
import { initWebSocketDebug } from '@/services/websocket-debug';

export default {
  name: 'WebSocketDebugger',
  data() {
    return {
      isActive: false
    };
  },
  methods: {
    toggleDebugger() {
      if (!this.isActive) {
        // Activer le débogage
        initWebSocketDebug();
        this.isActive = true;
        //console.log('[WEBSOCKET DEBUGGER] Débogage WebSocket activé');
      } else {
        // Désactiver le débogage (recharger la page)
        this.isActive = false;
        //console.log('[WEBSOCKET DEBUGGER] Débogage WebSocket désactivé (rechargement nécessaire)');
        alert('Pour désactiver complètement le débogage WebSocket, veuillez recharger la page.');
      }
    }
  }
};
</script>

<style scoped>
.websocket-debugger {
  position: fixed;
  bottom: 80px;
  right: 20px;
  z-index: 1000;
}

.debug-button {
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 10px 15px;
  cursor: pointer;
  font-weight: bold;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.debug-button:hover {
  background-color: #0b7dda;
}

.debug-status {
  margin-top: 10px;
  padding: 5px 10px;
  background-color: #4CAF50;
  color: white;
  border-radius: 5px;
  text-align: center;
  font-size: 12px;
}
</style>
