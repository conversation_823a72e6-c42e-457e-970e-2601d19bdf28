<template>
  <v-switch
    v-model="isActive"
    class="custom-switch"
    @change="handleChange"
    inset
    :class="{ 'switch-on': isActive, 'switch-off': !isActive }"
  ></v-switch>
</template>

<script>
export default {
  name: 'CustomSwitch',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isActive: this.value,
    };
  },
  methods: {
    handleChange() {
      if (this.isActive !== this.value) {
        this.$emit('input', this.isActive);
        this.$emit('change', this.isActive);
      }
    },
  },
  watch: {
    value(newValue) {
      this.isActive = newValue;
    },
  },
};
</script>

<style scoped>
.custom-switch {
  position: relative;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
}

.custom-switch .v-input--selection-controls__ripple {
  display: none;
}

.custom-switch .v-switch__track {
  width: 56px;
  height: 24px;
  border-radius: 72px;
  padding: 0;
  transition: background-color 0.4s ease;
}

.custom-switch .v-switch__thumb {
  width: 23px;
  height: 23px;
  border-radius: 50%;
  background-color: rgb(246, 179, 55);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease, background-color 0.4s ease;
}

.switch-on .v-switch__track {
  background-color: #26282b; /* Couleur du track quand activé */
}

.switch-off .v-switch__track {
  background-color: #e5e5e5; /* Couleur du track quand désactivé */
}

.switch-on .v-switch__thumb {
  transform: translateX(32px); /* Position du bouton activé */
}

.switch-off .v-switch__thumb {
  transform: translateX(0); /* Position du bouton désactivé */
}
</style>