<template>
  <div class="test-incoming-call">
    <button @click="simulateIncomingCall" class="test-button">
      Simuler un appel entrant
    </button>
  </div>
</template>

<script>
import { testIncomingCall, resetCallState, checkAndResetGlobalWebSocket } from '@/services/video-call.service';

export default {
  name: 'TestIncomingCall',
  methods: {
    async simulateIncomingCall() {
      //console.log('%c[APPEL] Simulation d\'un appel entrant pour le débogage', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

      // Réinitialiser l'état de l'appel avant de simuler un nouvel appel
      resetCallState();

      // Vérifier et réinitialiser le WebSocket global si nécessaire
      const webSocketOk = await checkAndResetGlobalWebSocket();
      if (!webSocketOk) {
        console.warn('%c[APPEL] WebSocket global non disponible, la simulation pourrait ne pas fonctionner correctement', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');
      }

      // Simuler un appel entrant avec un ID et un nom d'utilisateur fictifs
      const success = await testIncomingCall('123', 'Utilisateur Test');

      //console.log('%c[APPEL] Simulation d\'appel entrant:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', success ? 'Réussie' : 'Échouée');
    }
  }
};
</script>

<style scoped>
.test-incoming-call {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.test-button {
  background-color: #f6b337;
  color: black;
  border: none;
  border-radius: 5px;
  padding: 10px 15px;
  cursor: pointer;
  font-weight: bold;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.test-button:hover {
  background-color: #e5a730;
}
</style>
