<template>
  <div v-if="isVisible" class="incoming-call-notification">
    <div class="incoming-call-modal">
      <div class="incoming-call-icon-container swing-icon">
        <div class="ripple-effect"></div>
        <img
          src="@/assets/icons/call.svg"
          alt="Appel entrant"
          class="call-icon"
        />
      </div>
      <h3>Appel entrant</h3>
      <p v-if="callerName">{{ callerName }}</p>
      <div class="call-actions">
        <button @click="acceptCall" class="accept-btn">
          <img src="@/assets/icons/accept-call.svg" alt="Accepter" />
          Accepter
        </button>
        <button @click="rejectCall" class="reject-btn">
          <img src="@/assets/icons/reject-call.svg" alt="Refuser" />
          Refuser
        </button>
      </div>
    </div>
  </div>
</template>

<script>
  import { acceptCall, rejectCall } from '@/services/video-call.service';
  import { getUserById } from '@/services/account.service';
  import { toaster } from '@/utils/toast/toast';
  import { initializePrivateWebSocket } from '@/services/conversation-websocket.service';

  export default {
    name: 'GlobalIncomingCallNotification',
    data() {
      return {
        isVisible: false,
        callerName: '',
        callerId: null,
        callType: 'video', // Par défaut, on considère que c'est un appel vidéo
        offer: null,
        callData: null,
        webSocketConnection: null, // Connexion WebSocket privée pour écouter les messages end_call
      };
    },
    created() {
      //console.log('%c[APPEL] GlobalIncomingCallNotification: Composant créé', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
      // Ajouter les écouteurs d'événements dès la création du composant
      this.addEventListeners();
      // Initialiser le WebSocket privé pour écouter les messages end_call
      this.initializeWebSocket();
    },
    mounted() {
      //console.log('%c[APPEL] GlobalIncomingCallNotification: Composant monté', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
      // S'assurer que les écouteurs sont bien ajoutés
      this.addEventListeners();
    },
    beforeDestroy() {
      //console.log('%c[APPEL] GlobalIncomingCallNotification: Nettoyage des écouteurs', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
      // Nettoyer les écouteurs d'événements
      this.removeEventListeners();
      // Nettoyer le WebSocket
      this.cleanupWebSocket();
    },
    methods: {
      addEventListeners() {
        //console.log('%c[APPEL] GlobalIncomingCallNotification: Ajout des écouteurs d\'\u00e9vénements', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
        // Utiliser bind pour s'assurer que this fait référence au composant
        this.boundHandleIncomingCall = this.handleIncomingCall.bind(this);
        this.boundHideNotification = this.hideNotification.bind(this);

        // Écouter les événements d'appel entrant
        window.addEventListener('incoming-call', this.boundHandleIncomingCall);
        window.addEventListener('call-rejected', this.boundHideNotification);
        window.addEventListener('call-ended', this.boundHideNotification);
        window.addEventListener('call-accepted', this.boundHideNotification);

        //console.log('%c[APPEL] GlobalIncomingCallNotification: Écouteurs ajoutés avec succès', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

        // Tester si les écouteurs sont bien ajoutés
        const hasIncomingCallListener = this.hasEventListener('incoming-call');
        //console.log('%c[APPEL] GlobalIncomingCallNotification: Écouteur incoming-call présent:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', hasIncomingCallListener);
      },
      removeEventListeners() {
        //console.log('%c[APPEL] GlobalIncomingCallNotification: Suppression des écouteurs', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
        // Nettoyer les écouteurs d'événements
        window.removeEventListener('incoming-call', this.boundHandleIncomingCall);
        window.removeEventListener('call-rejected', this.boundHideNotification);
        window.removeEventListener('call-ended', this.boundHideNotification);
        window.removeEventListener('call-accepted', this.boundHideNotification);
      },
      hasEventListener(eventName) {
        // Cette fonction est une approximation, car JavaScript ne fournit pas de méthode native
        // pour vérifier si un écouteur est attaché
        try {
          const testEvent = new CustomEvent(eventName);
          let eventCaught = false;

          const tempListener = () => { eventCaught = true; };
          window.addEventListener(eventName, tempListener);
          window.dispatchEvent(testEvent);
          window.removeEventListener(eventName, tempListener);

          return eventCaught;
        } catch (error) {
          //console.error('%c[APPEL] Erreur lors de la vérification de l\'\u00e9couteur:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
          return false;
        }
      },
      async handleIncomingCall(event) {
        try {
          //console.log('%c[APPEL] GlobalIncomingCallNotification: Appel entrant reçu', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', event.detail);

          if (!event || !event.detail) {
            //console.error('%c[APPEL] GlobalIncomingCallNotification: Événement invalide', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', event);
            return;
          }

          const { senderId, senderName, call_type, offer } = event.detail;

          if (!senderId) {
            //console.error('%c[APPEL] GlobalIncomingCallNotification: ID d\'appelant manquant', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            return;
          }

          // Stocker toutes les données de l'appel
          this.callData = event.detail;
          this.callerId = senderId;
          this.callType = call_type || 'video';
          this.offer = offer;

          //console.log('%c[APPEL] GlobalIncomingCallNotification: ID de l\'appelant stocké:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', this.callerId);

          //console.log('%c[APPEL] GlobalIncomingCallNotification: Données d\'appel stockées', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', {
          //  callerId: this.callerId,
          //  callType: this.callType,
          //  hasOffer: !!this.offer
          //});

          // Si le nom de l'appelant est fourni, l'utiliser
          if (senderName) {
            this.callerName = senderName;
            //console.log('%c[APPEL] GlobalIncomingCallNotification: Nom de l\'appelant fourni:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', senderName);
          }
          // Sinon, essayer de récupérer les informations de l'appelant
          else if (senderId) {
            try {
              //console.log('%c[APPEL] GlobalIncomingCallNotification: Récupération des infos de l\'appelant avec ID:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', senderId);
              const caller = await getUserById(senderId);
              if (caller) {
                this.callerName = `${caller.first_name} ${caller.last_name}`;
                //console.log('%c[APPEL] GlobalIncomingCallNotification: Infos de l\'appelant récupérées:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', this.callerName);
              } else {
                console.warn('%c[APPEL] GlobalIncomingCallNotification: Appelant non trouvé', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');
                this.callerName = 'Utilisateur inconnu';
              }
            } catch (err) {
              //console.error('%c[APPEL] GlobalIncomingCallNotification: Erreur lors de la récupération des informations de l\'appelant:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', err);
              this.callerName = 'Utilisateur inconnu';
            }
          } else {
            this.callerName = 'Utilisateur inconnu';
          }

          // Rendre le popup visible
          this.isVisible = true;
          //console.log('%c[APPEL] GlobalIncomingCallNotification: Popup affiché pour l\'appelant:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', this.callerName);

          // Vérifier que le popup est bien visible
          this.$nextTick(() => {
            if (!this.isVisible) {
              //console.error('%c[APPEL] GlobalIncomingCallNotification: Le popup n\'est pas visible après avoir été défini comme tel!', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
              // Forcer l'affichage
              this.isVisible = true;
            } else {
              //console.log('%c[APPEL] GlobalIncomingCallNotification: Popup correctement affiché', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
            }
          });

          // Ne pas jouer de son de notification ici car la sonnerie d'appel est déjà jouée dans MessagingPage.vue
          // this.playNotificationSound();

          // Afficher une notification toast en plus du popup
          // toaster.showInfoPopup(`Appel entrant de ${this.callerName}`);

          // Afficher une alerte dans la console pour attirer l'attention
          //console.log('%c[APPEL ENTRANT] Vous avez un appel entrant de ' + this.callerName + ' !', 'background: #f6b337; color: black; font-size: 16px; padding: 5px 10px; border-radius: 3px; font-weight: bold;');
        } catch (error) {
          //console.error('%c[APPEL] GlobalIncomingCallNotification: Erreur lors du traitement de l\'appel entrant:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
        }
      },
      hideNotification() {
        //console.log('%c[APPEL] GlobalIncomingCallNotification: Masquage du popup', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

        // Masquer la notification
        this.isVisible = false;

        // Arrêter la sonnerie
        this.stopNotificationSound();

        // Vérifier que la notification est bien masquée
        this.$nextTick(() => {
          if (this.isVisible) {
            //console.error('%c[APPEL] GlobalIncomingCallNotification: La notification est toujours visible après hideNotification!', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            // Forcer le masquage
            this.isVisible = false;
          }
        });
      },
      acceptCall() {
        try {
          //console.log('%c[APPEL] GlobalIncomingCallNotification: Acceptation de l\'appel', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          if (!this.callerId) {
            //console.error('%c[APPEL] GlobalIncomingCallNotification: Impossible d\'accepter l\'appel, ID d\'appelant manquant', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            toaster.showErrorPopup("Impossible d'accepter l'appel");
            return;
          }

          // 🧠 Ajout à faire ici : envoyer les données à handleIncomingCall()
          const callData = {
            senderId: this.callerId,
            senderName: this.callerName,
            call_type: this.callType,
            offer: this.offer
          };

          // Appeler manuellement handleIncomingCall avec les bonnes données
          import('@/services/video-call.service').then(({ handleIncomingCall }) => {
            handleIncomingCall(callData).then(() => {
              // Ensuite, on peut appeler acceptCall du service
              acceptCall();
            });
          });

          // Masquer la notification
          this.isVisible = false;
          this.stopNotificationSound();
        } catch (error) {
          //console.error('%c[APPEL] GlobalIncomingCallNotification: Erreur lors de l\'acceptation de l\'appel:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
          toaster.showErrorPopup("Erreur lors de l'acceptation de l'appel");
        }
      },

      rejectCall() {
        try {
          //console.log('%c[APPEL] GlobalIncomingCallNotification: Rejet de l\'appel', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

          if (!this.callerId) {
            //console.error('%c[APPEL] GlobalIncomingCallNotification: Impossible de rejeter l\'appel, ID d\'appelant manquant', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            this.isVisible = false;
            this.stopNotificationSound();
            return;
          }

          // Masquer immédiatement la notification pour éviter les doubles clics
          this.isVisible = false;
          this.stopNotificationSound();

          // Appeler directement rejectCall sans passer par handleIncomingCall
          rejectCall();
          //console.log('%c[APPEL] GlobalIncomingCallNotification: Appel rejeté', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
        } catch (error) {
          //console.error('%c[APPEL] GlobalIncomingCallNotification: Erreur lors du rejet de l\'appel:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
          this.isVisible = false;
          this.stopNotificationSound();
        }
      },
      playNotificationSound() {
        // Ne rien faire car la sonnerie d'appel est déjà jouée dans MessagingPage.vue
        //console.log('%c[APPEL] GlobalIncomingCallNotification: Lecture du son de notification désactivée', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
        // Ne pas jouer de son ici pour éviter la duplication avec la sonnerie d'appel
      },
      stopNotificationSound() {
        //console.log('%c[APPEL] GlobalIncomingCallNotification: Arrêt du son de notification', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

        // Utiliser la fonction globale pour arrêter la sonnerie si elle existe
        if (window.stopCallRingtone && typeof window.stopCallRingtone === 'function') {
          //console.log('%c[APPEL] GlobalIncomingCallNotification: Appel de la fonction globale stopCallRingtone', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          window.stopCallRingtone();
        } else {
          // Sinon, émettre un événement pour arrêter la sonnerie
          //console.log('%c[APPEL] GlobalIncomingCallNotification: Émission de l\'événement stop-ringtone', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          window.dispatchEvent(new CustomEvent('stop-ringtone'));
        }
      },

      // Initialise le WebSocket privé pour écouter les messages end_call
      initializeWebSocket() {
        try {
          //console.log('%c[APPEL] GlobalIncomingCallNotification: Initialisation du WebSocket privé', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

          // Initialiser le WebSocket privé
          this.webSocketConnection = initializePrivateWebSocket();

          if (this.webSocketConnection) {
            //console.log('%c[APPEL] GlobalIncomingCallNotification: WebSocket privé initialisé avec succès', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

            // Configurer l'écouteur de messages
            this.setupWebSocketListener();
          } else {
            //console.error('%c[APPEL] GlobalIncomingCallNotification: Échec de l\'initialisation du WebSocket privé', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
          }
        } catch (error) {
          //console.error('%c[APPEL] GlobalIncomingCallNotification: Erreur lors de l\'initialisation du WebSocket privé:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
        }
      },

      // Configure l'écouteur de messages WebSocket
      setupWebSocketListener() {
        if (!this.webSocketConnection) {
          //console.error('%c[APPEL] GlobalIncomingCallNotification: WebSocket non initialisé', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
          return;
        }

        // Sauvegarder l'ancien gestionnaire onmessage s'il existe
        const oldOnMessage = this.webSocketConnection.onmessage;

        // Configurer le nouveau gestionnaire d'événements
        this.webSocketConnection.onmessage = (event) => {
          try {
            // Analyser les données reçues
            const data = JSON.parse(event.data);

            // Vérifier si le message est de type end_call
            if (data.type === 'end_call') {
              //console.log('%c[APPEL] GlobalIncomingCallNotification: Message end_call reçu', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', data);

              // Vérifier si la notification est visible
              if (this.isVisible) {
                //console.log('%c[APPEL] GlobalIncomingCallNotification: Notification visible lors de la réception de end_call', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

                // Vérifier si l'ID de l'appelant correspond à l'ID de l'expéditeur du message end_call
                // OU si l'ID de l'appelant correspond à l'ID du destinataire du message end_call
                if (this.callerId === data.sender_id || this.callerId === data.receiver_id) {
                  //console.log('%c[APPEL] GlobalIncomingCallNotification: L\'appelant a raccroché pendant que la notification était affichée', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
                  //console.log('%c[APPEL] GlobalIncomingCallNotification: ID appelant:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', this.callerId);
                  //console.log('%c[APPEL] GlobalIncomingCallNotification: ID expéditeur end_call:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', data.sender_id);

                  // Rejeter automatiquement l'appel
                  this.autoRejectCall();
                } else {
                  //console.log('%c[APPEL] GlobalIncomingCallNotification: Message end_call reçu mais les IDs ne correspondent pas', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
                  //console.log('%c[APPEL] GlobalIncomingCallNotification: ID appelant:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', this.callerId);
                  //console.log('%c[APPEL] GlobalIncomingCallNotification: ID expéditeur end_call:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', data.sender_id);
                  //console.log('%c[APPEL] GlobalIncomingCallNotification: ID destinataire end_call:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', data.receiver_id);
                }
              } else {
                //console.log('%c[APPEL] GlobalIncomingCallNotification: Message end_call reçu mais la notification n\'est pas visible', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
              }

              // Dans tous les cas, déclencher un événement call-ended pour s'assurer que tout est bien nettoyé
              window.dispatchEvent(new CustomEvent('call-ended'));
            }

            // Appeler l'ancien gestionnaire s'il existe
            if (oldOnMessage && typeof oldOnMessage === 'function') {
              oldOnMessage(event);
            }
          } catch (error) {
            //console.error('%c[APPEL] GlobalIncomingCallNotification: Erreur lors du traitement du message WebSocket:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);

            // Appeler l'ancien gestionnaire en cas d'erreur
            if (oldOnMessage && typeof oldOnMessage === 'function') {
              oldOnMessage(event);
            }
          }
        };

        //console.log('%c[APPEL] GlobalIncomingCallNotification: Écouteur de messages WebSocket configuré', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
      },

      // Nettoie le WebSocket
      cleanupWebSocket() {
        if (this.webSocketConnection) {
          //console.log('%c[APPEL] GlobalIncomingCallNotification: Nettoyage du WebSocket', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

          // Réinitialiser le gestionnaire de messages
          this.webSocketConnection.onmessage = null;

          // Ne pas fermer le WebSocket car il peut être utilisé par d'autres composants
          this.webSocketConnection = null;
        }
      },

      // Rejette automatiquement l'appel lorsque l'appelant raccroche
      autoRejectCall() {
        try {
          //console.log('%c[APPEL] GlobalIncomingCallNotification: Rejet automatique de l\'appel car l\'appelant a raccroché', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

          // Utiliser la méthode hideNotification pour masquer la notification
          this.hideNotification();

          // Afficher un message à l'utilisateur
          toaster.showInfoPopup("L'appelant a raccroché");

          // Déclencher un événement pour indiquer que l'appel a été rejeté
          window.dispatchEvent(new CustomEvent('call-rejected'));

          // Déclencher également un événement call-ended pour s'assurer que tout est bien nettoyé
          window.dispatchEvent(new CustomEvent('call-ended'));

          //console.log('%c[APPEL] GlobalIncomingCallNotification: Appel rejeté automatiquement', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

          // Vérifier que la notification est bien masquée
          this.$nextTick(() => {
            if (this.isVisible) {
              //console.error('%c[APPEL] GlobalIncomingCallNotification: La notification est toujours visible après autoRejectCall!', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
              // Forcer le masquage
              this.isVisible = false;
            } else {
              //console.log('%c[APPEL] GlobalIncomingCallNotification: Notification correctement masquée', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
            }
          });
        } catch (error) {
          //console.error('%c[APPEL] GlobalIncomingCallNotification: Erreur lors du rejet automatique de l\'appel:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);

          // En cas d'erreur, forcer le masquage de la notification
          this.isVisible = false;
        }
      }
    },
  };
</script>

<style scoped>
  .incoming-call-notification {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999; /* S'assurer qu'il est au-dessus de tout */
  }

  .incoming-call-modal {
    background-color: white;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 20px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  .swing-icon {
    animation: swing 1s infinite ease-in-out;
  }

  @keyframes swing {
    0% {
      transform: rotate(0deg);
    }
    25% {
      transform: rotate(-10deg);
    }
    50% {
      transform: rotate(10deg);
    }
    75% {
      transform: rotate(-10deg);
    }
    100% {
      transform: rotate(0deg);
    }
  }

  .incoming-call-icon-container {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background-color: var(--yellow-100);
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: visible;
  }

  .call-icon {
    width: 50px;
    height: 50px;
  }

  .ripple-effect {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100px;
    height: 100px;
    border: 1px solid var(--yellow-100);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(1);
    animation: ripple-animation 1.5s infinite;
    opacity: 1;
  }

  @keyframes ripple-animation {
    0% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 1;
    }
    100% {
      transform: translate(-50%, -50%) scale(1.4);
      opacity: 0;
    }
  }

  .call-actions {
    display: flex;
    gap: 20px;
  }

  .accept-btn,
  .reject-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 10px 20px;
    border-radius: 8px;
    border: none;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .accept-btn {
    background-color: #4caf50;
    color: white;
  }

  .reject-btn {
    background-color: #f44336;
    color: white;
  }

  .accept-btn:hover {
    background-color: #3e8e41;
  }

  .reject-btn:hover {
    background-color: #d32f2f;
  }

  .accept-btn img,
  .reject-btn img {
    width: 20px;
    height: 20px;
  }
</style>
