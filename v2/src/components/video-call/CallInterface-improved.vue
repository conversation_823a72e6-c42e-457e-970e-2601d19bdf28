<template>
  <div class="call-interface" v-if="isVisible">
    <div class="call-container">
      <div class="video-container">
        <div class="remote-video-container">
          <video ref="remoteVideo" class="remote-video" autoplay playsinline></video>
          <div class="call-status" v-if="callStatus">{{ callStatus }}</div>
        </div>
        <div class="local-video-container">
          <video ref="localVideo" class="local-video" autoplay playsinline muted></video>
        </div>
      </div>
      <div class="call-controls">
        <button class="control-button mute-button" @click="toggleMute" :class="{ active: isMuted }">
          <i :class="isMuted ? 'fas fa-microphone-slash' : 'fas fa-microphone'"></i>
        </button>
        <button class="control-button video-button" @click="toggleVideo" :class="{ active: isVideoOff }">
          <i :class="isVideoOff ? 'fas fa-video-slash' : 'fas fa-video'"></i>
        </button>
        <button class="control-button end-call-button" @click="endCall">
          <i class="fas fa-phone-slash"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { endCall, stopAllMediaStreams } from '@/services/video-call-auto-hangup';

export default {
  name: 'CallInterface',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    remoteStream: {
      type: MediaStream,
      default: null
    },
    localStream: {
      type: MediaStream,
      default: null
    }
  },
  data() {
    return {
      isVisible: this.visible,
      isMuted: false,
      isVideoOff: false,
      callStatus: '',
      callEndedListener: null,
      visibilityChangeListener: null
    };
  },
  watch: {
    visible(newVal) {
      this.isVisible = newVal;
    },
    remoteStream(newStream) {
      if (newStream && this.$refs.remoteVideo) {
        this.$refs.remoteVideo.srcObject = newStream;
      }
    },
    localStream(newStream) {
      if (newStream && this.$refs.localVideo) {
        this.$refs.localVideo.srcObject = newStream;
      }
    }
  },
  mounted() {
    //console.log('[APPEL] CallInterface: Composant monté');
    
    // Configurer les flux vidéo
    if (this.remoteStream && this.$refs.remoteVideo) {
      this.$refs.remoteVideo.srcObject = this.remoteStream;
    }
    
    if (this.localStream && this.$refs.localVideo) {
      this.$refs.localVideo.srcObject = this.localStream;
    }
    
    // Ajouter un écouteur pour l'événement de fin d'appel
    this.callEndedListener = this.handleCallEnded.bind(this);
    window.addEventListener('call-ended', this.callEndedListener);
    
    // Ajouter un écouteur pour le changement de visibilité de la page
    this.visibilityChangeListener = this.handleVisibilityChange.bind(this);
    document.addEventListener('visibilitychange', this.visibilityChangeListener);
    
    // Ajouter un écouteur pour la navigation
    this.setupNavigationListener();
  },
  beforeDestroy() {
    //console.log('[APPEL] CallInterface: Nettoyage des écouteurs');
    
    // Nettoyer les écouteurs d'événements
    if (this.callEndedListener) {
      window.removeEventListener('call-ended', this.callEndedListener);
    }
    
    if (this.visibilityChangeListener) {
      document.removeEventListener('visibilitychange', this.visibilityChangeListener);
    }
    
    // Arrêter les flux média si nécessaire
    this.cleanupMediaStreams();
  },
  methods: {
    toggleMute() {
      if (this.localStream) {
        const audioTracks = this.localStream.getAudioTracks();
        if (audioTracks.length > 0) {
          const enabled = !audioTracks[0].enabled;
          audioTracks.forEach(track => {
            track.enabled = enabled;
          });
          this.isMuted = !enabled;
          //console.log('[APPEL] Microphone:', this.isMuted ? 'Désactivé' : 'Activé');
        }
      }
    },
    toggleVideo() {
      if (this.localStream) {
        const videoTracks = this.localStream.getVideoTracks();
        if (videoTracks.length > 0) {
          const enabled = !videoTracks[0].enabled;
          videoTracks.forEach(track => {
            track.enabled = enabled;
          });
          this.isVideoOff = !enabled;
          //console.log('[APPEL] Caméra:', this.isVideoOff ? 'Désactivée' : 'Activée');
        }
      }
    },
    async endCall() {
      //console.log('[APPEL] CallInterface: Fin de l\'appel');
      
      // Masquer l'interface d'appel
      this.isVisible = false;
      
      // Utiliser la fonction endCall améliorée
      await endCall(true);
      
      // Émettre un événement pour informer le composant parent
      this.$emit('call-ended');
    },
    handleCallEnded(event) {
      //console.log('[APPEL] CallInterface: Événement de fin d\'appel reçu', event.detail);
      
      // Masquer l'interface d'appel
      this.isVisible = false;
      
      // Émettre un événement pour informer le composant parent
      this.$emit('call-ended');
    },
    handleVisibilityChange() {
      if (document.visibilityState === 'hidden' && this.isVisible) {
        //console.log('[APPEL] CallInterface: Page masquée pendant un appel, raccrochage automatique');
        this.endCall();
      }
    },
    setupNavigationListener() {
      // Intercepter les changements de route
      const originalPush = this.$router.push;
      this.$router.push = (location) => {
        if (this.isVisible) {
          //console.log('[APPEL] CallInterface: Navigation pendant un appel, raccrochage automatique');
          this.endCall();
        }
        return originalPush.call(this.$router, location);
      };
      
      const originalReplace = this.$router.replace;
      this.$router.replace = (location) => {
        if (this.isVisible) {
          //console.log('[APPEL] CallInterface: Navigation (replace) pendant un appel, raccrochage automatique');
          this.endCall();
        }
        return originalReplace.call(this.$router, location);
      };
    },
    cleanupMediaStreams() {
      // Utiliser la fonction stopAllMediaStreams améliorée
      stopAllMediaStreams();
    }
  }
};
</script>

<style scoped>
.call-interface {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.call-container {
  width: 100%;
  max-width: 1200px;
  height: 100%;
  max-height: 800px;
  display: flex;
  flex-direction: column;
}

.video-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  border-radius: 8px;
}

.remote-video-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.remote-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background-color: #1e1e1e;
}

.local-video-container {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 200px;
  height: 150px;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid white;
}

.local-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background-color: #2e2e2e;
}

.call-status {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
}

.call-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
}

.control-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: none;
  margin: 0 10px;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 20px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.2s ease;
}

.control-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.control-button.active {
  background-color: #f44336;
}

.end-call-button {
  background-color: #f44336;
}

.end-call-button:hover {
  background-color: #d32f2f;
}
</style>
