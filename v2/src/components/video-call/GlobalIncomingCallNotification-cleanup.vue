<template>
  <div class="global-incoming-call-notification" v-if="isVisible">
    <div class="notification-content">
      <div class="caller-info">
        <div class="caller-avatar">
          <i class="fas fa-user"></i>
        </div>
        <div class="caller-name">{{ callerName }}</div>
      </div>
      <div class="call-type">
        <i :class="callType === 'video' ? 'fas fa-video' : 'fas fa-phone'"></i>
        <span>{{ callType === 'video' ? 'Appel vidéo' : 'Appel audio' }}</span>
      </div>
      <div class="call-actions">
        <button class="reject-button" @click="rejectCall">
          <i class="fas fa-phone-slash"></i>
          Rejeter
        </button>
        <button class="accept-button" @click="acceptCall">
          <i class="fas fa-phone"></i>
          Accepter
        </button>
      </div>
    </div>
  </div>
</template>

<script>
  import { getUserById } from '@/services/account.service';
  import { acceptCall, rejectCall } from '@/services/video-call.service';
  import toaster from '@/services/toaster.service';

  export default {
    name: 'GlobalIncomingCallNotification',
    data() {
      return {
        isVisible: false,
        callerId: null,
        callerName: '',
        callType: 'video',
        offer: null,
        callData: null
      };
    },
    created() {
      //console.log('[APPEL] GlobalIncomingCallNotification: Composant créé');
      // Ajouter les écouteurs d'événements dès la création du composant
      this.addEventListeners();
    },
    mounted() {
      //console.log('[APPEL] GlobalIncomingCallNotification: Composant monté');
      // S'assurer que les écouteurs sont bien ajoutés
      this.addEventListeners();
    },
    beforeDestroy() {
      //console.log('[APPEL] GlobalIncomingCallNotification: Nettoyage des écouteurs');
      // Nettoyer les écouteurs d'événements
      this.removeEventListeners();
    },
    methods: {
      addEventListeners() {
        //console.log('[APPEL] GlobalIncomingCallNotification: Ajout des écouteurs d\'événements');
        // Utiliser bind pour s'assurer que this fait référence au composant
        this.boundHandleIncomingCall = this.handleIncomingCall.bind(this);
        this.boundHideNotification = this.hideNotification.bind(this);
        
        // Écouter les événements d'appel entrant
        window.addEventListener('incoming-call', this.boundHandleIncomingCall);
        window.addEventListener('call-rejected', this.boundHideNotification);
        window.addEventListener('call-ended', this.boundHideNotification);
        
        //console.log('[APPEL] GlobalIncomingCallNotification: Écouteurs ajoutés avec succès');
      },
      removeEventListeners() {
        //console.log('[APPEL] GlobalIncomingCallNotification: Suppression des écouteurs');
        // Nettoyer les écouteurs d'événements
        window.removeEventListener('incoming-call', this.boundHandleIncomingCall);
        window.removeEventListener('call-rejected', this.boundHideNotification);
        window.removeEventListener('call-ended', this.boundHideNotification);
      },
      async handleIncomingCall(event) {
        try {
          //console.log('[APPEL] GlobalIncomingCallNotification: Appel entrant reçu', event.detail);
          
          if (!event || !event.detail) {
            //console.error('[APPEL] GlobalIncomingCallNotification: Événement invalide');
            return;
          }
          
          const { senderId, senderName, call_type, offer } = event.detail;
          
          if (!senderId) {
            //console.error('[APPEL] GlobalIncomingCallNotification: ID d\'appelant manquant');
            return;
          }
          
          // Stocker toutes les données de l'appel
          this.callData = event.detail;
          this.callerId = senderId;
          this.callType = call_type || 'video';
          this.offer = offer;
          
          // Si le nom de l'appelant est fourni, l'utiliser
          if (senderName) {
            this.callerName = senderName;
          } 
          // Sinon, essayer de récupérer les informations de l'appelant
          else if (senderId) {
            try {
              const caller = await getUserById(senderId);
              if (caller) {
                this.callerName = `${caller.first_name} ${caller.last_name}`;
              } else {
                this.callerName = 'Utilisateur inconnu';
              }
            } catch (err) {
              //console.error('[APPEL] GlobalIncomingCallNotification: Erreur lors de la récupération des informations de l\'appelant:', err);
              this.callerName = 'Utilisateur inconnu';
            }
          } else {
            this.callerName = 'Utilisateur inconnu';
          }

          // Rendre le popup visible
          this.isVisible = true;
          //console.log('[APPEL] GlobalIncomingCallNotification: Popup affiché pour l\'appelant:', this.callerName);
          
          // Vérifier que le popup est bien visible
          this.$nextTick(() => {
            if (!this.isVisible) {
              //console.error('[APPEL] GlobalIncomingCallNotification: Le popup n\'est pas visible après avoir été défini comme tel!');
              // Forcer l'affichage
              this.isVisible = true;
            }
          });

          // Jouer un son de notification
          this.playNotificationSound();
          
          // Afficher une notification toast en plus du popup
          toaster.showInfoPopup(`Appel entrant de ${this.callerName}`);
          
          //console.log('[APPEL ENTRANT] Vous avez un appel entrant de ' + this.callerName);
        } catch (error) {
          //console.error('[APPEL] GlobalIncomingCallNotification: Erreur lors du traitement de l\'appel entrant:', error);
        }
      },
      hideNotification() {
        //console.log('[APPEL] GlobalIncomingCallNotification: Masquage du popup');
        this.isVisible = false;
        this.stopNotificationSound();
      },
      acceptCall() {
        try {
          //console.log('[APPEL] GlobalIncomingCallNotification: Acceptation de l\'appel');
          if (!this.callerId) {
            //console.error('[APPEL] GlobalIncomingCallNotification: Impossible d\'accepter l\'appel, ID d\'appelant manquant');
            toaster.showErrorPopup("Impossible d'accepter l'appel");
            return;
          }
          
          this.isVisible = false;
          this.stopNotificationSound();
          acceptCall();
          //console.log('[APPEL] GlobalIncomingCallNotification: Appel accepté, redirection en cours...');
        } catch (error) {
          //console.error('[APPEL] GlobalIncomingCallNotification: Erreur lors de l\'acceptation de l\'appel:', error);
          toaster.showErrorPopup("Erreur lors de l'acceptation de l'appel");
        }
      },
      rejectCall() {
        try {
          //console.log('[APPEL] GlobalIncomingCallNotification: Rejet de l\'appel');
          this.isVisible = false;
          this.stopNotificationSound();
          rejectCall();
          //console.log('[APPEL] GlobalIncomingCallNotification: Appel rejeté');
        } catch (error) {
          //console.error('[APPEL] GlobalIncomingCallNotification: Erreur lors du rejet de l\'appel:', error);
        }
      },
      playNotificationSound() {
        // TODO: Implémenter la lecture d'un son de notification
        //console.log('[APPEL] GlobalIncomingCallNotification: Lecture du son de notification');
      },
      stopNotificationSound() {
        // TODO: Implémenter l'arrêt du son de notification
        //console.log('[APPEL] GlobalIncomingCallNotification: Arrêt du son de notification');
      }
    }
  };
</script>

<style scoped>
.global-incoming-call-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 300px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.notification-content {
  padding: 16px;
}

.caller-info {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.caller-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f0f2f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.caller-avatar i {
  font-size: 20px;
  color: #65676b;
}

.caller-name {
  font-weight: 600;
  font-size: 16px;
}

.call-type {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  color: #65676b;
}

.call-type i {
  margin-right: 8px;
}

.call-actions {
  display: flex;
  justify-content: space-between;
}

.call-actions button {
  flex: 1;
  padding: 8px 0;
  border: none;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.call-actions button i {
  margin-right: 8px;
}

.reject-button {
  background-color: #f5f5f5;
  color: #65676b;
  margin-right: 8px;
}

.accept-button {
  background-color: #0084ff;
  color: white;
}

.reject-button:hover {
  background-color: #e4e6eb;
}

.accept-button:hover {
  background-color: #0070e0;
}
</style>
