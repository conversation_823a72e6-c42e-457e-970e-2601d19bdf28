<template>
  <div class="job-offer-wrapper">
    <div class="ad-card">
      <div class="ad-slot">
        <ins class="adsbygoogle"
            style="display:inline-block; width:320px; height:180px"
            data-ad-client="ca-pub-****************"
            data-ad-slot="5212440402"
            data-ad-format="auto">
        </ins>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GridAd',
  mounted() {
    this.$nextTick(() => {
      try {
        (window.adsbygoogle = window.adsbygoogle || []).push({});
      } catch (e) {
        //console.error('AdSense error:', e);
      }
    });
  }
}
</script>

<style scoped>
.job-offer-wrapper {
  display: flex;
  justify-content: center;
  width: 100%;
}

.ad-card {
  background-color: #fff;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.05);
  padding: 20px;
  width: 320px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ad-slot {
  width: 320px;
  height: 180px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
