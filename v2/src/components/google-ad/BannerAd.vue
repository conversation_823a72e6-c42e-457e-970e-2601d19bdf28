<template>
  <div class="banner-ad">
    <ins class="adsbygoogle"
         style="display:block; min-width:250px; width:100%;"
         data-ad-client="ca-pub-6599264593991393"
         data-ad-slot="9800157332"
         data-ad-format="auto"
         data-full-width-responsive="true">
    </ins>
  </div>
</template>

<script>
export default {
  name: 'BannerAd',
  mounted() {
    // Attendre que le DOM soit complètement chargé
    this.$nextTick(() => {
      if (window.adsbygoogle) {
        try {
          (window.adsbygoogle = window.adsbygoogle || []).push({});
        } catch (e) {
          //console.error('AdSense banner error:', e);
        }
      }
    });
  }
}
</script>
  
<style scoped>
.banner-ad {
  background: linear-gradient(45deg, #f7f7f7, #fff);
  min-height: 120px;
  width: 100%;
  min-width: 250px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;
  margin-bottom: 24px;
}
</style>
  
