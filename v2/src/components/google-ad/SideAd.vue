<template>
  <div class="side-ad">
    <ins class="adsbygoogle"
         style="display:block; min-width:250px; width:100%; min-height:250px;"
         data-ad-client="ca-pub-6599264593991393"
         data-ad-slot="9950170889"
         data-ad-format="auto"
         data-full-width-responsive="true">
    </ins>
  </div>
</template>

<script>
export default {
  name: 'SideAd',
  mounted() {
    this.$nextTick(() => {
      if (window.adsbygoogle) {
        try {
          (window.adsbygoogle = window.adsbygoogle || []).push({});
        } catch (e) {
          //console.error('AdSense SideAd error:', e);
        }
      }
    });
  }
}
</script>

<style scoped>
.side-ad {
  margin-top: 20px;
  background: #f9f9f9;
  border-radius: 10px;
  text-align: center;
  min-height: 250px;
  width: 100%;
  min-width: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
