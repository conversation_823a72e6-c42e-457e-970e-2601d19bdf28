<template>
    <div class="side-ad">
      <p class="label">Publicité</p>
      <div class="ad-box">Ici une pub Google s'affichera</div>
    </div>
  </template>
  
  <script>
  export default {
    name: 'SideFloatingAd',
  }
  </script>
  
  <style scoped>
  .side-ad {
    width: 160px;
    height: 600px;
    background: #f8f8f8;
    border: 2px dashed #bbb;
    border-radius: 10px;
    padding: 10px;
    box-sizing: border-box;
    text-align: center;
    position: fixed;
    top: 140px;
    z-index: 10;
  }
  
  .label {
    font-size: 0.75rem;
    color: #999;
    margin-bottom: 10px;
    text-transform: uppercase;
  }
  
  .ad-box {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    color: #777;
  }

  @media screen and (min-width: 1024px) and (max-width: 1280px) {
  .side-ad {
    width: 120px;
    height: 500px;
  }
}
  </style>
  