<template>
  <button class="like-button" @click="toggleLike" aria-label="Like or Unlike">
    <img
      v-if="isLiked"
      src="@/assets/social/social-page-post-liked-icon.svg"
      alt="Unlike"
    />
    <img
      v-else
      src="@/assets/social/social-page-post-like-icon.svg"
      alt="Like"
    />
  </button>
</template>

<script>
export default {
  name: "LikeButton",
  props: {
    post: {
      type: Object,
      required: true,
    },
    currentUser: {
      type: Object,
      required: true,
    },
  },
  computed: {
    isLiked() {
    return this.post.likers.some((liker) =>
      typeof liker === "object"
        ? liker.id === this.currentUser.id
        : liker === this.currentUser.id
    );
  },
  },
  methods: {
    toggleLike() {
      //console.log("like");
      this.$emit("like", !this.isLiked);
    },
  },
};
</script>

<style scoped>
.like-button img {
  width: 32px;
  height: 32px;
  margin-left: 5px;
}
</style>
