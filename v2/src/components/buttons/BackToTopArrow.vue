<template>
  <div class="arrow-container d-none" @click="goTopScreen" id="backBtn">
    <v-tooltip activator="parent" location="top">Retour en haut</v-tooltip>
    <img
      src="@/assets/search/search-page-backtotoparrow-icon.svg"
      alt="arrow pointed top"
      title="Permet de remonter en haut de la page"
    />
  </div>
</template>

<script>
  export default {
    name: 'BackToTopArrow',

    mounted() {
      window.addEventListener('scroll', this.scrollFunction, false);
      //window.onscroll = () => { this.scrollFunction() };
    },

    beforeUnmount() {
      window.removeEventListener('scroll', this.scrollFunction, false);
    },

    methods: {
      //  focus the user display on top of the page
      goTopScreen() {
        document.documentElement.scrollTop = 0;
      },

      scrollFunction() {
        let mybutton = document.getElementById('backBtn');
        if (document.documentElement.scrollTop > 1000)
          mybutton.classList = 'arrow-container';
        else mybutton.classList = 'arrow-container d-none';
      },
    },
  };
</script>

<style scoped>
  .arrow-container {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background-color: rgba(246, 179, 55, 1);
    width: 60px;
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 2px;
    cursor: pointer;
    z-index: 9999;
  }

  .d-none {
    display: none;
  }

  @media screen and (min-width: 992px) {
    .arrow-container {
      bottom: 5vh;
      right: 5vw;
    }
  }
</style>
