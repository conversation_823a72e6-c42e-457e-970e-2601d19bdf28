<template>
  <div class="container">
    <v-expansion-panels v-model="panelOpen" class="panel" :multiple="true">
      <v-expansion-panel elevation="0">
        <v-expansion-panel-title @click="toggleOpen()">{{
          title
        }}</v-expansion-panel-title>

        <v-expansion-panel-text
          v-for="(field, index) in fields"
          :key="index"
          :class="checkboxIsChecked[index] ? 'underline2' : 'underline1'"
          @click="toggleCheckbox(index)"
        >
          <div class="custom-field">
            <p class="badge-container">
              {{ field.label || field }}
              <!-- Afficher la bulle si un 'count' est passé -->
              <span v-if="field.count" class="badge">{{ field.count }}</span>
            </p>
            <div v-if="!nobox" class="custom-checkbox">
              <img
                v-if="checkboxIsChecked[index]"
                src="@/assets/search/search-page-filters-checkedbox.svg"
                alt="icône de case à cocher pour le filtrage"
              />
              <img
                v-else
                src="@/assets/search/search-page-filters-checkbox.svg"
                alt="icône de case à cocher pour le filtrage"
              />
            </div>
          </div>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>

    <CustomChipsField
      v-if="chips"
      :chipsList="datas"
      @chip-click="removeChip"
      clearable
    />
  </div>
</template>

<script>
  import CustomChipsField from '@/components/chips/CustomChipsField.vue';

  export default {
    name: 'CustomAccordion',

    components: {
      CustomChipsField,
    },

    props: {
      field: {
        type: String,
        required: true,
      },
      title: {
        type: String,
        default: 'Default title',
      },
      fields: {
        type: Array,
        default: () => [
          'Element déroulant 1',
          'Element déroulant 2',
          'Element déroulant 3',
          'Element déroulant 4',
        ],
      },
      multiple: {
        type: Boolean,
      },
      nobox: {
        type: Boolean,
      },
      reverse: {
        type: Boolean,
      },
      chips: {
        type: Boolean,
      },
      // Nouvelle prop pour ouvrir l'accordéon par défaut
      defaultOpen: {
        type: Boolean,
        default: false,
      },
      defaultChecked: {
        type: Array,
        default: () => [],
      },
    },

    data() {
      return {
        panelOpen: this.defaultOpen ? [0] : [], // Stocke les indices des panneaux ouverts
        checkboxIsChecked: [],
        datas: {}, // Pour stocker l'état des chips
      };
    },

    beforeMount() {
      this.populateVars();
    },

    methods: {
      populateVars() {
        for (let i = 0; i < this.fields.length; i++) {
          // Initialise avec les valeurs par défaut si présentes
          this.checkboxIsChecked.push(
            this.defaultChecked.includes(this.fields[i])
          );
          this.datas[this.fields[i]] = this.defaultChecked.includes(
            this.fields[i]
          );
        }
      },

      toggleOpen(index) {
        if (this.panelOpen.includes(index)) {
          this.panelOpen = this.panelOpen.filter((i) => i !== index); // Ferme le panneau
        } else {
          this.panelOpen.push(index); // Ouvre le panneau
        }
      },

      toggleCheckbox(index) {
        if (!this.multiple) this.resetFieldsToFalse();
        this.checkboxIsChecked[index] = !this.checkboxIsChecked[index];
        this.datas[this.fields[index]] = this.checkboxIsChecked[index];
        this.$emit('checkbox-state', this.field, this.datas);
      },

      resetFieldsToFalse() {
        for (let i = 0; i < this.fields.length; i++) {
          this.checkboxIsChecked[i] = false;
          this.datas[this.fields[i]] = false;
        }
      },

      removeChip(name) {
        let index = this.fields.indexOf(name);
        if (index !== -1) {
          this.checkboxIsChecked[index] = false; // Décochez la case correspondante
          this.datas[name] = false; // Mettez à jour les données
          this.$emit('checkbox-state', this.field, this.datas); // Émettre les nouveaux états
        }
      },
    },
  };
</script>

<style scoped>
  .panel {
    border-bottom: 1px solid rgba(38, 40, 43, 1);
  }

  .custom-field {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
  }

  .v-expansion-panel--active .v-expansion-panel-title {
    background-color: rgba(246, 179, 55, 0.2) !important;
  }

  .v-expansion-panel-text:hover {
    background-color: rgba(246, 179, 55, 0.2);
    cursor: pointer;
  }

  .underline2 {
    border-bottom: 2px solid rgba(246, 179, 55, 1);
  }

  .underline1 {
    border-bottom: 2px solid rgb(223, 219, 214, 1);
  }

  .custom-checkbox {
    display: flex;
    justify-content: center;
    align-items: center;
    width: fit-content;
  }

  /* Style de la bulle de notification */
  .badge {
    position: absolute;
    top: -5px; /* Positionnement en haut */
    right: -20px; /* Décalage vers la droite */
    background-color: red;
    color: white; /* Texte blanc pour le contraste */
    border-radius: 50%; /* Forme circulaire */
    width: 18px; /* Taille fixe */
    height: 18px;
    font-size: 12px; /* Taille de police ajustée */
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2); /* Ombre légère */
    z-index: 10; /* Pour s'assurer qu'il est au-dessus du reste */
  }

  .badge-container {
    position: relative; /* Permet d'aligner le badge */
    display: inline-block; /* S'adapte à la taille du texte */
    margin-right: 20px;
  }
</style>
