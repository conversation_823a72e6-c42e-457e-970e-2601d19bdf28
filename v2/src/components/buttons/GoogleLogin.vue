<template>
  <button type="button">
    <img src="@/assets/icons/google.svg" alt="google" />
    <p v-if="!loading">Se connecter avec Google</p>
    <v-progress-circular
      v-else
      indeterminate
      size="20"
      class="progress-circle"
    ></v-progress-circular>
  </button>
</template>

<script>
  export default {
    name: 'GoogleLogin',
    props: {
      loading: {
        type: Boolean,
        required: false,
        default: false,
      },
    },
  };
</script>

<style scoped>
  button {
    width: 100%; /* Le bouton prend 100% de l'espace parent pour être responsive */
    max-width: 274px; /* Limite la taille maximale pour ne pas étirer trop grand */
    height: 40px;
    border: 2px solid #1976d2;
    border-radius: 20px;
    background-color: #fefefe;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 5px;
  }

  img {
    height: 24px;
    width: 24px;
    position: absolute;
    left: 10px; /* Toujours à gauche */
  }

  p {
    padding-left: 35px; /* Ajouter de l'espace entre l'image et le texte */
    padding-right: 10px; /* Ajouter un peu de padding à droite pour équilibrer */
    text-align: center; /* Assurer que le texte est bien centré */
  }

  .progress-circle {
    color: #1976d2;
    width: 20px;
    height: 20px;
    margin-left: 10px;
  }
</style>
