<template>
  <v-expansion-panels variant="accordion" v-model="panel">
    <v-expansion-panel
      v-for="[i, faq] in faqs.entries()"
      :key="i"
      :value="i"
      class="faq"
    >
      <v-expansion-panel-title
        :class="[
          'faq-title',
          panel !== i ? 'faq-title-collapsed' : 'faq-title-expanded',
        ]"
      >
        {{ faq.title }}
      </v-expansion-panel-title>
      <v-expansion-panel-text class="faq-text">
        <div v-html="faq.text"></div>
      </v-expansion-panel-text>
    </v-expansion-panel>
  </v-expansion-panels>
</template>

<script>
  export default {
    name: 'FaqAccordion',
    props: {
      faqs: {
        type: Array,
        required: true,
      },
      class: {
        type: [String, Object, Array],
        required: false,
      },
    },
    data() {
      return {
        panel: null,
      };
    },
  };
</script>

<style>
  .faq {
    margin-block: 1px;
  }

  .faq-title {
    font-family: Roboto;
    font-size: 19px;
    font-weight: 500;
    line-height: 22.27px;
    text-align: left;
  }

  .faq-title-collapsed {
    background-color: var(--text-1);
    color: var(--surface-bg-2);
  }

  .faq-title-expanded {
    background-color: var(--primary-1);
    color: var(--text-1);
  }

  .faq-text {
    white-space: pre-wrap;
  }
  a {
    background-color: transparent;
    text-decoration: none;
    color: black;
    font-weight: 600;
    cursor: pointer;
  }
</style>
