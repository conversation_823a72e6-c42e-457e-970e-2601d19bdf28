<template>
  <!-- loop through each field, if field is checked add a orange underline -->
  <div
    v-for="(field, index) in fields"
    :key="index"
    :class="checkboxIsChecked[index] ? 'field underline2' : 'field underline1'"
    :style="underline ? '' : 'border: none'"
  >
    <div v-if="reverse" class="custom-field" @click="toggleCheckbox(index)">
      <p>{{ field }}</p>

      <!-- toggle between img if checkbox is checked , if nobox props is true, no checkbox is displayed -->
      <div class="custom-checkbox">
        <img
          v-if="checkboxIsChecked[index]"
          src="@/assets/search/search-page-filters-checkedbox.svg"
        />
        <img v-else src="@/assets/search/search-page-filters-checkbox.svg" />
      </div>
    </div>

    <div v-else class="custom-field" @click="toggleCheckbox(index)">
      <!-- toggle between img if checkbox is checked , if nobox props is true, no checkbox is displayed -->
      <div class="custom-checkbox">
        <img
          v-if="checkboxIsChecked[index]"
          src="@/assets/search/search-page-filters-checkedbox.svg"
        />
        <img v-else src="@/assets/search/search-page-filters-checkbox.svg" />
      </div>

      <p class="custom-p">{{ field }}</p>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'CustomCheckbox',

    props: {
      //  name of the field in the form data
      field: {
        type: String,
        required: true,
      },

      //  fields of the custom select
      fields: {
        type: Array,
      },

      //  component inital value
      cValue: {
        type: [String, Array],
      },

      //  allow multiple values to be selected
      multiple: {
        type: Boolean,
        default: false,
      },

      // reverse checkbox position
      reverse: {
        type: Boolean,
      },

      //  disable input
      readonly: {
        type: Boolean,
      },

      //  underline toggle
      underline: {
        type: Boolean,
      },
    },

    data() {
      return {
        accordionIsOpen: false, //  toggle accordion state
        checkboxIsChecked: [], //  toggle N checkbox states lol
        datas: {}, //  datas emitted to parent
      };
    },

    beforeMount() {
      this.populateVars();
    },

    methods: {
      //  populate variables with falses values
      populateVars() {
        if (this.cValue == null) return; // Verificar si cValue es nulo

        // Inicializar checkboxIsChecked y datas
        this.checkboxIsChecked = [];
        this.datas = {};

        for (let i = 0; i < this.fields.length; i++) {
          const isChecked = Array.isArray(this.cValue)
            ? this.cValue.includes(this.fields[i])
            : this.cValue === this.fields[i];

          this.checkboxIsChecked.push(isChecked);
          this.datas[this.fields[i]] = isChecked;
        }
      },

      //  toggle between accordion is open or not
      toggleOpen() {
        this.accordionIsOpen = !this.accordionIsOpen;
      },

      //   toggle between checked and not checked, and emit state to parent
      toggleCheckbox(index) {
        if (this.readonly) return;
        if (!this.multiple) this.resetFieldsToFalse();
        this.checkboxIsChecked[index] = !this.checkboxIsChecked[index];
        this.datas[this.fields[index]] = this.checkboxIsChecked[index];
        this.$emit('checkbox-state', this.field, this.datas); //  send all fields state
        this.$emit(
          'checkbox-selection',
          this.field,
          this.fields[index],
          this.checkboxIsChecked[index]
        ); //  send only last checked box

        let stringArray = [];
        for (let i = 0; i < this.fields.length; i++) {
          if (this.checkboxIsChecked[i]) stringArray.push(this.fields[i]);
        }
        this.$emit('checkbox-stringarray', this.field, stringArray); //  send an array contening a string of each true element
      },

      //  reset all variables entries to false
      resetFieldsToFalse() {
        for (let i = 0; i < this.fields.length; i++) {
          this.checkboxIsChecked[i] = false;
          this.datas[this.fields[i]] = false;
        }
      },
    },
  };
</script>

<style scoped>
  .field {
    width: 100%;
    margin-bottom: 10px;
  }

  .custom-field {
    display: flex;
    width: 100%;
    justify-content: space-evenly;
    align-items: center;
    margin-bottom: 5px;
  }

  .field:hover {
    background-color: rgba(246, 179, 55, 0.2);
    cursor: pointer;
  }

  .underline2 {
    border-bottom: 2px solid rgba(246, 179, 55, 1);
  }

  .underline1 {
    border-bottom: 2px solid rgba(245, 242, 239, 1);
  }

  .custom-checkbox {
    width: 20%;
    display: flex;
    justify-content: center;
    align-items: center;
    width: fit-content;
    padding: 10px;
  }

  .custom-p {
    margin-left: 10px;
    width: 80%;
  }
</style>
