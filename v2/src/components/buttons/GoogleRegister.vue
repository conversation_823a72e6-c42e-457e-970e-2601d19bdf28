<template>
  <button type="button">
    <img src="@/assets/icons/google.svg" alt="google" />
    <p v-if="!loading">Je m'inscris avec Google</p>
    <v-progress-circular
      v-else
      indeterminate
      size="20"
      class="progress-circle"
    ></v-progress-circular>
  </button>
</template>

<script>
  export default {
    name: 'GoogleLogin',
    props: {
      loading: {
        type: Boolean,
        required: false,
        default: false,
      },
    },
  };
</script>

<style scoped>
  button {
    max-width: 274px;
    padding-right: 10px;
    padding-left: 5px;
    height: 40px;
    border: 2px solid #1976d2;
    border-radius: 20px;
    background-color: #fefefe;
    display: flex;
    align-items: center;
  }

  img {
    height: 40px;
    width: 40px;
    padding-left: 5px;
    padding-right: 7px;
  }

  .progress-circle {
    margin-left: 30%;
    color: #1976d2;
    width: 20px;
    height: 20px;
  }
</style>
