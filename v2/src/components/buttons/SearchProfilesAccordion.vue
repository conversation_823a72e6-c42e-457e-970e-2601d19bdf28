<template>
  <div class="container">
    <v-expansion-panels v-model="panelOpen">
      <v-expansion-panel elevation="0">
        <v-expansion-panel-title>
          {{ title }}
        </v-expansion-panel-title>

        <v-expansion-panel-text>
          <div
            v-for="(field, index) in fields"
            :key="index"
            @click="toggleCheckbox(index)"
            class="custom-field"
          >
            <p class="badge-container">
              {{ field }}
            </p>
            <div class="custom-checkbox">
              <img
                :src="checkboxStates[index] ? checkedImage : uncheckedImage"
              />
            </div>
          </div>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>
  </div>
</template>

<script>
  import checkedImage from '@/assets/search/search-page-filters-checkedbox.svg';
  import uncheckedImage from '@/assets/search/search-page-filters-checkbox.svg';

  export default {
    name: 'SearchProfilesAccordion',
    props: {
      field: {
        type: String,
        required: true,
      },
      fields: {
        type: Array,
        default: () => [],
      },
      title: {
        type: String,
        default: 'Default title',
      },
    },
    data() {
      return {
        panelOpen: null,
        checkboxStates: [],
        checkedImage,
        uncheckedImage,
      };
    },
    created() {
      this.initializeFromURL();
    },
    methods: {
      initializeFromURL() {
        const query = this.$route.query;
        const urlValue = query[this.field];

        this.checkboxStates = this.fields.map(() => false);

        if (urlValue) {
          const selectedValues = urlValue
            .replace(/[\[\]]/g, '')
            .split(',')
            .map(decodeURIComponent);

          this.fields.forEach((field, index) => {
            if (selectedValues.includes(field)) {
              this.checkboxStates[index] = true;
            }
          });

          if (selectedValues.length > 0) {
            this.panelOpen = 0;
          }
        }
      },
      toggleCheckbox(index) {
        this.checkboxStates[index] = !this.checkboxStates[index];
        this.updateURL();
      },
      updateURL() {
        const params = { ...this.$route.query };

        const selectedValues = this.fields
          .filter((_, index) => this.checkboxStates[index])
          .map(encodeURIComponent);

        if (selectedValues.length > 0) {
          params[this.field] = `[${selectedValues.join(',')}]`;
        } else {
          delete params[this.field];
        }

        this.$router.replace({ query: params });
      },
    },
  };
</script>

<style scoped>
  .custom-field {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    cursor: pointer;
  }

  .custom-field:hover {
    background-color: rgba(246, 179, 55, 0.2);
  }

  .v-expansion-panel--active .v-expansion-panel-title {
    background-color: rgba(246, 179, 55, 0.2) !important;
  }

  .v-expansion-panel-text:hover {
    background-color: rgba(246, 179, 55, 0.2);
    cursor: pointer;
  }

  .underline2 {
    border-bottom: 2px solid rgba(246, 179, 55, 1);
  }

  .underline1 {
    border-bottom: 2px solid rgb(223, 219, 214, 1);
  }

  .custom-checkbox {
    display: flex;
    justify-content: center;
    align-items: center;
    width: fit-content;
  }

  /* Style de la bulle de notification */
  .badge {
    position: absolute;
    top: -5px; /* Positionnement en haut */
    right: -20px; /* Décalage vers la droite */
    background-color: red;
    color: white; /* Texte blanc pour le contraste */
    border-radius: 50%; /* Forme circulaire */
    width: 18px; /* Taille fixe */
    height: 18px;
    font-size: 12px; /* Taille de police ajustée */
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2); /* Ombre légère */
    z-index: 10; /* Pour s'assurer qu'il est au-dessus du reste */
  }

  .badge-container {
    position: relative; /* Permet d'aligner le badge */
    display: inline-block; /* S'adapte à la taille du texte */
    margin-right: 20px;
  }
</style>
