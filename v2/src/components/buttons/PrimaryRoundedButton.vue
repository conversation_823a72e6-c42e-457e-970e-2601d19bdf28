<template>
  <v-btn
    :class="buttonClasses"
    :color="buttonColor"
    :style="buttonStyle"
    :ripple="true"
    :loading="isLoading"
    :disabled="isDisabled"
    @click="$emit('btn-click')"
  >
    <template v-slot:prepend>
      <img v-if="back" src="@/assets/icons/arrow-left.svg" />
      <img v-if="ai" src="@/assets/icons/AI-button.svg" />
    </template>
    {{ textContent }}
    <template v-slot:append>
      <img v-if="send" src="@/assets/icons/arrow-right.svg" />
      <img
        v-if="email && btnColor === 'secondary'"
        src="@/assets/icons/send-msg-transparent.svg"
      />
      <img
        v-if="email && btnColor !== 'secondary'"
        src="@/assets/icons/icon-send-msg-primary1.svg"
      />
      <img v-if="eye" src="@/assets/icons/eye-black2.svg" />
      <img v-if="modify" src="@/assets/icons/pen-black.svg" />
      <img v-if="camera" src="@/assets/icons/camera.svg" />
      <img v-if="calendar" src="@/assets/icons/calendar.svg" />
    </template>
  </v-btn>
</template>

<script>
  export default {
    name: 'PrimaryRoundedButton',

    props: {
      textContent: {
        type: String,
        default: 'default text',
      },
      btnColor: {
        type: String,
        default: 'primary',
        validator: (value) =>
          ['primary', 'secondary', 'light', 'disabled', 'ai', 'blue'].includes(
            value
          ),
      },
      isLoading: {
        type: Boolean,
        default: false,
      },
      isDisabled: {
        type: Boolean,
        default: false,
      },
      send: {
        type: Boolean,
        default: false,
      },
      email: {
        type: Boolean,
        default: false,
      },
      back: {
        type: Boolean,
        default: false,
      },
      modify: {
        type: Boolean,
        default: false,
      },
      download: {
        type: Boolean,
        default: false,
      },
      look: {
        type: Boolean,
        default: false,
      },
      ai: {
        type: Boolean,
        default: false,
      },
      camera: {
        type: Boolean,
        default: false,
      },
      calendar: {
        type: Boolean,
        default: false,
      },
      eye: {
        type: Boolean,
        default: false,
      },
    },

    computed: {
      buttonClasses() {
        return {
          'black-text':
            this.btnColor === 'primary' || this.btnColor === 'light',
          'white-text':
            this.btnColor === 'secondary' ||
            this.btnColor === 'disabled' ||
            this.btnColor === 'ai',
        };
      },
      buttonColor() {
        return this.btnColor === 'ai'
          ? 'var(--light-rounded-btn-bg-color)'
          : this.getButtonColor();
      },
      buttonStyle() {
        return this.btnColor === 'ai'
          ? {
              border: '2px solid var(--ai-color)',
              color: 'var(--ai-color)',
            }
          : {};
      },
    },

    methods: {
      getButtonColor() {
        switch (this.btnColor) {
          case 'primary':
            return 'var(--primary-rounded-btn-bg-color)';
          case 'secondary':
            return 'var(--secondary-rounded-btn-bg-color)';
          case 'disabled':
            return 'var(--text-3)';
          case 'light':
            return 'var(--light-rounded-btn-bg-color)';
          case 'blue':
            return 'var(--secondary-2b2)';
          default:
            return '';
        }
      },
    },
  };
</script>

<style scoped>
  .v-btn {
    font-weight: var(--primary-rounded-btn-font-weight);
    font-size: var(--primary-rounded-btn-font-size);
    font-family: var(--primary-rounded-btn-font-familly);
    padding-inline: 24px;
    padding-block: 16px;
    min-width: fit-content;
    align-content: center;
    height: 40px;
    border-radius: 5px;
    box-shadow: none;
  }
  .v-btn:disabled {
    background-color: #f8f8f8 !important;
  }

  .white-text {
    color: var(--secondary-rounded-btn-color);
  }

  .black-text {
    color: var(--primary-rounded-btn-color);
  }

  img {
    height: 24px;
  }
</style>
