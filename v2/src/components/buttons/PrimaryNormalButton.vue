<template>
  <v-btn
    :class="buttonClasses"
    :color="buttonColor"
    :ripple="true"
    :loading="isLoading"
    :disabled="disabled"
  >
    {{ textContent }}

    <template v-if="alert" v-slot:append>
      <img
        src="@/assets/search/search-page-alert-button-icon.svg"
        alt="icone d'alerte du bouton"
      />
    </template>

    <template v-if="add" v-slot:append>
      <img src="@/assets/icons/add-plus.svg" alt="Icône pour ajouter" />
    </template>

    <template v-if="back" v-slot:prepend>
      <img
        src="@/assets/login-page-back-button-icon.svg"
        alt="icône de retour"
      />
    </template>

    <template v-if="send" v-slot:append>
      <img
        src="@/assets/icons/icon-send-msg-primary1.svg"
        alt="icone d'envoi de message"
      />
    </template>

    <template v-if="file" v-slot:append>
      <img
        src="@/assets/icons/icon-files-transparent.svg"
        alt="icone d'envoi de message"
      />
    </template>
  </v-btn>
</template>

<script>
  export default {
    name: 'PrimaryNormalButton',
    props: {
      disabled: {
        type: Boolean,
        required: false,
        default: false,
      },
      textContent: {
        type: String,
        required: false,
        default: null,
      },
      btnColor: {
        type: String,
        required: false,
        default: 'primary',
        validator: (value) => ['primary', 'secondary'].includes(value),
      },
      isLoading: {
        type: Boolean,
        required: false,
        default: false,
      },
      alert: {
        type: Boolean,
      },
      add: {
        type: Boolean,
      },
      back: {
        type: Boolean,
      },
      send: {
        type: Boolean,
      },
      file: {
        type: Boolean,
      },
    },
    computed: {
      buttonClasses() {
        return {
          'black-text':
            this.btnColor === 'primary' || this.btnColor === 'light',
          'white-text': this.btnColor === 'secondary' || this.disabled,
        };
      },

      buttonColor() {
        switch (this.btnColor) {
          case 'primary':
            return 'var(--primary-rounded-btn-bg-color)';
          case 'secondary':
            return 'var(--secondary-rounded-btn-bg-color)';
          case 'disabled':
            return 'var(--text-3)';
          case 'light':
            return 'var(--light-rounded-btn-bg-color)';
        }
      },
    },
  };
</script>

<style scoped>
  .v-btn {
    font-weight: var(--primary-rounded-btn-font-weight);
    font-size: var(--primary-rounded-btn-font-size);
    font-family: var(--primary-rounded-btn-font-familly);
    padding-inline: 24px;
    padding-block: 16px;
    min-height: 0;
    min-width: 0;
    align-items: center; /* Centrer verticalement le contenu */
    justify-content: center; /* Centrer horizontalement le contenu */
    display: inline-flex; /* Utiliser Flexbox pour gérer l'alignement */
    border-radius: 5px;
    box-shadow: none;
  }

  .v-btn img {
    object-fit: contain; /* Préserve le ratio de l'icône */
  }

  .v-btn:hover {
    box-shadow: 0px 4px 8px 0px #00000040;
  }

  .white-text {
    color: var(--secondary-rounded-btn-color);
  }

  .black-text {
    color: var(--primary-rounded-btn-color);
  }
</style>
