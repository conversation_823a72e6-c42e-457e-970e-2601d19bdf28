<template>
  <div class="options-menu-wrapper">
    <button
      v-if="
        isLoggedIn &&
        currentUser &&
        ((item.user && currentUser.id === item.user.id) ||
          (item.creator && currentUser.id === item.creator.id))
      "
      class="post-options"
      @click="toggleOptionsMenu(postIndex)"
    >
      <img src="@/assets/social/social-page-post-options-icon.svg" />
      <v-tooltip activator="parent" location="top">Options</v-tooltip>
    </button>

    <div v-if="showMenu" class="options-menu">
      <ul>
        <!-- Option Supprimer -->
        <li class="option" @click="deleteItem">
          <img src="@/assets/social/trash-white-bg.svg" />
          Supprimer
        </li>
        <!-- Option Modifier (si applicable) -->
        <!-- <li v-if="canEdit" @click="editItem">Modifier</li> -->
        <!-- Option Signaler (si applicable) -->
        <!-- <li v-if="canReport" @click="reportItem">
          <img src="@/assets/social/flag-white-bg.svg" /> Signaler
        </li> -->
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: "PostMenuOptions",
  props: {
    // L'élément (post ou commentaire) auquel les options s'appliquent
    item: Object,
    // Type d'élément (post ou commentaire)
    type: {
      type: String,
      required: true,
    },
    // L'utilisateur actuel
    currentUser: {
      type: Object,
      required: true,
    },
    // Actions à exécuter pour les options
    deleteAction: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      showMenu: false, // Etat pour afficher/masquer le menu
    };
  },
  computed: {
    isLoggedIn() {
      return Object.keys(this.currentUser).length > 0; // Vérifier si l'utilisateur est connecté
    },
    isOwner() {
      return (
        this.currentUser.id === this.item.user.id ||
        this.currentUser.id === this.item.creator?.id
      );
    },
    iconSource() {
      return this.type === "post"
        ? "@/assets/social/social-page-post-options-icon.svg"
        : "@/assets/social/social-page-comment-options-icon.svg";
    },
    canDelete() {
      return this.isOwner;
    },
    canEdit() {
      return (
        this.type === "comment" && this.currentUser.id === this.item.creator
      );
    },
    canReport() {
      return this.type === "post" || this.type === "comment";
    },
  },
  methods: {
    toggleOptionsMenu() {
      this.showMenu = !this.showMenu;
    },
    deleteItem() {
      //console.log("Item à supprimer : ", this.item);
      this.deleteAction(this.item);
      this.showMenu = false;
    },
    editItem() {
      if (this.editAction) {
        this.editAction(this.item);
        this.showMenu = false;
      }
    },
    reportItem() {
      if (this.reportAction) {
        this.reportAction(this.item);
        this.showMenu = false;
      }
    },
  },
};
</script>

<style scoped>
.options-menu-wrapper {
  position: absolute; /* Permet un positionnement absolu */
  top: 10px; /* Ajustez selon vos besoins */
  right: 10px; /* Ajustez selon vos besoins */
  z-index: 1;
}

.options-button {
  background: none;
  border: none;
  cursor: pointer;
  position: absolute;
  margin-top: 16px;
  right: 0;
}

.options-menu {
  position: absolute;
  top: 30px;
  right: 0;
  background-color: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 10px;
}

.options-menu ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.options-menu li {
  padding: 10px;
  cursor: pointer;
}

.options-menu li:hover {
  background-color: #f0f0f0;
}

.option {
    display: flex;
    gap: 8px;
}
</style>
