<template>
  <v-radio-group
    v-model="currentValue"
    v-for="(radio, index) in fields"
    :key="index"
    :class="
      fields[index] == currentValue
        ? 'item field underline2'
        : 'item field underline1'
    "
    :readonly="readonly"
  >
    <v-radio
      :label="radio"
      :value="radio"
      color="rgb(240,179,55)"
      @click="selectRadio(index)"
    />
  </v-radio-group>
</template>

<script>
  export default {
    name: 'CustomRadio',

    emits: ['radioSelection'],

    props: {
      //  name of the field in the form data
      field: {
        type: String,
        required: true,
      },

      //  fields of the custom select
      fields: {
        type: Array,
        default: ['Radio 1', 'Radio 2', 'Radio 3', 'Radio 4'],
      },

      //  component inital value
      cValue: {
        type: String,
      },

      //  disable input
      readonly: {
        type: Boolean,
      },
    },

    mounted() {
      this.currentValue = this.cValue;
    },

    watch: {
      cValue(newVal) {
        this.currentValue = newVal;
      },
    },

    data() {
      return {
        currentValue: null, //  value of component
      };
    },

    methods: {
      //  select one radio button and reset all others radios to false
      selectRadio(index) {
        if (this.readonly) return;
        this.currentValue = this.fields[index];

        this.$emit('radio-selection', this.field, this.currentValue);
      },
    },
  };
</script>

<style scoped>
  .field {
    width: 100%;
    margin-bottom: 10px;
  }

  .field:hover {
    background-color: rgba(246, 179, 55, 0.2);
    cursor: pointer;
  }

  .underline2 {
    border-bottom: 2px solid rgba(246, 179, 55, 1);
  }

  .underline1 {
    border-bottom: 2px solid rgba(245, 242, 239, 1);
  }
</style>
