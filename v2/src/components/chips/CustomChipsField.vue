<template>
  <!-- filter chips, chip must be true to be displayed -->
  <div class="packet-chips">
    <v-chip
      v-for="chip in Object.keys(chipsList).filter(function (key) {
        return chipsList[key];
      })"
      class="chip"
    >
      {{ chip }}

      <template v-if="clearable" v-slot:append>
        <img
          src="@/assets/search/search-page-alert-panel-close-icon.svg"
          @click="toggleChip(chip)"
          class="close-btn"
        />
      </template>
    </v-chip>
  </div>
</template>

<script>
  export default {
    name: 'CustomChipsField',

    props: {
      //  list of chips to display {key: chipname, value: boolean}
      chipsList: {
        type: Object,
        required: true,
      },

      // toggle chip deletion
      clearable: {
        type: <PERSON>olean,
      },
    },

    methods: {
      //  get click event on chip close icon and emit chip name to parent
      toggleChip(name) {
        this.$emit('chip-click', name);
      },
    },
  };
</script>

<style scoped>
  .packet-chips {
    display: flex;
    width: 100%;
    height: fit-content;
    flex-wrap: wrap;
    margin-block: 5px;
  }

  .chip {
    margin: 5px;
    background-color: var(--text-1);
    color: var(--surface-bg);
    border-radius: 5px;
  }

  .close-btn {
    cursor: pointer;
  }
</style>
