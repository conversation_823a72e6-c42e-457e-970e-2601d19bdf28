<template>
  <div ref="lazyContainer">
    <slot v-if="isVisible"></slot>
    <div v-else-if="showPlaceholder" class="lazy-placeholder">
      <slot name="placeholder">
        <div class="placeholder-content"></div>
      </slot>
    </div>
  </div>
</template>

<script>
  import { throttle } from '@/utils/performance';

  // Créer un observateur partagé pour tous les composants LazyLoader
  // Cela réduit le nombre d'instances d'IntersectionObserver
  let sharedObserver = null;
  const observerCallbacks = new Map();
  const observerOptions = {
    rootMargin: '200px',
    threshold: 0.1,
  };

  // Fonction pour initialiser l'observateur partagé
  function initSharedObserver() {
    if (sharedObserver) return;

    sharedObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        const callback = observerCallbacks.get(entry.target);
        if (callback && entry.isIntersecting) {
          callback();
          // Une fois que l'élément est visible, on arrête de l'observer
          sharedObserver.unobserve(entry.target);
          observerCallbacks.delete(entry.target);
        }
      });
    }, observerOptions);
  }

  export default {
    name: 'LazyLoader',
    props: {
      // Marge en pixels autour de l'élément pour déclencher le chargement avant qu'il soit visible
      rootMargin: {
        type: String,
        default: '200px',
      },
      // Seuil de visibilité (0-1) pour déclencher le chargement
      threshold: {
        type: Number,
        default: 0.1,
      },
      // Afficher un placeholder pendant le chargement
      showPlaceholder: {
        type: Boolean,
        default: true,
      },
    },
    data() {
      return {
        isVisible: false,
        observer: null,
      };
    },
    mounted() {
      // Utiliser l'Intersection Observer API pour détecter quand l'élément devient visible
      this.setupObserver();
    },
    beforeDestroy() {
      // Nettoyer l'observer
      this.destroyObserver();
    },
    created() {
      // Pré-initialiser l'observateur partagé pour éviter les retards
      initSharedObserver();

      // Créer une version throttle de la fonction de visibilité
      // pour éviter les appels trop fréquents
      this.handleVisibility = throttle(() => {
        this.isVisible = true;
        this.$emit('visible');
      }, 100);
    },

    methods: {
      setupObserver() {
        // Utiliser l'observateur partagé au lieu d'en créer un nouveau
        if (this.$refs.lazyContainer) {
          // Enregistrer le callback pour cet élément
          observerCallbacks.set(
            this.$refs.lazyContainer,
            this.handleVisibility
          );

          // Observer l'élément
          sharedObserver.observe(this.$refs.lazyContainer);
        }
      },

      destroyObserver() {
        // Nettoyer uniquement pour cet élément
        if (sharedObserver && this.$refs.lazyContainer) {
          sharedObserver.unobserve(this.$refs.lazyContainer);
          observerCallbacks.delete(this.$refs.lazyContainer);
        }
      },
    },
  };
</script>

<style scoped>
  .lazy-placeholder {
    min-height: 100px;
    background-color: #f5f5f5;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .placeholder-content {
    width: 100%;
    height: 100%;
    min-height: 100px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }

  @keyframes loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }
</style>
