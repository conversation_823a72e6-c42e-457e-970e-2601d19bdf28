<template>
  <div class="optimized-image-container" :style="containerStyle">
    <!-- Placeholder qui s'affiche pendant le chargement -->
    <div 
      v-if="showPlaceholder && !imageLoaded" 
      class="image-placeholder"
      :style="placeholderStyle"
    ></div>
    
    <!-- Image réelle avec lazy loading -->
    <img
      :src="imageSrc"
      :alt="alt"
      :width="width"
      :height="height"
      :class="{ 'image-loaded': imageLoaded, 'image-loading': !imageLoaded }"
      @load="onImageLoaded"
      @error="onImageError"
      loading="lazy"
    />
  </div>
</template>

<script>
import { getOptimizedImage, generatePlaceholder } from '@/utils/imageOptimizer';

export default {
  name: 'OptimizedImage',
  
  props: {
    // Source de l'image
    src: {
      type: [String, Object],
      required: true
    },
    // Texte alternatif
    alt: {
      type: String,
      default: ''
    },
    // Largeur de l'image
    width: {
      type: [Number, String],
      default: null
    },
    // Hauteur de l'image
    height: {
      type: [Number, String],
      default: null
    },
    // Format souhaité (webp, jpeg, png)
    format: {
      type: String,
      default: 'webp'
    },
    // Qualité de l'image (1-100)
    quality: {
      type: Number,
      default: 80
    },
    // Afficher un placeholder pendant le chargement
    showPlaceholder: {
      type: Boolean,
      default: true
    },
    // Style d'objet pour l'image
    objectFit: {
      type: String,
      default: 'cover'
    },
    // Priorité de chargement (pour les images critiques)
    priority: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      imageLoaded: false,
      hasError: false,
      placeholderSrc: ''
    };
  },
  
  computed: {
    // Source optimisée de l'image
    imageSrc() {
      return getOptimizedImage(this.src, {
        width: this.width,
        height: this.height,
        format: this.format,
        quality: this.quality
      });
    },
    
    // Style pour le conteneur
    containerStyle() {
      return {
        width: this.width ? `${this.width}px` : '100%',
        height: this.height ? `${this.height}px` : 'auto',
        position: 'relative',
        overflow: 'hidden'
      };
    },
    
    // Style pour le placeholder
    placeholderStyle() {
      return {
        backgroundImage: this.placeholderSrc ? `url(${this.placeholderSrc})` : null,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        filter: 'blur(10px)',
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        transition: 'opacity 0.3s ease'
      };
    }
  },
  
  mounted() {
    // Si l'image est prioritaire, on la charge immédiatement
    if (this.priority) {
      const img = new Image();
      img.src = this.imageSrc;
    }
    
    // Générer un placeholder si nécessaire
    if (this.showPlaceholder) {
      this.placeholderSrc = generatePlaceholder(this.src);
    }
  },
  
  methods: {
    // Quand l'image est chargée
    onImageLoaded() {
      this.imageLoaded = true;
      this.$emit('loaded');
    },
    
    // En cas d'erreur de chargement
    onImageError() {
      this.hasError = true;
      this.$emit('error');
      
      // Charger une image par défaut
      this.imageSrc = require('@/assets/home/<USER>');
    }
  }
};
</script>

<style scoped>
.optimized-image-container {
  display: inline-block;
}

.optimized-image-container img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: var(--object-fit, cover);
  transition: opacity 0.3s ease;
}

.image-loading {
  opacity: 0;
}

.image-loaded {
  opacity: 1;
}

.image-placeholder {
  z-index: 1;
}

.image-loaded + .image-placeholder {
  opacity: 0;
}
</style>
