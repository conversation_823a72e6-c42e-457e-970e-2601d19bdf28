<template>
  <div 
    ref="container" 
    class="virtual-list-container"
    @scroll="handleScroll"
  >
    <div 
      class="virtual-list-spacer" 
      :style="{ height: `${totalHeight}px` }"
    >
      <div 
        class="virtual-list-items"
        :style="{ transform: `translateY(${startOffset}px)` }"
      >
        <slot 
          v-for="item in visibleItems" 
          :key="item.id || item.key || item"
          :item="item"
          :index="getItemOriginalIndex(item)"
        ></slot>
      </div>
    </div>
  </div>
</template>

<script>
import { throttle } from '@/utils/performance';

export default {
  name: 'VirtualList',
  
  props: {
    // Liste complète des éléments
    items: {
      type: Array,
      required: true
    },
    // Hauteur estimée de chaque élément (en pixels)
    itemHeight: {
      type: Number,
      default: 100
    },
    // Nombre d'éléments à rendre en plus (buffer)
    buffer: {
      type: Number,
      default: 5
    }
  },
  
  data() {
    return {
      // Position de défilement actuelle
      scrollTop: 0,
      // Hauteur du conteneur
      containerHeight: 0,
      // Hauteurs réelles des éléments (si mesurées)
      itemHeights: {},
      // Indique si les hauteurs des éléments ont été mesurées
      heightsMeasured: false
    };
  },
  
  computed: {
    // Hauteur totale de la liste
    totalHeight() {
      if (this.heightsMeasured) {
        return this.items.reduce((total, item, index) => {
          return total + (this.itemHeights[index] || this.itemHeight);
        }, 0);
      }
      return this.items.length * this.itemHeight;
    },
    
    // Index de l'élément de départ
    startIndex() {
      return Math.max(0, Math.floor(this.scrollTop / this.itemHeight) - this.buffer);
    },
    
    // Index de l'élément de fin
    endIndex() {
      const visibleCount = Math.ceil(this.containerHeight / this.itemHeight);
      return Math.min(
        this.items.length - 1,
        this.startIndex + visibleCount + this.buffer * 2
      );
    },
    
    // Décalage du début de la liste visible
    startOffset() {
      if (this.heightsMeasured) {
        let offset = 0;
        for (let i = 0; i < this.startIndex; i++) {
          offset += this.itemHeights[i] || this.itemHeight;
        }
        return offset;
      }
      return this.startIndex * this.itemHeight;
    },
    
    // Éléments actuellement visibles
    visibleItems() {
      return this.items.slice(this.startIndex, this.endIndex + 1);
    }
  },
  
  mounted() {
    // Initialiser la hauteur du conteneur
    this.updateContainerHeight();
    
    // Ajouter un écouteur de redimensionnement
    window.addEventListener('resize', this.handleResize);
    
    // Émettre les éléments visibles initiaux
    this.$nextTick(() => {
      this.emitVisibleItems();
    });
  },
  
  beforeUnmount() {
    // Nettoyer l'écouteur de redimensionnement
    window.removeEventListener('resize', this.handleResize);
  },
  
  methods: {
    // Gérer le défilement
    handleScroll: throttle(function(event) {
      this.scrollTop = event.target.scrollTop;
      this.emitVisibleItems();
    }, 100),
    
    // Gérer le redimensionnement
    handleResize: throttle(function() {
      this.updateContainerHeight();
    }, 200),
    
    // Mettre à jour la hauteur du conteneur
    updateContainerHeight() {
      if (this.$refs.container) {
        this.containerHeight = this.$refs.container.clientHeight;
      }
    },
    
    // Émettre les éléments visibles
    emitVisibleItems() {
      this.$emit('visible-items-changed', {
        items: this.visibleItems,
        startIndex: this.startIndex,
        endIndex: this.endIndex
      });
    },
    
    // Mesurer les hauteurs réelles des éléments
    measureItemHeights() {
      this.heightsMeasured = true;
      this.$nextTick(() => {
        const itemElements = this.$el.querySelectorAll('.virtual-list-items > *');
        itemElements.forEach((el, index) => {
          const realIndex = this.startIndex + index;
          this.itemHeights[realIndex] = el.offsetHeight;
        });
      });
    },
    
    // Obtenir l'index original d'un élément
    getItemOriginalIndex(item) {
      return this.items.findIndex(i => 
        (i.id && item.id && i.id === item.id) || 
        (i.key && item.key && i.key === item.key) || 
        i === item
      );
    },
    
    // Faire défiler jusqu'à un élément spécifique
    scrollToItem(index) {
      if (index < 0 || index >= this.items.length) return;
      
      let offset = 0;
      if (this.heightsMeasured) {
        for (let i = 0; i < index; i++) {
          offset += this.itemHeights[i] || this.itemHeight;
        }
      } else {
        offset = index * this.itemHeight;
      }
      
      if (this.$refs.container) {
        this.$refs.container.scrollTop = offset;
      }
    }
  },
  
  watch: {
    // Surveiller les changements dans la liste d'éléments
    items: {
      handler() {
        this.$nextTick(() => {
          this.emitVisibleItems();
        });
      },
      deep: true
    }
  }
};
</script>

<style scoped>
.virtual-list-container {
  overflow-y: auto;
  position: relative;
  height: 100%;
  width: 100%;
}

.virtual-list-spacer {
  position: relative;
  width: 100%;
}

.virtual-list-items {
  position: absolute;
  width: 100%;
  will-change: transform;
}
</style>
