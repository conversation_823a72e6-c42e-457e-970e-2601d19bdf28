<template>
  <!--v-autocomplete
    v-model="location"
    label="Taper votre ville"
    type="text"
    onchange="location"
  /-->
  <v-text-field
    v-model="location"
    :label="textContent"
    type="text"
    :readonly="readOnly"
    @input="updateLocationValues"
  />
</template>

<script>
  export default {
    name: 'LocationInput',
    props: {
      paramLocation: {
        type: String,
        default: '',
      },
      textContent: {
        type: String,
        default: 'Saisis ton adresse complète',
      },
      readOnly: {
        type: Boolean,
      },
    },

    data() {
      return {
        citiesList: [],
        cities: ['Abe Paris', 'Paris', 'New York', 'London'],
        postalCodeList: [['75000'], ['69000'], ['13000'], ['34000']],
        location: '',
        postalCode: null,
      };
    },

    watch: {
      paramLocation: {
        immediate: true,
        handler(newVal) {
          if (newVal) {
            this.address = newVal;
          }
        },
      },
    },

    methods: {
      async fetchCities(event) {
        const query = event.target.value;
        if (!query || query.length < 2) {
          return;
        }

        try {
          const [listNom, listCode] = await getCityList(query);
          if (!Array.isArray(listNom) || !Array.isArray(listCode)) {
            //console.error('Les données récupérées ne sont pas valides.');
            return;
          }
          this.citiesList = listNom;
          this.postalCodeList = listCode;
        } catch (error) {
          //console.error('Erreur lors de la récupération des données :', error);
        }
      },
      async fetchFranceCities() {
        try {
          var headers = new Headers();
          headers.append('X-CSCAPI-KEY', 'API_KEY');

          var requestOptions = {
            method: 'GET',
            headers: headers,
            redirect: 'follow',
          };

          const response = await fetch('/fr.json');
          if (!response.ok) throw new Error('Failed to fetch cities');

          const data = await response.json();
          this.cities = data.map((city) => city.city); // Store city names in the array
          this.sortedCities = [...this.cities];
        } catch (error) {
          //console.error('Error fetching cities:', error);
        } finally {
          this.loading = false;
        }
      },
      resetFormDataAndUrl() {
        const params = { ...this.$route.query };
        delete params.ville;
        delete params.cp;
        this.$router.replace({ query: params }).catch((err) => {
          //console.error("Erreur lors de la mise à jour de l'URL:", err);
        });
        this.$emit('city-and-postal-code', ['', '']);
        this.address = '';
      },
      updateLocationValues() {
        if (!this.address) {
          this.$emit('city-and-postal-code', ['', '']);
          return;
        }

        // Nous utilisons l'adresse complète comme valeur de ville
        // et laissons le code postal vide (il sera rempli manuellement si nécessaire)
        this.$emit('city-and-postal-code', [this.address, '']);
      },
    },
  };
</script>

<style scoped>
  .v-text-field {
    width: 100%;
  }
</style>
