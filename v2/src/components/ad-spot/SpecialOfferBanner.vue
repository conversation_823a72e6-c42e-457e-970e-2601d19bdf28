<template>
  <div class="ad-sale-container">
    <div>
      <h2>Promotion</h2>
      <h5>20% offert sur votre 1er mois</h5>
    </div>
    <div class="ad-sale-button">
      <PrimaryRoundedButton textContent="J'en profite !" />
    </div>
  </div>
</template>

<script>
import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';

export default {
  name: 'SpecialOfferBanner',
  components: {
    PrimaryRoundedButton,
  },
};
 
</script>

<style scoped>
h2, h5 {
  color: var(--surface-bg);
}

h2 {
  padding-top: 15px;
}

.ad-sale-container {
  min-width: 722px;
  height: 123px;
  margin-top: 50px;
  padding-inline: 35px;      
  border-radius: 2px;

  background-color: var(--text-1);
  background-image: url('/src/assets/background/ad-shape-background.svg');
  background-position: right;
  background-repeat: no-repeat;
}

.ad-sale-button {
  display: flex;
  justify-content: center;
  top: -30px;
  position: relative;
}

</style>
