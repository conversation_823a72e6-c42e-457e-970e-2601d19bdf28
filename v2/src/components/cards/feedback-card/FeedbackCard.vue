<template>
  <article class="card-container">
    <img :src="feedback.img" alt="Image de avatar'" />
    <h6>{{ feedback.title }}</h6>
    <p class="card-subtitle">{{ feedback.subtitle }}</p>
    <div class="stars">
      <span v-for="n in 5" :key="n">
        <img
          v-if="n <= feedback.note"
          src="@/assets/icons/star-filled.svg"
          alt="Étoile remplie"
        />
        <img v-else src="@/assets/icons/star-empty.svg" alt="Étoile vide" />
      </span>
    </div>

    <p class="card-description">"{{ feedback.description }}"</p>
  </article>
</template>

<script>
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import StarFilled from '@/assets/icons/star-filled.svg';
  import StarEmpty from '@/assets/icons/star-empty.svg';

  export default {
    name: 'FeedbackCard',
    components: {
      PrimaryRoundedButton,
      StarFilled,
      StarEmpty,
    },
    props: {
      feedback: {
        type: Object,
        default: {},
      },
    },
  };
</script>

<style scoped>
  .card-container {
    padding: 16px;
    display: flex;
    gap: 7px;
    flex-direction: column;
    align-items: center;
    background-color: var(--home-cards-bg-color);
    width: 270px;
    height: 300px;
    border-radius: 30px;
  }

  .card-container img {
    height: 65px;
    width: 65px;
    border-radius: 50%;
  }

  .card-container h2 {
    margin-bottom: 40px;
  }

  .card-subtitle {
    font-size: 12px;
  }

  .card-description {
    font-size: 14px;
    font-style: italic;

    text-align: center;
  }

  .stars {
    display: flex;
    gap: 5px;
  }

  .stars img {
    height: 16px;
    width: 16px;
  }
</style>
