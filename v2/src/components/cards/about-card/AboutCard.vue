<template>
<article class="about-card-container">

  <div class="about-card-container-header">
    <h2>{{ title }}</h2>
  </div>

  <p class="text">{{description}}</p>

</article>
</template>

<script>

export default {
  name: 'AboutCard',

  props: {
    title: {
      type: String,
    },
    description: {
      type: String,
    },
  },
};
</script>

<style scoped>
.about-card-container {
  height: 300px;
  width: 372px;
  border-radius: 20px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  background-color: var(--primary-1b2);
}

.about-card-container-header {
  height: 30%;

}

.text {
  height: 60%;
}

@media screen and (min-width: 1200px) {
  .about-card-container {
    height: 250px;
  }
  
}

</style>