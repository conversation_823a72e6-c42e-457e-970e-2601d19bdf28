<template>
  <article
    class="joboffer-card"
    :class="[
      { 'highlight-card': jobOffer.source === 'Thanks_boss' },
      getPriorityClass(jobOffer)
    ]"
  >
    <div
      class="card-header"
      :class="{ 'highlight-header': jobOffer.source === 'Thanks_boss' }"
    >
      <div class="card-header-1">
        <!-- job company logo -->
        <div class="logo-wrapper">
          <img
            v-if="jobOffer.logo_url && (jobOffer.logo_url || jobOffer.logo_img)"
            :src="getImgPath(jobOffer.logo_url || jobOffer.logo_img)"
            alt="logo"
            class="logo"
          />
          <img
            v-else-if="jobOffer.source === 'Thanks_boss'"
            src="@/assets/icons/tb-logo-borderblack.svg"
            alt="Thanks Boss Logo"
            class="logo"
          />
          <img
            v-else-if="jobOffer.source === 'FT'"
            src="@/assets/icons/FT-logo.svg"
            alt="France Travail Logo"
            class="logo"
          />
          <img
            v-else
            src="@/assets/icons/company-logo.png"
            alt="Default Company Logo"
            class="logo"
          />
        </div>

        <!-- job title -->
        <div class="title-wrapper">
          <h6>{{ jobOffer.title }}</h6>
        </div>
      </div>

      <!-- favorite job icon -->
      <div
        class="fav-wrapper"
        @click="handleFavoriteJobClick(jobOffer.id, jobOffer)"
      >
        <v-tooltip v-if="jobIsLiked" activator="parent" location="bottom"
          >Retirer des favoris</v-tooltip
        >
        <v-tooltip v-else activator="parent" location="bottom"
          >Ajouter aux favoris</v-tooltip
        >

        <img
          v-if="jobOffer.source === 'Thanks_boss'"
          :src="
            jobIsLiked === true
              ? require('@/assets/icons/heart-filled-black.svg')
              : require('@/assets/icons/heart-empty-black.svg')
          "
          alt="heart like filled"
          class="favorite-icon"
        />

        <img
          v-else
          :src="
            jobIsLiked === true
              ? require('@/assets/icons/heart-filled.svg')
              : require('@/assets/icons/heart-empty.svg')
          "
          alt="heart like empty"
          class="favorite-icon"
        />
      </div>
    </div>

    <div class="card-body">
      <div class="double-field-row top">
        <!-- location -->
        <div class="body-field">
          <img
            src="@/assets/search/search-page-card-location-icon.svg"
            alt="location icon"
          />
          <p>{{ jobOffer.local }}</p>
        </div>

        <!-- remote option -->
        <div class="body-field">
          <img
            src="@/assets/search/search-page-card-remote-icon.svg"
            alt="remote options icon"
          />
          <p>{{ remote }}</p>
        </div>
      </div>

      <div class="double-field-row bottom">
        <!-- contract type -->
        <div class="body-field">
          <img
            src="@/assets/search/search-page-card-contract-icon.svg"
            alt="contract icon"
          />
          <p>{{ jobOffer.contract }}</p>
        </div>

        <!-- experience required -->
        <div class="body-field">
          <img
            src="@/assets/search/search-page-card-exp-icon.svg"
            alt="experience icon"
          />
          <p>{{ jobOffer.expérience }}</p>
        </div>
      </div>
    </div>

    <div class="card-footer">
      <!-- job description -->
      <p class="cut-description-3">{{ jobOffer.descriptif }}</p>

      <!-- btn to see job detail -->
      <PrimaryRoundedButton
        textContent="Voir l'offre"
        @click="navigateToJobOffer(jobOffer.id, jobOffer, false)"
      />
    </div>
  </article>
</template>

<script>
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import {
    addFavoriteJob,
    removeFavoriteJob,
  } from '@/services/favoriteJob.service';
  import { TELETRAVAIL_FIELDS } from '@/utils/base/teletravail.js';
  import getImgPath from '@/utils/imgpath.js';
  import gotoPage from '@/utils/router.js';
  import { toaster } from '@/utils/toast/toast';
  import { navigateToJobOffer, getPriorityClass } from '@/utils/jobNavigation.js';
  import store from '../../../store';

  export default {
    name: 'LargeJobCard',

    props: {
      jobOffer: {
        type: Object,
        required: true,
      },
      favoriteJobList: {
        type: Array,
      },
    },

    components: {
      PrimaryRoundedButton,
    },

    data() {
      return {
        jobIsLiked: this.jobOffer.is_favorite,
        remote: '',
      };
    },

    async mounted() {
      let teletravailIndex = 0;

      if (this.jobOffer.jours_teletravail === 0) {
        teletravailIndex = 2;
      } else if (this.jobOffer.jours_teletravail === 5) {
        teletravailIndex = 0;
      } else {
        teletravailIndex = 1;
      }

      if (
        teletravailIndex >= 0 &&
        teletravailIndex < TELETRAVAIL_FIELDS.length
      ) {
        this.remote = TELETRAVAIL_FIELDS[teletravailIndex].teletravail;
      } else {
        //console.error(
        //  'Invalid jours_teletravail value:',
        //  this.jobOffer.jours_teletravail
        //);
        this.remote = 'Default Value'; // or handle the error appropriately
      }
    },

    methods: {
      gotoPage,
      getImgPath,
      navigateToJobOffer,
      getPriorityClass,
      openJobOffer(jobId) {
        window.open(`/offre-d-emplois/${jobId}`, '_blank');
      },
      //  handle click on favorite job icon, add or remove favorite job
      async handleFavoriteJobClick(jobId, jobOffer) {
        const isLoggedIn = store.getters.isLoggedIn;
        if (!isLoggedIn) {
          toaster.showErrorPopup(
            'Veuillez vous connecter pour ajouter des offres aux favoris.'
          );
          return;
        }

        if (this.jobIsLiked) {
          const response = await removeFavoriteJob(jobId);
          if (response) {
            toaster.showSuccessPopup('Annonce retirée des favoris.');
            this.jobIsLiked = false;
          }
        } else {
          const response = await addFavoriteJob(jobId, jobOffer);
          if (response) {
            toaster.showSuccessPopup('Annonce ajoutée aux favoris.');
            this.jobIsLiked = true;
          }
        }
      },
    },
  };
</script>

<style scoped>
  /* HelloWork-inspired design */
  .joboffer-card {
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-radius: 12px;
    width: 100%;
    max-width: 320px;
    margin: 0 auto;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease-in-out;
    overflow: hidden;
  }

  .joboffer-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
    border-color: #d1d5db;
  }

  /* Priority styling for Thanks-Boss jobs */
  .priority-job {
    border: 2px solid #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
  }

  .priority-job:hover {
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.25);
    border-color: #2563eb;
  }

  /* Legacy highlight styling for backward compatibility */
  .highlight-card {
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .highlight-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #d1d5db;
  }

  .card-header {
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    display: flex;
    justify-content: space-between;
    padding: 20px;
    background-color: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
  }

  .highlight-header {
    background-color: #eff6ff;
    border-bottom-color: #3b82f6;
  }

  .priority-job .card-header {
    background-color: #eff6ff;
    border-bottom-color: #3b82f6;
  }

  .card-header-1 {
    display: flex;
    gap: 12px;
    align-items: center;
    flex: 1;
  }

  .logo-wrapper {
    width: 48px;
    height: 48px;
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .logo {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    object-fit: contain;
  }

  .title-wrapper {
    flex: 1;
    min-width: 0;
  }

  .title-wrapper h3 {
    font-size: 16px;
    font-weight: 600;
    color: #111827;
    margin: 0 0 4px 0;
    line-height: 1.4;
  }
  .card-body {
    padding: 16px 20px;
    width: 100%;
  }

  .double-field-row {
    display: flex;
    gap: 24px;
    width: 100%;
    flex-wrap: wrap;
  }

  .bottom {
    margin-top: 12px;
  }

  .body-field {
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 1;
    min-width: 120px;
  }

  .body-field img {
    width: 16px;
    height: 16px;
    opacity: 0.7;
  }

  .body-field p {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
    font-weight: 500;
  }

  .card-footer {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 20px;
    border-top: 1px solid #f3f4f6;
    background-color: #fafafa;
  }

  .card-footer p {
    font-size: 14px;
    color: #4b5563;
    line-height: 1.5;
    margin: 0;
  }

  .fav-wrapper {
    cursor: pointer;
  }

  .favorite-icon {
    width: 24px;
    height: 24px;
  }

  .cut-description-2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    line-clamp: 2;
    -webkit-line-clamp: 2; /* edit this line to change the number of lines displayed */
    line-height: 1.2;
  }

  .cut-description-3 {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    line-clamp: 3;
    -webkit-line-clamp: 3; /* edit this line to change the number of lines displayed */
    line-height: 1.2;
  }

  /* @media screen and (min-width: 992px) {
    .double-field-row {
      justify-content: center;
    }
  } */
</style>
