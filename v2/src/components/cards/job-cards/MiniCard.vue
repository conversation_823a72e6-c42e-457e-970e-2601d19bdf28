<template>
<div class="job-card-container border-radius-5">
  <!-- header -->
  <div class="job-card-header">
    <!-- company logo with job title  -->
    <div class="logo-wrapper job-card-header-left d-flex a-center">
      <img 
            v-if="job.authorInfo && (job.authorInfo.logo_url || job.authorInfo.logo_img)" 
            :src="getImgPath(job.authorInfo.logo_url || job.authorInfo.logo_img)" 
            alt="logo" 
            class="logo job-card-header-left-logo border-radius-5" 
          />
      
      <img v-else-if="job.source === 'Thanks_boss'" src="@/assets/icons/tb-logo-borderblack.svg" alt="Thanks Boss Logo" class="logo job-card-header-left-logo border-radius-5" />
      
      <img v-else-if="job.source === 'FT'" src="@/assets/icons/FT-logo.svg" alt="France Travail Logo" class="logo job-card-header-left-logo border-radius-5" />
      
      <img v-else src="@/assets/icons/company-logo.png" alt="Default Company Logo" class="logo job-card-header-left-logo border-radius-5" />
      
      <h4>{{ job.title }}</h4>
    </div>

    <!-- heart ico with dynamic class or src for favorite -->
    <img 
        class="job-card-header-heart cursor-pointer" 
        :src="isFavorite ? require('@/assets/search/search-page-card-like-icon-filled.svg') 
        : require('@/assets/search/search-page-card-like-icon.svg')"
        @click="$emit('heart-click', job.id)" 
      />

  </div>
  <div class="padding">
    <!-- localisation row -->
    <div class="job-card-localisation d-flex a-center body-field  ">
      <img class="job-card-icon" src="@/assets/icons/localization.svg" />
      <p class="job-card-localisation-text">{{ job.local }}</p>
    </div>

    <!-- contract type -->
    <div class="body-field ">
      <img class="job-card-icon" src="@/assets/search/search-page-card-contract-icon.svg" alt="contract icon" />
      <p>{{ job.contract }}</p>
    </div>

    <!-- remote option -->
    <div class="body-field" v-if="job.remote">
      <img class="job-card-icon" src="@/assets/search/search-page-card-remote-icon.svg" alt="remote options icon" />
      <p>{{ job.remote }}</p>
    </div>

    <!-- experience required -->
    <div class="body-field ">
      <img class="job-card-icon" src="@/assets/search/search-page-card-exp-icon.svg" alt="experience icon" />
      <p class="cut-description-3 ">{{ job.expérience }}</p>
    </div>
  </div>
  <div class="center">
    <!-- button -->
    <PrimaryRoundedButton textContent="Voir l'offre" @click="navigateToJobOffer(job.id, job, true)" />
  </div>
</div>
</template>

<script>
import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
import getImgPath from '@/utils/imgpath.js';
import { navigateToJobOffer } from '@/utils/jobNavigation.js';

export default {
  name: 'MiniCard',

  components: {
    PrimaryRoundedButton
  },

  props: {
    job: {
      type: Object,
      default: () => ({
        title: 'Nom du poste',
        local: 'Localisation',
        logo_url: '',
        remote: 'Type de travail',
        contract: 'Type de contrat recherché',
        expérience: 'Niv Xp',
      }),
    },

    isFavorite: {
      type: Boolean,
      default: false,
    },
  },

  methods: {
    navigateToJobOffer,
    getImgPath,
    openInNewTab() {
      window.open(`/offre-d-emplois/${this.job.id}`, '_blank');
    },
  },

};
</script>

<style scoped>
.body-field {
  display: flex;
  margin: 4px;

}

.center {
  display: flex;
  justify-content: center;
}

.center button {
  width: 85%;
}

h4 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  line-clamp: 2;
  -webkit-line-clamp: 2; /* edit this line to change the number of lines displayed */
  line-height: 1.2;
  font-size: 16px;
}

p {
  font-size: 12px;
}

.padding {
  padding: 4px;
}

/* container */
.job-card-container {
  width: 225px;
  height: 220px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  background-color: var(--surface-bg-2);
  justify-content: center;

}

/* header */
.job-card-header {
  width: 100%;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.job-card-header-left {
  width: 83%;
}

.job-card-header-left-logo {
  width: 40px;
  height: 40px;
  margin-right: 12px;
  border: 1px solid var(--primary-3);
}

.job-card-header-heart {
  height: 24px;
  width: 24px;
}

/* localisation */
.job-card-icon {
  margin-right: 5px;
  height: 20px;
  width: 20px;
}

.job-card-localisation-text {
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
