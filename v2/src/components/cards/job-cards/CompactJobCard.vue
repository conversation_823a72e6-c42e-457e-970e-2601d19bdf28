<template>
  <div
    v-if="job"
    class="job-card-container"
    :class="[
      { 'highlight-card': job.source === 'Thanks_boss' },
      getPriorityClass(job)
    ]"
  >
    <!-- header -->
    <div
      class="job-card-header"
      :class="{ 'highlight-header': job.source === 'Thanks_boss' }"
    >
      <!-- company logo with job title  -->
      <div class="logo-wrapper job-card-header-left d-flex a-center">
        <img
          v-if="authorInfo && (authorInfo.logo_url || authorInfo.logo_img)"
          :src="getImgPath(authorInfo.logo_url || authorInfo.logo_img)"
          alt="logo"
          class="logo job-card-header-left-logo border-radius-5"
        />

        <img
          v-else-if="job.source === 'Thanks_boss'"
          src="@/assets/icons/tb-logo-borderblack.svg"
          alt="Thanks Boss Logo"
          class="logo job-card-header-left-logo border-radius-5"
        />

        <img
          v-else-if="job.source === 'FT'"
          src="@/assets/icons/FT-logo.svg"
          alt="France Travail Logo"
          class="logo job-card-header-left-logo border-radius-5"
        />

        <img
          v-else
          src="@/assets/icons/company-logo.png"
          alt="Default Company Logo"
          class="logo job-card-header-left-logo border-radius-5"
        />
        <h4>{{ job.title }}</h4>
      </div>
      <!-- heart ico -->
      <div
        class="fav-wrapper job-card-header-heart cursor-pointer"
        @click="handleFavoriteJobClick(job.id, job)"
      >
        <v-tooltip v-if="jobIsLiked" activator="parent" location="bottom"
          >Retirer des favoris</v-tooltip
        >
        <v-tooltip v-else activator="parent" location="bottom"
          >Ajouter aux favoris</v-tooltip
        >

        <img
          v-if="job.source === 'Thanks_boss'"
          :src="
            jobIsLiked === true
              ? require('@/assets/icons/heart-filled-black.svg')
              : require('@/assets/icons/heart-empty-black.svg')
          "
          alt="heart like filled"
          class="favorite-icon"
        />

        <img
          v-else
          :src="
            jobIsLiked === true
              ? require('@/assets/icons/heart-filled.svg')
              : require('@/assets/icons/heart-empty.svg')
          "
          alt="heart like empty"
          class="favorite-icon"
        />
      </div>
    </div>
    <!-- localisation row -->
    <div class="job-card-localisation d-flex a-center">
      <img
        class="job-card-localisation-icon"
        src="@/assets/icons/localization.svg"
      />
      <p class="job-card-localisation-text">{{ job.local }}</p>
    </div>
    <!-- body row -->
    <div class="job-card-description">
      <p>{{ job.descriptif }}</p>
    </div>
    <!-- button -->
    <div class="job-card-button">
      <PrimaryRoundedButton
        textContent="Voir l'offre"
        @click="navigateToJobOffer(job.id, job, false)"
      />
    </div>
  </div>
</template>
<script>
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import { getUserById } from '@/services/account.service.js';
  import {
    addFavoriteJob,
    removeFavoriteJob,
  } from '@/services/favoriteJob.service';
  import { isJobFavoriteById } from '@/services/job.service.js';
  import getImgPath from '@/utils/imgpath.js';
  import { toaster } from '@/utils/toast/toast';
  import { navigateToJobOffer, getPriorityClass } from '@/utils/jobNavigation.js';
  import store from '../../../store';

  export default {
    name: 'CompactJobCard',

    components: {
      PrimaryRoundedButton,
    },

    props: {
      job: {
        type: Object,
        default: () => ({
          logo_url: '',
        }),
      },
    },

    data() {
      return {
        authorInfo: null, // Stockera les infos utilisateur
        jobIsLiked: false,
      };
    },

    async mounted() {
      this.fetchAuthorInfo();

      this.jobIsLiked = isJobFavoriteById(this.job.id);
    },

    methods: {
      getImgPath,
      navigateToJobOffer,
      getPriorityClass,

      async fetchAuthorInfo() {
        // Si le job a un auteur, récupérez les infos utilisateur
        if (this.job.author) {
          try {
            this.authorInfo = await getUserById(this.job.author);
          } catch (error) {
            //console.error(
            //  'Erreur lors de la récupération des informations de l’auteur:',
            //  error
            //);
          }
        }
      },

      //  handle click on favorite job icon, add or remove favorite job
      async handleFavoriteJobClick(jobId, job) {
        const isLoggedIn = store.getters.isLoggedIn;
        if (!isLoggedIn) {
          toaster.showErrorPopup(
            'Veuillez vous connecter pour ajouter des offres aux favoris.'
          );
          return;
        }

        if (this.jobIsLiked) {
          const response = await removeFavoriteJob(jobId);
          if (response) {
            toaster.showSuccessPopup('Annonce retirée des favoris.');
            this.jobIsLiked = false;
          }
        } else {
          const response = await addFavoriteJob(jobId, job);
          if (response) {
            toaster.showSuccessPopup('Annonce ajoutée aux favoris.');
            this.jobIsLiked = true;
          }
        }
      },
    },
  };
</script>

<style scoped>
  h4 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    line-clamp: 2;
    -webkit-line-clamp: 2; /* edit this line to change the number of lines displayed */
    line-height: 1.2;
    font-size: 16px;
  }

  p {
    font-size: 12px;
  }

  /* HelloWork-inspired container design */
  .job-card-container {
    width: 100%;
    max-width: 280px;
    height: 280px;
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease-in-out;
    overflow: hidden;
  }

  .job-card-container:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
    border-color: #d1d5db;
  }

  /* Priority styling for Thanks-Boss jobs */
  .priority-job {
    border: 2px solid #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
  }

  .priority-job:hover {
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.25);
    border-color: #2563eb;
  }

  /* Legacy highlight styling */
  .highlight-card {
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .highlight-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #d1d5db;
  }

  /* header */
  .job-card-header {
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
    display: flex;
    justify-content: space-between;
    padding: 10px;
  }
  .highlight-header {
    background-color: #eff6ff;
    border-bottom-color: #3b82f6;
  }

  .job-card-header-left {
    width: 83%;
  }

  .job-card-header-left-logo {
    height: 40px;
    margin-right: 12px;
  }

  .job-card-header-heart {
    height: 24px;
    width: 24px;
  }
  .job-card-localisation {
    padding: 0 20px;
    margin-top: 20px;
  }
  /* localisation */
  .job-card-localisation-icon {
    margin-right: 5px;
    height: 20px;
    width: 20px;
  }

  .job-card-localisation-text {
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* description */
  .job-card-description {
    height: 50%;
    padding: 0 20px;
    margin-top: 20px;
  }

  .job-card-description p {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    line-clamp: 3;
    -webkit-line-clamp: 3; /* edit this line to change the number of lines displayed */
    line-height: 1.2;
  }

  .job-card-button {
    padding: 0 20px;
    margin: 20px 0;
    text-align: center;
  }
</style>
