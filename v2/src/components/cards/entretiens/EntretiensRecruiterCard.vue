<template>
  <div
    :class="{
      'entretien-card': true,
      new: postulation.status === 'next',
    }"
  >
    <div class="entretien-horizontal">
      <div
        v-if="availability"
        :class="{
          'date-container': true,
          'prochainement-text':
            postulation.status === 'next' && availability.date === todayDate,
          'attente-text': postulation.status === 'pending',
          'passe-text': postulation.status === 'passed',
          'annule-text': postulation.status === 'canceled',
        }"
      >
        <p class="date-jour">
          {{ formatDay(availability?.date || postulation.entretien.date) }}
        </p>
        <p class="date-date">
          {{ formatDateNum(availability?.date || postulation.entretien.date) }}
        </p>
      </div>
      <div
        v-else
        :class="{
          'date-container': true,
          'attente-text': postulation.status === 'pending',
          'passe-text': postulation.status === 'passed',
          'annule-text': postulation.status === 'canceled',
        }"
      >
        <p class="date-jour"></p>
        <p class="date-date">!</p>
      </div>

      <!-- Détails de l'entretien -->
      <div class="entretien-details">
        <div class="img-div">
          <img :src="getImgPath(postulation.user_photo) || imageAvatar" />
        </div>
        <div>
          <h6>
            {{ postulation.user_first_name }}
            {{ postulation.user_last_name }}
          </h6>
          <p v-if="availability?.starttime">
            {{ postulation.jobOffer.title }} -
            {{ formatTime(availability.starttime) }}
          </p>
          <p v-else>
            {{ postulation.jobOffer.title }}
          </p>
        </div>
      </div>

      <!-- Bouton pour rejoindre l'entretien -->
      <div
        class="options-btn"
        v-if="postulation.status === 'next' || postulation.status === 'pending'"
      >
        <button class="sidebar-icon">
          <v-tooltip location="top" activator="parent">
            <span>Fonctionnalité bientôt disponible</span>
          </v-tooltip>
          <img src="@/assets/icons/three-dots-option.svg" />
        </button>
        <PrimaryRoundedButton
          v-if="canJoin"
          textContent="Rejoindre l'entretien"
          camera
          @click="showConfirmationModal = true"
        />
        <PrimaryRoundedButton
          v-else-if="postulation.status === 'next'"
          :textContent="buttonText"
          camera
          disabled
        />
        <PrimaryRoundedButton
          v-else-if="postulation.status === 'pending'"
          textContent="Message"
          email
          @click="sendEmail"
        />
      </div>
    </div>
  </div>
  <ConfirmationCallModal
    v-model:show="showConfirmationModal"
    @confirm="handleConfirmJoinEntretien"
    @cancel="showConfirmationModal = false"
  />
</template>

<script>
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import getImgPath from '@/utils/imgpath.js';
  import { format } from 'date-fns';
  import { fr } from 'date-fns/locale';
  import { useRouter } from 'vue-router';
  import ConfirmationCallModal from '@/components/modals/ConfirmationCallModal.vue';

  export default {
    name: 'EntretiensRecruiterCard',

    setup() {
      const router = useRouter();
      return { router };
    },

    components: {
      PrimaryRoundedButton,
      ConfirmationCallModal
    },

    props: {
      postulation: {
        type: Object,
        required: true
      },
      jobOffer: Object,
      imageAvatar: String,
      todayDate: String,
      availability: Object,
      canJoin: Boolean,
    },

    data() {
      return {
        showConfirmationModal: false
      }
    },

    computed: {
      canJoin() {
        if (!this.availability?.starttime || !this.availability?.endtime) return false;
        
        const now = new Date();
        const start = new Date(this.availability.starttime.replace(/Z$/, ''));
        const end = new Date(this.availability.endtime.replace(/Z$/, ''));

        return now >= start && now <= end;
      },
      isBeforeEntretien() {
        if (!this.availability?.starttime) return true;

        const now = new Date();
        const start = new Date(this.availability.starttime.replace(/Z$/, ''));
        
        return now < start;
      },
      isAfterEntretien() {
        if (!this.availability?.endtime) return false;

        const now = new Date();
        const end = new Date(this.availability.endtime.replace(/Z$/, ''));
        
        return now > end;
      },
      buttonText() {
        if (this.isBeforeEntretien) return 'Entretien à venir';
        if (this.isAfterEntretien) return 'Entretien terminé';
        return 'Rejoindre l\'entretien';
      }
    },

    methods: {
      getImgPath,

      formatTime(dateTime) {
        const date = new Date(dateTime);
        return `${date.getUTCHours().toString().padStart(2, '0')}:${date
          .getUTCMinutes()
          .toString()
          .padStart(2, '0')}`;
      },

      formatDay(date) {
        return format(new Date(date), 'EEE', { locale: fr }).replace('.', '');
      },
      formatDateNum(date) {
        return format(new Date(date), 'dd');
      },

      sendEmail() {
        if (!this.postulation?.postulant?.email) {
          alert("L'adresse email du postulant est introuvable.");
          return;
        }

        const email = this.postulation.email;
        const subject = encodeURIComponent('Entretien en attente');
        const body = encodeURIComponent(
          `Bonjour ${this.postulation.user_first_name},

  Je vous contacte concernant votre entretien pour le poste de "${this.postulation.jobOffer.title}".

  N'hésitez pas à me répondre si vous avez des questions.

  Cordialement,
  ${this.postulation.jobOffer.nom_recruteur}`
        );

        window.location.href = `mailto:${email}?subject=${subject}&body=${body}`;
      },
      handleConfirmJoinEntretien() {
        this.showConfirmationModal = false;
        this.joinEntretien();
      },
      joinEntretien() {
        if (!this.postulation?.user_id) {
          //console.error('ID utilisateur non trouvé');
          return;
        }

        this.$router.push({
          path: '/messagerie',
          query: { 
            chat: this.postulation.user_id,
            call: 'true',
            entretien: 'true',
            entretien_id: this.postulation.id
          }
        });
      }
    },
  };
</script>

<style scoped>
  .entretien-card {
    background: var(--surface-bg-2);
    border-radius: 25px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.15);
    padding: 8px;
  }
  .entretien-card.new {
    box-shadow: 0px 0px 10px var(--primary-1);
  }

  .entretien-horizontal {
    display: grid;
    grid-template-columns: 1fr 4fr 3fr;
    gap: 15px;
    align-items: center;
    min-height: 91px;
  }
  .entretien-horizontal.en-attente {
    display: grid;
    grid-template-columns: 5fr 3fr;
    gap: 15px;
    align-items: flex-start;
    min-height: 150px;
    padding: 10px 0 10px 10px;
  }
  .entretien-horizontal.en-attente .entretien-details {
    display: flex;
    justify-content: left;
    align-items: flex-start;
  }

  .date-container {
    border-right: 1px solid #c4c4c4;
    text-align: center;
  }
  .prochainement-text {
    color: var(--secondary-2);
  }
  .attente-text {
    color: var(--primary-1);
  }
  .passe-text {
    color: var(--gray-100);
  }
  .annule-text {
    color: #d55658;
  }

  .date-container .date-jour {
    font-size: 20px;
    font-weight: 500;
    margin-bottom: -0.5rem;
  }
  .date-container .date-date {
    margin-top: -0.5rem;
    font-size: 32px;
    font-weight: 500;
  }

  .entretien-details {
    display: flex;
    justify-content: left;
    align-items: center;
  }
  .img-div img {
    width: 45px;
    height: 45px;
    object-fit: cover;
    border-radius: 50%;
    margin-right: 10px;
  }
  .entretien-details .else {
    font-size: 12px;
    margin-top: 10px;
  }

  .options-btn {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 10px;
    margin: 0 10px 10px 10px;
    position: relative;
  }
  .options-btn:not(:has(.sidebar-icon)) {
    justify-content: flex-end;
    height: 100%;
  }
  .options-btn :deep(.v-btn) {
    font-size: 16px;
    font-weight: 500;
  }
  .options-btn .refuser {
    color: var(--gray-100);
    text-decoration: underline;
    cursor: pointer;
    font-size: 12px;
  }
  .options-btn .refuser:hover {
    opacity: 0.6;
  }
</style>
