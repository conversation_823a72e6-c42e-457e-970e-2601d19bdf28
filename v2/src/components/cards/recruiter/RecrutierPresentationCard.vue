<template>
  <div class="card">
    <div class="card-header">
      <img
        :src="
          user.photo
            ? getImgPath(user.logo_img)
            : require('@/assets/icons/company-logo.png')
        "
        alt="Logo de la compagnie"
        class="company-logo"
      />
      <div class="card-title title-edit-icon">
        <div>
          <h2>
            {{ user.company }}
          </h2>
          <p v-if="user.company_creation_year">
            <strong>Créée en : </strong> {{ user.company_creation_year }}
          </p>
          <p v-if="user.adress">
            <strong>Adresse : </strong> {{ user.adress }}
          </p>
          <!-- <p v-if="user.secteur"><strong>Secteur : </strong> {{ user.secteur }}</p> -->
        </div>
        <div class="actions">
          <img
            v-if="isRecruiterProfilePage"
            class="edit-icon"
            src="@/assets/icons/edit-icon-new.svg"
            alt="Edit"
            @click="goToSection('my-info-entreprise')"
          />
        </div>
      </div>
    </div>

    <div class="card-body">
      <div class="company-details">
        <div class="company-details-item">
          <div class="title-edit-icon">
            <h3>QUI SOMMES-NOUS</h3>
            <div class="actions">
              <img
                v-if="isRecruiterProfilePage"
                class="edit-icon"
                src="@/assets/icons/edit-icon-new.svg"
                alt="Edit"
                @click="goToSection('my-profile-entreprise')"
              />
            </div>
          </div>
          <p v-if="user.company_details && user.company_details.length">
            {{ user.company_details }}
          </p>
          <p v-else :class="{ 'owner-empty-item-message': owner }">
            {{
              owner
                ? 'Ajoutez un résumé de votre entreprise !'
                : "L'entreprise n'a pas spécifié de description"
            }}
          </p>
        </div>

        <div class="company-details-item">
          <div class="title-edit-icon">
            <h3>POURQUOI NOUS REJOINDRE</h3>
            <div class="actions">
              <img
                v-if="isRecruiterProfilePage"
                class="edit-icon"
                src="@/assets/icons/edit-icon-new.svg"
                alt="Edit"
                @click="goToSection('my-profile-entreprise')"
              />
            </div>
          </div>
          <p v-if="user.rejoindre && user.rejoindre.length">
            {{ user.rejoindre }}
          </p>
          <p v-else :class="{ 'owner-empty-item-message': owner }">
            {{
              owner
                ? 'Ajoutez une description sur pourquoi rejoindre votre entreprise !'
                : "L'entreprise n'a pas spécifié de description"
            }}
          </p>
        </div>

        <div class="company-details-item">
          <div class="title-edit-icon">
            <h3>AVANTAGES</h3>
            <div class="actions">
              <img
                v-if="isRecruiterProfilePage"
                class="edit-icon"
                src="@/assets/icons/edit-icon-new.svg"
                alt="Edit"
                @click="goToSection('my-profile-entreprise')"
              />
            </div>
          </div>
          <p v-if="user.avantages && user.avantages.length">
            <span
              v-for="(advantage, index) in formatAdvantages(user.avantages)"
              :key="index"
              class="advantage-item"
            >
              <Chip :textContent="advantage" />
            </span>
          </p>
          <p v-else :class="{ 'owner-empty-item-message': owner }">
            {{
              owner
                ? 'Ajoutez vos avantages !'
                : "L'entreprise n'a pas spécifié d'avantage"
            }}
          </p>
        </div>

        <div class="company-details-item">
          <div class="title-edit-icon">
            <h3>PROCESSUS DE RECRUTEMENT</h3>
            <div class="actions">
              <img
                v-if="isRecruiterProfilePage"
                class="edit-icon"
                src="@/assets/icons/edit-icon-new.svg"
                alt="Edit"
                @click="goToSection('my-profile-entreprise')"
              />
            </div>
          </div>
          <p
            v-if="
              user.processus_recrutement && user.processus_recrutement.length
            "
          >
            {{ user.processus_recrutement }}
          </p>
          <p v-else :class="{ 'owner-empty-item-message': owner }">
            {{
              owner
                ? 'Ajoutez une description sur en quoi consiste votre processus de recrutement !'
                : "L'entreprise n'a pas spécifié de description"
            }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import getImgPath from '@/utils/imgpath.js';
  import Chip from '@/components/chips/Chip.vue';

  export default {
    components: {
      Chip,
    },

    props: {
      owner: {
        type: Boolean,
        required: false,
      },
      user: {
        type: Object,
        required: true,
      },
    },

    computed: {
      isRecruiterProfilePage() {
        return this.$route.path === '/recruteur/profil';
      },
    },

    methods: {
      getImgPath,
      formatAdvantages(advantages) {
        if (!advantages) return [];
        return typeof advantages === 'string'
          ? advantages.split(',')
          : advantages;
      },

      goToSection(sectionId) {
        this.$router
          .push({
            path: '/profil/edition',
            query: { section: sectionId },
          })
          .then(() => {
            this.$nextTick(() => {
              this.$emit('toggleSection', sectionId); // Ouvre la bonne section

              setTimeout(() => {
                const element = document.getElementById(sectionId);
                if (element) {
                  const navbarHeight =
                    document.querySelector('.navbar')?.offsetHeight || 80;
                  const elementPosition =
                    element.getBoundingClientRect().top + window.scrollY;
                  window.scrollTo({
                    top: elementPosition - navbarHeight - 20,
                    behavior: 'smooth',
                  });
                }
              }, 300); // Attend que le menu s'affiche avant de scroller
            });
          });
      },
    },
  };
</script>

<style scoped>
  .card {
    width: 100%;
    margin: 0 auto;
    background-color: var(--white-200);
    border-radius: 10px;
    padding: 1rem 2rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  .card-header {
    width: 100%;
    padding: 2rem 0rem 2rem 0rem;
    display: flex;
    justify-content: flex-start;
    border-radius: 10px 10px 0 0;
  }

  .company-logo {
    object-fit: contain;
    width: 250px;
    flex-shrink: 0;
    border: #32394c solid 1px;
  }

  .card-title {
    font-size: 3rem;
    text-align: left;
    margin-left: 2rem;
  }
  .card-title h2 {
    font-size: 3rem;
  }
  .card-title p {
    font-size: 1rem;
  }

  .card-body {
    width: 100%;
  }

  .card-body p {
    margin: 0.5rem 0;
    font-size: 1rem;
    color: var(--text-secondary);
  }

  .company-details-item {
    margin: 2rem 0 0 0;
  }

  .title-edit-icon {
    display: flex;
  }
  .title-edit-icon h3 {
    font-size: 1.1em;
    margin: 0 0 0 0;
    color: #32394c;
  }
  .edit-icon {
    margin: 0 0 0 1rem;
    cursor: pointer;
    filter: none;
  }
  .edit-icon svg path {
    stroke: #32394c;
  }

  .owner-empty-item-message {
    /* color: red; */
    color: var(--text-1) !important;
    border: solid 1px red;
    padding: 5px;
    border-radius: 10px;
    font-size: 0.95em;
    background-color: rgba(248, 141, 141, 0.347);
    width: fit-content;
  }
</style>
