<template>
  <div class="job-offer">
    <!-- <div class="company-logo">
      <img
        :src="offer.logo_url ? getImgPath(offer.logo_url) : defaultLogo"
        alt="company logo"
        @error="handleImageError"
        class="logo"
      />
    </div> -->

    <div class="right-side">
      <div class="card-header-1">
        <h3>{{ offer.title }}</h3>
        <div class="job-details">
          <div class="detail-item">
            <span class="detail-value-candidate">
              <img :src="statusIcon" alt="Status Icon" class="icon" />
              <span>{{ statusLabel }}</span>
            </span>
          </div>
          <div class="detail-item">
            <span class="detail-value-candidate">
              <p class="circle-color">
                {{ offer.postulants ? offer.postulants.length : 0 }}
              </p>
              Candidatures
            </span>
          </div>
        </div>
      </div>
      <!-- Section body with job details -->
      <div class="section-body">
        <div class="tag">
          <img src="@/assets/icons/localisation-mini.svg" alt="Location Icon" />
          <p class="fs12">{{ offer.local }}</p>
        </div>
        <div class="tag">
          <img src="@/assets/icons/list-mini.svg" alt="Contract Icon" />
          <p class="fs12">{{ offer.contract }}</p>
        </div>
        <div class="tag">
          <img src="@/assets/icons/pc-mini.svg" alt="Remote Icon" />
          <p class="fs12">{{ remoteWorkLabel }}</p>
        </div>
        <div class="tag">
          <img src="@/assets/icons/calendly.svg" alt="Calendar Icon" />
          <p class="fs12">Crée le: {{ formatDate(offer.created_at) }}</p>
        </div>
      </div>
      <!-- Section footer with job description and actions -->
      <div class="section-footer">
        <div class="job-description">
          <div class="cut-description-2" v-html="formattedJobDescription"></div>
        </div>
        <div class="job-actions">
          <div class="group-btn">
            <div @click="editOffer">
              <img
                src="@/assets/icons/pen-icon.svg"
                alt="Edit Icon"
                class="btn"
              />
            </div>
            <div @click="viewOffer">
              <img
                src="@/assets/icons/eye-orange.svg"
                alt="View Icon"
                class="btn"
              />
            </div>
            <div @click="deleteOffer">
              <img
                src="@/assets/icons/trash-icon.svg"
                alt="Delete Icon"
                class="btn"
              />
            </div>
          </div>
          <div class="btn-wrapper">
            <!-- Conditional buttons based on offer status -->
            <PrimaryNormalButton
              v-if="offer.closed === false"
              textContent="Archiver"
              btnColor="secondary"
              @click="archiveOffer"
            />
            <PrimaryNormalButton
              v-if="offer.closed === true"
              textContent="Désarchiver"
              btnColor="secondary"
              @click="unarchiveOffer"
            />

            <PrimaryNormalButton
              :disabled="!offer.postulants || offer.postulants.length === 0"
              class="margin-left"
              :btnColor="offer.publie === false ? 'disabled' : 'primary'"
              textContent="Voir les candidatures"
              @click="handleOfferRedirection"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import getImgPath from '@/utils/imgpath.js';

  export default {
    name: 'OfferCardTemplate',
    props: {
      offer: {
        type: Object,
        required: true,
      },
      statusLabel: {
        type: String,
        required: true,
      },
      statusIcon: {
        type: String,
        required: true,
      },
    },
    components: {
      PrimaryNormalButton,
    },
    computed: {
      formattedJobDescription() {
        if (!this.offer || !this.offer.descriptif) return '';

        let formattedText = this.offer.descriptif;

        // 🔹 Convertir "#### Sous-Titre" en <h4>
        formattedText = formattedText.replace(/####\s(.*)/g, (match, p1) => {
          return `<h4 style="text-decoration: underline; margin: 20px 0 10px 0;">${p1}</h4>`;
        });

        // 🔹 Convertir "### Titre" en <h3>
        formattedText = formattedText.replace(/###\s(.*)/g, (match, p1) => {
          return `<h3 style="font-weight: bold;">${p1}</h3>`;
        });

        // 🔹 Convertir "**Texte en gras**" en <b>
        formattedText = formattedText.replace(/\*\*(.*?)\*\*/g, (match, p1) => {
          return `<b>${p1}</b>`;
        });

        // 🔹 Convertir les numéros "1." en pastilles alignées
        formattedText = formattedText.replace(
          /(\d+)\.\s(.+)/g,
          (match, p1, p2) => {
            return `<div style="display: flex; align-items: center; margin: 10px 0;">
            <span style=" display: flex; justify-content: center; align-items: center; font-weight: bold; font-size: 16px; color: #f7bc53; width: 25px; height: 25px; border: 2px solid #f7bc53; border-radius: 50%; flex-shrink: 0; text-align: center; line-height: 30px;">${p1}</span>
            <span style="margin-left: 10px;">${p2}</span>
          </div>`;
          }
        );

        // 🔹 Convertir les listes non numérotées "- Texte" en puces "• Texte"
        formattedText = formattedText.replace(/^\s*-\s(.+)/gm, (match, p1) => {
          return `<div style="display: flex; align-items: flex-start; margin-left: 20px; margin-top: 10px;">
            <span style="font-size: 18px; color: black; margin-right: 10px;">•</span>
            <span>${p1}</span>
          </div>`;
        });

        // 🔹 Convertir les liens email Markdown [texte](mailto:email) en liens HTML
        formattedText = formattedText.replace(
          /\[([^\]]+)\]\(mailto:([^)]+)\)/g,
          (match, p1, p2) => {
            return `<a href="mailto:${p2}" style="color: #007bff; text-decoration: underline;">${p1}</a>`;
          }
        );

        // 🔹 Supprimer les <br> entre les <div> (évite les sauts de ligne entre éléments de liste)
        formattedText = formattedText.replace(
          /<\/div>\s*<br>\s*<div/g,
          '</div><div>'
        );

        // 🔹 Supprimer les <br> qui suivent immédiatement un <div> (évite les espaces vides)
        formattedText = formattedText.replace(/<div[^>]*>\s*<br>/g, '<div>');

        // 🔹 Convertir les retours à la ligne (\n) en <br>, sauf après <h4> et entre les <div>
        formattedText = formattedText.replace(/\n(?!<\/h4>|<\/div>)/g, '<br>');

        // 🔹 Nettoyer les <br> après <h4> pour éviter un saut de ligne supplémentaire
        formattedText = formattedText.replace(/<\/h4><br>/g, '</h4>');

        return formattedText;
      },
      // Mapper la valeur numérique en texte
      remoteWorkLabel() {
        switch (this.offer.jours_teletravail) {
          case 5:
            return 'Télétravail complet';
          case 0:
            return 'En présentiel';
          case 2:
            return 'Hybride';
          case 3:
            return 'Hybride';
          case 1:
            return 'Télétravail partiel';
          case 4:
            return 'Télétravail partiel';
          default:
            return 'En présentiel'; // Au cas où la valeur serait invalide ou non définie
        }
      },
    },

    methods: {
      getImgPath,
      handleOfferRedirection() {
        this.$store.commit('setSelectedJobOffer', this.offer);
        this.$router.push(`/recruteur/offre/${this.offer.id}/candidatures`);
      },
      editOffer() {
        this.$emit('edit', this.offer.id); // Émet un événement pour éditer
      },
      viewOffer() {
        this.$emit('view', this.offer.id); // Émet un événement pour afficher les détails
      },
      deleteOffer() {
        this.$emit('delete', this.offer.id); // Émet un événement pour supprimer
      },
      archiveOffer() {
        // Logique d'archivage
        this.$emit('archive', this.offer.id);
      },
      unarchiveOffer() {
        // Logique de désarchivage
        this.$emit('unarchive', this.offer.id); // Émet un événement pour désarchiver
      },

      getTodayDate() {
        const today = new Date();
        return today.toLocaleDateString('fr-FR', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        });
      },

      formatDate(dateString) {
        if (!dateString) return this.getTodayDate();
        const date = new Date(dateString);
        return date.toLocaleDateString('fr-FR', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        });
      },
    },
  };
</script>

<style scoped>
  .margin-left {
    margin-left: 19px;
  }

  .v-btn {
    border-radius: 16px;
  }

  .group-btn {
    display: flex;
  }

  .group-btn img {
    margin: 7px;
    cursor: pointer;
    box-shadow: none;
    border-radius: 6px;
  }
  .group-btn img:hover {
    box-shadow: 0px 4px 8px 0px #00000040;
  }

  .tag {
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: center;
  }

  .tag p {
    padding-left: 9px;
  }

  .section-body {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    padding: 8px;
    display: flex;
    gap: 10px;
    width: 100%;
    justify-content: space-between;
    margin-top: 10px;
  }

  .job-offer {
    display: flex;
    background-color: var(--search-candidate-candidatecard-bg-color);
    border-radius: 20px;
    padding: 20px;
    width: 100%;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.15);
  }

  .company-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 279px;
    aspect-ratio: 1 / 1;
    overflow: hidden;
    margin-right: 25px;
  }
  .company-logo img {
    width: 100%;
    /* height: 100%; */
    object-fit: cover;
  }
  .logo {
    border: 1px solid rgba(223, 219, 214, 1);
    width: 92px;
    border-radius: 8px;
    object-fit: contain;
  }

  .right-side {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
  }

  .circle-color {
    padding: 17px;
    background: #fae2b7;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 7px;
  }

  .card-header-1 {
    display: flex;
    gap: 10px;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: solid 1px var(--surface-bg);
  }

  .job-header {
    border-bottom: 1px solid #ddd;
    padding-bottom: 8px;
    margin-bottom: 16px;
  }

  .job-header p {
    display: flex;
    align-items: center;
  }

  .icon {
    width: 39px;
    height: 35px;
    margin-right: 7px;
  }

  .job-details {
    padding-left: 58px;
    display: flex;
  }

  .detail-value-candidate {
    padding-left: 42px;
    font-size: 12px;
  }

  span {
    display: flex;
    align-items: center;
  }

  .detail-item {
    margin-bottom: 8px;
  }

  .detail-label {
    font-weight: bold;
  }

  .detail-value {
    margin-left: 8px;
  }

  .job-description {
    padding: 9px;
  }
  .cut-description-2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    line-clamp: 2;
    -webkit-line-clamp: 2; /* edit this line to change the number of lines displayed */
    line-height: 1.2;
  }

  .job-actions {
    display: flex;
    gap: 10px;
    justify-content: space-between;
    align-items: center;
  }

  @media (max-width: 768px) {
    .card-header-1 {
      flex-direction: column;
    }
    .section-body {
      flex-wrap: wrap;
      display: flex;
      justify-content: center;
    }
    .job-details[data-v-93a8bbcc] {
      padding-left: 0px;
      margin-top: 10px;
      align-items: center;
    }
    .detail-value-candidate {
      padding-left: 12px;
    }
    .job-actions {
      flex-direction: column;
    }
    .job-offer {
      flex-direction: column;
      align-items: center;
    }
    .company-logo {
      width: 70%;
      margin-right: 0px;
    }
    .cut-description-2 {
      text-align: center;
    }
  }
</style>
