<template>
  <div
    :class="[
      'subscription-card-container border-radius-15',
      { active: subscription.id === activeIndex },
    ]"
    @click="setActive(subscription.id)"
  >
    <!-- subscription title  -->
    <div class="subscription-card-header">
      <h5>{{ subscription.nom }}</h5>
    </div>
    <!-- 3 first items of description  -->
    <div class="subscription-card-description">
      <div
        v-for="item in subscription.description"
        :key="subscription.id"
        class="subscription-card-description-row"
      >
        <img class="validate-button" src="@/assets/icons/validate-round.svg" />
        <p>{{ item.description }}</p>
      </div>
    </div>
    <!-- button -->
    <!-- <div class="info-button">
      <PrimaryRoundedButton textContent="En savoir plus" :btnColor="subscription.id === activeIndex ? 'primary' : 'secondary'"
        @click="$router.push(`/buy/${abonnements.id}`)" go />
    </div> -->
  </div>
</template>

<script>
  import PrimaryRoundedButton from '../../buttons/PrimaryRoundedButton.vue';
  export default {
    name: 'SubscriptionCard',
    components: {
      PrimaryRoundedButton,
    },
    props: {
      subscription: {
        type: Object,
        default: () => {},
      },
      activeIndex: {
        type: Number,
        default: 0,
      },
    },
    methods: {
      setActive(id) {
        this.$emit('update-active', id);
      },
    },
  };
</script>

<style scoped>
  /* container */
  .subscription-card-container {
    width: 100%;
    height: 300px;
    padding: 8px;
    margin-bottom: 16px;
    display: flex;
    flex-direction: column;
    background-color: var(--Colors-primary-1b-2, #f6b33733);
  }

  .subscription-card-container.active {
    background-color: var(--black-200);
    color: var(--white-200);
  }

  /* header */
  .subscription-card-header {
    height: 15%;
    width: 100%;
    margin-top: 8px;
    text-align: center;
    text-transform: capitalize;
  }

  .subscription-card-description {
    display: flex;
    flex-direction: column;
    gap: 16px;

    width: 100%;
    margin-right: 16px;
    margin-bottom: 8px;
    padding: 8px;
    background-color: #fffdfc80;
  }

  .subscription-card-container.active .subscription-card-description {
    background-color: var(--surface-bg-3);
  }

  /* description */
  .subscription-card-description-row {
    height: inherit;
    gap: 10px;
    display: flex;
    align-items: center;
  }

  .subscription-card-description-row p {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    /* edit this line to change the number of lines displayed */
    line-height: 1.2;
  }

  .validate-button {
    background-color: var(--yellow-100);
    padding: 2px;
    border-radius: 15%;
  }

  .info-button {
    align-self: center;
    margin: 8px;
  }

  @media screen and (min-width: 992px) {
    .subscription-card-container {
      width: 100%;
      height: fit-content;
      padding: 12px;
      margin-bottom: 24px;
      display: flex;
      flex-direction: row;
      background-color: var(--Colors-primary-1b-2, #f6b33733);
    }

    /* header */
    .subscription-card-header {
      height: 15%;
      width: 15%;
      margin-top: 20px;
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .subscription-card-description {
      display: flex;
      flex-direction: column;
      height: 100%;
      width: 75%;
      margin-right: 16px;
      padding: 12px;
    }

    .validate-button {
      background-color: var(--yellow-100);
      padding: 2px;
      border-radius: 15%;
    }

    .info-button {
      align-self: flex-end;
    }
  }
</style>
