<template>
  <article class="alert-card">
    <div class="card-header">
      <h4 v-if="alert.nom">{{ alert.nom }}</h4>
      <img
        v-if="alert.active"
        src="@/assets/icons/alert-bell.svg"
        alt="cloche alerte"
        class="icon"
        @click="toggleAlertActivation"
      />
      <img
        v-else
        src="@/assets/icons/alert-bell-empty.svg"
        alt="cloche alerte"
        class="icon"
        @click="toggleAlertActivation"
      />
    </div>

    <div class="card-body">
      <img
        src="@/assets/icons/alert-mini-calendar.svg"
        alt="calendrier de sortie"
      />
      <p v-if="alert.created_at && alert.created_at.length > 10">
        Créer le : {{ alert.created_at.slice(0, 10) }}
      </p>
    </div>

    <div class="card-footer" @click="toggleAlertePanel(alert.id, alertIndex)">
      <PrimaryRoundedButton textContent="Voir l'alerte" />
    </div>
  </article>
</template>

<script>
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import { toggleJobAlertActivation } from '@/services/alert.service.js';

  export default {
    name: 'AlertCard',

    components: {
      PrimaryRoundedButton,
    },

    props: {
      //  alert object
      alert: {
        type: Object,
        default: null,
      },

      //  alert index in the list
      alertIndex: {
        type: Number,
      },
    },

    data() {
      return {
        active: false, //  if alert is activated
      };
    },

    methods: {
      //  toggle alert panel with alert id
      toggleAlertePanel(alertId, alertIndex) {
        this.$emit('toggle-alert-panel', alertId, alertIndex);
      },

      //  toggle alert on/off
      toggleAlertActivation() {
        toggleJobAlertActivation(this.alert.id);
        this.alert.active = !this.alert.active;
      },
    },
  };
</script>

<style scoped>
  /* tags */
  p {
    font-size: 12px;
  }

  /* layout */
  .alert-card {
    border-radius: 15px;
    background-color: var(--surface-bg-2);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 20px;
    padding: 15px;
    width: 258px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Ombre portée (gris pâle) */
    transition: box-shadow 0.3s ease; /* Animation douce pour l'ombre */
  }
  .alert-card:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4); /* Ombre portée plus intense */
  }

  .card-header {
    display: flex;
    justify-content: space-between;
  }

  .card-body {
    display: flex;
    gap: 10px;
  }

  .card-footer {
    display: flex;
    justify-content: center;
    width: 100%;
  }

  /* buttons */
  .icon {
    cursor: pointer;
  }
</style>
