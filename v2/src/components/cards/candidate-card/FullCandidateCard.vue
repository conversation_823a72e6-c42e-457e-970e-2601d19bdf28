<template>
  <div class="candidate-card-container">
    <div class="candidate-card-infos">
      <div class="infos-col1">
        <!-- add to friends button -->
        <div class="add-button">
          <!-- Affichage du tooltip et de l'image selon l'état -->
          <v-tooltip location="bottom" activator="parent">
            <span>{{ buttonTooltip }}</span>
          </v-tooltip>
          <img
            :src="buttonIcon"
            alt="icon"
            class="plus-icon"
            :class="{ disabled: invitationPending }"
            @click="handleButtonClick"
          />
        </div>

        <!-- candidate photo -->
        <img
          :src="candidatePhotoUrl"
          alt="logo"
          class="candidate-logo"
          @error="handleImageError"
        />

        <!-- favorite candidate icon -->
        <div class="fav-wrapper" @click="handleFavoriteProfilClick()">
          <v-tooltip
            v-if="candidateIsLiked"
            activator="parent"
            location="bottom"
            >Retirer des favoris</v-tooltip
          >
          <v-tooltip v-else activator="parent" location="bottom"
            >Ajouter aux favoris</v-tooltip
          >

          <img
            v-if="candidateIsLiked"
            src="@/assets/search/search-page-card-like-icon-filled.svg"
            alt="heart like filled"
            class="favorite-icon"
          />

          <img
            v-else
            src="@/assets/search/search-page-card-like-icon.svg"
            alt="heart like empty"
            class="favorite-icon"
          />
        </div>
      </div>

      <div class="infos-col2">
        <!-- candidate name -->
        <h3 class="name item">
          {{ candidate.user_first_name }} {{ candidate.user_last_name }}
        </h3>

        <!-- candidate metier -->
        <h5 class="metier item">{{ candidate.user_metier }}</h5>

        <div class="infos-col2-botom">
          <!-- candidate ville -->
          <div class="ville item tag">
            <img src="@/assets/icons/localisation-mini.svg" alt="" />
            <p>{{ candidate.user_ville }}</p>
          </div>

          <!-- candidate expérience -->
          <div class="experience item tag">
            <img src="@/assets/icons/suitcase-mini.svg" />
            <p>{{ candidate.user_expience }}</p>
          </div>

          <!-- candidate télétravail -->
          <div class="teletravail item tag">
            <img src="@/assets/icons/pc-mini.svg" />
            <p>{{ candidate.teletravail }}</p>
          </div>
        </div>
      </div>

      <div class="infos-col3">
        <span class="candidature-status">
          <img
            :src="getIconForCandidate(candidate)"
            alt="Status Icon"
            class="icon"
          />
          <span>{{ getTextForCandidate(candidate) }}</span>
        </span>
      </div>
    </div>

    <!-- candidate message -->
    <div class="candidate-card-message item">
      <h6>Pourquoi moi ?</h6>
      <p class="textPourquoiMoi">{{ candidate.postulation.lettre }}</p>
    </div>

    <div class="candidate-card-buttons">
      <div class="buttons-col1">
        <!-- CV par défaut dans le profil du candidat -->
        <div class="cv">
          <PrimaryRoundedButton
            v-if="candidate.cv.file"
            class="cv"
            look
            textContent="Voir le CV"
            btnColor="secondary"
            :href="candidate.cv.file"
            target="_blank"
          />
          <PrimaryRoundedButton
            v-else
            class="cv"
            textContent="CV non disponible"
            btnColor="light"
          />
        </div>

        <!-- candidate lettre de motivation -->
        <div class="letter">
          <PrimaryRoundedButton
            v-if="candidate.postulation.lettre_motivation"
            class="letter"
            look
            textContent="Voir la lettre"
            btnColor="secondary"
            :href="buildUrl(candidate.postulation.lettre_motivation)"
            target="_blank"
          />
          <PrimaryRoundedButton
            v-else
            class="letter"
            textContent="Lettre non disponible"
            btnColor="light"
          />
        </div>

        <!-- candidate email -->
        <div class="email">
          <PrimaryRoundedButton
            email
            textContent="Envoyer un message"
            btnColor="secondary"
            :href="`mailto:${candidate.email}`"
          />
        </div>
      </div>

      <div class="buttons-col2">
        <PrimaryRoundedButton
          class="btn-show"
          look
          textContent="Voir la candidature"
          @click="handleCandidatureRedirection"
        />

        <!-- Si déjà accepté : afficher message -->
        <template v-if="candidate.user_status === 'Accepté'">
          <PrimaryRoundedButton
            class="btn-show"
            textContent="Entretien proposé"
            disabled
          />
        </template>

        <!-- Sinon, bouton actif -->
        <template v-else>
          <PrimaryRoundedButton
            class="btn-show"
            textContent="Proposer un entretien"
            @click="$emit('open-entretien-modal', candidate)"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<script>
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import { baseUrl } from '@/services/axios';
  import {
    addFavoriteProfil,
    removeFavoriteProfil,
  } from '@/services/favoriteProfil.service';
  import {
    friendsInvitations,
    friendsList,
    removeFriend,
    sendFriendInvitation,
  } from '@/services/friend.service';
  import { toaster } from '@/utils/toast/toast';
  import gotoPage from '@/utils/router';

  export default {
    name: 'FullCandidateCard',

    components: {
      PrimaryRoundedButton,
    },

    props: {
      recruteur: {
        type: Object,
        required: true,
      },
      candidate: {
        type: Object,
        required: true,
      },
      jobOffer: {
        type: Object,
        required: true,
        default: () => ({}),
      },
    },

    data() {
      return {
        invitationSent: false,
        isModalOpen: false,
        candidateIsLiked: this.candidate.candidateIsLiked,
        isFriend: false,
        invitationPending: false,
      };
    },

    computed: {
      // Détermine l'icône du bouton
      buttonIcon() {
        if (this.isFriend) {
          return require('@/assets/icons/friend-remove-black.svg');
        }
        if (this.invitationPending) {
          return require('@/assets/icons/friend-pending-black.svg');
        }
        return require('@/assets/icons/friend-add-black.svg');
      },

      // Détermine le texte du tooltip
      buttonTooltip() {
        if (this.isFriend) {
          return 'Retirer des amis';
        }
        if (this.invitationPending) {
          return "Invitation d'amitié en attente";
        }
        return 'Ajouter aux amis';
      },

      candidatePhotoUrl() {
        return this.candidate.user_photo
          ? `${this.candidate.user_photo}`
          : require('@/assets/icons/avatar.png');
      },
    },

    async mounted() {
      await this.checkIfCandidateIsFriend();
    },

    methods: {
      gotoPage,

      handleCandidatureRedirection() {
        this.$router.push(
          `/recruteur/offre/${this.jobOffer.id}/candidature/${this.candidate.user_id}`
        );
      },
      handleImageError(event) {
        event.target.src = require('@/assets/icons/avatar.png');
      },
      buildUrl(path) {
        // On doit vérifier si le chemin est une URL absolue
        if (path.startsWith('http://') || path.startsWith('https://')) {
          return path;
        }
        // Supprime le slash de fin de baseUrl, si présent, et le slash de début de path
        return `${baseUrl.replace(/\/+$/, '')}/${path.replace(/^\/+/, '')}`;
      },

      getTextForCandidate(candidate) {
        if (candidate.user_status === 'Accepté') {
          return 'Acceptée';
        } else if (candidate.user_status === 'En Cours') {
          return 'Non traitée';
        } else if (candidate.user_status === "A l'étude") {
          return "En attente d'un retour";
        } else if (candidate.user_status === 'Refuser') return 'Refusée';
      },

      getIconForCandidate(candidate) {
        if (candidate.user_status === 'Accepté') {
          return require('@/assets/icons/valid-icon.svg');
        } else if (candidate.user_status === 'En Cours') {
          return require('@/assets/icons/unpublished-icon.svg');
        } else if (candidate.user_status === "A l'étude") {
          return require('@/assets/icons/unpublished-icon.svg');
        } else if (candidate.user_status === 'Refuser')
          return require('@/assets/icons/deleted-icon.svg');
      },

      async checkIfCandidateIsFriend() {
        try {
          const [friends, invitations] = await Promise.all([
            friendsList(),
            friendsInvitations(),
          ]);

          const userId = this.candidate.user_id;

          // Vérifier si l'utilisateur est déjà ami
          this.isFriend = friends.some((friend) => friend.id === userId);

          // Vérifier si une invitation est en attente
          const sentPending = invitations.sent_invitations.some(
            (inv) => inv.recipient === userId && inv.state === 'pending'
          );
          const receivedPending = invitations.received_invitations.some(
            (inv) => inv.sender === userId && inv.state === 'pending'
          );

          // Si une invitation est en attente, mettre l'état de l'invitation à true
          this.invitationPending = sentPending || receivedPending;

          // Si l'invitation est en attente ou l'utilisateur est ami, le bouton doit être désactivé
          if (this.isFriend || this.invitationPending) {
            this.invitationPending = true;
          }
        } catch (error) {
          //console.error('Erreur lors de la vérification des relations:', error);
        }
      },

      handleButtonClick() {
        if (this.isFriend) {
          this.handleRemoveFriendClick();
        } else if (!this.invitationPending) {
          this.handleAddFriendClick();
        }
        // Si "invitationPending", aucun clic n'est autorisé.
      },

      async handleAddFriendClick() {
        //console.log('handleAddFriendClick called for:', this.candidate.user_id);
        try {
          await sendFriendInvitation(this.candidate.user_id);
          toaster.showSuccessPopup('Invitation envoyée avec succès.');
          this.invitationPending = true;
        } catch (error) {
          toaster.showErrorPopup("Échec de l'envoi de l'invitation.");
        }
      },

      async handleRemoveFriendClick() {
        //console.log(
        //  'handleRemoveFriendClick called for:',
        //  this.candidate.user_id
        //);
        try {
          await removeFriend(this.candidate.user_id);
          toaster.showSuccessPopup('Ami supprimé avec succès.');
          this.invitationPending = false;
          this.isFriend = false;
        } catch (error) {
          toaster.showErrorPopup("Échec de la suppression de l'ami.");
        }
      },

      async handleFavoriteProfilClick() {
        try {
          if (this.candidateIsLiked === true) {
            // Récupérer l'id du favorite_apply a partir de l'id du candidat (le bon id c'est celui de la liste des recruteurFavorites qui est le resultat de la requête api/best_profils/list_user_favorites)
            const favoriteApply = this.recruteur.recruteurFavorites.find(
              (favorite) => favorite.apply_user[0].id === this.candidate.user_id
            );
            const response = await removeFavoriteProfil(favoriteApply.id);
            if (response) {
              toaster.showSuccessPopup('Profil retiré des favoris.');
              const updatedFavoriteList =
                this.recruteur.recruteurFavorites.filter(
                  (favorite) =>
                    favorite.apply_user[0].id !== this.candidate.user_id
                );
              this.$store.commit('updateUser', {
                type: null,
                payload: {
                  ...this.recruteur,
                  recruteurFavorites: updatedFavoriteList,
                },
              });
              this.candidateIsLiked = false;
            }
          } else if (this.candidateIsLiked === false) {
            const response = await addFavoriteProfil(this.candidate.user_id);
            if (response) {
              toaster.showSuccessPopup('Profil ajouté aux favoris.');
              const updatedFavoriteList = [
                ...this.recruteur.recruteurFavorites,
                response.data,
              ];
              this.$store.commit('updateUser', {
                type: null,
                payload: {
                  ...this.recruteur,
                  recruteurFavorites: updatedFavoriteList,
                },
              });
              this.candidateIsLiked = true;
            }
          }
        } catch (error) {
          //console.error('Erreur lors de la mise à jour du favori :', error);
          toaster.showErrorPopup('Une erreur est survenue.');
        }
      },
    },
  };
</script>

<style scoped>
  /* backdrop pour le modal de la creation de l'entretien */
  .backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  /* container */
  .candidate-card-container {
    background-color: var(--search-candidate-candidatecard-bg-color);
    border-radius: 5px;
    padding: 12px;
    width: 100%;
    height: 100%;
    margin-block: 30px;
  }

  .candidate-card-infos {
    display: flex;
  }
  .candidate-card-infos .item {
    margin-left: 20px;
  }
  .candidate-card-infos .tag {
    display: flex;
    justify-content: start;
    align-items: center;
  }
  .candidate-card-infos .teletravail p,
  .candidate-card-infos .ville p,
  .candidate-card-infos .experience p {
    padding-left: 9px;
  }

  .infos-col1 {
    width: 20%;
    position: relative;
  }
  .infos-col1 .candidate-logo {
    height: 200px;
    width: 100%;
    object-fit: cover;
    border-radius: 15px;
  }
  .add-button {
    cursor: pointer;
    position: absolute; /* Place le cœur en haut à droite */
    top: 8px;
    left: 8px;
    z-index: 10;
  }
  .plus-icon {
    width: 32px;
    height: 32px;
    cursor: pointer;
  }
  .fav-wrapper {
    cursor: pointer;
    position: absolute; /* Place le cœur en haut à droite */
    top: 8px;
    right: 8px;
    z-index: 10;
  }
  .favorite-icon {
    width: 32px;
    height: 32px;
  }

  .infos-col2 {
    width: 50%;
  }
  .infos-col2-botom {
    display: flex;
    margin-top: 9px;
  }

  .infos-col3 {
    width: 30%;
    justify-items: flex-end;
  }
  .candidature-status {
    font-size: 12px;
  }
  span {
    display: flex;
    align-items: center;
  }
  .candidature-status span {
    padding: 0 12px;
  }

  .candidate-card-message {
    margin-left: calc(20% + 20px);
    position: relative;
    top: -100px;
    right: 0px;
    height: 0px;
  }

  .candidate-card-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 40px;
    flex-wrap: wrap;
    gap: 10px;
  }

  .cv .lettre {
    border: 1px solid var(--primary-rounded-btn-bg-color);
    border-radius: 10px;
    width: fit-content;
  }

  .buttons-col1,
  .buttons-col2 {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }
  .buttons-col2 button {
    width: fit-content;
  }

  @media (max-width: 764px) {
    .textPourquoiMoi {
      padding: 10px;
    }
    .candidate-card-infos {
      display: flex;
      flex-direction: column;
    }
    .infos-col1 {
      width: auto;
    }
    .infos-col2 {
      display: flex;
      width: 100%;
      align-items: center;
      flex-direction: column;
    }
    .infos-col2-botom {
      flex-direction: row;
      align-items: left;
    }

    .infos-col2-botom .tag {
      margin-bottom: 10px;
    }

    .infos-col3 {
      display: flex;
      justify-content: center;
      width: 100%;
    }

    .candidate-card-message {
      margin-left: 0;
      top: 0;
      height: auto;
      text-align: center;
    }

    .candidate-card-buttons {
      flex-direction: column;
      align-items: center;
      gap: 20px;
    }

    .buttons-col1,
    .buttons-col2 {
      width: 100%;
      text-align: center;
      display: flex;
      justify-content: center;
    }

    .buttons-col1 {
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
      gap: 10px;
      display: flex;
      flex-direction: column;
    }

    .favorite-icon {
      width: 28px;
      height: 28px;
    }
  }
</style>
