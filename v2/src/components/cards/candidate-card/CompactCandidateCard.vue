<template>
  <LazyLoader @visible="loadCardData">
    <div
      class="candidate-card-container"
      :class="{ 'highlight-card': candidateTypeUser === 'recruiter' }"
    >
      <!-- header -->
      <div class="candidate-card-header">
        <!-- add to friends button -->
        <div class="add-button">
          <!-- Affichage du tooltip et de l'image selon l'état -->
          <v-tooltip location="bottom" activator="parent">
            <span>{{ buttonTooltip }}</span>
          </v-tooltip>
          <img
            :src="buttonIcon"
            alt="icon"
            class="plus-icon"
            :class="{ disabled: invitationPending }"
            @click="handleButtonClick"
          />
        </div>

        <div class="card-header-1">
          <!-- candidate profession  -->
          <div class="logo-wrapper">
            <img
              v-if="candidate.photo"
              :src="getImgPath(candidate.photo)"
              alt="logo"
              class="logo"
            />
            <img
              v-else
              src="@/assets/search/search-page-jobcard-emptylogo-icon.jpg"
              alt="logo"
              class="logo"
            />
            <!-- Cercle ajouté -->
            <div
              :class="[
                'status-circle',
                {
                  connected: candidate.connected,
                  disconnected: !candidate.connected,
                },
              ]"
            ></div>
          </div>
        </div>

        <!-- heart ico -->
        <div
          class="fav-wrapper"
          @click="handleFavoriteProfilClick(candidate.id)"
        >
          <v-tooltip
            v-if="candidateIsLiked"
            activator="parent"
            location="bottom"
            >Retirer des favoris</v-tooltip
          >
          <v-tooltip v-else activator="parent" location="bottom"
            >Ajouter aux favoris</v-tooltip
          >

          <img
            v-if="candidateIsLiked"
            src="@/assets/search/search-page-card-like-icon-filled.svg"
            alt="heart like filled"
            class="favorite-icon candidate-card-header-heart cursor-pointer"
          />

          <img
            v-else
            src="@/assets/search/search-page-card-like-icon.svg"
            alt="heart like empty"
            class="favorite-icon candidate-card-header-heart cursor-pointer"
          />
        </div>
      </div>

      <div class="candidate-card-body">
        <!-- candidate metier -->
        <div class="title-wrapper">
          <h4>{{ formattedMetier }}</h4>
        </div>
        <!-- candidate name -->
        <div class="candidate-card-name d-flex a-center">
          <p>{{ formattedName }}</p>
        </div>
        <!-- offer title -->
        <div class="offer-card-title d-flex a-center">
          <p>Pour le poste : {{ jobOfferTitle }}</p>
        </div>
      </div>

      <!-- button -->
      <div class="candidate-card-button">
        <PrimaryRoundedButton
          textContent="Voir la candidature"
          @click="
            $router.push(
              `/recruteur/offre/${jobOfferId}/candidature/${candidate.id}/`
            )
          "
        />
      </div>
    </div>
  </LazyLoader>
</template>

<script>
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import LazyLoader from '@/components/utils/LazyLoader.vue';
  import { getUserById } from '@/services/account.service';
  import {
    addFavoriteProfil,
    isCandidateFavoriteById,
    removeFavoriteProfil,
  } from '@/services/favoriteProfil.service.js';
  import {
    friendsList,
    friendsInvitations,
    removeFriend,
    sendFriendInvitation,
  } from '@/services/friend.service';
  import getImgPath from '@/utils/imgpath.js';
  import { toaster } from '@/utils/toast/toast';
  import store from '../../../store';

  export default {
    name: 'CompactCandidateCard',

    components: {
      PrimaryRoundedButton,
      LazyLoader,
    },

    props: {
      candidate: {
        type: Object,
        default: () => ({}),
      },
      jobOfferId: {
        type: String,
        required: true,
      },
      jobOfferTitle: {
        type: String,
        required: true,
      },
    },

    data() {
      return {
        candidateIsLiked: false, // Pour déterminer si le profil est favori
        candidateConnected: false,
        candidateTypeUser: '',
        isFriend: false,
        invitationPending: false,
      };
    },

    computed: {
      // Détermine l'icône du bouton
      buttonIcon() {
        if (this.isFriend) {
          return require('@/assets/icons/friend-remove-black.svg');
        }
        if (this.invitationPending) {
          return require('@/assets/icons/friend-pending-black.svg');
        }
        return require('@/assets/icons/friend-add-black.svg');
      },

      // Détermine le texte du tooltip
      buttonTooltip() {
        if (this.isFriend) {
          return 'Retirer des amis';
        }
        if (this.invitationPending) {
          return 'Invitation en attente';
        }
        return 'Ajouter aux amis';
      },

      // Formater le nom complet du candidat
      formattedName() {
        const firstName = this.candidate.first_name
          ? this.capitalizeFirstLetter(this.candidate.first_name)
          : '';
        const lastName = this.candidate.last_name
          ? this.capitalizeFirstLetter(this.candidate.last_name)
          : '';
        return `${firstName} ${lastName}`.trim();
      },
      // Formater la profession du candidat
      formattedMetier() {
        return this.candidate.metier
          ? this.capitalizeFirstLetter(this.candidate.metier)
          : '';
      },
    },

    mounted() {
      // Les appels API seront déclenchés uniquement lorsque la carte devient visible
    },

    methods: {
      getImgPath,

      // Méthode appelée lorsque la carte devient visible
      loadCardData() {
        this.checkCandidateConnection(this.candidate.id);
        this.checkIfFavorite();
        this.checkIfCandidateIsFriend();
      },

      async checkIfFavorite() {
        try {
          const favoritesList = await isCandidateFavoriteById();
          // Vérifie si le candidat actuel est dans la liste des favoris
          this.candidateIsLiked = favoritesList.some(
            (fav) =>
              fav.apply_user &&
              fav.apply_user.length > 0 &&
              fav.apply_user[0].id === this.candidate.id
          );
        } catch (error) {
          //console.error('Erreur lors de la vérification du favori :', error);
        }
      },

      async checkCandidateConnection(candidateId) {
        try {
          const userData = await getUserById(candidateId);
          this.candidateConnected = userData.connected;
          this.candidateTypeUser = userData.type_user;
          //console.log('userData', userData);
        } catch (error) {
          //console.error(
          //  "Erreur lors de la récupération de l'état de connexion de l'utilisateur:",
          //  error
          //);
          this.candidateConnected = false; // Définir à false en cas d'erreur
        }
      },

      // Gérer la suppression du profil dans les favoris
      async handleFavoriteProfilClick(candidateId) {
        const isLoggedIn = store.getters.isLoggedIn;
        if (!isLoggedIn) {
          toaster.showErrorPopup(
            'Veuillez vous connecter pour ajouter des profils aux favoris.'
          );
          return;
        }

        try {
          if (this.candidateIsLiked) {
            const favoritesList = await isCandidateFavoriteById();
            const favoriteItem = favoritesList.find(
              (fav) =>
                fav.apply_user &&
                fav.apply_user.length > 0 &&
                fav.apply_user[0].id === candidateId
            );

            if (favoriteItem) {
              const favoriteId = favoriteItem.id;
              const response = await removeFavoriteProfil(favoriteId);
              if (response) {
                toaster.showSuccessPopup('Profil retiré des favoris.');
                this.candidateIsLiked = false;
                this.$emit('remove-favorite', candidateId);
              }
            } else {
              //console.error('Aucun favori correspondant trouvé pour cet ID.');
            }
          } else {
            const response = await addFavoriteProfil(candidateId);
            if (response) {
              toaster.showSuccessPopup('Profil ajouté aux favoris.');
              this.candidateIsLiked = true;
            }
          }
        } catch (error) {
          //console.error('Erreur lors de la mise à jour du favori :', error);
          toaster.showErrorPopup('Une erreur est survenue.');
        }
      },

      async checkIfCandidateIsFriend() {
        try {
          const [friends, invitations] = await Promise.all([
            friendsList(),
            friendsInvitations(),
          ]);

          const userId = this.candidate.id;

          // Vérifier si l'utilisateur est déjà ami
          this.isFriend = friends.some((friend) => friend.id === userId);

          // Vérifier si une invitation est en attente
          const sentPending = invitations.sent_invitations.some(
            (inv) => inv.recipient === userId && inv.state === 'pending'
          );
          const receivedPending = invitations.received_invitations.some(
            (inv) => inv.sender === userId && inv.state === 'pending'
          );

          // Si une invitation est en attente, mettre l'état de l'invitation à true
          this.invitationPending = sentPending || receivedPending;

          // Si l'invitation est en attente ou l'utilisateur est ami, le bouton doit être désactivé
          if (this.isFriend || this.invitationPending) {
            this.invitationPending = true;
          }
        } catch (error) {
          //console.error('Erreur lors de la vérification des relations:', error);
        }
      },

      handleButtonClick() {
        if (this.isFriend) {
          this.handleRemoveFriendClick();
        } else if (!this.invitationPending) {
          this.handleAddFriendClick();
        }
        // Si "invitationPending", aucun clic n'est autorisé.
      },

      async handleAddFriendClick() {
        //console.log('handleAddFriendClick called for:', this.candidate.id);
        try {
          await sendFriendInvitation(this.candidate.id);
          toaster.showSuccessPopup('Invitation envoyée avec succès.');
          this.invitationPending = true;
        } catch (error) {
          toaster.showErrorPopup("Échec de l'envoi de l'invitation.");
        }
      },

      async handleRemoveFriendClick() {
        //console.log('handleRemoveFriendClick called for:', this.candidate.id);
        try {
          await removeFriend(this.candidate.id);
          toaster.showSuccessPopup('Ami supprimé avec succès.');
          this.invitationPending = false;
          this.isFriend = false;
        } catch (error) {
          toaster.showErrorPopup("Échec de la suppression de l'ami.");
        }
      },

      // Méthode pour capitaliser la première lettre d'un mot
      capitalizeFirstLetter(str) {
        if (!str) return str;
        return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
      },
    },
  };
</script>

<style scoped>
  h4 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    line-clamp: 2;
    -webkit-line-clamp: 2; /* edit this line to change the number of lines displayed */
    line-height: 1.2;
    font-size: 16px;
  }

  p {
    font-size: 12px;
  }

  /* container */
  .candidate-card-container {
    width: 258px;
    height: fit-content;
    display: flex;
    flex-direction: column;
    background-color: var(--surface-bg-2);
    padding: 15px;
    border-radius: 15px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Ombre portée (gris pâle) */
    transition: box-shadow 0.3s ease; /* Animation douce pour l'ombre */
  }
  .candidate-card-container:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4); /* Ombre portée plus intense */
  }
  .highlight-card {
    box-shadow:
      inset 0 0 0 5px var(--primary-1),
      /* Ombre interne (bordure simulée) */ 0 4px 8px rgba(0, 0, 0, 0.2); /* Ombre portée (gris pâle) */
    transition: box-shadow 0.3s ease; /* Animation douce pour l'ombre */
  }
  .highlight-card:hover {
    box-shadow:
      inset 0 0 0 5px var(--primary-1),
      /* Ombre interne reste la même */ 0 6px 12px rgba(0, 0, 0, 0.4); /* Ombre portée plus intense */
  }

  /* header */
  .candidate-card-header {
    width: 100%;
    display: flex;
    justify-content: center;
    position: relative;
  }

  .card-header-1 {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .add-button {
    position: absolute;
    top: 0;
    left: 0;
    cursor: pointer;
    z-index: 10;
  }

  .plus-icon {
    width: 32px;
    height: 32px;
    cursor: pointer;
  }

  .logo-wrapper {
    width: 125px;
    height: 125px;
    border-radius: 5px;
    position: relative; /* Nécessaire pour positionner le cercle à l'intérieur */
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .logo {
    width: 100%;
    height: 100%;
    border-radius: 10px;
    object-fit: cover;
  }

  .fav-wrapper {
    position: absolute;
    top: 0;
    right: 0;
  }

  .status-circle {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    position: absolute;
    bottom: 0;
    right: 0;
    transform: translate(15%, 15%);
    border: 2px solid white;
  }
  .status-circle.connected {
    background-color: green; /* Vert pour connecté */
  }

  .status-circle.disconnected {
    background-color: gray; /* Gris pour hors connexion */
  }

  .title-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    margin-top: 10px;
  }

  .candidate-card-header-heart {
    height: 32px;
    width: 32px;
  }

  .candidate-card-body {
    margin-top: 10px;
    margin-bottom: 20px;
  }

  /* name */
  .candidate-card-name {
    display: flex;
    justify-content: center; /* Centre le nom */
    align-items: center;
    width: 100%;
    text-align: center;
    margin-top: 5px;
  }

  .offer-card-title {
    padding: 3px;
    border-radius: 10px;
    font-size: 1rem;
    background-color: rgba(246, 179, 55, 0.2);
    justify-content: center;
    align-items: center;
    width: 100%;
    text-align: center;
  }

  /* description */
  .candidate-card-description {
    padding-top: 25px;
  }

  .candidate-card-description p {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    line-clamp: 3;
    -webkit-line-clamp: 3; /* edit this line to change the number of lines displayed */
    line-height: 1.2;
  }

  .candidate-card-button {
    padding: 0 20px;
    text-align: center;
  }
</style>
