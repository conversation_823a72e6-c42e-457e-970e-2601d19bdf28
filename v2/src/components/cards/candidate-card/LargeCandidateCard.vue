<template>
  <article class="candidate-card">
    <div
      class="card-header"
      :class="{ 'highlight-card': candidateTypeUser === 'recruiter' }"
    >
      <!-- add to friends button -->
      <div class="add-button" v-if="!isCurrentUser">
        <v-tooltip location="bottom" activator="parent">
          <span v-if="localFriendStatus === 'ami'">Retirer des amis</span>
          <span v-else-if="localFriendStatus === 'pending'">
            Invitation d'amitié en attente
          </span>
          <span v-else>Ajouter aux amis</span>
        </v-tooltip>
        <img
          v-if="localFriendStatus === 'ami'"
          src="@/assets/icons/friend-remove-black.svg"
          alt="Retirer ami"
          class="plus-icon"
          @click="handleFriendsClick"
        />
        <img
          v-else-if="localFriendStatus === 'pending'"
          src="@/assets/icons/friend-pending-black.svg"
          alt="Invitation en attente"
          class="plus-icon disabled"
        />
        <img
          v-else
          src="@/assets/icons/friend-add-black.svg"
          alt="Ajouter ami"
          class="plus-icon"
          @click="handleFriendsClick"
        />
      </div>

      <!-- Logo et Métier -->
      <div class="card-header-left">
        <div class="logo-wrapper">
          <img
            :src="
              candidate.photo
                ? getImgPath(candidate.photo)
                : require('@/assets/search/search-page-jobcard-emptylogo-icon.jpg')
            "
            alt="logo"
            class="logo"
          />
          <!-- Cercle ajouté -->
          <div
            :class="[
              'status-circle',
              {
                connected: candidate.connected,
                disconnected: !candidate.connected,
              },
            ]"
          ></div>
        </div>

        <!-- candidate job -->
        <div class="title-wrapper">
          <p class="">{{ formattedMetier }}</p>
        </div>
      </div>

      <!-- favorite profil icon -->
      <div
        v-if="isLoggedIn && userRole === 'recruiter' && !isCurrentUser"
        class="fav-wrapper"
        @click="handleFavoriteProfilClick(candidate.id)"
      >
        <v-tooltip location="bottom" activator="parent">
          <span v-if="localIsFavorite">Retirer des favoris</span>
          <span v-else>Ajouter aux favoris</span>
        </v-tooltip>

        <img
          v-if="localIsFavorite"
          src="@/assets/search/search-page-card-like-icon-filled.svg"
          alt="heart like filled"
          class="favorite-icon"
        />

        <img
          v-else
          src="@/assets/search/search-page-card-like-icon.svg"
          alt="heart like empty"
          class="favorite-icon"
        />
      </div>
    </div>

    <div class="card-body">
      <!-- location -->
      <div class="candidate-name">
        <h6>{{ formattedName }}</h6>
      </div>
      <div class="candidate-id">
        <!--small>ID: {{ candidate.id }}</small-->
      </div>
      <div class="body-field">
        <img
          src="@/assets/search/search-page-card-location-icon.svg"
          alt="location icon"
        />
        <p>{{ candidate.ville }}</p>
      </div>

      <div class="info-container">
        <!-- First row: Remote and Contract -->
        <div class="info-row">
          <div class="body-field" v-if="candidate.teletravail">
            <img
              src="@/assets/search/search-page-card-remote-icon.svg"
              alt="remote options icon"
            />
            <p>{{ candidate.teletravail }}</p>
          </div>

          <div class="body-field" v-if="candidate.contrat">
            <img
              src="@/assets/search/search-page-card-contract-icon.svg"
              alt="contract icon"
              class="contract-icon"
            />
            <p>{{ candidate.contrat }}</p>
          </div>
        </div>

        <!-- Second row: Experience -->
        <div class="info-row">
          <div class="body-field" v-if="candidate.experiences">
            <img
              src="@/assets/search/search-page-card-exp-icon.svg"
              alt="experience icon"
            />
            <p>{{ candidate.experiences }}</p>
          </div>
        </div>
      </div>
    </div>

    <div class="card-footer">
      <!-- candidate description
      <p class="cut-description-3">{{ candidate.about }}</p> -->

      <!-- btn to see candidate detail -->
      <PrimaryRoundedButton
        textContent="Voir le profil"
        @click="gotoPage(`/utilisateur/${candidate.id}`)"
      />
    </div>
  </article>
</template>

<script>
  import { mapGetters } from 'vuex';
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import gotoPage from '@/utils/router.js';
  import {
    addFavoriteProfil,
    removeFavoriteProfil,
    isCandidateFavoriteById,
  } from '@/services/favoriteProfil.service';
  import {
    removeFriend,
    sendFriendInvitation,
  } from '@/services/friend.service';
  import { toaster } from '@/utils/toast/toast';
  import getImgPath from '@/utils/imgpath.js';
  import { useMessagerieStore } from '@/store/messagerie';

  export default {
    name: 'LargeCandidateCard',

    props: {
      candidate: {
        type: Object,
        required: true,
      },
      currentUserId: {
        type: [String, Number],
        default: null,
      },
    },

    components: {
      PrimaryRoundedButton,
    },

    data() {
      const candidateTypeUser = this.candidate.type_user;

      return {
        candidateTypeUser: candidateTypeUser,
        localFriendStatus: this.candidate.friend_status,
        localIsFavorite: this.candidate.is_favorite,
        invitationPending: this.candidate.friend_status === 'pending',
      };
    },

    computed: {
      ...mapGetters(['isLoggedIn', 'getUser', 'userRole']),

      isCurrentUser() {
        return (
          this.currentUserId &&
          (String(this.currentUserId) === String(this.candidate.id))
        );
      },

      formattedName() {
        const firstName = this.candidate.first_name
          ? this.capitalizeFirstLetter(this.candidate.first_name)
          : '';
        const lastName = this.candidate.last_name
          ? this.capitalizeFirstLetter(this.candidate.last_name)
          : '';
        return `${firstName} ${lastName}`.trim();
      },
      formattedMetier() {
        return this.candidate.metier
          ? this.capitalizeFirstLetter(this.candidate.metier)
          : '';
      },
    },

    methods: {
      gotoPage,
      getImgPath,

      async handleFavoriteProfilClick(candidateId) {
        const isLoggedIn = this.isLoggedIn;
        if (!isLoggedIn) {
          toaster.showErrorPopup(
            'Veuillez vous connecter pour ajouter des profils aux favoris.'
          );
          return;
        }

        try {
          if (this.localIsFavorite) {
            // Si déjà en favori, retirer
            const favoritesList = await isCandidateFavoriteById();

            const favoriteItem = favoritesList.find(
              (fav) =>
                fav.apply_user &&
                fav.apply_user.length > 0 &&
                fav.apply_user[0].id === candidateId
            );

            if (favoriteItem) {
              const favoriteId = favoriteItem.id;

              const response = await removeFavoriteProfil(favoriteId);
              if (response) {
                toaster.showSuccessPopup('Profil retiré des favoris.');
                this.localIsFavorite = false; // Modifier la copie locale
              }
            }
          } else {
            // Si pas encore en favori, ajouter
            const response = await addFavoriteProfil(candidateId);
            if (response) {
              toaster.showSuccessPopup('Profil ajouté aux favoris.');
              this.localIsFavorite = true; // Modifier la copie locale
            }
          }
        } catch (error) {
          toaster.showErrorPopup(
            'Une erreur est survenue lors de la mise à jour des favoris.'
          );
          //console.error('Erreur handleFavoriteProfilClick:', error);
        }
      },

      async handleFriendsClick() {
        const messagerieStore = useMessagerieStore();
        try {
          if (this.localFriendStatus === 'ami') {
            await removeFriend(this.candidate.id);
            toaster.showSuccessPopup('Ami supprimé avec succès.');
            this.localFriendStatus = 'non ami';
            // Mise à jour du store Pinia
            messagerieStore.users = messagerieStore.users.filter(u => u.id !== this.candidate.id);
          } else if (this.localFriendStatus === 'non ami') {
            await sendFriendInvitation(this.candidate.id);
            toaster.showSuccessPopup('Invitation envoyée avec succès.');
            this.localFriendStatus = 'pending';
            // Mise à jour du store Pinia
            messagerieStore.addSentInvitation({ recipient: this.candidate.id, id: Date.now(), state: 'pending' });
          } else if (this.localFriendStatus === 'pending') {
            toaster.showInfoPopup('Invitation déjà en attente.');
          }
        } catch (error) {
          toaster.showErrorPopup(
            'Une erreur est survenue lors du traitement de la demande.'
          );
        }
      },

      // Méthode pour capitaliser la première lettre d'un mot
      capitalizeFirstLetter(str) {
        if (!str) return str; // Si la chaîne est vide, retourner telle quelle
        return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
      },
    },
  };
</script>

<style scoped>
  .candidate-card {
    background-color: var(--search-candidate-candidatecard-bg-color);
    border-radius: 15px;
    width: 100%;
    position: relative;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Ombre portée (gris pâle) */
    transition: box-shadow 0.3s ease; /* Animation douce pour l'ombre */
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .candidate-card:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4); /* Ombre portée plus intense */
  }
  .highlight-card {
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
    background: linear-gradient(
      to bottom,
      var(--primary-1),
      var(--surface-bg-2)
    );
  }

  .card-header {
    padding-top: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .add-button {
    position: absolute;
    top: 10px;
    left: 10px;
    cursor: pointer;
    z-index: 10;
  }

  .plus-icon {
    width: 24px;
    height: 24px;
    cursor: pointer;
    pointer-events: auto;
  }

  .card-header-left {
    display: flex;
    flex-direction: column;
    align-items: center; /* Centre la photo et le métier */
    flex: 1; /* Prend tout l'espace disponible */
    position: relative;
  }

  .logo-wrapper {
    width: 100px;
    height: 100px;
    position: relative; /* Nécessaire pour positionner le cercle à l'intérieur */
    flex-shrink: 0; /* Empêche la réduction de la taille de l'image */
  }

  .logo {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
  }

  .status-circle {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    position: absolute;
    bottom: 0;
    right: 0;
    transform: translate(10%, 10%);
    border: 2px solid white;
  }
  .status-circle.connected {
    background-color: green; /* Vert pour connecté */
  }

  .status-circle.disconnected {
    background-color: gray; /* Gris pour hors connexion */
  }

  .title-wrapper {
    margin-top: 20px;
    display: flex;
    align-items: center;
    text-align: center;
    word-break: keep-all;
    padding: 0 2%;
  }

  .fav-wrapper {
    cursor: pointer;
    position: absolute; /* Place le cœur en haut à droite */
    top: 10px;
    right: 10px;
  }

  .favorite-icon {
    width: 24px;
    height: 24px;
  }

  .card-body {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding: 0 2%;
  }

  .info-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    margin: 10px 0;
  }

  .info-row {
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 20px;
    margin: 5px 0;
  }

  .body-field {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 3px 0;
    min-width: 80px;
  }

  .body-field img {
    margin-right: 5px;
    height: 20px;
    width: 20px;
    flex-shrink: 0;
  }

  .body-field p {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .contract-icon {
    height: 18px;
    width: 18px;
  }

  .candidate-name {
    display: flex;
    justify-content: space-around;
  }

  .card-footer {
    margin-top: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    margin: 20px 0;
  }

  .cut-description-2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    line-clamp: 2;
    -webkit-line-clamp: 2; /* edit this line to change the number of lines displayed */
    line-height: 1.2;
  }

  .cut-description-3 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    line-clamp: 3;
    -webkit-line-clamp: 3; /* edit this line to change the number of lines displayed */
    line-height: 1.2;
  }

  @media screen and (max-width: 768px) {
    .candidate-card {
      flex-direction: row;
      align-items: stretch;
      min-height: 160px;
      height: auto;
      padding: 8px 0;
      position: relative;
    }
    .card-header {
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      width: 120px;
      min-width: 120px;
      padding-top: 0;
      position: relative;
      background: none;
      box-shadow: none;
    }
    .add-button {
      position: absolute;
      top: 5px;
      right: 5px;
      left: auto;
      z-index: 20;
    }
    .fav-wrapper {
      top: 40px;
      right: 5px;
      left: auto;
      z-index: 10;
    }
    .card-header-left {
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      flex: none;
    }
    .logo-wrapper {
      width: 70px;
      height: 70px;
      margin-bottom: 8px;
    }
    .title-wrapper {
      margin-top: 8px;
      font-size: 0.95rem;
      padding: 0;
    }
    .card-body {
      flex: 1 1 0%;
      padding: 8px 8px 8px 0;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      gap: 4px;
      position: relative;
    }
    .candidate-name {
      margin-top: 0;
      margin-bottom: 0px;
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
      padding-left: 0;
    }
    .candidate-name h6 {
      font-size: 1.05rem;
      margin-bottom: 0;
      margin-top: 0;
    }
    .body-field {
      min-width: 0;
      font-size: 0.95rem;
      margin: 0 4px 0 0;
      padding: 0;
      align-items: flex-start;
      justify-content: flex-start;
      padding-left: 0;
    }
    .body-field:first-of-type {
      margin-top: 0px;
      margin-bottom: 2px;
      padding-left: 0;
      margin-left: 0;
      justify-content: flex-start;
    }
    .body-field img {
      height: 16px;
      width: 16px;
      margin-right: 3px;
      margin-left: 0;
    }
    .body-field p {
      margin-left: 0;
      padding-left: 0;
    }
    .info-container {
      margin: 4px 0;
      width: 100%;
    }
    .info-row {
      flex-direction: row;
      gap: 10px;
      margin: 2px 0;
      justify-content: flex-start;
    }
    .card-footer {
      flex-direction: column;
      align-items: flex-end;
      justify-content: flex-end;
      margin: 0 8px 0 0;
      min-width: 110px;
      width: auto;
      position: absolute;
      bottom: 8px;
      right: 8px;
    }
    .PrimaryRoundedButton {
      width: 100%;
      min-width: 100px;
      font-size: 0.95rem;
      padding: 6px 0;
    }
  }

  @media screen and (min-width: 992px) {
    .info-row {
      justify-content: space-around;
      width: 100%;
    }

    .body-field {
      min-width: 120px;
    }
  }

  @media screen and (min-width: 1200px) {
    .info-container {
      width: 90%;
      margin: 10px auto;
    }
  }
</style>
