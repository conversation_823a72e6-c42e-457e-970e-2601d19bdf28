<template>
  <div class="purchase-footer">
    <ul>
      <li @click="gotoPage('/mentions-legales')">Mentions légales</li>
      <li @click="gotoPage('/politique-confidentialite')">
        Politique de confidentialité
      </li>
      <li @click="gotoPage('/cgu')">Conditions générales d'utilisation</li>
      <li @click="gotoPage('/cgv')">Conditions générales de vente</li>
    </ul>
  </div>
</template>

<script>
  import gotoPage from '@/utils/router.js';
  export default {
    name: 'PurchaseFooter',
    methods: {
      gotoPage,
    },
  };
</script>
<style>
  .purchase-footer {
    width: 100vw;
    margin: 50px 0;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
  }

  a {
    text-decoration: none;
    color: var(--black-200);
  }

  ul {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    list-style: none;
  }

  li {
    cursor: pointer;
  }

  @media screen and (min-width: 992px) {
    .purchase-footer {
      flex-direction: row;
    }

    ul {
      width: 80vw;
      margin: auto;
      flex-direction: row;
      justify-content: space-around;
    }
  }
</style>
