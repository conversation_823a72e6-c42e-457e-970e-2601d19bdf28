<template>
  <div class="card-container">
    <h5>{{ subscription.nom }}</h5>
    <table>
      <tbody>
        <tr>
          <td class="td-left">Prix HT</td>
          <td class="td-right">{{ subscription.prix_ht }} €</td>
        </tr>
        <tr>
          <td class="td-left">Réduction</td>
          <td class="td-right">Promo €</td>
        </tr>
        <tr>
          <td class="td-left">TVA</td>
          <td class="td-right">
            {{ (subscription.prix_ttc - subscription.prix_ht).toFixed(2) }} €
          </td>
        </tr>
      </tbody>
      <tfoot>
        <tr>
          <td class="td-left">Prix TTC</td>
          <td class="td-right">{{ subscription.prix_ttc }} €</td>
        </tr>
      </tfoot>
    </table>
  </div>
</template>

<script>
  export default {
    name: 'PurchaseCard',
    props: {
      subscription: {
        type: Object,
        default: () => ({}),
      },
    },
  };
</script>

<style scoped>
  .card-container {
    background-color: var(--white-200);
    border: 1px solid var(--primary-3b);
    border-radius: 15px;
    text-align: center;
    padding-top: 16px;
    margin: 16px 0;
  }

  table {
    width: 100%;
    height: 80%;
  }

  tr:nth-child(3) td {
    border-bottom: 1px solid var(--black-200);
  }

  tfoot {
    font-weight: bold;
    vertical-align: bottom;
  }

  .td-left {
    text-align: left;
    padding-left: 32px;
  }

  .td-right {
    text-align: right;
    padding-right: 32px;
  }

  @media screen and (min-width: 992px) {
    .card-container {
      height: 300px;
      background-color: var(--white-200);
      border: 1px solid var(--primary-3b);
      border-radius: 15px;
      text-align: center;
      padding-top: 16px;
    }
  }
</style>
