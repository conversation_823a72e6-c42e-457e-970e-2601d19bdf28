<template>
    <div class="purchase-header">
        <h1>Votre commande</h1>
    </div>
</template>

<script setup>
import { ref } from 'vue';

</script>

<script>

import gotoPage from '@/utils/router.js';
import PrimaryRoundedButton from '../../../components/buttons/PrimaryRoundedButton.vue';


export default {
    name: 'PurchaseTitle',
    components: {
        PrimaryRoundedButton
    },
    data() {
        return {

        };
    },
    methods: {
        gotoPage,
    },
};
</script>

<style scoped>
.purchase-header {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-bottom: 32px;
}


@media screen and (min-width: 992px) {

    .purchase-header {
        flex-direction: column;
        justify-content: center;
        width: 80%;
        margin: 16px auto;
    }

    .purchase-header-btn {
        align-self: flex-start;
    }
}


/* * {
    border: 1px red solid;
  } */
</style>