<template>
  <div class="purchase-card-header">
    <div class="purchase-card-header-title" v-for="(title, index) in titles" :key="index">
      <div class="purchase-card-header-number">{{ index + 1 }}</div>
      <PurchaseCardHeaderTitle :title="title" />
      <span v-if="index < titles.length - 1"> / </span>
    </div>
  </div>
</template>

<script>
import PurchaseCardHeaderTitle from './PurchaseCardHeaderTitle.vue';

export default {
  name: 'PurchaseCardHeader',
  components: {
    PurchaseCardHeaderTitle
  },
  data() {
    return {
      titles: [
        {
          id: 1,
          name: 'Récapitulatif'
        },
        {
          id: 2,
          name: 'Information<PERSON>'
        },
        {
          id: 3,
          name: '<PERSON><PERSON>'
        },
        {
          id: 4,
          name: '<PERSON><PERSON><PERSON>'
        },
      ]
    }
  }
}
</script>

<style scoped>
.purchase-card-header-title {
  display: none;
}

@media screen and (min-width: 992px) {
  .purchase-card-header {
    display: flex;
  }

  .purchase-card-header-title {
    margin: 8px;
    display: flex;
    align-items: center;
  }

  .purchase-card-header-number {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    height: 24px;
    width: 24px;
    border: 1px solid black;
    border-radius: 50%;
    text-align: center;

  }
}
</style>