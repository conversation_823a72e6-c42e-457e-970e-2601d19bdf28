<template>
    <article class="card-container">
      <div class="photo-wrapper">
        <img
          :src="getImgPath(article?.photo)"
          alt="preview photo"
          class="photo"
          @error="handleImageError"
        />
      </div>
  
      <div :class="blackCard ? 'card-body black-bg' : 'card-body'">
        <p :class="blackCard ? 'text white-text fs12' : 'text fs12'">
          {{ article?.categorie }}/{{ article?.theme }}
        </p>
  
        <h6 :class="blackCard ? 'white-text' : ''">{{ article?.titre }}</h6>
  
        <p
          v-if="article?.petit_description"
          :class="blackCard ? 'text white-text' : 'text'"
        >
          {{ article?.petit_description }}
        </p>
        <p v-else :class="blackCard ? 'text white-text' : 'text'">
          content not available
        </p>
  
        <p :class="blackCard ? 'white-text fs12' : ''">
          {{
            article?.date_publication
              ? article.date_publication.slice(0, 10)
              : 'Date not available'
          }}
        </p>
  
        <div class="btn-container">
          <PrimaryRoundedButton
            textContent="Voir plus"
            @btn-click="$emit('card-btn-click')"
          style="padding-block: 0;"
          />
        </div>
      </div>
    </article>
  </template>
  
  <script>
    import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
    import getImgPath from '@/utils/imgpath.js';
  
    export default {
      name: 'BlockNewsCard',
  
      components: {
        PrimaryRoundedButton,
      },
  
      props: {
        //  article object
        article: {
          type: Object,
          required: true,
        },
  
        //  path of the photo in the card header
        photoPath: {
          type: String,
          default: 'default Path',
        },
  
        //  title of the article
        title: {
          type: String,
          default: 'default title',
        },
  
        //  content of the article
        textContent: {
          type: String,
          default: 'default text',
        },
  
        //  pubication date
        publicationDate: {
          type: String,
          default: 'jj/mm/aaaa',
        },
  
        //  secondary card theme
        blackCard: {
          type: Boolean,
        },
      },
      methods: {
        getImgPath,
        handleImageError(event) {
          event.target.src = require('@/assets/home/<USER>');
        },
      },
    };
  </script>
  
  <style scoped>
    .card-container {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      background-color: white;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
      border-radius: 10px;
    }
  
    .photo-wrapper {
      width: 100%;
      height: 180px;
    }
  
    .photo {
      width: 100%;
      height: 100%;
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
      object-fit: cover;
    }
  
    .card-body {
      padding: 25px;
      background-color: white;
      border-bottom-left-radius: 20px;
      border-bottom-right-radius: 20px;
      display: flex;
      flex-direction: column;
      height: 260px;
      justify-content: space-around;
    }
  
    .black-bg {
      background-color: var(--black-100);
    }
  
    .white-text {
      color: var(--white-200);
    }
  
    .card-body h5 {
      margin-bottom: 40px;
      text-align: center;
    }
  
    .text {
      margin-bottom: 15px;
    }
  
    .fs12 {
      font-size: 12px;
    }
  
    .btn-container {
      display: flex;
      justify-content: end;
    }
  </style>
  