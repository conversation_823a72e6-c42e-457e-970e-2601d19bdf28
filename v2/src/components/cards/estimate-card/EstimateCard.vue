<template>
  <div class="container">
    <img class="estimateImg" src="@/assets/background/img-devis.jpeg" alt="" />
    <div class="estimateText">
      <h5>Un devis vous attend, ne passez pas à côté !</h5>
      <p>Retrouvez votre devis là où vous l'avez laissé.</p>
    </div>
    <div class="estimateBtn">
      <PrimaryRoundedButton
        textContent="Reprendre le devis"
        btnColor="secondary"
        ok
        @click="$emit('resume-devis')"
      />
    </div>
    <div></div>
  </div>
</template>

<script>
  import PrimaryRoundedButton from '../../buttons/PrimaryRoundedButton.vue';

  export default {
    name: 'EstimateCard',

    components: {
      PrimaryRoundedButton,
    },

    props: {
      factures: {
        type: Array,
        default: () => [],
      },
      subscriptions: {
        type: Array,
        default: () => [],
      },
    },
  };
</script>

<style scoped>
  .container {
    width: 100%;
    height: 200px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    background: linear-gradient(
      180deg,
      #e0d9e0 0%,
      #dfd7df 56.74%,
      #e0d7df 74.07%,
      #f1ecf0 100%
    );
  }

  .estimateImg {
    height: 200px;
    justify-self: baseline;
    transform: scaleX(-1);
    -webkit-transform: scaleX(-1);
  }

  .estimateText {
    text-align: center;
    line-height: 1.5;
  }

  .estimateText h5 {
    font-size: 1.2rem;
  }

  .estimateText p {
    font-size: 1rem;
  }

  .estimateBtn {
    align-self: flex-end;
    margin-bottom: 36px;
  }

  /* Media Queries for responsiveness */

  @media (max-width: 768px) {
    .container {
      flex-direction: column;
      height: auto;
      text-align: center;
    }

    .estimateImg {
      height: 150px;
      transform: scaleX(-1);
      margin: 10px auto;
    }

    .estimateText h5 {
      font-size: 1rem;
      margin-top: 10px;
    }

    .estimateText p {
      font-size: 0.9rem;
      margin-bottom: 10px;
    }

    .estimateBtn {
      align-self: center;
      margin-top: 10px;
      margin-bottom: 16px;
    }
  }

  @media (max-width: 480px) {
    .estimateImg {
      height: 100px;
    }

    .estimateText h5 {
      font-size: 0.9rem;
    }

    .estimateText p {
      font-size: 0.8rem;
    }

    .estimateBtn {
      margin-bottom: 12px;
    }
  }
</style>
