<template>
  <article
    class="card"
    @click="
      gotoPage(
        `/actualite/${article?.categorie}/${slugify(article?.theme)}/${slugify(article?.titre)}/${article?.id}`
      )
    "
  >
    <div class="card-photo">
      <img
        :src="getImgPath(article?.photo)"
        alt="preview photo"
        class="photo"
        @error="handleImageError"
      />
    </div>

    <div class="card-body">
      <p class="themes">{{ article.categorie }}/{{ article.theme }}</p>
      <h6>{{ article?.titre?.slice(0, 18) }}...</h6>
      <p v-if="article.petit_description">
        {{ article?.petit_description?.slice(0, 18) }}...
      </p>
      <p v-else>content not available for this article</p>
      <p class="publication">{{ article?.date_publication?.slice(0, 10) }}</p>
    </div>
  </article>
</template>

<script setup>
  import gotoPage from '@/utils/router';
</script>

<script>
  import getImgPath from '@/utils/imgpath.js';
  import { slugify } from '../../utils/slugify';

  export default {
    name: 'NewsCardMini',

    props: {
      //  article object
      article: {
        type: Object,
        required: true,
      },

      //  photo path of the card
      photoPath: {
        type: String,
      },

      //  theme category of the card
      theme: {
        type: String,
        default: 'Thème',
      },

      //  subthemere category of the card
      subtheme: {
        type: String,
        default: 'Sous-thème',
      },

      //  title of the card
      title: {
        type: String,
        default: "Titre de l'article",
      },

      //  text content of the card
      textContent: {
        type: String,
        default:
          'Lorem ipsum dolor sit amet consectetur. Turpis lobortis fusce dignissim aenean habitasse cursus at. Tortor nulla a morbi elementum. Dictum aliquet tristique et orci non sollicitudin mi facilisis scelerisque. Eu turpis proin ut congue purus scelerisque placerat nec donec. Est ornare lorem pulvinar malesuada sem suspendisse. Nulla sed porttitor in porttitor vestibulum urna sodales. Amet sem elementum vitae vel quis imperdiet scelerisque dictum. Vel egestas diam purus nullam.',
      },

      //  publication date of the card
      publicationDate: {
        type: String,
        default: 'jj/mm/aaaa',
      },

      //  section type of the article
      sectionType: {
        type: String,
        default: 'candidate',
      },

      //  id of the article
      articleId: {
        type: String,
        default: '0',
      },
    },

    methods: {
      getImgPath,
      handleImageError(event) {
        event.target.src = require('@/assets/home/<USER>');
      },
    },
  };
</script>

<style scoped>
  .card {
    display: flex;
    background-color: var(--white-200);
    border-radius: 20px;
    width: 100%;
    cursor: pointer;
    height: 170px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    max-width: 330px; /* width changed to max-width to resolve responsive issues */
  }

  .card-photo {
    width: 50%;
    overflow: hidden;
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;
  }

  .photo {
    width: 138px;
    height: 100%;
    object-fit: cover;
  }

  .card-body {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 10px;
  }

  .themes,
  .publication {
    font-size: 12px;
  }
</style>
