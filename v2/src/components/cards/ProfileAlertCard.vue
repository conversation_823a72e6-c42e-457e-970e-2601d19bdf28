<template>
  <article class="alert-card">
    <div class="card-header">
      <h4 v-if="alert.nom_alerte">{{ alert.nom_alerte }}</h4>
      <img
        v-if="alert.active"
        src="@/assets/icons/alert-bell.svg"
        alt="cloche alerte"
        class="icon"
        @click="toggleAlertActivation"
      />
      <img
        v-else
        src="@/assets/icons/alert-bell-empty.svg"
        alt="cloche alerte"
        class="icon"
        @click="toggleAlertActivation"
      />
    </div>

    <div class="card-body">
      <img
        src="@/assets/icons/alert-mini-calendar.svg"
        alt="calendrier de sortie"
      />
      <p v-if="alert.updated_at && alert.updated_at.length > 10">
        Créer le : {{ alert.updated_at.slice(0, 10) }}
      </p>
    </div>

    <div class="card-footer" @click="toggleAlertePanel(alert, alertIndex)">
      <PrimaryRoundedButton textContent="Voir l'alerte" />
    </div>
  </article>
</template>

<script>
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';

  export default {
    name: 'AlertCard',

    components: {
      PrimaryRoundedButton,
    },

    props: {
      //  alert object
      alert: {
        type: Object,
        default: null,
      },

      //  alert index in the list
      alertIndex: {
        type: Number,
      },
    },

    data() {
      return {
        active: false, //  if alert is activated
      };
    },

    methods: {
      //  toggle alert panel with alert id
      toggleAlertePanel(alert) {
        this.$emit('toggle-alert-panel', alert);
      },

      //  toggle alert on/off
      toggleAlertActivation() {
        this.$emit('toggle-alert-activation', this.alert);
      },
    },
  };
</script>

<style scoped>
  /* tags */
  p {
    font-size: 12px;
  }

  /* layout */
  .alert-card {
    border-radius: 15px;
    background-color: var(--surface-bg-2);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 20px;
    padding: 15px;
    width: 258px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Ombre portée (gris pâle) */
    transition: box-shadow 0.3s ease; /* Animation douce pour l'ombre */
  }
  .alert-card:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4); /* Ombre portée plus intense */
  }

  .card-header {
    display: flex;
    justify-content: space-between;
  }

  .card-body {
    display: flex;
    gap: 10px;
  }

  .card-footer {
    display: flex;
    justify-content: center;
    width: 100%;
  }

  /* buttons */
  .icon {
    cursor: pointer;
  }
</style>
