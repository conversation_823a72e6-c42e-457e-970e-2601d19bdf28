<template>
  <article class="card-container">
    <div class="card-container-header">
      <div class="number-box">
        <p class="number">{{ cardNumber }}</p>
      </div>
      <h5>{{ title }}</h5>
    </div>

    <p class="text" v-html="textContent"></p>
  </article>
</template>

<script>
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';

  export default {
    name: 'NumberedCard',

    components: {
      PrimaryRoundedButton,
    },

    props: {
      cardNumber: {
        type: String,
        default: '0',
      },
      title: {
        type: String,
        default: 'default title',
      },
      textContent: {
        type: String,
        default: 'default text',
      },
    },
  };
</script>

<style scoped>
  .card-container {
    padding: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    background-color: var(--home-cards-bg-color-2);
    width: 320px;
    height: 387px;
    border-radius: 20px;
    margin-bottom: 21px;
  }

  .card-container h5 {
    text-align: center;
  }

  .card-container h5 {
    margin-top: 25px;
    margin-bottom: 20px;
    text-align: center;
    font-weight: 500;
  }

  .card-container-header {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  .number-box {
    background-color: var(--home-cards-number-bg-color);
    width: 57px;
    height: 57px;
    border-radius: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .number {
    font-family: 'Anton';
    font-size: 35px;
    font-weight: 500;
  }

  .text {
    height: 60%;
    margin-bottom: 10px;
  }

  .v-btn__content {
    display: none !important;
  }
</style>
