<template>
  <article class="candidature-card">
    <div class="candidature-logo">
      <img
        :src="candidature.img ? getImgPath(candidature.img) : defaultLogo"
        alt="candidature logo"
        @error="handleImageError"
        class="logo"
      />
    </div>
    <div class="right-side">
      <div class="candidature-card-header">
        <div>
          <h3>{{ candidature.title }}</h3>
          <p>{{ candidature.jobOffert.nom_recruteur }}</p>
        </div>

        <div>
          <div class="job-status-container">
            <div class="candidature-date">
              <img
                src="@/assets/icons/calendly.svg"
                alt="calendar icon"
                class="candidature-icon"
              />
              <p>{{ candidatureDate }}</p>
            </div>
            <img
              :src="iconStatus"
              alt="icon du status de la candidature"
              class="candidature-icon"
            />
            <p>{{ mappedStatus }}</p>
          </div>
          <!-- heart icon -->
          <div
            class="fav-wrapper job-card-header-heart"
            @click="
              handleFavoriteJobClick(
                candidature.jobOffert.id,
                candidature.jobOffert
              )
            "
          >
            <v-tooltip activator="parent" location="bottom">
              {{ jobIsLiked ? 'Retirer des favoris' : 'Ajouter aux favoris' }}
            </v-tooltip>
            <img
              v-if="jobIsLiked"
              src="@/assets/search/search-page-card-like-icon-filled.svg"
              alt="heart like filled"
              class="favorite-icon"
            />
            <img
              v-else
              src="@/assets/search/search-page-card-like-icon.svg"
              alt="heart like empty"
              class="favorite-icon"
            />
          </div>
        </div>
      </div>
      <div class="candidature-card-description">
        {{ candidature.description }}
      </div>
      <!-- boutons -->
      <div class="candidature-card-footer">
        <div>
          <button class="btn-annuler-candidature" @click="handleShowModal">
            Annuler
          </button>
          <PrimaryRoundedButton
            :disabled="candidature.status === 'Supprimée'"
            textContent="Voir l'offre"
            :btnColor="
              candidature.status === 'Supprimée' ? 'disabled' : 'primary'
            "
            @click="
              $router.push(`/offre-d-emplois/${candidature.jobOffert.id}`)
            "
          />
        </div>
        <v-tooltip location="bottom">
          <template #activator="{ props }">
            <div v-bind="props" class="tooltip-wrapper">
              <PrimaryRoundedButton
                textContent="Contacter le recruteur"
                disabled
              />
            </div>
          </template>
          <span>Fonctionnalité disponible prochainement</span>
        </v-tooltip>
      </div>
    </div>
  </article>
  <div class="backdrop-modal" v-if="modalIsOpen">
    <div class="modal-container">
      <ConfirmationModal
        class="confirmation-modal"
        title="Annuler sa candidature"
        @close="handleShowModal"
        @confirm="handleDeclineCandidature(candidature.jobOffert.id)"
      />
    </div>
  </div>
</template>

<script setup>
  import defaultLogo from '@/assets/logo-tb.svg';
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import { isJobFavoriteById } from '@/services/job.service.js';
  import getImgPath from '@/utils/imgpath.js';
  import { computed, defineProps, ref } from 'vue';
  import store from '../../../store';
  import ConfirmationModal from '../../modal/confirmation/ConfirmationModal.vue';

  import {
    addFavoriteJob,
    removeFavoriteJob,
  } from '@/services/favoriteJob.service.js';
  import { toaster } from '@/utils/toast/toast';

  const props = defineProps({
    candidature: {
      type: Object,
      required: true,
    },
  });
  const emits = defineEmits(['decline-candidature']);

  const statusMapping = {
    'en cours': 'Envoyées',
    "a l'étude": "À l'étude",
    accepté: 'Acceptées',
    refuser: 'Refusées',
  };

  const mappedStatus = computed(() => {
    const normalizedStatus = props.candidature?.status?.toLowerCase();
    return statusMapping[normalizedStatus] || props.candidature?.status || '';
  });

  // Mise en place d'une image par défaut en cas d'erreur de chargement du logo
  const handleImageError = (event) => {
    event.target.src = defaultLogo;
  };

  const jobIsLiked = ref(false);
  const candidatureDate = computed(() => {
    const date = new Date(props.candidature.createdAt);
    return date.toLocaleDateString('fr', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  });

  const iconStatus = computed(() => {
    switch (props.candidature.status) {
      case "A l'étude":
        return require('@/assets/icons/candidature-en-cours.svg');
      case 'Accepté':
        return require('@/assets/icons/valid-icon.svg');
      case 'Refuser':
        return require('@/assets/icons/candidature-refusee.svg');
      case 'Auto candidature':
        return require('@/assets/icons/candidature-auto-candidature.svg');
      default:
        return require('@/assets/icons/candidature-brouillon.svg');
    }
  });

  // Vérifie si la candidature est déjà ajoutée aux favoris au montage du composant
  const checkFavoriteStatus = async () => {
    jobIsLiked.value = await isJobFavoriteById(props.candidature.jobOffert.id);
  };

  checkFavoriteStatus();

  const handleFavoriteJobClick = async (jobId, jobOffert) => {
    const isLoggedIn = store.getters.isLoggedIn;
    if (!isLoggedIn) {
      toaster.showErrorPopup(
        'Veuillez vous connecter pour ajouter des offres aux favoris.'
      );
      return;
    }

    if (jobIsLiked.value) {
      // Retirer du favoris
      const response = await removeFavoriteJob(jobId);
      if (response) {
        store.dispatch('removeFavoriteJob', jobId); // Retirer du store
        toaster.showSuccessPopup('Annonce retirée des favoris.');
        jobIsLiked.value = false;
      }
    } else {
      // Ajouter aux favoris
      const response = await addFavoriteJob(jobId, jobOffert);
      if (response) {
        store.dispatch('addFavoriteJob', jobOffert); // Ajouter au store
        toaster.showSuccessPopup('Annonce ajoutée aux favoris.');
        jobIsLiked.value = true;
      }
    }
  };

  const modalIsOpen = ref(false);
  const handleShowModal = () => {
    modalIsOpen.value = !modalIsOpen.value;
  };
  const handleDeclineCandidature = (jobId) => {
    emits('decline-candidature', jobId);
    handleShowModal();
  };
</script>

<style scoped>
  .backdrop-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
  }
  .backdrop-modal .modal-container {
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: red;
    position: relative;
  }
  .confirmation-modal {
    transform: translate(-50%, -50%);
  }
  .btn-annuler-candidature {
    padding-inline: 16px;
    width: fit-content;
    height: 40px;
    border-radius: 5px;
    box-shadow: none;
    font-weight: 500;
    /* color: var(--yellow-100); */
    border: solid 3px var(--yellow-100);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .candidature-icon,
  .favorite-icon {
    width: 32px;
    aspect-ratio: 1/1;
  }
  .candidature-card {
    position: relative;
    display: flex;
    gap: 20px;
    width: 100%;
    min-height: 227px;
    padding: 20px;
    border-radius: 5px;
    background-color: var(--surface-bg-2);
    flex-direction: row;
  }
  .candidature-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 150px;
    aspect-ratio: 1/1;
    overflow: hidden;
  }
  .candidature-date {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-right: 40px;
  }
  .candidature-logo img {
    width: 100%;
    /* height: 100%; */
    object-fit: cover;
  }
  .logo {
    border: 1px solid rgba(223, 219, 214, 1);
    width: 92px;
    border-radius: 8px;
    object-fit: contain;
  }
  .right-side {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .candidature-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    margin-bottom: 10px;
    gap: 12px;
    border-bottom: solid 1px var(--surface-bg);
  }
  .candidature-card-header div:nth-child(2) {
    display: flex;
    gap: 15px;
  }
  .job-status-container {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
  }
  .job-status-container p {
    font-weight: 600;
  }
  .candidature-card-description {
    width: 90%;
    font-size: 12px;
    overflow: hidden;
    height: 100%;
    max-height: 70px;
    margin-bottom: 10px;
  }
  .candidature-card-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
  .candidature-card-footer :first-child {
    display: flex;
    gap: 15px;
  }
  @media (max-width: 768px) {
    .btn-content {
      text-align: center;
    }
    .job-card-header-heart {
      position: absolute;
      top: 5px;
      right: 5px;
    }
    .candidature-date {
      margin-right: 5px;
    }
    .candidature-card-header {
      flex-direction: column;
    }
    .candidature-card {
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 0;
    }
    .right-side {
      align-items: center;
    }
    .tooltip-wrapper button {
      width: 100%;
      max-width: 300px;
    }
    .candidature-card-footer {
      margin-top: 20px;
      width: 100%;
      max-width: 300px;
      flex-direction: column-reverse;
      justify-content: space-between;
      flex-wrap: wrap;
    }
    .candidature-card-footer button {
      flex: 1;
      text-align: center;
    }
    .candidature-logo {
      width: 100px;
      /* aspect-ratio: auto; */
    }
    .candidature-card-description {
      width: 100%;
      text-align: center;
    }
  }
</style>
