<template>
  <article class="candidature-card">
    <div class="candidature-logo">
      <img :src="defaultLogo" alt="logo" class="logo" />
    </div>
    <div class="right-side">
      <div class="candidature-card-header">
        <div>
          <h3>{{ candidature.nom_job }}</h3>
          <p>{{ candidature.contrat }}</p>
        </div>

        <div class="job-status-container">
          <div class="candidature-date">
            <img
              src="@/assets/icons/calendly.svg"
              alt="calendar icon"
              class="candidature-icon"
            />
            <p>{{ formattedDate }}</p>
          </div>
          <img
            src="@/assets/icons/candidature-auto-candidature.svg"
            alt="auto-candidature"
            class="candidature-icon"
          />
          <p>{{ mappedStatus }}</p>
        </div>
      </div>

      <div class="candidature-card-description">
        {{
          candidature.lettre_motivation ||
          candidature.description ||
          'Aucune information fournie.'
        }}
      </div>

      <div class="candidature-card-footer">
        <PrimaryNormalButton
          textContent="Modifier"
          @click="$emit('edit', candidature)"
        />
        <PrimaryNormalButton
          textContent="Supprimer"
          btnColor="secondary"
          @click="$emit('delete', candidature.id)"
        />
      </div>
    </div>
  </article>
</template>

<script setup>
  import { computed } from 'vue';
  import defaultLogo from '@/assets/logo-tb.svg';
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';

  const props = defineProps({
    candidature: Object,
  });
  const emits = defineEmits(['edit', 'delete']);

  const formattedDate = computed(() =>
    new Date(props.candidature.created_at).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  );

  const mappedStatus = computed(() => {
    return props.candidature.status || 'En cours';
  });
</script>

<style scoped>
  .backdrop-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
  }
  .backdrop-modal .modal-container {
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: red;
    position: relative;
  }
  .confirmation-modal {
    transform: translate(-50%, -50%);
  }
  .btn-annuler-candidature {
    padding-inline: 16px;
    width: fit-content;
    height: 40px;
    border-radius: 5px;
    box-shadow: none;
    font-weight: 500;
    /* color: var(--yellow-100); */
    border: solid 3px var(--yellow-100);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .candidature-icon,
  .favorite-icon {
    width: 32px;
    aspect-ratio: 1/1;
  }
  .candidature-card {
    position: relative;
    display: flex;
    gap: 20px;
    width: 100%;
    min-height: 227px;
    padding: 20px;
    border-radius: 5px;
    background-color: var(--surface-bg-2);
    flex-direction: row;
  }
  .candidature-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 150px;
    aspect-ratio: 1/1;
    overflow: hidden;
  }
  .candidature-date {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-right: 40px;
  }
  .candidature-logo img {
    width: 100%;
    /* height: 100%; */
    object-fit: cover;
  }
  .logo {
    border: 1px solid rgba(223, 219, 214, 1);
    width: 92px;
    border-radius: 8px;
    object-fit: contain;
  }
  .right-side {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .candidature-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    margin-bottom: 10px;
    gap: 12px;
    border-bottom: solid 1px var(--surface-bg);
  }
  .candidature-card-header div:nth-child(2) {
    display: flex;
    gap: 15px;
  }
  .job-status-container {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
  }
  .job-status-container p {
    font-weight: 600;
  }
  .candidature-card-description {
    width: 90%;
    font-size: 12px;
    overflow: hidden;
    height: 100%;
    max-height: 70px;
    margin-bottom: 10px;
  }
  .candidature-card-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
  .candidature-card-footer :first-child {
    display: flex;
    gap: 15px;
  }
  @media (max-width: 768px) {
    .btn-content {
      text-align: center;
    }
    .job-card-header-heart {
      position: absolute;
      top: 5px;
      right: 5px;
    }
    .candidature-date {
      margin-right: 5px;
    }
    .candidature-card-header {
      flex-direction: column;
    }
    .candidature-card {
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 0;
    }
    .right-side {
      align-items: center;
    }
    .tooltip-wrapper button {
      width: 100%;
      max-width: 300px;
    }
    .candidature-card-footer {
      margin-top: 20px;
      width: 100%;
      max-width: 300px;
      flex-direction: column-reverse;
      justify-content: space-between;
      flex-wrap: wrap;
    }
    .candidature-card-footer button {
      flex: 1;
      text-align: center;
    }
    .candidature-logo {
      width: 100px;
      /* aspect-ratio: auto; */
    }
    .candidature-card-description {
      width: 100%;
      text-align: center;
    }
  }
</style>
