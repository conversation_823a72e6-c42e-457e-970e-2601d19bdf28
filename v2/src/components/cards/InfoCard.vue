<template>
  <article class="card-container">
    <h2>{{ title }}</h2>
    <p v-html="textContent"></p>
    <div
      class="card-container-button"
      v-if="btnContent"
      @click="href ? this.$router.push(href) : ''"
    >
      <PrimaryRoundedButton :textContent="btnContent" btnColor="secondary" />
    </div>
  </article>
</template>

<script>
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  export default {
    name: 'InfoCard',
    components: {
      PrimaryRoundedButton,
    },
    props: {
      title: {
        type: String,
        default: 'default title',
      },
      textContent: {
        type: String,
        default: 'default text',
      },
      btnContent: {
        type: String,
        default: null,
      },
      href: {
        type: String,
        default: null,
      },
    },
  };
</script>

<style scoped>
  .card-container {
    padding-block: 10px;
    padding-inline: 20px;
    display: flex;
    flex-direction: column;
    align-items: baseline;
    justify-content: center;
    background-color: var(--home-cards-bg-color-2);
    max-width: 52rem;
    min-width: 26rem;
    height: 100%;
    border-radius: 30px;
  }

  .card-container h2 {
    margin-bottom: 10px;
  }
  /* @media screen and (max-width: 768px) {
    .card-container {
      min-width: 20rem;
    }

    .card-container h2 {
      text-align: center;
    }
  }

  @media screen and (min-width: 992px) {
    .card-container {
      padding: 30px;
      background-color: var(--home-cards-bg-color);
    }
    .card-container h2 {
      margin-bottom: 40px;
    }
  }

  .card-container-button {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 30px;
  } */
</style>
