<template>
  <div class="bubble-selector">
    <p class="bubble-title"><i>Par où commencer ... ?</i></p>
    <div class="bubble-container">
      <button
        v-for="(bubble, index) in bubbles"
        :key="index"
        class="bubble"
        @click="handleBubbleClick(bubble)"
      >
        {{ bubble }}
      </button>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'BubbleSelector',
    props: {
      bubbles: {
        type: Array,
        required: true,
      },
    },
    methods: {
      handleBubbleClick(bubble) {
        this.$emit('bubble-clicked', bubble); // Émet l'événement avec le message
      },
    },
  };
</script>

<style scoped>
  .bubble-selector {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: absolute;
    top: -6rem;
    width: 100%;
    text-align: center;
  }

  .bubble-title {
    margin-top: 15px;
    margin-bottom: 10px;
    color: var(--text-3);
  }

  .bubble-container {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    justify-content: center;
  }

  .bubble {
    padding: 5px 30px;
    border-radius: 20px;
    background-color: #fff;
    color: var(--text-1);
    cursor: pointer;
    border: 2px solid var(--primary-1);
    outline: none;
    transition: transform 0.2s ease;
  }

  .bubble:hover {
    transform: scale(1.04);
  }

  @media (max-width: 768px) {
    .bubble-selector {
      position: relative;
      top: 0;
    }

    .bubble-container {
      flex-direction: column;
      gap: 5px;
    }

    .bubble {
      width: 100%;
    }
  }
</style>
