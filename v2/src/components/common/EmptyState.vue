<!-- composant qui affiche un message d'état vide -->
<template>
    <div class="empty-state">
        <p>{{ message }}</p>
    </div>
</template>

<script>
    export default {
        props: {
            message: {
                type: String,
                default: "Aucune donnée disponible.",
            },
        },
    };
</script>

<style scoped>
    .empty-state {
        text-align: center;
        margin: 20px 0;
        font-size: 1.2rem;
        color: var(--text-1);
    }
</style>