<template>
  <div class="custom-select-container">
    <input
      type="text"
      :value="modelValue"
      @input="$emit('update:modelValue', $event.target.value)"
      @keyup.enter="$emit('enter')"
      :placeholder="placeholder"
      :class="inputClass"
      @click="showDropdown = false"
    /><!--
        <div class="select-trigger" @click="toggleDropdown">
            <span class="arrow">▼</span>
        </div>
        <div v-if="showDropdown" class="options-list">
            <div
                v-for="option in options"
                :key="option"
                class="option"
                @click="selectOption(option)"
            >
                {{ option }}
            </div>
        </div>-->
  </div>
</template>

<script>
  export default {
    name: 'CustomSelect',
    props: {
      modelValue: {
        type: String,
        required: true,
      },
      options: {
        type: Array,
        required: true,
      },
      placeholder: {
        type: String,
        default: '',
      },
      inputClass: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        showDropdown: false,
      };
    },
    methods: {
      toggleDropdown() {
        this.showDropdown = !this.showDropdown;
      },
      selectOption(option) {
        this.$emit('update:modelValue', option);
        this.showDropdown = false;
      },
    },
    mounted() {
      document.addEventListener('click', (e) => {
        if (!this.$el.contains(e.target)) {
          this.showDropdown = false;
        }
      });
    },
  };
</script>

<style scoped>
  .custom-select-container {
    position: relative;
    width: 100%;
  }

  .custom-select-container input {
    width: 100%;
    padding-right: 30px;
  }

  .select-trigger {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    z-index: 1;
  }

  .arrow {
    color: white;
    font-size: 12px;
  }

  .options-list {
    position: absolute;
    top: auto;
    top: 100%;
    left: 0;
    right: 0;
    background-color: #121212;
    border: 1px solid var(--surface-bg-4);
    border-radius: 5px;
    margin-top: 5px;
    height: 60px;
    overflow-y: auto;
    z-index: 1000;
  }

  .option {
    padding: 8px 12px;
    cursor: pointer;
    color: white;
  }

  .option:hover {
    background: #2a2a2a;
  }
</style>
