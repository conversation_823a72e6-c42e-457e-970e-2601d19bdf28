<template>
  <div v-if="show" class="modal-overlay">
    <div class="modal-container">
      <div class="modal-content">
        <button class="close-btn" @click="$emit('update:show', false)">&#10006;</button>
        
        <div class="modal-header">
          <div class="icon-container">
            <img src="@/assets/icons/camera.svg" alt="camera icon" class="camera-icon"/>
          </div>
          <h2>Rejoindre l'entretien</h2>
        </div>

        <div class="modal-body">
          <p>
            Êtes-vous prêt à rejoindre l'entretien vidéo ? 
            Assurez-vous d'être dans un environnement calme avec une bonne connexion internet.
          </p>
        </div>

        <div class="modal-actions">
          <button 
            class="btn-secondary border-radius-10"
            @click="$emit('cancel')"
          >
            Annuler
          </button>
          <button 
            class="btn-primary border-radius-10"
            @click="$emit('confirm')"
          >
            Rejoindre maintenant
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ConfirmationCallModal',
  props: {
    show: {
      type: Boolean,
      required: true
    }
  },
  emits: ['update:show', 'confirm', 'cancel']
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(38, 40, 43, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  width: 100%;
  max-width: 480px;
  margin: 20px;
}

.modal-content {
  background-color: var(--surface-bg-2);
  border-radius: 15px;
  padding: 30px;
  position: relative;
}

.close-btn {
  position: absolute;
  right: 20px;
  top: 20px;
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: var(--text-1);
}

.modal-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
}

.icon-container {
  background-color: var(--yellow-100);
  width: 64px;
  height: 64px;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
}

.camera-icon {
  width: 32px;
  height: 32px;
}

.modal-header h2 {
  font-size: 36px;
  font-weight: bold;
  color: var(--text-1);
  text-align: center;
  margin: 0;
}

.modal-body {
  margin-bottom: 32px;
}

.modal-body p {
  font-size: 16px;
  color: var(--text-3);
  text-align: center;
  line-height: 1.4;
}

.modal-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.btn-primary, .btn-secondary {
  font-size: 14px;
  padding: 12px 24px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
  border: none;
}

.btn-primary {
  background-color: var(--yellow-100);
  color: var(--text-1);
}

.btn-primary:hover {
  background-color: var(--yellow-80);
}

.btn-secondary {
  background-color: var(--surface-bg);
  color: var(--text-1);
  border: 1px solid var(--gray-light);
}

.btn-secondary:hover {
  background-color: var(--surface-bg-3);
}

@media (max-width: 480px) {
  .modal-container {
    margin: 16px;
  }

  .modal-content {
    padding: 20px;
  }

  .modal-header h2 {
    font-size: 28px;
  }

  .modal-actions {
    flex-direction: column;
  }

  .btn-primary, .btn-secondary {
    width: 100%;
  }
}
</style>




