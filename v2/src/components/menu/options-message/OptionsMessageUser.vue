<template>
  <!-- user messages -->
  <div class="option-message-menu border-radius-2">
    <img :src="deleteMessageToggle ? TrashWhiteBg : TrashBlackBg" @click="handleDeleteUserMessage" />
    <img :src="copyToggle ? CheckWhiteBg : CopyBlackBg" @click="handleCopyMessage" />
  </div>
</template>

<script>
import TrashBlackBg from '@/assets/icons/trash-black-bg.svg';
import TrashWhiteBg from '@/assets/icons/trash-white-bg.svg';

import CopyBlackBg from '@/assets/icons/copy-black-bg.svg';
import CheckWhiteBg from '@/assets/icons/check-white-bg.svg';


export default {
  name: 'OptionsMessageMenu',

  props: {
    isIa: {
      type: Boolean,
      required: true,
      default: false,
    },
  },
  data() {
    return {
      TrashBlackBg,
      TrashWhiteBg,
      CopyBlackBg,
      CheckWhiteBg,

      copyToggle: false,
      deleteMessageToggle: false,
    };
  },

  methods: {
    handleCopyMessage() {
      this.$emit('copy-message');
      this.copyToggle = 1;
      // reset copyToggle after 3 seconds
      if(this.copyToggle = 1) {
        setTimeout(() => {
          this.copyToggle = 0;
        }, 3000);
      }
     
    },
    handleDeleteUserMessage() {
      this.$emit('delete-message');
      this.deleteMessageToggle ^= 1;
    },
  },
}
</script>

<style scoped>
.option-message-menu {
  height: 32px;
  width: fit-content;
  padding: 4px;
  gap: 8px;
  border: 1px solid var(--surface-bg-3);
  background-color: var(--text-2);
  display: flex;
  align-items: center;
  position: absolute;
  z-index: 111;
  top: -22px;
  left: 10%;
}

.option-message-menu img {
  height: 24px;
  width: 24px;
  background-color: var(--surface-bg-3);
  cursor: pointer;
  border-radius: 2px;
}

.option-message-menu img:hover {
  background-color: var(--primary-1b2);
}
</style>