<template>
  <!-- ia message -->
  <div v-if="isIa" class="option-message-menu border-radius-2">
    <img :src="thumbsUpToggle ? ThumbsUpWhiteImg : ThumbsUpBlackImg" @click="handleThumbsUp" />
    <img :src="thumbsDownToggle ? ThumbsDownWhiteImg : ThumbsDownBlackImg" @click="handleThumbsDown" />
    <img :src="refreshToggle ? RefreshWhiteImg : RefreshBlackImg" @click="handleRefreshIaMessage" />
    <img :src="copyToggle ? CheckWhiteBg : CopyBlackBg" @click="handleCopyMessage" />
    <img :src="signalIaMessageToggle ? FlagWhiteBg : FlagBlackBg" @click="handlesignalMessage" />
  </div>
</template>

<script>
import ThumbsUpBlackImg from '@/assets/icons/thumbs-up-black-bg.svg';
import ThumbsUpWhiteImg from '@/assets/icons/thumbs-up-white-bg.svg';

import ThumbsDownBlackImg from '@/assets/icons/thumbs-down-black-bg.svg';
import ThumbsDownWhiteImg from '@/assets/icons/thumbs-down-white-bg.svg';

import RefreshBlackImg from '@/assets/icons/refresh-black-bg.svg';
import RefreshWhiteImg from '@/assets/icons/refresh-white-bg.svg';

import TrashBlackBg from '@/assets/icons/trash-black-bg.svg';
import TrashWhiteBg from '@/assets/icons/trash-white-bg.svg';

import CopyBlackBg from '@/assets/icons/copy-black-bg.svg';
import CheckBlackBg from '@/assets/icons/check-black-bg.svg';
import CheckWhiteBg from '@/assets/icons/check-white-bg.svg';

import FlagBlackBg from '@/assets/icons/flag-black-bg.svg';
import FlagWhiteBg from '@/assets/icons/flag-white-bg.svg';

export default {
  name: 'OptionsMessageIA',

  props: {
    isIa: {
      type: Boolean,
      required: true,
      default: false,
    },
    
  },
  data() {
    return {
      ThumbsUpBlackImg,
      ThumbsUpWhiteImg,
      ThumbsDownBlackImg,
      ThumbsDownWhiteImg,
      RefreshBlackImg,
      RefreshWhiteImg,
      TrashBlackBg,
      TrashWhiteBg,
      CopyBlackBg,
      CheckBlackBg,
      CheckWhiteBg,
      FlagBlackBg,
      FlagWhiteBg,
      // for ia
      thumbsUpToggle: false,
      thumbsDownToggle: false,
      refreshToggle: false,
      copyToggle: false,
      signalIaMessageToggle: false,
    };
  },

  methods: {
    handleThumbsUp() {
      this.$emit('thumbs-up');
      this.thumbsUpToggle ^= 1;
    },
    handleThumbsDown() {
      this.$emit('thumbs-down');
      this.thumbsDownToggle ^= 1;
    },
    handleRefreshIaMessage() {
      this.$emit('refresh');
      this.refreshToggle ^= 1;
    },
    handleCopyMessage() {
      this.$emit('copy-message');
      this.copyToggle = 1;
      // reset copyToggle after 3 seconds
      if(this.copyToggle = 1) {
        setTimeout(() => {
          this.copyToggle = 0;
        }, 3000);
      }
    },
    handlesignalMessage() {
      this.$emit('signal-post');
      this.signalIaToggle ^= 1;
    },
  },
}
</script>

<style scoped>
.option-message-menu {
  height: 32px;
  width: fit-content;
  padding: 4px;
  gap: 8px;
  border: 1px solid var(--surface-bg-3);
  background-color: var(--text-2);
  display: flex;
  align-items: center;
  position: absolute;
  z-index: 111;
  top: -22px;
  left: 60%;
}

.option-message-menu img {
  height: 24px;
  width: 24px;
  background-color: var(--surface-bg-3);
  cursor: pointer;
  border-radius: 2px;
}

.option-message-menu img:hover {
  background-color: var(--primary-1b2);
}
</style>