<template>
  <div class="option-panel">
    <div class="close-btn">
      <v-btn icon="mdi-close" @click="toggleOptionPanel" flat></v-btn>
    </div>
    <div class="options-btns-wrapper">
      <v-btn v-if="postedByUser" prepend-icon="mdi-trash-can-outline" @click="deletePost" flat>Supprimer</v-btn>
      <v-btn v-else @click="hidePost" flat>Ne plus voir</v-btn>
      <v-btn @click="signalPost" prepend-icon="mdi-flag-outline" flat>Signaler</v-btn>
    </div>
  </div>

</template>

<script>
export default {
name: 'OptionsConversationMenu',
  props: {
    postedByUser: {
      type: [Boolean, String],
        required: false,
        default: 'none',
      },
  },
  methods: {
    deletePost() {
      this.$emit('delete-post');
      this.$emit('toggle-option-panel');

    },
    signalPost() {
      this.$emit('signal-post');
      this.$emit('toggle-option-panel');
    },
    toggleOptionPanel() {
      this.$emit('toggle-option-panel');
    },
  },
}
</script>

<style scoped>
.option-panel {
  width: 165px;
  padding-left: 10px;
  padding-bottom: 10px;
  height: fit-content;
  background-color: var(--surface-bg-2);
  border-radius: 5px;
  box-shadow: 0px 0px 10px 0px rgba(38, 40, 43, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;

}

.options-btns-wrapper {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: baseline;
}

.close-btn {
  display: flex;
  width: 100%;
  justify-content: end;
}
</style>