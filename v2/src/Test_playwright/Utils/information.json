[{"NameFile": "<PERSON><PERSON>-social.test", "TimeOfEnd": "0:00:12.277", "total_page": 3, "total_error": 0, "total_success": 1, "paths": "C:\\Users\\<USER>\\Desktop\\frontht\\front_tb_vue\\v2\\src\\Test_playwright\\Test\\screenshots\\Reseau-social.test\\LoginReseaux-social.test.png", "info": ["Goto /connexion", "Fill email", "Fill password", "Click login", "Wait 3s", "<PERSON><PERSON> /reseau-social", "Wait 2s", "Click textarea", "Fill message", "Click send post", "Refresh page", "Wait 3s", "Wait for message", "Message sent and visible", "Screenshot", "Click post options", "Wait for options menu", "<PERSON><PERSON>", "Wait for post to be deleted"], "error": []}, {"NameFile": "RegisterPage.test", "TimeOfEnd": "error", "total_page": 0, "total_error": 1, "total_success": 0, "paths": "D:\\thanksBoss_IA\\front_tb_vue\\v2\\src\\Test_playwright\\Test\\screenshots\\RegisterPage.test\\registerPage-error.test.png", "info": ["Goto /inscription", "Fill email", "❌ Erreur: page.fill: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[placeholder=\"Votre email\"]')\u001b[22m\n"], "error": ["page.fill: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[placeholder=\"Votre email\"]')\u001b[22m\n"]}]