const fs = require('fs'), path = require('path');


async function addInformation(NameFile = "Inconnu", TimeOfEnd = "0:00:000", total_page = 0, total_error = 0, total_success = 0, paths, info = ["Aucune"], error) {
  const filePath = path.join(__dirname, 'information.json');
  const data = { NameFile, TimeOfEnd, total_page, total_error, total_success, paths, info };
  if (error !== undefined) {
    data.error = error;
  }

  try {
    const existing = fs.existsSync(filePath) ? JSON.parse(fs.readFileSync(filePath, 'utf8')) : [];
    existing.push(data);
    fs.writeFileSync(filePath, JSON.stringify(existing, null, 2));
    console.log(`✅ Info saved:`, data);
  } catch (err) {
    console.error(`❌ Write error: ${err.message}`);
  }
}


module.exports = { addInformation };