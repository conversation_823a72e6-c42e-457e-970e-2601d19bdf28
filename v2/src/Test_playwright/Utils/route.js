

// const url = "http://localhost:8080" // Local Front
const url = "https://frontpreprod.thanks-boss.com" // Marseille Front
// const url = "https://www.thanks-boss.com" // Paris Front

const routeUrl = [
  '/',
  '/candidate/interview',
  '/create-interview',
  '/interviews',
  '/candidat',
  '/recruteur',
  '/recherche',
  '/chat-ia',
  '/inscription',
  '/connexion',
  '/reseau-social',
  '/tarifs',
  '/actualites',
  '/contact',
  '/faq',
  '/favoris',
  '/alertes',
  '/tableau-de-bord',
  '/candidatures',
  '/auto-candidature',
  '/cgu',
  '/politique-confidentialite',
  '/cgv',
  '/mentions-legales',
  '/profil',
  '/aperçu-profil',
  '/parametres',
  '/profil/edition',
  '/recruteur/profil',
  '/a-propos',
  '/achat',
  '/paiement-valide',
  '/paiement-refuse',
  '/reinitialisation-mot-de-passe',
  '/reinitialisation-mot-de-passe-confirme',
  '/recruteur/tableau-de-bord',
  '/recruteur/inscription',
  '/recruteur/aperçu-profil',
  '/recruteur/offres',
  '/communaute',
  '/recruteur/connexion',
  '/recruteur/favoris',
  '/recruteur/offre/redaction',
  '/recruteur/offre/preview',
  '/recruteur/messagerie',
  '/messagerie',
  '/merci',
  '/amis'
];


const routeApi = [
  { route: '/api/user/register/', method: 'POST', body: { email: "<EMAIL>", password: "Azerty3112.", password2: "Azerty3112.", type_user: "applicant", first_name: "Jean", last_name: "Dupont", metier: "Développeur", numberPhone: "0606060606", recherche_info: "Employé(e)", venu_info: "Facebook", } },
]


module.exports = { url, routeUrl, routeApi }


