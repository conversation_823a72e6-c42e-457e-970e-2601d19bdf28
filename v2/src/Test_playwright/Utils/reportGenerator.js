const fs = require('fs');
const path = require('path');

/**
 * Retourne le chemin du prochain rapport HTML disponible dans le dossier reports/
 * Format : report_YYYY-MM-DD_HH-mm-ss.html
 */
function getNextReportPath() {
  const reportsDir = path.join(__dirname, '..', 'reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }
  function getDateString() {
    const now = new Date();
    const pad = n => n.toString().padStart(2, '0');
    return `${now.getFullYear()}-${pad(now.getMonth()+1)}-${pad(now.getDate())}_${pad(now.getHours())}-${pad(now.getMinutes())}-${pad(now.getSeconds())}`;
  }
  let baseName = `report_${getDateString()}.html`;
  let reportPath = path.join(reportsDir, baseName);
  let idx = 1;
  while (fs.existsSync(reportPath)) {
    baseName = `report_${getDateString()}_${idx}.html`;
    reportPath = path.join(reportsDir, baseName);
    idx++;
  }
  return reportPath;
}

/**
 * Retourne la liste des rapports HTML existants dans le dossier reports/
 */
function getReportHistory(currentReportPath) {
  const reportsDir = path.join(__dirname, '..', 'reports');
  if (!fs.existsSync(reportsDir)) return [];
  const files = fs.readdirSync(reportsDir)
    .filter(f => f.startsWith('report_') && f.endsWith('.html'))
    .sort();
  return files.map(f => ({
    name: f,
    path: path.join(reportsDir, f),
    isCurrent: currentReportPath && path.basename(currentReportPath) === f
  }));
}

/**
 * Génère ou met à jour l'index.html dans le dossier reports/ avec la liste de tous les rapports
 */
function generateReportsIndex() {
  const reportsDir = path.join(__dirname, '..', 'reports');
  if (!fs.existsSync(reportsDir)) return;
  const files = fs.readdirSync(reportsDir)
    .filter(f => f.startsWith('report_') && f.endsWith('.html'))
    .sort();
  const html = `<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Historique des rapports Playwright</title>
  <style>
    body { font-family: 'Segoe UI', Arial, sans-serif; background: linear-gradient(135deg, #232526 0%, #414345 100%); color: #e0e0e0; max-width: 900px; margin: 0 auto; padding: 40px 20px; }
    h1 { color: #ffb347; border-bottom: 2px solid #ffb347; padding-bottom: 10px; margin-bottom: 30px; display: flex; align-items: center; gap: 10px; }
    h1::before { content: '🗂️'; font-size: 1.5em; }
    .report-list { display: flex; flex-wrap: wrap; gap: 16px; margin-top: 24px; }
    .report-link { background: #23272b; color: #ffd580; border-radius: 8px; padding: 16px 28px; text-decoration: none; font-size: 1.15em; font-weight: 500; border: 1.5px solid #444; box-shadow: 0 2px 8px rgba(255,179,71,0.08); transition: background 0.2s, color 0.2s; display: flex; align-items: center; gap: 10px; }
    .report-link:hover { background: #ffb347; color: #232526; }
    .timestamp { color: #b0b0b0; font-size: 0.98em; margin-top: 40px; text-align: right; }
  </style>
</head>
<body>
  <h1>Historique des rapports Playwright</h1>
  <div class="report-list">
    ${files.map(f => `<a class="report-link" href="${f}" target="_blank">${f}</a>`).join('')}
  </div>
  <div class="timestamp">Page générée le ${new Date().toLocaleString()}</div>
</body>
</html>`;
  fs.writeFileSync(path.join(reportsDir, 'index.html'), html);
}

/**
 * Génère un rapport HTML à partir des données dans information.json
 * @param {string} outputPath - Chemin où sauvegarder le rapport HTML
 */
function generateHtmlReport(outputPath = null) {
  const infoPath = path.join(__dirname, 'information.json');
  if (!fs.existsSync(infoPath)) {
    console.error('❌ Fichier information.json introuvable');
    return;
  }
  try {
    const testData = JSON.parse(fs.readFileSync(infoPath, 'utf8'));
    // Détermination du chemin du rapport à générer
    const finalReportPath = outputPath || getNextReportPath();
    // Récupération de l'historique des rapports
    const reportHistory = getReportHistory(finalReportPath);
    // Génération du HTML
    const html = `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Rapport de Tests Playwright</title>
      <style>
        body {
          font-family: 'Segoe UI', Arial, sans-serif;
          line-height: 1.6;
          max-width: 1200px;
          margin: 0 auto;
          padding: 20px;
          color: #e0e0e0;
          background: linear-gradient(135deg, #232526 0%, #414345 100%);
        }
        .back-index {
          display: inline-block;
          margin-bottom: 24px;
          background: #23272b;
          color: #ffd580;
          border-radius: 8px;
          padding: 12px 28px;
          text-decoration: none;
          font-size: 1.1em;
          font-weight: 500;
          border: 1.5px solid #444;
          box-shadow: 0 2px 8px rgba(255,179,71,0.08);
          transition: background 0.2s, color 0.2s;
        }
        .back-index:hover {
          background: #ffb347;
          color: #232526;
        }
        h1 {
          color: #ffb347;
          border-bottom: 2px solid #ffb347;
          padding-bottom: 10px;
          display: flex;
          align-items: center;
          gap: 10px;
        }
        h1::before {
          content: '🧪';
          font-size: 1.5em;
        }
        .summary {
          background: rgba(40, 44, 52, 0.95);
          border-radius: 10px;
          padding: 18px;
          margin-bottom: 28px;
          box-shadow: 0 4px 16px rgba(0,0,0,0.18);
          border: 1.5px solid #444;
        }
        .test-card {
          background: rgba(30, 32, 36, 0.98);
          border-radius: 10px;
          padding: 18px;
          margin-bottom: 18px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.18);
          border-left: 6px solid #ffb347;
          transition: box-shadow 0.2s;
        }
        .test-card.success {
          border-left-color: #2ecc71;
        }
        .test-card.error {
          border-left-color: #e74c3c;
        }
        .test-card:hover {
          box-shadow: 0 6px 24px rgba(255,179,71,0.12);
        }
        .stats {
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          gap: 10px;
        }
        .stat-item {
          flex: 1;
          min-width: 150px;
          padding: 12px;
          background: #23272b;
          border-radius: 6px;
          text-align: center;
          margin: 4px 0;
          color: #ffd580;
          box-shadow: 0 1px 4px rgba(0,0,0,0.10);
        }
        .stat-item h3, .stat-item h4 {
          margin: 0 0 6px 0;
          color: #ffb347;
        }
        .timestamp {
          color: #b0b0b0;
          font-size: 0.95em;
        }
        .screenshots {
          margin-top: 12px;
        }
        .screenshots a {
          color: #ffd580;
          text-decoration: underline;
          cursor: pointer;
          font-weight: bold;
          font-size: 1.1em;
        }
        .screenshots a:hover {
          color: #ffb347;
        }
        .all-screenshots {
          display: none;
          margin-top: 12px;
          gap: 16px;
          flex-wrap: wrap;
        }
        .all-screenshots.active {
          display: flex;
        }
        .screenshot-card {
          background: #23272b;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.12);
          padding: 10px;
          margin: 6px;
          display: flex;
          flex-direction: column;
          align-items: center;
          max-width: 260px;
        }
        .screenshot-card img {
          max-width: 220px;
          max-height: 140px;
          border-radius: 6px;
          margin-bottom: 6px;
          border: 1.5px solid #444;
        }
        .screenshot-card span {
          color: #ffd580;
          font-size: 0.95em;
          word-break: break-all;
        }
        .emoji {
          font-size: 1.2em;
          margin-right: 4px;
        }
        .stat-item .emoji {
          font-size: 1.1em;
        }
        .test-card h3 {
          display: flex;
          align-items: center;
          gap: 8px;
        }
        .screenshots-btn {
          display: inline-block;
          background: linear-gradient(90deg, #ffb347 0%, #ffcc80 100%);
          color: #232526;
          font-weight: bold;
          border: none;
          border-radius: 6px;
          padding: 8px 18px;
          font-size: 1.1em;
          cursor: pointer;
          margin-top: 10px;
          margin-bottom: 6px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.10);
          transition: background 0.2s, color 0.2s;
        }
        .screenshots-btn:hover {
          background: linear-gradient(90deg, #ffd580 0%, #ffb347 100%);
          color: #232526;
        }
        .modal {
          display: none;
          position: fixed;
          z-index: 9999;
          left: 0;
          top: 0;
          width: 100vw;
          height: 100vh;
          background: rgba(0,0,0,0.85);
          justify-content: center;
          align-items: center;
        }
        .modal.active {
          display: flex;
        }
        .modal img {
          max-width: 90vw;
          max-height: 90vh;
          border-radius: 12px;
          box-shadow: 0 4px 32px rgba(0,0,0,0.5);
        }
        .modal-close {
          position: absolute;
          top: 30px;
          right: 40px;
          font-size: 2.5em;
          color: #ffd580;
          cursor: pointer;
          z-index: 10001;
          transition: color 0.2s;
        }
        .modal-close:hover {
          color: #ffb347;
        }
        .stat-item.clickable {
          cursor: pointer;
          background: linear-gradient(90deg, #444 0%, #666 100%);
          color: #ffd580;
          box-shadow: 0 2px 8px rgba(255,179,71,0.10);
          border: 1.5px solid #555;
          transition: background 0.2s, color 0.2s;
          opacity: 0.85;
        }
        .stat-item.clickable:hover {
          background: linear-gradient(90deg, #555 0%, #888 100%);
          color: #fff;
        }
        .stat-item.clickable.disabled,
        .stat-item.clickable.disabled:hover {
          cursor: default;
          background: #23272b;
          color: #555;
          border: 1.5px solid #23272b;
          opacity: 0.5;
        }
        .details-list-container {
          display: block;
          width: 100%;
          background: linear-gradient(90deg, #23272b 60%, #232526 100%);
          border-radius: 10px;
          box-shadow: 0 2px 12px rgba(46,204,113,0.10);
          border: 2px solid #2ecc71;
          margin-top: 12px;
          padding: 18px 24px;
          color: #e0ffe0;
          font-size: 1.13em;
          max-height: 350px;
          overflow-y: auto;
          transition: box-shadow 0.2s;
        }
        .details-list-container.error {
          border: 2px solid #e74c3c;
          background: linear-gradient(90deg, #2c1e1e 60%, #232526 100%);
          color: #ffd6d6;
        }
        .details-list {
          margin: 0;
          padding-left: 0;
          list-style: none;
        }
        .details-list li {
          padding: 10px 0 10px 0;
          border-bottom: 1px solid #333;
          font-size: 1.08em;
          display: flex;
          align-items: center;
          gap: 12px;
          word-break: break-word;
        }
        .details-list li:last-child {
          border-bottom: none;
        }
        .success-detail {
          color: #2ecc71;
          font-weight: bold;
        }
        .error-detail {
          color: #e74c3c;
          font-weight: bold;
        }
        .details-panel {
          display: none;
          position: fixed;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          background: rgba(0,0,0,0.85);
          z-index: 10000;
          justify-content: center;
          align-items: center;
        }
        .details-panel.active {
          display: flex;
        }
        .details-panel-content {
          background: linear-gradient(90deg, #23272b 60%, #232526 100%);
          border-radius: 16px;
          box-shadow: 0 4px 32px rgba(46,204,113,0.18);
          border: 2px solid #2ecc71;
          padding: 32px 40px;
          color: #e0ffe0;
          font-size: 1.18em;
          max-width: 700px;
          max-height: 80vh;
          overflow-y: auto;
          position: relative;
          animation: fadeInPanel 0.2s;
        }
        .details-panel-content.error {
          border: 2px solid #e74c3c;
          background: linear-gradient(90deg, #2c1e1e 60%, #232526 100%);
          color: #ffd6d6;
        }
        .details-panel-close {
          position: absolute;
          top: 18px;
          right: 28px;
          font-size: 2.2em;
          color: #ffd580;
          cursor: pointer;
          z-index: 10001;
          transition: color 0.2s;
        }
        .details-panel-close:hover {
          color: #ffb347;
        }
        @keyframes fadeInPanel {
          from { opacity: 0; transform: scale(0.98); }
          to { opacity: 1; transform: scale(1); }
        }
        .report-history {
          background: #23272b;
          border-radius: 8px;
          padding: 14px 18px;
          margin-bottom: 24px;
          box-shadow: 0 2px 8px rgba(255,179,71,0.08);
          border: 1.5px solid #444;
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          align-items: center;
        }
        .report-history-title {
          color: #ffd580;
          font-weight: bold;
          margin-right: 12px;
        }
        .report-history-link {
          color: #ffb347;
          background: #232526;
          border-radius: 5px;
          padding: 6px 14px;
          text-decoration: none;
          font-weight: 500;
          margin-right: 6px;
          border: 1px solid #444;
          transition: background 0.2s, color 0.2s;
        }
        .report-history-link:hover {
          background: #ffb347;
          color: #232526;
        }
        .report-history-link.active {
          background: #ffd580;
          color: #232526;
          font-weight: bold;
          border: 2px solid #ffb347;
          cursor: default;
        }
      </style>
    </head>
    <body>
      <a class="back-index" href="index.html">← Retour à l'index des rapports</a>
      <h1>Rapport de Tests Playwright</h1>
      <div class="summary">
        <h2>Résumé</h2>
        <div class="stats">
          <div class="stat-item"><span class="emoji">🧪</span><h3>Tests exécutés</h3><p>${testData.length}</p></div>
          <div class="stat-item"><span class="emoji">📄</span><h3>Pages testées</h3><p>${testData.reduce((sum, test) => sum + test.total_page, 0)}</p></div>
          <div class="stat-item"><span class="emoji">✅</span><h3>Succès</h3><p>${testData.reduce((sum, test) => sum + test.total_success, 0)}</p></div>
          <div class="stat-item"><span class="emoji">❌</span><h3>Erreurs</h3><p>${testData.reduce((sum, test) => sum + test.total_error, 0)}</p></div>
        </div>
      </div>
      <h2>Détails des tests</h2>
      ${testData.map((test, idx) => {
        // Récupérer tous les screenshots du dossier si possible
        let screenshots = [];
        try {
          const baseDir = test.paths ? test.paths.replace(/\\/g, '/').replace(/\/[^/]*$/, '') : '';
          if (baseDir && fs.existsSync(baseDir)) {
            screenshots = fs.readdirSync(baseDir)
              .filter(f => f.match(/\.(png|jpg|jpeg|webp)$/i))
              .map(f => baseDir + '/' + f);
          } else if (test.paths && test.paths.match(/\.(png|jpg|jpeg|webp)$/i)) {
            screenshots = [test.paths];
          }
        } catch (e) { screenshots = test.paths ? [test.paths] : []; }
        return `
        <div class="test-card ${test.total_error === 0 ? 'success' : 'error'}">
          <h3>${test.total_error === 0 ? '✅' : '❌'} ${test.NameFile}</h3>
          <div class="stats">
            <div class="stat-item"><span class="emoji">📄</span><h4>Pages</h4><p>${test.total_page}</p></div>
            <div class="stat-item clickable${test.total_success === 0 ? ' disabled' : ''}" onclick="${test.total_success === 0 ? '' : `showPanel('success-panel-${idx}')`}"><span class="emoji">✅</span><h4>Succès</h4><p>${test.total_success}</p></div>
            <div class="stat-item clickable${test.total_error === 0 ? ' disabled' : ''}" onclick="${test.total_error === 0 ? '' : `showPanel('error-panel-${idx}')`}"><span class="emoji">❌</span><h4>Erreurs</h4><p>${test.total_error}</p></div>
            ${test.info && test.info.length ? `<div id="success-panel-${idx}" class="details-panel"><div class="details-panel-content"><span class="details-panel-close" onclick="closePanel('success-panel-${idx}')">&times;</span><h3 style='margin-top:0;'>✅ Détails des succès</h3><ul class='details-list'>${test.info.map(s => `<li class='success-detail'>✅ ${s}</li>`).join('')}</ul></div></div>` : ''}
            ${test.total_error > 0 && test.error && test.error.length ? `<div id="error-panel-${idx}" class="details-panel"><div class="details-panel-content error"><span class="details-panel-close" onclick="closePanel('error-panel-${idx}')">&times;</span><h3 style='margin-top:0;'>❌ Détails des erreurs</h3><ul class='details-list'>${test.error.map(e => `<li class='error-detail'>❌ ${e}</li>`).join('')}</ul></div></div>` : ''}
            <div class="stat-item"><span class="emoji">⏱️</span><h4>Durée</h4><p>${test.TimeOfEnd}</p></div>
          </div>
          <div class="screenshots">
            <button class="screenshots-btn" onclick="toggleScreenshots('screenshots-${idx}')">🖼️ Captures d'écran (${screenshots.length})</button>
            <div class="all-screenshots" id="screenshots-${idx}">
              ${screenshots.map((s, i) => `
                <div class='screenshot-card'>
                  <img src="file:///${s.replace(/\\/g, '/')}" alt="screenshot" onclick="showModal('modal-img-${idx}-${i}')" style="cursor:pointer;" />
                  <span>${s.split('/').pop()}</span>
                  <div id="modal-img-${idx}-${i}" class="modal" onclick="closeModal(event, 'modal-img-${idx}-${i}')">
                    <span class="modal-close" onclick="closeModal(event, 'modal-img-${idx}-${i}')">&times;</span>
                    <img src="file:///${s.replace(/\\/g, '/')}" alt="screenshot-large" />
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
        </div>
        `;
      }).join('')}
      
      <footer>
        <p class="timestamp">Rapport généré le ${new Date().toLocaleString()}</p>
      </footer>
    </body>
    <script>
    function toggleDetails(id) {
      var el = document.getElementById(id);
      if (el) el.style.display = el.style.display === 'none' ? 'block' : 'none';
    }
    function toggleScreenshots(id) {
      var el = document.getElementById(id);
      if (el) el.classList.toggle('active');
    }
    function showModal(modalId) {
      var modal = document.getElementById(modalId);
      if (modal) modal.classList.add('active');
    }
    function closeModal(event, modalId) {
      event.stopPropagation();
      var modal = document.getElementById(modalId);
      if (modal) modal.classList.remove('active');
    }
    function showPanel(id) {
      var panel = document.getElementById(id);
      if(panel) panel.classList.add('active');
    }
    function closePanel(id) {
      var panel = document.getElementById(id);
      if(panel) panel.classList.remove('active');
    }
    // Fermer le modal si on clique en dehors de l'image
    document.addEventListener('click', function(e) {
      if (e.target.classList && e.target.classList.contains('modal')) {
        e.target.classList.remove('active');
      }
    });
    </script>
    </html>
    `;

    const reportPath = getNextReportPath();

    fs.writeFileSync(reportPath, html);
    console.log(`✅ Rapport HTML généré avec succès: ${reportPath}`);
    generateReportsIndex(); 
    return reportPath;
  } catch (err) {
    console.error(`❌ Erreur lors de la génération du rapport: ${err.message}`);
    return null;
  }
}
// generateHtmlReport()

module.exports = { generateHtmlReport };