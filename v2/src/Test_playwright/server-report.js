const express = require('express');
const basicAuth = require('express-basic-auth');
const path = require('path');

const app = express();

// Définissez ici les utilisateurs autorisés
app.use(basicAuth({
  users: { 'team': 'motdepasse' }, // changez le mot de passe !
  challenge: true,
  realm: 'Playwright Report'
}));

// Chemin vers le dossier du rapport généré par Playwright
const reportPath = path.join(__dirname, 'playwright-report'); // adaptez si besoin

app.use(express.static(reportPath));

const PORT = 3001;
app.listen(PORT, () => {
  console.log(`Rapport Playwright protégé par mot de passe sur http://localhost:${PORT}`);
  console.log('✅ Rapport généré dans le dossier reports/');
});