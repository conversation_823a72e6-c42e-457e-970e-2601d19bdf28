const fs = require('fs');
const path = require('path');
const { generateHtmlReport } = require('./Utils/reportGenerator');
const { exec } = require('child_process');

const deleteFolderRecursive = (dirPath) => {
  if (fs.existsSync(dirPath)) {
    fs.readdirSync(dirPath).forEach((file) => {
      const curPath = path.join(dirPath, file);
      if (fs.statSync(curPath).isDirectory()) {
        deleteFolderRecursive(curPath);
      } else {
        fs.unlinkSync(curPath);
      }
    });
    fs.rmdirSync(dirPath);
  }
};

const screenshotDir = path.join(__dirname, 'Test', 'screenshots');

console.log(`🗑️ Suppression du dossier de captures d'écran: ${screenshotDir}`);

try {
  if (fs.existsSync(screenshotDir)) {
    deleteFolderRecursive(screenshotDir);
    console.log('✅ Dossier supprimé avec succès.');
  } else {
    console.log('ℹ️ Aucun dossier à supprimer.');
  }
} catch (err) {
  console.error('❌ Erreur lors de la suppression du dossier:', err);
}

const testDir = path.join(__dirname, 'Test');
const reportDir = path.join(__dirname, 'reports');

if (!fs.existsSync(reportDir)) {
  fs.mkdirSync(reportDir, { recursive: true });
}

console.log('🚀 Démarrage des tests Playwright...');

function openBrowser(url) {
  const platform = process.platform;
  let command;

  if (platform === 'win32') {
    command = `start "" "${url}"`;
  } else if (platform === 'darwin') {
    command = `open "${url}"`;
  } else {
    command = `xdg-open "${url}"`;
  }

  exec(command, (error) => {
    if (error) {
      console.error(`❌ Impossible d'ouvrir le rapport automatiquement: ${error.message}`);
      console.log(`📋 Veuillez ouvrir manuellement le rapport à l'adresse: ${url}`);
    } else {
      console.log(`✅ Rapport ouvert automatiquement dans votre navigateur`);
    }
  });
}

async function main() {
  const infoPath = path.join(__dirname, 'Utils', 'information.json');
  if (fs.existsSync(infoPath)) { fs.unlinkSync(infoPath); console.log('✅ Fichier information.json supprimé'); }


  try {
    const testFiles = fs.readdirSync(testDir).filter(file =>
      fs.statSync(path.join(testDir, file)).isFile() && file.endsWith('.js')
    );

    let completedTests = 0;
    const testPromises = [];

    for (const file of testFiles) {
      const filePath = path.join(testDir, file);
      console.log(`▶️  Exécution du test: ${file}`);

      try {
        const testPromise = require(filePath);
        testPromises.push(testPromise);

        testPromise
          .then(() => { completedTests++; })
          .catch(error => {
            console.error(`❌ Erreur lors de l'exécution de ${file}:`, error);
          });
      } catch (error) {
        console.error(`❌ Erreur lors du chargement de ${file}:`, error);
      }
    }

    await Promise.allSettled(testPromises);

    console.log(`\n✅ ${completedTests}/${testFiles.length} tests terminés`);

    const reportPath = generateHtmlReport(path.join(reportDir, 'test-report.html'));

    if (reportPath) {
      console.log(`📊 Rapport disponible à: ${reportPath}`);
      try {
        openBrowser(reportPath);
      } catch (error) {
        console.error(`❌ Impossible d'ouvrir le rapport automatiquement: ${error.message}`);
        console.log(`📋 Veuillez ouvrir manuellement le rapport à l'adresse: ${reportPath}`);
      }
    }
  } catch (error) {
    console.error('❌ Erreur lors de l\'exécution des tests:', error);
  }
}

main();
