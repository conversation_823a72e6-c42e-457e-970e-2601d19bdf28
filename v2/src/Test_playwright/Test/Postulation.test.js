const { url, routeUrl } = require('../Utils/route');
const { addInformation } = require('../Utils/startfunc');
const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');
let total_page = 0;
let total_error = 0;
let total_success = 0;

const screenshotDir = path.join(__dirname, 'screenshots');
if (!fs.existsSync(screenshotDir)) {
  fs.mkdirSync(screenshotDir);
}

const scriptName = path.basename(__filename, path.extname(__filename));
const scriptScreenshotDir = path.join(screenshotDir, scriptName);
fs.mkdirSync(scriptScreenshotDir, { recursive: true });

// Système de log d'actions inspiré de Reseau-social.test.js
const actionsLogPath = path.join(scriptScreenshotDir, 'actions.log');
let actionsList = [];
function logAction(action) {
  const logEntry = `${action}`;
  actionsList.push(logEntry);
  fs.appendFileSync(actionsLogPath, logEntry + '\n');
}

async function logAnd(action, fn) {
  logAction(action);
  console.log(action);
  return await fn();
}

module.exports = (async () => {
  const startTime = Date.now();
  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext({ viewport: { width: 1920, height: 1080 } });
  const page = await context.newPage();

  let duration = '';
  let filePath = '';
  let errors = [];

  try {
    await logAnd('Goto /connexion', () => page.goto(`${url}/connexion`, { waitUntil: 'domcontentloaded', timeout: 15000 }));
    total_page++;
    await logAnd('Fill email', () => page.fill('input[placeholder="Votre email"]', '<EMAIL>'));
    await logAnd('Fill password', () => page.fill('input[placeholder="*********"]', 'Azerty3112.'));
    await logAnd('Click login', () => page.click('button.v-btn:has(span.v-btn__content:has-text("Je me connecte"))'));
    filePath = path.join(scriptScreenshotDir, `Postulation.test.png`);
    await logAnd('Wait 3s', () => page.waitForTimeout(3000));
    await logAnd('Screenshot', () => page.screenshot({ path: filePath, fullPage: true }));
    logAction(`✅ Screenshot saved: ${filePath}`);
    total_page++;
    total_success++;

    await page.goto(`${url}/recherche`, { waitUntil: 'domcontentloaded', timeout: 15000 });
    total_page++;

    await page.waitForTimeout(2000);

    await page.waitForSelector('.job-offer-grid .job-offer-wrapper');

    const offers = await page.$$('.job-offer-grid .job-offer-wrapper');

    if (offers.length === 0) {
      errors.push('Aucune offre trouvée.');
      throw new Error('Aucune offre trouvée.');
    }
    const randomIndex = Math.floor(Math.random() * offers.length);
    const randomOffer = offers[randomIndex];

    const offerButton = await randomOffer.$('span.v-btn__content:text("Voir l\'offre")');
    if (!offerButton) {
      errors.push('Bouton "Voir l\'offre" non trouvé dans l\'offre sélectionnée.');
      throw new Error('Bouton "Voir l\'offre" non trouvé dans l\'offre sélectionnée.');
    }

    await offerButton.click();
    console.log(`✅ Offre #${randomIndex + 1} cliquée avec succès.`);
    total_success++;








    await page.waitForTimeout(5000);




    await page.screenshot({ path: filePath, fullPage: true });
    console.log(`✅ Screenshot saved: ${filePath}`);
    total_page++;

    const durationMs = Date.now() - startTime;
    const miliseconds = Math.floor(durationMs % 1000);
    const seconds = Math.floor((durationMs / 1000) % 60);
    const minutes = Math.floor((durationMs / (1000 * 60)) % 60);
    const hours = Math.floor(durationMs / (1000 * 60 * 60));
    const duration = `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${miliseconds.toString().padStart(3, '0')}`;

    logAction(`⏱️ Durée totale: ${duration}`);
    await addInformation(scriptName, duration, total_page, total_error, total_success, filePath, actionsList, errors);

  } catch (error) {
    total_error++;
    filePath = path.join(scriptScreenshotDir, `Postulation-error.test.png`);
    await page.screenshot({ path: filePath, fullPage: true });
    errors.push(error.message);
    logAction(`❌ Erreur: ${error.message}`);
    await addInformation(scriptName, 'error', total_page, total_error, total_success, filePath, actionsList, errors);
  } finally {
    await browser.close();
  }
})();
