const { url, routeUrl } = require('../Utils/route');
const { addInformation } = require('../Utils/startfunc');
const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');
let total_page = 0;
let total_error = 0;
let total_success = 0;
let errors = [];

const screenshotDir = path.join(__dirname, 'screenshots');
if (!fs.existsSync(screenshotDir)) {
    fs.mkdirSync(screenshotDir);
}

const scriptName = path.basename(__filename, path.extname(__filename));
const scriptScreenshotDir = path.join(screenshotDir, scriptName);
fs.mkdirSync(scriptScreenshotDir, { recursive: true });

// Système de log d'actions inspiré de Reseau-social.test.js
const actionsLogPath = path.join(scriptScreenshotDir, 'actions.log');
let actionsList = [];
function logAction(action) {
    const logEntry = `${action}`;
    actionsList.push(logEntry);
    fs.appendFileSync(actionsLogPath, logEntry + '\n');
}

async function logAnd(action, fn) {
    logAction(action);
    return await fn();
}

module.exports = (async () => {
    const startTime = Date.now();
    const browser = await chromium.launch({ headless: true });
    const context = await browser.newContext({ viewport: { width: 1920, height: 1080 } });
    const page = await context.newPage();
    let filePath;
    try {
        await logAnd('Goto /connexion', () => page.goto(`${url}/connexion`, { waitUntil: 'domcontentloaded', timeout: 15000 }));
        await logAnd('Fill email', () => page.fill('input[placeholder="Votre email"]', '<EMAIL>'));
        await logAnd('Fill password', () => page.fill('input[placeholder="*********"]', 'Azerty3112.'));
        await logAnd('Click login', () => page.click('button:has-text("Je me connecte")'));
        await logAnd('Wait 2s', () => page.waitForTimeout(2000));
        filePath = path.join(scriptScreenshotDir, `login.png`);
        await logAnd('Screenshot', () => page.screenshot({ path: filePath, fullPage: true }));
        logAction(`✅ Screenshot saved: ${filePath}`);
        total_page++;
        total_success++;

        // Aller sur la page profil
        await logAnd('Goto /profil', () => page.goto(`${url}/profil`, { waitUntil: 'domcontentloaded', timeout: 15000 }));
        await logAnd('Wait 2s', () => page.waitForTimeout(2000));
        filePath = path.join(scriptScreenshotDir, `profil.png`);
        await logAnd('Screenshot', () => page.screenshot({ path: filePath, fullPage: true }));
        total_page++;
        total_success++;

        // Aller sur la page d'édition du profil
        await logAnd('Goto /profil/edition', () => page.goto(`${url}/profil/edition`, { waitUntil: 'domcontentloaded', timeout: 15000 }));
        await logAnd('Wait 2s', () => page.waitForTimeout(2000));
        filePath = path.join(scriptScreenshotDir, `profil-edition.png`);
        await logAnd('Screenshot', () => page.screenshot({ path: filePath, fullPage: true }));
        total_page++;
        total_success++;

        // Déplier la section "Votre profil de membre" si besoin
        try {
            const sectionHeader = await page.$('div.header-container.bg-color:has-text("Mon profil candidat")');
            if (sectionHeader) {
                await sectionHeader.click();
                await page.waitForTimeout(1000);
                filePath = path.join(scriptScreenshotDir, `profil-edition-section-open.png`);
                await page.screenshot({ path: filePath, fullPage: true });
                total_page++;
                total_success++;
            }
        } catch (e) {
            console.error('❌ Erreur lors de l\'ouverture de la section "Mon profil candidat":', e);
            total_error++;
            errors.push(`Erreur section Mon profil candidat: ${e.message}`);
        }

        try {
            // Vider tous les champs du profil
            // await page.fill('input[id="input-v-18"]', ''); //! Métier (Mon métier)
            await page.fill('input[id="input-v-9"]', ''); // Nom
            await page.fill('input[id="input-v-11"]', ''); // Prénom
            await page.fill('input[id="input-v-15"]', ''); // Téléphone (nouveau champ formaté)
            await page.fill('input[id="input-v-17"]', ''); // Ville (Tapez votre ville)
            await page.fill('textarea[id="input-v-19"]', ''); // À propos de moi
            await page.fill('input[id="input-v-21"]', ''); // Site internet
            await page.fill('input[id="input-v-23"]', ''); // LinkedIn
            await page.fill('input[id="input-v-25"]', ''); // instagram
            await page.fill('input[id="input-v-27"]', ''); // Facebook
            await page.fill('input[id="input-v-29"]', ''); // TikTok
            await page.fill('input[id="input-v-31"]', ''); // Portfolio
            await page.fill('input[id="input-v-33"]', ''); // Autre site

            // Remettre toutes les valeurs dans les champs du profil
            // await page.fill('input[id="input-v-18]', 'Développeur'); //! Métier
            await page.fill('input[id="input-v-9"]', 'TestNom'); // Nom
            await page.fill('input[id="input-v-11"]', 'TestPrénom'); // Prénom
            await page.fill('input[id="input-v-15"]', '0601020304'); // Téléphone
            await page.fill('input[id="input-v-17"]', 'Paris'); // Localisation
            await page.fill('textarea[id="input-v-19"]', 'Ceci est un test.'); // À propos de moi
            await page.fill('input[id="input-v-21"]', 'https://monsite.com'); // Site internet
            await page.fill('input[id="input-v-23"]', 'https://linkedin.com/in/test'); // LinkedIn
            await page.fill('input[id="input-v-25"]', 'https://intagram.com/in/test'); // instagram
            await page.fill('input[id="input-v-27"]', 'https://facebook.com/test'); // Facebook
            await page.fill('input[id="input-v-29"]', 'https://tiktok.com/@test'); // TikTok
            await page.fill('input[id="input-v-31"]', 'https://portfolio.com/test'); // Portfolio
            await page.fill('input[id="input-v-33"]', 'https://autresite.com'); // Autre site


            await logAnd('Click Enregistrer', () => page.click('button:has-text("Enregistrer tout ")'));
            await logAnd('Wait 2s', () => page.waitForTimeout(2000));
            filePath = path.join(scriptScreenshotDir, `profil-edition-save-all.png`);
            await logAnd('Screenshot', () => page.screenshot({ path: filePath, fullPage: true }));
            total_page++;
            total_success++;
        } catch (e) {
            console.error('❌ Erreur lors de la modification du profil (Mon profil candidat):', e);
            total_error++;
            errors.push(`Erreur modification profil: ${e.message}`);
        }

        try {
            const sectionHeader = await page.$('div.header-container.bg-color:has-text("Mes compétences")');
            if (sectionHeader) {
                await sectionHeader.click();
                await page.waitForTimeout(1000);
                filePath = path.join(scriptScreenshotDir, `profil-edition-mes-competences-section-open.png`);
                await page.screenshot({ path: filePath, fullPage: true });
                total_page++;
                total_success++;
            } else {
                console.warn('⚠️ Section "Mes compétences" non trouvée.');
                total_error++;
            }
        } catch (e) {
            console.error('❌ Erreur lors de l\'ouverture de la section "Mes compétences":', e);
            total_error++;
            errors.push(`Erreur section Mes compétences: ${e.message}`);
        }
        try {
            const skillChips = await page.$$('.skills .v-autocomplete .v-chip');
            for (const chip of skillChips) {
                const closeBtn = await chip.$('i.mdi-close');
                if (closeBtn) {
                    await closeBtn.click();
                    await page.waitForTimeout(300);
                }
            }

            const skillsToAdd = ['JavaScript', 'Vue.js', 'Playwright'];
            for (const skill of skillsToAdd) {
                await page.fill('input[id="input-v-36"]', skill);
                await page.keyboard.press('Enter');
                await page.waitForTimeout(300);
            }

            const langChips = await page.$$('.langage .v-autocomplete .v-chip');
            for (const chip of langChips) {
                const closeBtn = await chip.$('i.mdi-close');
                if (closeBtn) {
                    await closeBtn.click();
                    await page.waitForTimeout(300);
                }
            }

            const langsToAdd = ['Français', 'Anglais'];
            for (const lang of langsToAdd) {
                await page.fill('input[id="input-v-39"]', lang);
                await page.keyboard.press('Enter');
                await page.waitForTimeout(300);
            }

            const permitChips = await page.$$('.permit-mobility .v-select .v-chip');
            for (const chip of permitChips) {
                const closeBtn = await chip.$('i.mdi-close');
                if (closeBtn) {
                    await closeBtn.click();
                    await page.waitForTimeout(300);
                }
            }

            const permitsToAdd = ['Permis A (Moto)', 'Permis B (Voiture)'];
            for (const permit of permitsToAdd) {
                await page.fill('input[id="input-v-42"]', permit);
                await page.keyboard.press('Enter');
                await page.waitForTimeout(300);
            }

            const mobilityChips = await page.$$('.permit-mobility .v-input-field:last-child .v-select .v-chip');
            for (const chip of mobilityChips) {
                const closeBtn = await chip.$('i.mdi-close');
                if (closeBtn) {
                    await closeBtn.click();
                    await page.waitForTimeout(300);
                }
            }

            const mobilityToAdd = ['Voiture', 'Vélo'];
            for (const mobility of mobilityToAdd) {
                await page.fill('input[id="input-v-45"]', mobility);
                await page.keyboard.press('Enter');
                await page.waitForTimeout(300);
            }

            await logAnd('Click Enregistrer', () => page.click('button:has-text("Enregistrer tout ")'));
            filePath = path.join(scriptScreenshotDir, `profil-edition-competences-langues-mobilite.png`);
            await logAnd('Screenshot', () => page.screenshot({ path: filePath, fullPage: true }));
            total_page++;
            total_success++;
        } catch (e) {
            console.error('❌ Erreur lors de la modification des compétences/langues/mobilité:', e);
            total_error++;
            errors.push(`Erreur modification compétences/langues/mobilité: ${e.message}`);
        }





        // Modifier le titre du profil si possible
        // try {
        //     await page.fill('input[name="profile_title"], input[placeholder="Titre du profil"]', 'Développeur');
        //     await page.click('button:has-text("Enregistrer")');
        //     await page.waitForTimeout(1500);
        //     filePath = path.join(scriptScreenshotDir, `profil-edition-save.png`);
        //     await page.screenshot({ path: filePath, fullPage: true });
        //     total_page++;
        //     total_success++;
        // } catch (e) {
        //     total_error++;
        // }

        // // Vérifier la modification sur la page profil
        // await page.goto(`${url}/profil`, { waitUntil: 'domcontentloaded', timeout: 15000 });
        // await page.waitForTimeout(2000);
        // filePath = path.join(scriptScreenshotDir, `profil-verif.png`);
        // await page.screenshot({ path: filePath, fullPage: true });
        // total_page++;
        // total_success++;

        // // Aller sur la page des favoris
        // await page.goto(`${url}/parametres`, { waitUntil: 'domcontentloaded', timeout: 15000 });
        // await page.waitForTimeout(2000);
        // filePath = path.join(scriptScreenshotDir, `favoris.png`);
        // await page.screenshot({ path: filePath, fullPage: true });
        // total_page++;
        // total_success++;

        // Aller sur la page détail profil si bouton présent
        try {
            const detailBtn = await page.$('button, .carteprofil-ct-adtail, .profile-detail-btn');
            if (detailBtn) {
                await detailBtn.click();
                await page.waitForTimeout(2000);
                filePath = path.join(scriptScreenshotDir, `profil-detail.png`);
                await page.screenshot({ path: filePath, fullPage: true });
                total_page++;
                total_success++;
            }
        } catch (e) {
            console.error('❌ Erreur lors de l\'ouverture du détail du profil:', e);
            total_error++;
            errors.push(`Erreur ouverture détail profil: ${e.message}`);
        }
    } catch (error) {
        total_error++;
        filePath = path.join(scriptScreenshotDir, `Profile-error.test.png`);
        await page.screenshot({ path: filePath, fullPage: true });
        errors.push(error.message);
        logAction(`❌ Erreur: ${error.message}`);
        await addInformation(scriptName, 'error', total_page, total_error, total_success, filePath, actionsList, errors);
        await browser.close();
    } finally {
        const durationMs = Date.now() - startTime;
        const miliseconds = Math.floor(durationMs % 1000);
        const seconds = Math.floor((durationMs / 1000) % 60);
        const minutes = Math.floor((durationMs / (1000 * 60)) % 60);
        const hours = Math.floor(durationMs / (1000 * 60 * 60));
        const duration = `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${miliseconds.toString().padStart(3, '0')}`;
        logAction(`⏱️ Durée totale: ${duration}`);
        await addInformation(scriptName, duration, total_page, total_error, total_success, filePath, actionsList, errors);
        await browser.close();
    }
})();