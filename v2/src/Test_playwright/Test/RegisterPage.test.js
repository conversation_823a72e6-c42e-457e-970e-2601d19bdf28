const { url, routeUrl, routeApi } = require('../Utils/route');
const { addInformation } = require('../Utils/startfunc'); const { chromium } = require('playwright');
const fs = require('fs'); const path = require('path');
let total_page = 0; let total_error = 0;
let total_success = 0;
const screenshotDir = path.join(__dirname, 'screenshots'); if (!fs.existsSync(screenshotDir)) {
  fs.mkdirSync(screenshotDir);
}
const scriptName = path.basename(__filename, path.extname(__filename));
const scriptScreenshotDir = path.join(screenshotDir, scriptName); fs.mkdirSync(scriptScreenshotDir, { recursive: true });
// Système de log d'actions
const actionsLogPath = path.join(scriptScreenshotDir, 'actions.log'); let actionsList = [];
function logAction(action) {
  const logEntry = `${action}`;
  actionsList.push(logEntry); fs.appendFileSync(actionsLogPath, logEntry + '\n');
}
async function logAnd(action, fn) {
  logAction(action);
  console.log(action); return await fn();
}
module.exports = (async () => {
  const startTime = Date.now();
  const browser = await chromium.launch({ headless: true }); const page = await browser.newPage();
  let errors = [];
  try {
    // Aller à la page d'inscription
    await logAnd('Goto /inscription', () =>
      page.goto(`${url}/inscription`, { waitUntil: 'domcontentloaded', timeout: 15000 })
    );

    // Remplir le formulaire d'inscription
    await logAnd('Fill email', () =>
      page.fill('input[placeholder="Votre email"]', 'test_user_' + Date.now() + '@example.com')
    );
    await logAnd('Fill password', () =>
      page.fill('input[placeholder="Votre mot de passe"]', 'Test123456!')
    );
    await logAnd('Fill password confirmation', () =>
      page.fill('input[placeholder="Confirmer votre mot de passe"]', 'Test123456!')
    );

    // Remplir les informations personnelles
    await logAnd('Fill first name', () =>
      page.fill('input[placeholder="Prénom"]', 'Test')
    );
    await logAnd('Fill last name', () =>
      page.fill('input[placeholder="Nom"]', 'User')
    );
    await logAnd('Fill phone number', () =>
      page.fill('input[placeholder="Téléphone"]', '0612345678')
    );

    // Accepter les conditions
    await logAnd('Check terms', () =>
      page.check('input[type="checkbox"]')
    );

    // Cliquer sur le bouton d'inscription
    await logAnd('Click register button', () =>
      page.click('button:has-text("Je m\'inscris")')
    );

    // Attendre que l'inscription soit traitée
    await logAnd('Wait for registration processing', () =>
      page.waitForTimeout(3000)
    );

    // Prendre une capture d'écran
    const screenshotPath = path.join(scriptScreenshotDir, `registerPage.test.png`);
    await logAnd('Screenshot', () =>
      page.screenshot({ path: screenshotPath, fullPage: true })
    );
    logAction(`✅ Screenshot saved: ${screenshotPath}`);

    total_page++;
    total_success++;

    // Calculer la durée du test
    const durationMs = Date.now() - startTime;
    const miliseconds = Math.floor(durationMs % 1000);
    const seconds = Math.floor((durationMs / 1000) % 60);
    const minutes = Math.floor((durationMs / (1000 * 60)) % 60);
    const hours = Math.floor(durationMs / (1000 * 60 * 60));
    const duration = `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${miliseconds.toString().padStart(3, '0')}`;

    logAction(`⏱️ Durée totale: ${duration}`);
    addInformation(scriptName, duration, total_page, total_error, total_success, screenshotPath, actionsList, errors);

    await browser.close();
  } catch (error) {
    total_error++;
    const errorScreenshotPath = path.join(scriptScreenshotDir, `registerPage-error.test.png`);
    await page.screenshot({ path: errorScreenshotPath, fullPage: true });
    errors.push(error.message);
    logAction(`❌ Erreur: ${error.message}`);
    addInformation(scriptName, 'error', total_page, total_error, total_success, errorScreenshotPath, actionsList, errors);
    await browser.close();
  }
})();













































