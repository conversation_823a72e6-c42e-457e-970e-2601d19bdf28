const { url, routeUrl } = require('../Utils/route');
const { addInformation } = require('../Utils/startfunc');
const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');
let total_page = 0;
let total_error = 0;
let total_success = 0;
let errors = [];

const screenshotDir = path.join(__dirname, 'screenshots');
if (!fs.existsSync(screenshotDir)) {
  fs.mkdirSync(screenshotDir);
}

const scriptName = path.basename(__filename, path.extname(__filename));
const scriptScreenshotDir = path.join(screenshotDir, scriptName);
fs.mkdirSync(scriptScreenshotDir, { recursive: true });

// Système de log d'actions inspiré de Reseau-social.test.js
const actionsLogPath = path.join(scriptScreenshotDir, 'actions.log');
let actionsList = [];
function logAction(action) {
  const logEntry = `${action}`;
  actionsList.push(logEntry);
  fs.appendFileSync(actionsLogPath, logEntry + '\n');
}

async function logAnd(action, fn) {
  logAction(action);
  return await fn();
}

module.exports = (async () => {
  const startTime = Date.now();
  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext({ viewport: { width: 1920, height: 1080 } });
  const page = await context.newPage();
  try {
    await logAnd('Goto /connexion', () => page.goto(`${url}/connexion`, { waitUntil: 'domcontentloaded', timeout: 15000 }));
    total_page++;

    await logAnd('Fill email', () => page.fill('input[placeholder="Votre email"]', '<EMAIL>'));
    await logAnd('Fill password', () => page.fill('input[placeholder="*********"]', 'Azerty3112.'));
    await logAnd('Click login', () => page.click('button:has-text("Je me connecte")'));
    const filePath = path.join(scriptScreenshotDir, `ChatIaMessage.test.png`);
    await logAnd('Wait 3s', () => page.waitForTimeout(3000));
    await logAnd('Goto /chat-ia', () => page.goto(`${url}/chat-ia`, { waitUntil: 'domcontentloaded', timeout: 15000 }));
    total_page++;
    await logAnd('Wait 2s', () => page.waitForTimeout(2000));
    await logAnd('Click new chat', () => page.click('button[data-v-bb00b13a=""]', { force: true }));
    await logAnd('Wait 2s', () => page.waitForTimeout(2000));
    await logAnd('Click chat 1', () => page.click('div.v-list-item-title:has-text("Chat 1")', { force: true }));
    await logAnd('Wait 2s', () => page.waitForTimeout(2000));
    await logAnd('Fill message', () => page.fill('input[placeholder="Saisis ton message"]', 'je veux un travail a paris de developeur web'));
    await logAnd('Click send', () => page.click('img[alt="Icône d\'envoi de message"]', { force: true }));
    await logAnd('Wait 15s', () => page.waitForTimeout(15000));
    try {
      await logAnd('Wait for job box', () => page.waitForSelector('div.message-received-container.best-jobs-message-container', { timeout: 20000 }));
      total_success++;
      logAction('✅ La box des offres d\'emploi est présente.');
    } catch (e) {
      total_error++;
      errors.push("Aucune box d'offres trouvée après 20 secondes.");
      logAction('❌ Aucune box d\'offres trouvée après 20 secondes.');
    }
    await logAnd('Screenshot', () => page.screenshot({ path: filePath, fullPage: true }));
    logAction(`✅ Screenshot saved: ${filePath}`);
    total_page++;
    const durationMs = Date.now() - startTime;
    const miliseconds = Math.floor(durationMs % 1000);
    const seconds = Math.floor((durationMs / 1000) % 60);
    const minutes = Math.floor((durationMs / (1000 * 60)) % 60);
    const hours = Math.floor(durationMs / (1000 * 60 * 60));
    const duration = `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${miliseconds.toString().padStart(3, '0')}`;
    logAction(`⏱️ Durée totale: ${duration}`);
    addInformation(scriptName, duration, total_page, total_error, total_success, filePath, actionsList, errors);
  } catch (error) {
    total_error++;
    const filePath = path.join(scriptScreenshotDir, `ChatIaMessage-error.test.png`);
    await page.screenshot({ path: filePath, fullPage: true });
    errors.push(error.message);
    logAction(`❌ Erreur: ${error.message}`);
    addInformation(scriptName, 'error', total_page, total_error, total_success, filePath, actionsList, errors);
  } finally {
    await browser.close();
  }
})();
