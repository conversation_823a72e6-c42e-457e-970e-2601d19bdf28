const { url, routeUrl } = require('../Utils/route');
const { addInformation } = require('../Utils/startfunc');
const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');
let total_page = 0;
let total_error = 0;
let total_success = 0;
let errors = [];

const screenshotDir = path.join(__dirname, 'screenshots');
if (!fs.existsSync(screenshotDir)) {
  fs.mkdirSync(screenshotDir);
}

const scriptName = path.basename(__filename, path.extname(__filename));
const scriptScreenshotDir = path.join(screenshotDir, scriptName);
fs.mkdirSync(scriptScreenshotDir, { recursive: true });

const actionsLogPath = path.join(scriptScreenshotDir, 'actions.log');
let actionsList = [];
function logAction(action) {
  const logEntry = `${action}`;
  actionsList.push(logEntry);
  fs.appendFileSync(actionsLogPath, logEntry + '\n');
}

module.exports = (async () => {
  const startTime = Date.now();
  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext({ viewport: { width: 1920, height: 1080 } });
  const page = await context.newPage();

  // Log mouse and keyboard events
  page.on('click', (event) => {
    logAction(`Click at (${event.x}, ${event.y}) on selector: ${event.selector}`);
  });
  page.on('mousemove', (event) => {
    logAction(`Mouse move to (${event.x}, ${event.y})`);
  });
  page.on('keydown', (event) => {
    logAction(`Key down: ${event.key}`);
  });
  page.on('keyup', (event) => {
    logAction(`Key up: ${event.key}`);
  });

  // Helper to log actions in your script
  async function logAnd(action, fn) {
    logAction(action);
    console.log(action);
    return await fn();
  }

  try {
    await logAnd('Goto /connexion', () => page.goto(`${url}/connexion`, { waitUntil: 'domcontentloaded', timeout: 15000 }));
    total_page++;

    await logAnd('Fill email', () => page.fill('input[placeholder="Votre email"]', '<EMAIL>'));
    await logAnd('Fill password', () => page.fill('input[placeholder="*********"]', 'Azerty3112.'));

    await logAnd('Click login', () => page.click('button:has-text("Je me connecte")'));
    const filePath = path.join(scriptScreenshotDir, `LoginReseaux-social.test.png`);

    await logAnd('Wait 3s', () => page.waitForTimeout(3000));

    await logAnd('Goto /reseau-social', () => page.goto(`${url}/reseau-social`, { waitUntil: 'domcontentloaded', timeout: 15000 }));
    total_page++;

    await logAnd('Wait 2s', () => page.waitForTimeout(2000));

    const message = "Ceci est un joli message automatisé par Playwright 😊";

    await logAnd('Click textarea', () => page.click('textarea[placeholder="Clique pour commencer à écrire ton post"]'));
    await logAnd('Fill message', () => page.fill('textarea[placeholder="Clique pour commencer à écrire ton post"]', message));
    await logAnd('Click send post', () => page.click('button.send-post[aria-describedby="v-tooltip-v-11"]'));
    // refresh the page to ensure the message is sent
    await logAnd('Refresh page', () => page.reload({ waitUntil: 'domcontentloaded' }));
    await logAnd('Wait 3s', () => page.waitForTimeout(3000));
    await logAnd('Wait for message', () => page.waitForSelector(`text=${message}`, { timeout: 5000 }));

    const isMessageSent = await page.isVisible(`text=${message}`);
    if (isMessageSent) {
      total_success++;
      logAction("Message sent and visible");
      console.log("✅ Le message a été envoyé et affiché avec succès !");
    } else {
      total_error++;
      errors.push("Message not found after send");
      logAction("Message not found after send");
      console.error("❌ Le message n'a pas été trouvé après l'envoi.");
    }

    await logAnd('Screenshot', () => page.screenshot({ path: filePath, fullPage: true }));
    console.log(`✅ Screenshot saved: ${filePath}`);
    total_page++;

    // Delete the message
    // Open post options menu
    await logAnd('Click post options', () => page.click('button.post-options'));
    await logAnd('Wait for options menu', () => page.waitForSelector('.options-menu', { timeout: 3000 }));
    await logAnd('Click Supprimer', () => page.click('.options-menu .option:has-text("Supprimer")'));
    await logAnd('Wait for post to be deleted', () => page.waitForSelector(`text=${message}`, { state: 'detached', timeout: 5000 }));

    const durationMs = Date.now() - startTime;
    const miliseconds = Math.floor(durationMs % 1000);
    const seconds = Math.floor((durationMs / 1000) % 60);
    const minutes = Math.floor((durationMs / (1000 * 60)) % 60);
    const hours = Math.floor(durationMs / (1000 * 60 * 60));
    const duration = `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${miliseconds.toString().padStart(3, '0')}`;

    console.log(`⏱️ Durée totale: ${duration}`);

    addInformation(scriptName, duration, total_page, total_error, total_success, filePath, actionsList, errors);

  } catch (error) {
    total_error++;
    const filePath = path.join(scriptScreenshotDir, `Reseau-social.test.png`);
    await page.screenshot({ path: filePath, fullPage: true });
    errors.push(error.message);
    console.error("❌ Une erreur est survenue :", error);
    addInformation(scriptName, "error", total_page, total_error, total_success, filePath, actionsList, errors);
  } finally {
    await browser.close();
  }
})();
