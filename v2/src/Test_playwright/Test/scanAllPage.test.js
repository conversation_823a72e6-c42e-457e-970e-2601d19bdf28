const { url, routeUrl } = require('../Utils/route');
const { addInformation } = require('../Utils/startfunc');
const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');


const screenshotDir = path.join(__dirname, 'screenshots');
if (!fs.existsSync(screenshotDir)) {
  fs.mkdirSync(screenshotDir);
}
const scriptName = path.basename(__filename, path.extname(__filename));
const scriptScreenshotDir = path.join(screenshotDir, scriptName);
fs.mkdirSync(scriptScreenshotDir, { recursive: true });

let total_page = 0;
let total_error = 0;
let total_success = 0;
let error = [];
let info = [];
let errors = [];

// Système de log d'actions inspiré de Reseau-social.test.js
const actionsLogPath = path.join(scriptScreenshotDir, 'actions.log');
let actionsList = [];
function logAction(action) {
  const logEntry = `${action}`;
  actionsList.push(logEntry);
  fs.appendFileSync(actionsLogPath, logEntry + '\n');
}

async function logAnd(action, fn) {
  logAction(action);
  console.log(action);
  return await fn();
}

module.exports = (async () => {
  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    const startTime = Date.now();
    for (const route of routeUrl) {
      const fullUrl = `${url}${route}`;
      const cleanRoute = route.replace(/[\/\\:?*"<>|]/g, '') || '/';
      const filePath = path.join(scriptScreenshotDir, `${cleanRoute}.png`);
      try {
        logAction(`Navigating to: ${fullUrl}`);
        await logAnd(`Goto ${fullUrl}`, () => page.goto(fullUrl, { waitUntil: 'domcontentloaded', timeout: 15000 }));
        await logAnd('Wait 2s', () => page.waitForTimeout(2000));
        await logAnd('Screenshot', () => page.screenshot({ path: filePath, fullPage: true }));
        logAction(`✅ Screenshot saved: ${filePath}`);
        total_page++;
        total_success++;
      } catch (err) {
        total_error++;
        errors.push(`Erreur sur ${fullUrl}: ${err.message}`);
        logAction(`❌ Erreur sur ${fullUrl}: ${err.message}`);
        try {
          await page.screenshot({ path: filePath.replace('.png', '_error.png'), fullPage: true });
          logAction(`🛑 Error screenshot saved: ${filePath.replace('.png', '_error.png')}`);
        } catch (screenshotErr) {
          errors.push(`Failed to save error screenshot: ${screenshotErr.message}`);
          logAction(`⚠️ Failed to save error screenshot: ${screenshotErr.message}`);
        }
      }
    }
    const durationMs = Date.now() - startTime;
    const miliseconds = Math.floor(durationMs % 1000);
    const seconds = Math.floor((durationMs / 1000) % 60);
    const minutes = Math.floor((durationMs / (1000 * 60)) % 60);
    const hours = Math.floor(durationMs / (1000 * 60 * 60));
    const duration = `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${miliseconds.toString().padStart(3, '0')}`;
    logAction(`⏱️ Durée totale: ${duration}`);
    await addInformation(scriptName, duration, total_page, total_error, total_success, scriptScreenshotDir, actionsList, errors);
    logAction(`\nTotal de pages traitées: ${total_page}`);
    await browser.close();
    return { total_page, total_error, total_success };
  } catch (error) {
    total_error++;
    errors.push(`Erreur globale: ${error.message}`);
    logAction(`❌ Erreur globale: ${error.message}`);
    await addInformation(scriptName, 'error', total_page, total_error, total_success, scriptScreenshotDir, actionsList, errors);
    await browser.close();
  }
})();
