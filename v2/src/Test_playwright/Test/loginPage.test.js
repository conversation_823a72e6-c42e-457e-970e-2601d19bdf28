const { url, routeUrl } = require('../Utils/route');
const { addInformation } = require('../Utils/startfunc');
const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');
let total_page = 0;
let total_error = 0;
let total_success = 0;

const screenshotDir = path.join(__dirname, 'screenshots');
if (!fs.existsSync(screenshotDir)) {
  fs.mkdirSync(screenshotDir);
}

// ❌ Supprimé : fs.mkdirSync(screenshotDir);

const scriptName = path.basename(__filename, path.extname(__filename));
const scriptScreenshotDir = path.join(screenshotDir, scriptName);
fs.mkdirSync(scriptScreenshotDir, { recursive: true });

// Système de log d'actions inspiré de Reseau-social.test.js
const actionsLogPath = path.join(scriptScreenshotDir, 'actions.log');
let actionsList = [];
function logAction(action) {
  const logEntry = `${action}`;
  actionsList.push(logEntry);
  fs.appendFileSync(actionsLogPath, logEntry + '\n');
}

async function logAnd(action, fn) {
  logAction(action);
  console.log(action);
  return await fn();
}

module.exports = (async () => {
  const startTime = Date.now();
  const browser = await chromium.launch({ headless: true });
  const page = await browser.newPage();
  let errors = [];

  try {
    await logAnd('Goto /connexion', () => page.goto(`${url}/connexion`, { waitUntil: 'domcontentloaded', timeout: 15000 }));
    await logAnd('Fill email', () => page.fill('input[placeholder="Votre email"]', '<EMAIL>'));
    await logAnd('Fill password', () => page.fill('input[placeholder="*********"]', 'Azerty3112.'));
    await logAnd('Click login', () => page.click('button:has-text("Je me connecte")'));
    const filePath = path.join(scriptScreenshotDir, `loginPage.test.png`);
    await logAnd('Wait 3s', () => page.waitForTimeout(3000));
    await logAnd('Screenshot', () => page.screenshot({ path: filePath, fullPage: true }));
    logAction(`✅ Screenshot saved: ${filePath}`);
    total_page++;
    total_success++;

    const durationMs = Date.now() - startTime;
    const miliseconds = Math.floor(durationMs % 1000);
    const seconds = Math.floor((durationMs / 1000) % 60);
    const minutes = Math.floor((durationMs / (1000 * 60)) % 60);
    const hours = Math.floor(durationMs / (1000 * 60 * 60));
    const duration = `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${miliseconds.toString().padStart(3, '0')}`;
    logAction(`⏱️ Durée totale: ${duration}`);

    addInformation(scriptName, duration, total_page, total_error, total_success, filePath, actionsList, errors);
    await browser.close();
  } catch (error) {
    total_error++;
    const filePath = path.join(scriptScreenshotDir, `loginPage-error.test.png`);
    await page.screenshot({ path: filePath, fullPage: true });
    errors.push(error.message);
    logAction(`❌ Erreur: ${error.message}`);
    addInformation(scriptName, 'error', total_page, total_error, total_success, filePath, actionsList, errors);
    await browser.close();
  }
})();
