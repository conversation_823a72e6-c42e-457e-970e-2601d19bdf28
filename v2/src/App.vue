<template>
  <Navbar v-if="showComponent" @view-switched="handleViewSwitch" />
  <div class="loader-container" v-if="!isReady">
    <div class="optimized-loader">
      <div class="loader-spinner"></div>
      <p class="loader-text">Chargement en cours...</p>
    </div>
  </div>

  <div class="content-container">
    <router-view
      class="router-view"
      v-if="isReady"
      :user="getUser"
      :is-viewing-as-recruiter="isViewingAsRecruiter"
      :key="$route.fullPath"
    />
  </div>
  <MessagerieLayout v-if="$store.state.isLoggedIn" />
  <NewsLetter v-if="$route.fullPath == '/actualites'" />
  <GlobalIncomingCallNotification v-if="$store.state.isLoggedIn" />
  <Footer v-if="showComponent && showFooter" />
</template>

<script>
  // Import des composants critiques pour le rendu initial
  import Navbar from '@/components/layout/navbar/Navbar.vue';
  import Footer from '@/components/layout/footer/Footer.vue';
  import { defineAsyncComponent, onBeforeUnmount, getCurrentInstance } from 'vue';

  // Import asynchrone des composants non critiques
  const MessagerieLayout = defineAsyncComponent(
    () => import('@/components/layout/messagerie/MessagerieLayout.vue')
  );

  import { addUserToStore } from '@/services/account.service.js';
  import { mapGetters } from 'vuex';
  import { mapActions } from 'vuex/dist/vuex.cjs.js';
  import { checkAuthStatus } from './services/account.service';
  import NewsLetter from '@/components/layout/newsletter/NewsLetter.vue';
  // Importer le service de gestion des appels vidéo
  import { initializeCallListener } from '@/services/video-call.service';
  import GlobalIncomingCallNotification from '@/components/video-call/GlobalIncomingCallNotification.vue';
  // Importer le service de websocket privé global
  import { initializeGlobalPrivateWebSocket } from '@/services/global-websocket.service';

  export default {
    components: {
      Navbar,
      Footer,
      MessagerieLayout,
      NewsLetter,
      GlobalIncomingCallNotification,
    },
    name: 'App',
    setup() {
      // Utiliser onBeforeUnmount pour nettoyer les ressources lors de la destruction du composant
      onBeforeUnmount(() => {
        //console.log('%c[APP] Nettoyage des ressources avant destruction du composant', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');

        // Cette fonction sera appelée juste avant que le composant ne soit détruit
        // Nous nettoierons tous les intervalles ici
        const app = getCurrentInstance()?.proxy;
        if (app) {
          // Nettoyer l'intervalle de vérification du WebSocket
          if (app.intervals?.webSocketCheck) {
            clearInterval(app.intervals.webSocketCheck);
            //console.log('%c[APP] Intervalle de vérification du WebSocket nettoyé', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
          }

          // Nettoyer l'intervalle de reconnexion du WebSocket
          if (app.intervals?.webSocketReconnect) {
            clearInterval(app.intervals.webSocketReconnect);
            //console.log('%c[APP] Intervalle de reconnexion du WebSocket nettoyé', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
          }

          // Nettoyer l'intervalle de reconnexion de l'écouteur d'appels
          if (app.intervals?.callListenerReconnect) {
            clearInterval(app.intervals.callListenerReconnect);
            //console.log('%c[APP] Intervalle de reconnexion de l\'écouteur d\'appels nettoyé', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
          }
        }
      });

      return {};
    },
    data() {
      return {
        isUserLogin: false,
        isReady: false,
        actualRoute: '',
        isViewingAsRecruiter: false,
        // Stocker les références aux intervalles pour pouvoir les nettoyer
        intervals: {
          webSocketCheck: null,
          webSocketReconnect: null,
          callListenerReconnect: null
        }
      };
    },
    watch: {
      $route: {
        immediate: true,
        handler(to) {
          this.actualRoute = to.path;
          //console.log('Route changed to:', to.path);
        },
      },
    },
    methods: {
      ...mapActions(['initializeUserConnectionWebSocket']),
      checkRoute() {
        // Liste des routes où la navbar et le footer ne doivent pas être affichés
        const routesWithoutNavbarFooter = [
          '/register',
          '/login',
          '/password-reset',
          '/password-reset-confirm',
          '/recruiter/purchase/recap',
          '/achat',
          '/purchase/info',
          '/purchase/devis',
          '/purchase/paiement',
          '/recruiter/purchase/recap',
          '/recruiter/purchase/info',
          '/recruiter/purchase/devis',
          '/recruiter/purchase/paiement',
          '/recruiter/login',
          '/recruiter/register',
          '/inscription',
          '/connexion',
          '/reinitialisation-mot-de-passe',
          '/reinitialisation-mot-de-passe-confirme',
          '/recruteur/inscription',
          '/recruteur/connexion',
        ];

        // Vérifier si la route actuelle est dans la liste des routes sans navbar/footer
        if (routesWithoutNavbarFooter.includes(this.actualRoute)) {
          return false;
        }

        // Vérifier les routes avec paramètres
        if (
          this.actualRoute.startsWith('/purchase/') ||
          this.actualRoute.startsWith('/recruiter/purchase/')
        ) {
          return false;
        }

        // Par défaut, afficher navbar et footer
        return true;
      },
      checkFooterRoute() {
        // Liste des routes où le footer ne doit pas être affiché
        const routesWithoutFooter = [
          '/recherche',
        ];

        // Vérifier si la route actuelle est dans la liste des routes sans footer
        return !routesWithoutFooter.includes(this.actualRoute);
      },
      handleViewSwitch(value) {
        this.isViewingAsRecruiter = value;
        //console.log('View switched to:', value ? 'recruiter' : 'candidate');
      },
      isFullWidthPage() {
        return ['/home', '/landing'].includes(this.$route.path); // Ajoutez les routes concernées
      },
      // Charger les données utilisateur de manière asynchrone
      async loadUserData() {
        try {
          // Vérifier si la route nécessite de récupérer les données utilisateur
          if (this.checkRoute()) {
            // Vérifier si l'utilisateur est connecté et si le token est valide
            const isLoggedIn = await checkAuthStatus();
            if (isLoggedIn) {
              await addUserToStore();
              this.isUserLogin = true;
            }
          }

          // Initialiser le websocket de connexion utilisateur
          // Ce websocket est utilisé pour écouter les notifications de l'utilisateur et pour définir son état de connexion
          this.initializeUserConnectionWebSocket();

          // Initialiser le websocket privé global
          // Ce websocket est utilisé pour écouter les messages privés, y compris les appels vidéo
          if (this.$store.state.isLoggedIn) {
            //console.log('%c[WEBSOCKET] Initialisation du WebSocket privé global...', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
            const privateWs = initializeGlobalPrivateWebSocket();

            if (privateWs) {
              //console.log('%c[WEBSOCKET] WebSocket privé global initialisé avec succès', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');

              // Configurer une vérification périodique du WebSocket
              this.intervals.webSocketCheck = setInterval(() => {
                if (!privateWs || privateWs.readyState !== WebSocket.OPEN) {
                  //console.log('%c[WEBSOCKET] WebSocket privé global déconnecté, tentative de reconnexion...', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');
                  initializeGlobalPrivateWebSocket();
                }
              }, 60000); // Vérifier toutes les minutes
            } else {
              //console.error('%c[WEBSOCKET] Échec de l\'initialisation du WebSocket privé global', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');

              // Nouvelle tentative après un délai
              setTimeout(() => {
                //console.log('%c[WEBSOCKET] Nouvelle tentative d\'initialisation du WebSocket privé global...', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
                const retryWs = initializeGlobalPrivateWebSocket();
                if (retryWs) {
                  //console.log('%c[WEBSOCKET] WebSocket privé global initialisé avec succès après nouvelle tentative', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
                } else {
                  //console.error('%c[WEBSOCKET] Échec de l\'initialisation du WebSocket privé global après nouvelle tentative', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');

                  // Planifier des tentatives périodiques
                  this.intervals.webSocketReconnect = setInterval(() => {
                    //console.log('%c[WEBSOCKET] Tentative périodique d\'initialisation du WebSocket privé global...', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
                    const periodicWs = initializeGlobalPrivateWebSocket();
                    if (periodicWs) {
                      //console.log('%c[WEBSOCKET] WebSocket privé global initialisé avec succès après tentative périodique', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
                      clearInterval(this.intervals.webSocketReconnect);
                      this.intervals.webSocketReconnect = null;
                    }
                  }, 30000); // Tenter toutes les 30 secondes
                }
              }, 3000);
            }
          }

          // Initialiser l'écouteur d'appel vidéo global après l'initialisation du WebSocket
          if (this.$store.state.isLoggedIn && typeof initializeCallListener === 'function') {
            // Première tentative immédiate
            //console.log('%c[APPEL] Initialisation de l\'écouteur d\'appels vidéo...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
            const success = await initializeCallListener();

            if (!success) {
              //console.log('%c[APPEL] Première tentative d\'initialisation de l\'écouteur d\'appels échouée, nouvelle tentative dans 2 secondes...', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');

              // Deuxième tentative après un délai
              setTimeout(async () => {
                const retrySuccess = await initializeCallListener();
                if (retrySuccess) {
                  //console.log('%c[APPEL] Écouteur d\'appel vidéo global initialisé avec succès après nouvelle tentative', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
                } else {
                  //console.error('%c[APPEL] Impossible d\'initialiser l\'écouteur d\'appels après plusieurs tentatives', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');

                  // Planifier des tentatives périodiques
                  this.intervals.callListenerReconnect = setInterval(async () => {
                    //console.log('%c[APPEL] Tentative périodique d\'initialisation de l\'écouteur d\'appels...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
                    const periodicSuccess = await initializeCallListener();
                    if (periodicSuccess) {
                      //console.log('%c[APPEL] Écouteur d\'appel vidéo global initialisé avec succès après tentative périodique', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
                      clearInterval(this.intervals.callListenerReconnect);
                      this.intervals.callListenerReconnect = null;
                    }
                  }, 30000); // Tenter toutes les 30 secondes
                }
              }, 2000);
            } else {
              //console.log('%c[APPEL] Écouteur d\'appel vidéo global initialisé avec succès', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
            }
          }
        } catch (error) {
          //console.error('Error in loadUserData:', error);
        }
      },
    },
    async mounted() {
      // Marquer l'application comme prête immédiatement pour améliorer la perception de vitesse
      // Les données utilisateur seront chargées en arrière-plan
      setTimeout(() => {
        this.isReady = true;
      }, 300); // Délai minimal pour éviter le flash du loader

      try {
        this.actualRoute = this.$route.path;

        // L'initialisation de l'écouteur d'appel est maintenant gérée dans loadUserData()

        await this.loadUserData();
      } catch (error) {
        //console.error('Error in App.vue mounted:', error);
      }
    },

    computed: {
      ...mapGetters(['getUser']),
      showComponent() {
        const shouldShow = this.checkRoute();
        //console.log(
        //  'showComponent for route',
        //  this.actualRoute,
        //  ':',
        //  shouldShow
        //);
        return shouldShow;
      },
      showFooter() {
        return this.checkFooterRoute();
      },
    },
  };
</script>

<style>
  :root {
    /* colors variables, couleurs variables */
    --white-100: #f5f2ef;
    --white-200: #fffdfc;
    --white-10: hsl(20, 100%, 99%, 0.1);

    --black-100: #26282b;
    --black-200: #232123;

    --gray-100: #626161;
    --gray-10: rgba(38, 40, 43, 10%);
    --gray-light: #3b3d40;
    --gray-light-opacity: rgba(59, 61, 64, 0.2);

    --yellow-100: #f6b337;
    --yellow-80: #f5c772;
    --yellow-20: rgba(246, 179, 55, 0.2);

    --brown-100: #504538;
    --brown-80: #554e46;
    --brown-60: #b69d7f;

    --beige-100: #b6a999;
    --beige-80: #cfc6ba;
    --beige-60: #dfdbd6;

    --turquoise-100: #00dec4;
    --turquoise-80: #8bdbd1;

    --blue-100: #00a58e;
    --blue-80: #58a096;
    --blue-20: rgba(89, 161, 153, 0.2);

    --pink: #efbec1;

    --purple: #a287e9;

    /* primary colors, couleurs primaires */
    --primary-1: var(--yellow-100);
    --primary-1b: var(--yellow-80);
    --primary-1b2: var(--yellow-20);
    --primary-2: var(--brown-100);
    --primary-2b: var(--brown-80);
    --primary-2b2: var(--brown-60);
    --primary-3: var(--beige-100);
    --primary-3b: var(--beige-80);
    --primary-3b2: var(--beige-60);
    --secondary-1: var(--turquoise-100);
    --secondary-1b: var(--turquoise-80);
    --secondary-2: var(--blue-100);
    --secondary-2b: var(--blue-80);
    --secondary-2b2: var(--blue-20);
    --ai-color: var(--purple);

    /* text / background colors, couleurs de texte / fond */
    --surface-bg: var(--white-100);
    --surface-bg-2: var(--white-200);
    --surface-bg-3: var(--white-10);
    --surface-bg-4: var(--gray-10);
    --surface-bg-5: var(--black-100);
    --text-1: var(--black-100);
    --text-2: var(--black-200);
    --text-3: var(--gray-100);

    /* navbar, barre de navigation */
    /* navbar colors, barre de navigation couleurs */
    --navbar-bg-color: var(--text-1);
    --navbar-font-color: var(--surface-bg-2);
    /* navbar hover colors, barre de navigation survol couleurs */
    --navbar-hover-bg-color: var(--primary-1);
    --navbar-hover-font-color: var(--surface-bg-2);
    /* navbar active colors, barre de navigation active couleurs */
    --navbar-active-bg-color: var(--primary-1);
    --navbar-active-font-color: var(--surface-bg-2);
    /* navbar font, barre de navigation police */
    --navbar-font-size: 12px;

    /* footer colors, pied de page couleurs */
    --footer-bg-color: var(--black-100);
    --footer-font-color: var(--white-200);
    --footer-font-size: 12px;
    --footer-font-family: 'Roboto', sans-serif;
    --footer-font-weight: 400;
    --footer-font-style: normal;

    /* primary rounded button, bouton rond primaire */
    --primary-rounded-btn-bg-color: var(--yellow-100);
    --secondary-rounded-btn-bg-color: var(--black-100);
    --light-rounded-btn-bg-color: var(--white-200);
    --primary-rounded-btn-color: var(--black-100);
    --secondary-rounded-btn-color: var(--white-200);
    --light-rounded-btn-color: var(--black-100);
    --light-rounded-btn-border-color: var(--yellow-100);
    --primary-rounded-btn-font-weight: 400;
    --primary-rounded-btn-font-size: 14px;
    --primary-rounded-btn-font-familly: 'Roboto', sans-serif;

    /* home page, page principale */
    --home-hero-section-bg-color: var(--surface-bg);
    --home-about-section-bg-color: var(--surface-bg-5);
    --home-how-section-bg-color: var(--surface-bg);
    --home-news-section-bg-color: var(--surface-bg-2);
    --home-sponsor-section-bg-color: var(--surface-bg);
    --home-search-section-bg-color: var(--surface-bg-2);
    --home-cards-bg-color: var(--surface-bg);
    --home-cards-bg-color-2: var(--surface-bg-2);
    --home-cards-number-bg-color: var(--yellow-80);
    --home-cards-number-font-familly: 'Anton', sans-serif;
    --home-searchdiv-bg-color: var(--yellow-100);

    /* candidates search page, page de recherche des candidats */
    --search-candidate-bg-color: var(--surface-bg);
    --search-candidate-searchfield-bg-color: var(--surface-bg-2);
    --search-candidate-searchbtn-bg-color: var(
      --secondary-rounded-btn-bg-color
    );
    --search-candidate-candidatecard-bg-color: var(--surface-bg-2);

    /* subscription, abonnement */
    --subscription-card-font-color: var(--black);
  }
  /* dark theme
html[data-theme='dark'] {
  --text-color: var(--white);
}
*/
  #app,
  html,
  body,
  main {
    min-height: 100%;
    width: 100%;
    font-family: 'Roboto', sans-serif;
    font-weight: 400;
    font-style: normal;
    scroll-behavior: smooth;
    background-color: var(--surface-bg);
    max-width: 100vw;
    overflow-x: hidden; /* Empêche le scroll horizontal */
  }

  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  /* Permet un fond en pleine largeur */
  .page-container {
    width: 100vw;
    min-height: 100vh;
  }

  /* Centre le contenu avec des marges latérales */
  .content-container {
    min-height: 100vh;
    max-width: 1440px;
    margin: 0 auto;
    background-color: var(--surface-bg);
  }

  .loader-container {
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  /* Le conteneur principal de les pages */
  main.container {
    min-height: 100vh;
    margin-top: 30px;
    margin-bottom: 80px;
  }

  .d-flex {
    display: flex;
  }

  .d-none {
    display: none;
  }

  .j-content {
    justify-content: center;
  }

  .a-center {
    align-items: center;
  }

  .flex-column {
    flex-direction: column;
  }

  .text-underline {
    text-decoration: underline;
  }

  /* padding classes */
  .padding-container {
    padding-inline: 3vw;
  }

  .border-radius-2 {
    border-radius: 2px !important;
  }

  .border-radius-5 {
    border-radius: 5px !important;
  }

  .border-radius-10 {
    border-radius: 10px !important;
  }

  .border-radius-15 {
    border-radius: 15px !important;
  }

  .border-radius-20 {
    border-radius: 20px !important;
  }

  /* fonts */
  /* fonts size classes */
  .fs-14 {
    font-size: 14px;
  }

  .fs-12 {
    font-size: 12px;
  }

  /* tags */
  h1 {
    font-size: 45px;
    font-family: 'Anton', sans-serif;
    font-weight: 400;
    font-style: normal;
  }

  h2 {
    font-size: 35px;
    font-family: 'Anton', sans-serif;
    font-weight: 400;
    font-style: normal;
  }

  h3 {
    font-size: 23px;
    font-family: 'Average Sans', sans-serif;
    font-weight: 400;
    font-style: normal;
  }

  h4 {
    font-size: 19px;
    font-family: 'Roboto', sans-serif;
    font-weight: 400;
    font-style: normal;
  }

  h5 {
    font-size: 19px;
    font-family: 'Roboto', sans-serif;
    font-weight: 500;
    font-style: normal;
  }

  h6 {
    font-size: 16px;
    font-family: 'Roboto', sans-serif;
    font-weight: 500;
    font-style: normal;
  }

  label {
    font-size: 19px;
    font-family: 'Roboto', sans-serif;
    font-weight: 500;
    font-style: normal;
  }

  p {
    font-size: 14px;
    font-family: 'Roboto', sans-serif;
    font-weight: 400;
    font-style: normal;
  }

  input {
    padding: 4px;
  }

  input::placeholder {
    font-size: 14px;
    font-family: 'Roboto', sans-serif;
    font-weight: 400;
    font-style: italic;
  }

  button {
    font-size: 14px !important;
  }

  input:focus,
  select:focus,
  textarea:focus,
  button:focus {
    outline: none;
  }

  /* buttons / boutons */
  /* set all v-btn text to lower case / applique le texte de tous les v-btn en minuscule */
  .v-btn {
    text-transform: none !important;
    letter-spacing: inherit !important;
    font-size: 12px !important;
  }

  /* ✅ MOBILE : petits smartphones (iPhone SE, etc.) */
  @media screen and (max-width: 480px) {
    .content-container {
      margin: 0 !important;
      max-width: 100vw !important;
      width: 100vw !important;
      padding: 0 !important;
    }
  }

  /* ✅ MOBILE LARGE : smartphones standards (iPhone 12, Galaxy S21, etc.) */
  @media screen and (min-width: 481px) and (max-width: 767px) {
    /* body {
      background-color: lightgreen;
    } */
    .content-container {
      max-width: 481px;
    }
  }

  /* ✅ TABLETTE : format portrait (iPad, Galaxy Tab) */
  @media screen and (min-width: 768px) and (max-width: 1023px) {
    /* body {
      background-color: yellow;
    } */
    .content-container {
      max-width: 768px;
    }
  }

  /* ✅ TABLETTE LARGE / PETIT DESKTOP : transition vers écran large */
  @media screen and (min-width: 1024px) and (max-width: 1279px) {
    /* body {
      background-color: lightcoral;
    } */
    .content-container {
      max-width: 1080px;
    }
  }

  /* ✅ DESKTOP STANDARD : PC portable / écrans normaux */
  @media screen and (min-width: 1280px) and (max-width: 1439px) {
    /* body {
      background-color: blue;
    } */
    .content-container {
      max-width: 1080px;
    }
  }

  /* ✅ TON ÉCRAN DE BASE : 1440px */
  @media screen and (min-width: 1440px) and (max-width: 1919px) {
    /* body {
      background-color: orange;
    } */
    .content-container {
      max-width: 1080px;
    }
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    /* body {
      background-color: red;
    } */
    .content-container {
      max-width: 1080px;
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    /* body {
      background-color: purple;
    } */
    .content-container {
      max-width: 1440px;
    }
  }
  /* Styles optimisés pour le loader */
  .optimized-loader {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .loader-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #f6b337;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loader-text {
    margin-top: 20px;
    font-size: 16px;
    color: #333;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
</style>
