export default {
  state: {
    posts: [],
    comments: [],
  },
  mutations: {
    addPost(state, post) {
      state.posts.push(post);
    },
    removePost(state, postId) {
      state.posts = state.posts.filter((post) => post.id !== postId);
    },
    addCommentInStore(state, { postId, newComment }) {
      const postIndex = state.posts.findIndex((post) => post.id === postId);
      if (postIndex !== -1) {
        const post = state.posts[postIndex];
        if (!Array.isArray(post.commentList)) {
          post.commentList = [];
        }
        post.commentList = [...post.commentList, newComment];
        post.comments = (post.comments || 0) + 1;
        state.posts = [
          ...state.posts.slice(0, postIndex),
          post,
          ...state.posts.slice(postIndex + 1),
        ];
      }
    },
    removeCommentFromStore(state, commentId) {
      state.comments = state.comments.filter(
        (comment) => comment.id !== commentId
      );
    },
    setPosts(state, posts) {
      state.posts = posts.map((post) => ({
        ...post,
        isLiked: post.likers.some((liker) => liker.id === state.currentUser.id),
      }));
    },
    incrementLikes(state, postId) {
      const post = state.posts.find((p) => p.id === postId);
      if (post) post.likes++;
    },
    decrementLikes(state, postId) {
      const post = state.posts.find((p) => p.id === postId);
      if (post && post.likes > 0) post.likes--;
    },
  },
  actions: {
    handleAddPost({ commit }, post) {
      commit('addPost', post);
    },
    handleRemovePost({ commit }, postId) {
      commit('removePost', postId);
    },
    handleAddComment({ commit }, { postId, newComment }) {
      commit('addCommentInStore', { postId, newComment });
    },
    handleRemoveComment({ commit }, commentId) {
      commit('removeCommentFromStore', commentId);
    },
  },
  getters: {
    getPosts: (state) => state.posts,
    getComments: (state) => state.comments,
  },
};
