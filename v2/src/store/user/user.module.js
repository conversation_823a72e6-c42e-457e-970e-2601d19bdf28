import { isCandidateFavoriteById } from '@/services/favoriteProfil.service';
import { getJobOffers, getCandidatesForOffer } from '@/services/search.service';
import { getUser } from '../../services/account.service';
import { handleWebSocketUserConnection } from '../../utils/webSocketUtils';

export default {
  state: {
    webSocketUserConnection: null,
    isLoggedIn: false,
    isGoogle: false,
    posts: [],
    comments: [],
    user: {
      role: '',
      conversation: [],
      alerte: [],
      favorie_job: [],
    },
  },
  mutations: {
    setWebSocketUserConnection(state, webSocket) {
      state.webSocketUserConnection = webSocket;
    },
    setSelectedJobOffer(state, payload) {
      state.selectedJobOffer = payload;
    },
    setSelectedCandidate(state, payload) {
      state.selectedCandidate = payload;
    },
    updateUser(state, { type, payload }) {
      if (type) {
        if (!Array.isArray(state.user[type])) {
          state.user[type] = []; // Initialise en tant que tableau si ce n'est pas un tableau
        }
        state.user[type] = [...state.user[type], payload];
      } else {
        state.user = payload;
      }
    },
    setJobOffers(state, jobOffers) {
      state.jobOffers = jobOffers;
    },
    updateUserRole(state, role) {
      state.user.role = role;
    },
    resetUser(state) {
      state.user = { role: '' };
    },
    deleteUserPropreties(state, { type, payload }) {
      state.user[type] = state.user[type].filter((list) => list.id !== payload);
    },
    deleteUser(state) {
      //console.log("Réinitialisation de l'utilisateur");
      state.user = {};
    },

    // alert
    addAlert(state, payload) {
      state.user.alerte = [...state.user.alerte, payload];
    },
    updateAlert(state, payload) {
      if (!Array.isArray(state.user.alerte)) {
        state.user.alerte = [];
      }

      // Créer une copie de state.user.alerte
      const alerteCopy = [...state.user.alerte];

      // Trouver l'index de l'alerte à mettre à jour
      const alertIndex = alerteCopy.findIndex(
        (alerte) => alerte.id === payload.id
      );

      if (alertIndex !== -1) {
        // Remplacer l'alerte existante par la nouvelle dans la copie
        alerteCopy[alertIndex] = payload;
        alerteCopy.pop();
      } else {
        // Ajouter une nouvelle alerte à la copie
        alerteCopy.push(payload);
      }

      // Mettre à jour state.user.alerte avec la copie modifiée
      state.user.alerte = alerteCopy;
    },
    // favorite
    addFavoris(state, payload) {
      state.user.favorie_job = [
        ...state.user.favorie_job,
        { job_offer: payload },
      ];
    },
    deleteFavoris(state, payload) {
      state.user.favorie_job = state.user.favorie_job.filter(
        (job) => job.job_offer.id !== payload
      );
    },
    setLoggedIn(state, payload) {
      state.isLoggedIn = payload;
    },
  },
  actions: {
    // Ce websocket est utilisé pour écouter les notifications de l'utilisateur et pour definir son état de connection
    initializeUserConnectionWebSocket({ state, commit }) {
      if (state.webSocket) {
        //console.log('webSocket deja initialise');
        return;
      }
      const userId = state.user.id;
      if (!userId) {
        //console.error("Aucun ID d'utilisateur trouvé.");
        return;
      }
      const { webSocket } = handleWebSocketUserConnection(userId);
      if (!webSocket) {
        console.warning("imposible d'établir la connection webSocket");
        return;
      }
      commit('setWebSocketUserConnection', webSocket);
      webSocket.onopen = () => {
        //console.log('webSocket de connection établie');
      };
      webSocket.onclose = () => {
        //console.log('WebSocket déconnecté');
        commit('setWebSocketUserConnection', null);
      };
    },
    async fetchUser({ commit }, payload) {
      try {
        let userData = await getUser();
        if (!userData || !userData.type_user) {
          commit('setLoggedIn', false);
          commit('resetUser');
          return;
        }
        let jobOffers = [];
        if (userData.type_user === 'recruiter') {
          const recruteurJobOffers = await getJobOffers();
          userData = { ...userData, recruteurJobOffers };

          if (recruteurJobOffers && Array.isArray(recruteurJobOffers.results)) {
            jobOffers = await Promise.all(
              recruteurJobOffers.results.map(async (offer) => {
                try {
                  const candidates = await getCandidatesForOffer(offer.id);
                  return { ...offer, postulants: candidates.postulants || [] };
                } catch (error) {
                  //console.error(`Erreur pour l'offre ID ${offer.id}:`, error);
                  return { ...offer, postulants: [] };
                }
              })
            );
          }
        }
        if (userData.type_user === 'recruiter') {
          const recruteurFavorites = await isCandidateFavoriteById();
          userData = { ...userData, recruteurFavorites };
        }
        commit('setJobOffers', jobOffers);
        commit('updateUser', { type: null, payload: userData });
        commit('setLoggedIn', true);
        commit('setGoogleConnected', payload);
      } catch (error) {
        commit('setLoggedIn', false);
        commit('resetUser');
        //console.log('error store fetchUser', error);
      }
    },

    /**
     * handle user change in store
     * @param {String} type - type of user data : alert, candidature, conversation, favoris
     * - set to null for whole user object
     * @param {Object} payload - user data
     */
    handleUserChange({ commit }, { type, payload }) {
      commit('updateUser', { type, payload });
    },
    handleUserRoleChange({ commit }, role) {
      commit('updateUserRole', role);
    },
    handleDeleteUserPropreties({ commit }, { type, payload }) {
      commit('deleteUserPropreties', { type, payload });
    },
    logout({ commit }) {
      //console.log('Action logout appelée');
      // Nettoyer tous les formats de tokens possibles
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('token'); // Ancien format

      // Réinitialiser l'utilisateur
      commit('deleteUser');
    },
    // alert
    /**
     * Add a new alert to the user's alert list
     * @param {Object} payload - The alert to add
     * @returns {void}
     * */
    handleAddAlert({ commit }, payload) {
      commit('addAlert', payload);
    },

    // favorite
    /**
     * Add a job offer to the user's favorite list
     * @param {Object} payload - The job offer to add
     * @returns {void}
     */
    handleAddFavoris({ commit }, payload) {
      commit('addFavoris', payload);
    },
    /**
     * Remove a job offer from the user's favorite list
     * @param {Object} payload - The job ID offer to remove
     * @returns {void}
     * */
    handleDeleteFavoris({ commit }, payload) {
      commit('deleteFavoris', payload);
    },

    handleChangeAlert({ commit }, payload) {
      commit('updateAlert', payload);
    },
  },
  getters: {
    getUser: (state) => {
      if (!state.user) return {};
      const userCopy = { ...state.user };
      // sort conversation by date modification
      if (Array.isArray(userCopy.conversation)) {
        userCopy.conversation = [...userCopy.conversation].sort((a, b) => {
          return new Date(b.date_modification) - new Date(a.date_modification);
        });
      }
      return userCopy;
    },
    getJobOffers: (state) => state.jobOffers || [],
    userRole: (state) => {
      return state.user?.type_user || '';
    },
  },
};
