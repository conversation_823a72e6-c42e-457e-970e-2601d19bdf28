import { defineStore } from 'pinia';
import { fetchChatUsers, fetchConversations } from '../services/messagerieApi';

export const useMessagerieStore = defineStore('messagerie', {
  state: () => ({
    users: JSON.parse(localStorage.getItem('messagerie_users') || '[]'),
    usersLoaded: localStorage.getItem('messagerie_usersLoaded') === 'true',
    conversations: [],
    conversationsLoaded: false,
    receivedInvitations: [], // Ajout invitations reçues
    sentInvitations: [],     // Ajout invitations envoyées
  }),
  actions: {
    async loadUsers() {
      if (!this.usersLoaded) {
        this.users = await fetchChatUsers();
        this.usersLoaded = true;
        localStorage.setItem('messagerie_users', JSON.stringify(this.users));
        localStorage.setItem('messagerie_usersLoaded', 'true');
      }
    },
    async loadConversations() {
      if (!this.conversationsLoaded) {
        this.conversations = await fetchConversations();
        this.conversationsLoaded = true;
      }
    },
    async loadInvitations(friendsInvitationsApi) {
      // friendsInvitationsApi doit être la fonction asynchrone qui fetch les invitations
      const data = await friendsInvitationsApi();
      this.receivedInvitations = data.received_invitations || [];
      this.sentInvitations = data.sent_invitations || [];
      // Optionnel : persist in localStorage si besoin
    },
    addFriend(newFriend) {
      // Ajoute un ami à la session courante
      this.users.push(newFriend);
      localStorage.setItem('messagerie_users', JSON.stringify(this.users));
    },
    addReceivedInvitation(invitation) {
      this.receivedInvitations.push(invitation);
    },
    addSentInvitation(invitation) {
      this.sentInvitations.push(invitation);
    },
    removeReceivedInvitation(invitationId) {
      this.receivedInvitations = this.receivedInvitations.filter(inv => inv.id !== invitationId);
    },
    removeSentInvitation(invitationId) {
      this.sentInvitations = this.sentInvitations.filter(inv => inv.id !== invitationId);
    },
    clearUsers() {
      this.users = [];
      this.usersLoaded = false;
      localStorage.removeItem('messagerie_users');
      localStorage.removeItem('messagerie_usersLoaded');
    },
    clearConversations() {
      this.conversations = [];
      this.conversationsLoaded = false;
    },
    clearInvitations() {
      this.receivedInvitations = [];
      this.sentInvitations = [];
    },
  },
});
