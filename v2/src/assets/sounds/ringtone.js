// Fichier contenant la référence à la sonnerie d'appel
// Le fichier .wav est placé dans le dossier public/sounds/

// Chemins vers les fichiers de sonnerie
const RINGTONE_PATHS = [
  '/sounds/sound2.wav',
  '/sounds/notification.wav'
];

// Fonction pour créer un élément audio avec la sonnerie
export function createRingtone() {
  //console.log('%c[APPEL] Création de la sonnerie...', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');

  // Essayer chaque chemin de sonnerie jusqu'à ce qu'un fonctionne
  for (const ringtonePath of RINGTONE_PATHS) {
    try {
      //console.log('%c[APPEL] Tentative avec le chemin:', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;', ringtonePath);

      // Créer un nouvel élément audio
      const audio = new Audio(ringtonePath);

      // Configurer les options de l'élément audio
      audio.loop = true;
      audio.preload = 'auto';
      audio.volume = 1.0; // Volume maximum

      // Ajouter des gestionnaires d'événements pour déboguer
      audio.addEventListener('canplaythrough', () => {
        //console.log('%c[APPEL] Sonnerie chargée et prête à être jouée', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
      });

      audio.addEventListener('error', (e) => {
        //console.error('%c[APPEL] Erreur lors du chargement de la sonnerie:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', e);
      });

      // Précharger le fichier audio pour éviter les délais lors de la lecture
      audio.load();

      //console.log('%c[APPEL] Sonnerie initialisée avec succès', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');

      // Stocker le chemin utilisé pour référence
      audio.ringtonePath = ringtonePath;

      return audio;
    } catch (error) {
      //console.error('%c[APPEL] Erreur lors de l\'initialisation de la sonnerie avec le chemin ' + ringtonePath + ':', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
    }
  }

  // Si aucun des chemins n'a fonctionné, créer un élément audio avec une sonnerie par défaut
  console.warn('%c[APPEL] Aucun des chemins de sonnerie n\'a fonctionné, création d\'une sonnerie par défaut', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');

  try {
    // Créer un élément audio avec un son de base64 intégré
    const audio = new Audio();
    audio.loop = true;
    audio.preload = 'auto';
    audio.volume = 1.0;

    // Utiliser un son très simple encodé en base64
    // C'est un bip court qui sera joué en boucle
    audio.src = 'data:audio/wav;base64,UklGRl9vT19XQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YU9vT18AAAAA//////////////////////////////////////////8=';

    // Précharger le fichier audio
    audio.load();

    //console.log('%c[APPEL] Sonnerie par défaut créée', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');

    return audio;
  } catch (finalError) {
    //console.error('%c[APPEL] Erreur fatale lors de la création de la sonnerie par défaut:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', finalError);

    // Créer un élément audio vide en dernier recours
    const emptyAudio = new Audio();
    emptyAudio.loop = true;

    return emptyAudio;
  }
}

// Fonction pour vérifier si la sonnerie fonctionne
export function testRingtone(ringtone) {
  if (!ringtone) {
    //console.error('%c[APPEL] Impossible de tester la sonnerie: objet non défini', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
    return false;
  }

  try {
    //console.log('%c[APPEL] Test de la sonnerie...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

    // Vérifier l'état de la sonnerie
    //console.log('%c[APPEL] État de la sonnerie:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', {
    //  paused: ringtone.paused,
    //  currentTime: ringtone.currentTime,
    //  duration: ringtone.duration,
    //  readyState: ringtone.readyState,
    //  networkState: ringtone.networkState,
    //  src: ringtone.src
    //});

    // Tester la lecture de la sonnerie
    const playPromise = ringtone.play();

    if (playPromise !== undefined) {
      playPromise
        .then(() => {
          //console.log('%c[APPEL] Test de sonnerie réussi', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');

          // Arrêter la sonnerie après un court délai
          setTimeout(() => {
            ringtone.pause();
            ringtone.currentTime = 0;
            //console.log('%c[APPEL] Sonnerie arrêtée après test', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
          }, 500);

          return true;
        })
        .catch(error => {
          //console.error('%c[APPEL] Erreur lors du test de la sonnerie:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);

          // Si l'erreur est liée à l'interaction utilisateur, on l'ignore
          if (error.name === 'NotAllowedError') {
            //console.log('%c[APPEL] Le test de sonnerie nécessite une interaction utilisateur, ce qui est normal', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');
          }

          return false;
        });
    } else {
      console.warn('%c[APPEL] La méthode play() n\'a pas retourné de promesse', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');
      return false;
    }
  } catch (error) {
    //console.error('%c[APPEL] Erreur lors du test de la sonnerie:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
    return false;
  }

  return true;
}
