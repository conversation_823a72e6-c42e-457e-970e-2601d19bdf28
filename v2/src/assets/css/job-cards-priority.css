/* 
 * Styles globaux pour le système de priorité des cartes d'offres d'emploi
 * Inspiré du design HelloWork avec système de priorité Thanks-Boss
 */

/* Styles de base pour toutes les cartes d'offres d'emploi */
.job-card-base {
  background-color: #ffffff;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
  overflow: hidden;
}

.job-card-base:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
  border-color: #d1d5db;
}

/* Styles de priorité pour les offres Thanks-Boss (14 jours) */
.priority-job {
  border: 2px solid #3b82f6 !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15) !important;
  position: relative;
}



.priority-job:hover {
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.25) !important;
  border-color: #2563eb !important;
}

.priority-job .card-header {
  background-color: #eff6ff !important;
  border-bottom-color: #3b82f6 !important;
}

/* Animation pour attirer l'attention sur les cartes prioritaires */
@keyframes priority-pulse {
  0% {
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
  }
  50% {
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
  }
  100% {
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
  }
}

.priority-job.animate-priority {
  animation: priority-pulse 2s ease-in-out infinite;
}

/* Styles pour les cartes en mode compact */
.compact-priority-job {
  border: 2px solid #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}



/* Styles pour les cartes mini */
.mini-priority-job {
  border: 2px solid #3b82f6;
  box-shadow: 0 1px 4px rgba(59, 130, 246, 0.15);
}



/* Styles pour les titres des offres prioritaires */
.priority-job .job-title {
  color: #1e40af !important;
  font-weight: 600 !important;
}

/* Styles pour les boutons des offres prioritaires */
.priority-job .primary-button {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
  border: none !important;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3) !important;
}

.priority-job .primary-button:hover {
  background: linear-gradient(135deg, #2563eb, #1e3a8a) !important;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4) !important;
}

/* Styles responsives */
@media (max-width: 768px) {
  /* Styles responsives pour les cartes prioritaires sans logos */
}

/* Styles pour les indicateurs de source */
.source-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: #f3f4f6;
  color: #6b7280;
}

.source-indicator.thanks-boss {
  background-color: #eff6ff;
  color: #3b82f6;
  border: 1px solid #bfdbfe;
}

.source-indicator.france-travail {
  background-color: #fef3c7;
  color: #d97706;
  border: 1px solid #fde68a;
}

/* Styles pour les badges de nouveauté */
.new-job-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: #10b981;
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 3px 6px;
  border-radius: 4px;
  z-index: 10;
}

/* Amélioration de la lisibilité des textes */
.job-card-base .job-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  line-height: 1.4;
  margin-bottom: 4px;
}

.job-card-base .job-company {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.job-card-base .job-location {
  font-size: 13px;
  color: #9ca3af;
  display: flex;
  align-items: center;
  gap: 4px;
}

.job-card-base .job-salary {
  font-size: 14px;
  color: #059669;
  font-weight: 600;
}

.job-card-base .job-contract {
  font-size: 12px;
  color: #6b7280;
  background-color: #f9fafb;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

/* Styles pour les descriptions tronquées */
.job-description {
  font-size: 14px;
  color: #4b5563;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Styles pour les métadonnées des offres */
.job-metadata {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.job-metadata-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6b7280;
}

.job-metadata-item svg,
.job-metadata-item img {
  width: 14px;
  height: 14px;
  opacity: 0.7;
}
