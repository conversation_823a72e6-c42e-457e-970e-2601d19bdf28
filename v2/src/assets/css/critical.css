/* Styles critiques pour le rendu initial */

/* Reset de base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Variables CSS essentielles */
:root {
  /* Couleurs principales */
  --white-100: #f5f2ef;
  --white-200: #fffdfc;
  --black-100: #26282b;
  --yellow-100: #f6b337;
  --blue-100: #00a58e;
  
  /* Polices */
  --font-family-primary: 'Roboto', sans-serif;
  --font-family-secondary: '<PERSON>', sans-serif;
  
  /* Espacement */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* Tailles de police */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 24px;
  
  /* Rayons de bordure */
  --border-radius-sm: 5px;
  --border-radius-md: 10px;
  --border-radius-lg: 20px;
}

/* Styles de base pour le corps et l'application */
html, body {
  font-family: var(--font-family-primary);
  font-weight: 400;
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--black-100);
  background-color: var(--white-100);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: smooth;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Conteneur principal */
.content-container {
  flex: 1;
  max-width: 1440px;
  margin: 0 auto;
  width: 100%;
}

/* Loader */
.loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}

/* Utilitaires flexbox */
.d-flex {
  display: flex;
}

.j-content {
  justify-content: center;
}

.a-center {
  align-items: center;
}

.flex-column {
  flex-direction: column;
}

/* Utilitaires de padding */
.padding-container {
  padding-inline: 3vw;
}

/* Utilitaires de texte */
.text-center {
  text-align: center;
}

.text-underline {
  text-decoration: underline;
}

/* Utilitaires de bordure */
.border-radius-10 {
  border-radius: 10px;
}

.border-radius-20 {
  border-radius: 20px;
}

/* Styles de base pour les boutons */
button {
  cursor: pointer;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  border: none;
  outline: none;
}

/* Styles de base pour les entrées */
input, select, textarea {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  outline: none;
}

/* Styles de base pour les liens */
a {
  color: inherit;
  text-decoration: none;
}

/* Styles pour les titres */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
}

h1 {
  font-size: 45px;
  font-family: var(--font-family-secondary);
  font-weight: 400;
}

/* Styles pour les images */
img {
  max-width: 100%;
  height: auto;
}

/* Styles pour les placeholders */
.placeholder {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Media queries de base */
@media screen and (max-width: 480px) {
  .content-container {
    max-width: 480px;
    margin: 0 20px;
  }
}

@media screen and (min-width: 481px) and (max-width: 767px) {
  .content-container {
    max-width: 767px;
    margin: 0 20px;
  }
}

@media screen and (min-width: 768px) and (max-width: 1023px) {
  .content-container {
    max-width: 1023px;
    margin: 0 20px;
  }
}

@media screen and (min-width: 1024px) {
  .content-container {
    max-width: 1440px;
    margin: 0 auto;
  }
}
