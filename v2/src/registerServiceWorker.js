/* eslint-disable no-console */

import { register } from 'register-service-worker';

if (process.env.NODE_ENV === 'production') {
  register(`${process.env.BASE_URL}service-worker.js`, {
    ready() {
      //console.log(
      //  'App is being served from cache by a service worker.\n' +
      //    'For more details, visit https://goo.gl/AFskqB'
      //);
    },
    registered(registration) {
      //console.log('Service worker has been registered.');

      // Vérifier les mises à jour toutes les 60 minutes
      setInterval(
        () => {
          registration.update();
        },
        1000 * 60 * 60
      );
    },
    cached() {
      //console.log('Content has been cached for offline use.');
    },
    updatefound() {
      //console.log('New content is downloading.');
    },
    updated(registration) {
      //console.log('New content is available; please refresh.');

      // Émettre un événement pour informer l'application qu'une mise à jour est disponible
      document.dispatchEvent(
        new CustomEvent('swUpdated', { detail: registration })
      );
    },
    offline() {
      //console.log(
      //  'No internet connection found. App is running in offline mode.'
      //);
    },
    error(error) {
      //console.error('Error during service worker registration:', error);
    },
  });
}

// Note: Le code de configuration du service worker doit être placé dans un fichier séparé (service-worker.js)
// qui sera généré par workbox-webpack-plugin ou similaire.
// Ce fichier registerServiceWorker.js est uniquement utilisé pour enregistrer le service worker.
