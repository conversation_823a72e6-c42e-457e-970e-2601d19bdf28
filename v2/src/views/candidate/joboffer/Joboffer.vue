<template>
  <main class="job-detail-page" :class="getPriorityClass(joboffer)">
    <!-- Container principal selon la maquette -->
    <div class="job-container">
      <!-- Header avec logo et informations principales -->
      <div class="job-header-card" :class="{ 'priority-job': isJobPriority(joboffer) }">
        <!-- Badge de priorité pour Thanks-Boss -->
        <div v-if="isJobPriority(joboffer)" class="priority-badge">
          <img src="@/assets/icons/tb-logo-borderblack.svg" alt="Thanks-Boss" class="priority-icon" />
          <span>Offre prioritaire</span>
        </div>

        <div class="header-main">
          <!-- Logo entreprise -->
          <div class="company-logo-section">
            <img
              v-if="joboffer && (joboffer.logo_url || joboffer.logo_img)"
              :src="getImgPath(joboffer.logo_url || joboffer.logo_img)"
              alt="Logo entreprise"
              class="company-logo"
            />
            <img
              v-else-if="joboffer.source === 'Thanks_boss'"
              src="@/assets/icons/tb-logo-borderblack.svg"
              alt="Thanks-Boss"
              class="company-logo"
            />
            <img
              v-else-if="joboffer.source === 'FT'"
              src="@/assets/icons/FT-logo.svg"
              alt="France Travail"
              class="company-logo"
            />
            <img
              v-else
              src="@/assets/icons/company-logo.png"
              alt="Entreprise"
              class="company-logo"
            />
          </div>

          <!-- Informations du poste -->
          <div class="job-info-section">
            <h1 class="job-title">{{ joboffer.title }}</h1>
            <h2 class="company-name">{{ joboffer.company || joboffer.nom_recruteur }}</h2>

            <!-- Actions favoris et partage -->
            <div class="job-actions-top">
              <button
                class="action-btn favorite-action"
                :class="{ 'is-favorite': jobofferIsFavorite }"
                @click="handleFavoriteJobClick(joboffer.id)"
              >
                <img
                  :src="jobofferIsFavorite
                    ? require('@/assets/icons/heart-filled.svg')
                    : require('@/assets/icons/heart-empty.svg')"
                  alt="Favoris"
                  class="action-icon"
                />
                <span>{{ jobofferIsFavorite ? 'Mis en favoris' : 'Mettre en favoris' }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Section Informations de base (selon maquette) -->
      <div class="job-info-details">
        <div class="info-item">
          <img src="@/assets/icons/calendar.svg" alt="Date" class="info-icon" />
          <span class="info-label">Publié le :</span>
          <span class="info-value">{{ formatDate(joboffer.created_at) }}</span>
        </div>
        <div class="info-item">
          <img src="@/assets/icons/map-pin.svg" alt="Localisation" class="info-icon" />
          <span class="info-label">Localisation</span>
          <span class="info-value">{{ joboffer.local || 'Non renseigné' }}</span>
        </div>
        <div class="info-item">
          <img src="@/assets/icons/suitcase-mini.svg" alt="Type de contrat" class="info-icon" />
          <span class="info-label">Type de contrat</span>
          <span class="info-value">{{ joboffer.contract || 'Non renseigné' }}</span>
        </div>
        <div class="info-item">
          <img src="@/assets/icons/carte-id-icon.svg" alt="Niveau d'expérience" class="info-icon" />
          <span class="info-label">Niveau d'expérience</span>
          <span class="info-value">{{ joboffer.expérience || 'Non renseigné' }}</span>
        </div>
      </div>

      <!-- Section Compétences requises (selon maquette) - First -->
      <div v-if="hasSavoirPro" class="competences-section">
        <h3 class="section-title">Compétences requises</h3>
        <div class="competences-chips">
          <span
            v-for="(savoir_pro, index) in processedSavoirPro"
            :key="`savoir-pro-${index}`"
            class="competence-chip savoir-pro"
          >
            {{ savoir_pro }}
          </span>
        </div>
      </div>

      <!-- Section Savoir-être (selon maquette) - Second -->
      <div v-if="hasSavoirEtre" class="competences-section">
        <h3 class="section-title">Savoir-être</h3>
        <div class="competences-chips">
          <span
            v-for="(savoir_etre, index) in processedSavoirEtre"
            :key="`savoir-etre-${index}`"
            class="competence-chip savoir-etre"
          >
            {{ savoir_etre }}
          </span>
        </div>
      </div>

      <!-- Section Langues (selon maquette) - Third -->
      <div v-if="hasLangues" class="competences-section">
        <h3 class="section-title">Langues</h3>
        <div class="competences-chips">
          <span
            v-for="(langue, index) in processedLangues"
            :key="`langue-${index}`"
            class="competence-chip langue"
          >
            {{ langue }}
          </span>
        </div>
      </div>

      <!-- Section Formation demandée (selon maquette) -->
      <div v-if="hasFormationDemandee" class="competences-section">
        <h3 class="section-title">Formation demandée</h3>
        <div class="competences-chips">
          <span
            v-for="(formation, index) in processedFormationDemandee"
            :key="`formation-demandee-${index}`"
            class="competence-chip formation-demandee"
          >
            {{ formation }}
          </span>
        </div>
      </div>

      <!-- Section Formation (selon maquette) -->
      <div v-if="hasFormation" class="competences-section">
        <h3 class="section-title">Formation</h3>
        <div class="competences-chips">
          <span
            v-for="(formation, index) in processedFormation"
            :key="`formation-${index}`"
            class="competence-chip formation"
          >
            {{ formation }}
          </span>
        </div>
      </div>

      <!-- Section Permis (selon maquette) -->
      <div v-if="hasPermis" class="competences-section">
        <h3 class="section-title">Permis</h3>
        <div class="competences-chips">
          <span
            v-for="(permis, index) in processedPermis"
            :key="`permis-${index}`"
            class="competence-chip permis"
          >
            {{ permis }}
          </span>
        </div>
      </div>
      <!-- Boutons d'action principaux (selon maquette) -->
      <div class="main-actions">
        <PrimaryNormalButton
          :disabled="userHasCandidate || isApplying"
          :textContent="getApplyButtonText()"
          @click="handleJobApplication(joboffer)"
          class="apply-main-btn"
        />
      </div>
      <!-- Section Poste à pourvoir -->
      <section class="content-section">
        <h3 class="section-title">Poste à pourvoir</h3>
        <div
          class="section-content"
          v-html="isMarkdown ? formattedMarkdownJobDescription : formattedJobDescription"
        ></div>
      </section>

      <!-- Section Qui sommes-nous ? -->
      <section v-if="joboffer.source != 'FT' && joboffer.authorInfo && joboffer.authorInfo.company_details" class="content-section">
        <h3 class="section-title">Qui sommes-nous ?</h3>
        <div class="section-content">
          <p>{{ joboffer.authorInfo.company_details }}</p>
        </div>
      </section>

      <!-- Section Pourquoi nous rejoindre ? -->
      <section v-if="joboffer.source != 'FT' && joboffer.authorInfo && joboffer.authorInfo.rejoindre" class="content-section">
        <h3 class="section-title">Pourquoi nous rejoindre ?</h3>
        <div class="section-content">
          <p>{{ joboffer.authorInfo.rejoindre }}</p>
        </div>
      </section>

      <!-- Section Nos avantages -->
      <section v-if="joboffer.source != 'FT' && joboffer.authorInfo && joboffer.authorInfo.avantages" class="content-section">
        <h3 class="section-title">Nos avantages</h3>
        <div class="avantages-chips">
          <Chip
            v-for="(avantage, index) in joboffer.authorInfo.avantages.split(',')"
            :key="`avantage-${index}`"
            :textContent="avantage.trim()"
            class="avantage-chip"
          />
        </div>
      </section>

      <!-- Section Le processus de recrutement -->
      <section v-if="joboffer.source != 'FT' && joboffer.authorInfo && joboffer.authorInfo.processus_recrutement" class="content-section">
        <h3 class="section-title">Le processus de recrutement</h3>
        <div class="section-content">
          <p>{{ joboffer.authorInfo.processus_recrutement }}</p>
        </div>
      </section>

      <!-- Boutons d'action du bas (répétition selon maquette) -->
      <div class="bottom-actions">
        <PrimaryNormalButton
          :disabled="userHasCandidate || isApplying"
          :textContent="getApplyButtonText()"
          @click="handleJobApplication(joboffer)"
          class="apply-bottom-btn"
        />
      </div>
    </div>

    <!-- Application Modal -->
    <div
      v-if="selectedJobForApplication"
      class="modal-overlay"
      @click.self="closeApplicationModal"
    >
      <JobApplication
        :joboffer="selectedJobForApplication"
        @close-modal="closeApplicationModal"
      />
    </div>

    <BackToTopArrow />
  </main>
</template>

<script>
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import Chip from '@/components/chips/Chip.vue';
  import JobApplication from '@/components/modal/application/JobApplication.vue';
  import { getUserById } from '@/services/account.service.js';
  import {
    addFavoriteJob,
    removeFavoriteJob,
  } from '@/services/favoriteJob.service.js';
  import {
    getJobofferById,
    isJobFavoriteById,
  } from '@/services/job.service.js';
  import { TELETRAVAIL_FIELDS } from '@/utils/base/teletravail.js';
  import getImgPath from '@/utils/imgpath.js';
  import gotoPage from '@/utils/router.js';
  import { toaster } from '@/utils/toast/toast.js';
  import { isJobPriority, getPriorityClass } from '@/utils/jobNavigation.js';
  import { useRoute, useRouter } from 'vue-router';
  import { validateNotEmpty } from '../../../utils/validationRules';
  import store from '../../../store';

  export default {
    name: 'JobofferPage',
    components: {
      BackToTopArrow,
      PrimaryNormalButton,
      Chip,
      JobApplication,
    },
    props: {
      userIsRecruter: {
        type: Boolean,
        default: false,
      },
      user: {
        type: Object,
        required: true,
      },
    },

    data() {
      return {
        userHasCandidate: false,
        jobofferIsFavorite: false,
        teletravail: '',
        showSkills: true,
        isApplying: false,
        showSoftSkills: true,
        showLogo: true,
        joboffer: {
          authorInfo: {},
        },
        notEmptyRules: [(v) => validateNotEmpty(v) || true],
        selectedJobForApplication: null,
      };
    },
    computed: {
      // Vérifier si on a des savoir-faire
      hasSavoirPro() {
        return this.processedSavoirPro.length > 0;
      },

      // Vérifier si on a des savoir-être
      hasSavoirEtre() {
        return this.processedSavoirEtre.length > 0;
      },

      // Traiter les savoir-faire pour gérer différents formats (maintenant affiche savoir_etre)
      processedSavoirPro() {
        if (!this.joboffer.savoir_etre) return [];

        // Si c'est une chaîne, la diviser par virgules
        if (typeof this.joboffer.savoir_etre === 'string') {
          return this.joboffer.savoir_etre
            .split(',')
            .map(item => item.trim())
            .filter(item => item.length > 0);
        }

        // Si c'est un tableau d'objets avec libelle
        if (Array.isArray(this.joboffer.savoir_etre)) {
          return this.joboffer.savoir_etre
            .map(item => {
              if (typeof item === 'string') return item.trim();
              if (item && item.libelle) return item.libelle.trim();
              if (item && item.nom) return item.nom.trim();
              return String(item).trim();
            })
            .filter(item => item.length > 0);
        }

        return [];
      },

      // Traiter les savoir-être pour gérer différents formats (maintenant affiche savoir_pro)
      processedSavoirEtre() {
        if (!this.joboffer.savoir_pro) return [];

        // Si c'est une chaîne, la diviser par virgules
        if (typeof this.joboffer.savoir_pro === 'string') {
          return this.joboffer.savoir_pro
            .split(',')
            .map(item => item.trim())
            .filter(item => item.length > 0);
        }

        // Si c'est un tableau d'objets avec libelle
        if (Array.isArray(this.joboffer.savoir_pro)) {
          return this.joboffer.savoir_pro
            .map(item => {
              if (typeof item === 'string') return item.trim();
              if (item && item.libelle) return item.libelle.trim();
              if (item && item.nom) return item.nom.trim();
              return String(item).trim();
            })
            .filter(item => item.length > 0);
        }

        return [];
      },

      // Vérifier si on a une formation
      hasFormation() {
        return this.processedFormation.length > 0;
      },

      // Vérifier si on a une formation demandée
      hasFormationDemandee() {
        return this.processedFormationDemandee.length > 0;
      },

      // Vérifier si on a des langues
      hasLangues() {
        return this.processedLangues.length > 0;
      },

      // Vérifier si on a des permis
      hasPermis() {
        return this.processedPermis.length > 0;
      },

      // Traiter la formation pour gérer différents formats
      processedFormation() {
        if (!this.joboffer.formation_recruteur) return [];

        // Si c'est une chaîne, la diviser par virgules
        if (typeof this.joboffer.formation_recruteur === 'string') {
          return this.joboffer.formation_recruteur
            .split(',')
            .map(item => item.trim())
            .filter(item => item.length > 0);
        }

        // Si c'est un tableau d'objets avec libelle
        if (Array.isArray(this.joboffer.formation_recruteur)) {
          return this.joboffer.formation_recruteur
            .map(item => {
              if (typeof item === 'string') return item.trim();
              if (item && item.libelle) return item.libelle.trim();
              if (item && item.nom) return item.nom.trim();
              return String(item).trim();
            })
            .filter(item => item.length > 0);
        }

        return [];
      },

      // Traiter la formation demandée pour gérer différents formats
      processedFormationDemandee() {
        if (!this.joboffer.formation_demandee && !this.joboffer.formation_requise) return [];

        const formationField = this.joboffer.formation_demandee || this.joboffer.formation_requise;

        // Si c'est une chaîne, la diviser par virgules
        if (typeof formationField === 'string') {
          return formationField
            .split(',')
            .map(item => item.trim())
            .filter(item => item.length > 0);
        }

        // Si c'est un tableau d'objets avec libelle
        if (Array.isArray(formationField)) {
          return formationField
            .map(item => {
              if (typeof item === 'string') return item.trim();
              if (item && item.libelle) return item.libelle.trim();
              if (item && item.nom) return item.nom.trim();
              return String(item).trim();
            })
            .filter(item => item.length > 0);
        }

        return [];
      },

      // Traiter les langues pour gérer différents formats (avec suppression des doublons)
      processedLangues() {
        if (!this.joboffer.langue_recruteur) return [];

        const langueField = this.joboffer.langue_recruteur;
        let langues = [];

        // Si c'est une chaîne, la diviser par virgules
        if (typeof langueField === 'string') {
          langues = langueField
            .split(',')
            .map(item => item.trim())
            .filter(item => item.length > 0);
        }

        // Si c'est un tableau d'objets avec libelle ou langue
        else if (Array.isArray(langueField)) {
          langues = langueField
            .map(item => {
              if (typeof item === 'string') return item.trim();
              if (item && item.libelle) return item.libelle.trim();
              if (item && item.langue) return item.langue.trim();
              if (item && item.nom) return item.nom.trim();
              return String(item).trim();
            })
            .filter(item => item.length > 0);
        }

        // Supprimer les doublons en utilisant Set et convertir en minuscules pour la comparaison
        const uniqueLangues = [...new Set(langues.map(lang => lang.toLowerCase()))]
          .map(lowerLang => {
            // Retrouver la casse originale
            return langues.find(lang => lang.toLowerCase() === lowerLang);
          });

        return uniqueLangues;
      },

      // Traiter les permis pour gérer différents formats
      processedPermis() {
        if (!this.joboffer.permis_recruteur) return [];

        // Si c'est une chaîne, la diviser par virgules
        if (typeof this.joboffer.permis_recruteur === 'string') {
          return this.joboffer.permis_recruteur
            .split(',')
            .map(item => item.trim())
            .filter(item => item.length > 0);
        }

        // Si c'est un tableau d'objets avec libelle
        if (Array.isArray(this.joboffer.permis_recruteur)) {
          return this.joboffer.permis_recruteur
            .map(item => {
              if (typeof item === 'string') return item.trim();
              if (item && item.libelle) return item.libelle.trim();
              if (item && item.nom) return item.nom.trim();
              return String(item).trim();
            })
            .filter(item => item.length > 0);
        }

        return [];
      },
      formattedJobDescription() {
        if (!this.joboffer?.descriptif) return '';
        return this.joboffer.descriptif
          .split('\n')
          .filter((paragraph) => paragraph.trim())
          .map((paragraph) => {
            const trimmedParagraph = paragraph.trim();
            if (
              trimmedParagraph.includes('-') &&
              trimmedParagraph.includes(':')
            ) {
              const title = trimmedParagraph.split(':')[0];
              const restOfText = trimmedParagraph.split(':').slice(1).join(':');
              return `<h5 style="font-weight: 400;font-size: 1.125em; margin-top: 20px; margin-bottom: 10px;">${title}:</h5><p style="margin-top: 10px;">${restOfText.trim()}</p>`;
            }
            if (trimmedParagraph.includes(':')) {
              const title = trimmedParagraph.split(':')[0];
              const restOfText = trimmedParagraph.split(':').slice(1).join(':');
              return `<h4 style="font-weight: 500;font-size: 1.25em; margin-top: 20px; margin-bottom: 10px;">${title}:</h4><p style="margin-top: 10px;">${restOfText.trim()}</p>`;
            }
            if (trimmedParagraph.startsWith('*')) {
              return `<p style="margin-bottom: 6px;">${trimmedParagraph}</p>`;
            }

            return `<p style="margin-top: 15px; margin-bottom: 15px;">${trimmedParagraph}</p>`;
          })
          .join('');
      },
      formattedMarkdownJobDescription() {
        if (!this.joboffer?.descriptif) return '';

        let formattedText = this.joboffer?.descriptif;

        // 🔹 Convertir "#### Sous-Titre" en <h4>
        formattedText = formattedText.replace(/####\s(.*)/g, (match, p1) => {
          return `<h4 style="text-decoration: underline; margin: 20px 0 10px 0;">${p1}</h4>`;
        });

        // 🔹 Convertir "### Titre" en <h3>
        formattedText = formattedText.replace(/###\s(.*)/g, (match, p1) => {
          return `<h3 style="font-weight: bold;">${p1}</h3>`;
        });

        // 🔹 Convertir "**Texte en gras**" en <b>
        formattedText = formattedText.replace(/\*\*(.*?)\*\*/g, (match, p1) => {
          return `<b>${p1}</b>`;
        });

        // 🔹 Convertir les numéros "1." en pastilles alignées
        formattedText = formattedText.replace(
          /(\d+)\.\s(.+)/g,
          (match, p1, p2) => {
            return `<div style="display: flex; align-items: center; margin: 10px 0;">
            <span style=" display: flex; justify-content: center; align-items: center; font-weight: bold; font-size: 16px; color: #f7bc53; width: 25px; height: 25px; border: 2px solid #f7bc53; border-radius: 50%; flex-shrink: 0; text-align: center; line-height: 30px;">${p1}</span>
            <span style="margin-left: 10px;">${p2}</span>
          </div>`;
          }
        );

        // 🔹 Convertir les listes non numérotées "- Texte" en puces "• Texte"
        formattedText = formattedText.replace(/^\s*-\s(.+)/gm, (match, p1) => {
          return `<div style="display: flex; align-items: flex-start; margin-left: 20px; margin-top: 10px;">
            <span style="font-size: 18px; color: black; margin-right: 10px;">•</span>
            <span>${p1}</span>
          </div>`;
        });

        // 🔹 Convertir les liens email Markdown [texte](mailto:email) en liens HTML
        formattedText = formattedText.replace(
          /\[([^\]]+)\]\(mailto:([^)]+)\)/g,
          (match, p1, p2) => {
            return `<a href="mailto:${p2}" style="color: #007bff; text-decoration: underline;">${p1}</a>`;
          }
        );

        // 🔹 Supprimer les <br> entre les <div> (évite les sauts de ligne entre éléments de liste)
        formattedText = formattedText.replace(
          /<\/div>\s*<br>\s*<div/g,
          '</div><div>'
        );

        // 🔹 Supprimer les <br> qui suivent immédiatement un <div> (évite les espaces vides)
        formattedText = formattedText.replace(/<div[^>]*>\s*<br>/g, '<div>');

        // 🔹 Convertir les retours à la ligne (\n) en <br>, sauf après <h4> et entre les <div>
        formattedText = formattedText.replace(/\n(?!<\/h4>|<\/div>)/g, '<br>');

        // 🔹 Nettoyer les <br> après <h4> pour éviter un saut de ligne supplémentaire
        formattedText = formattedText.replace(/<\/h4><br>/g, '</h4>');

        return formattedText;
      },
      isMarkdown() {
        if (!this.joboffer || !this.joboffer.descriptif) return false;

        const text = this.joboffer.descriptif.trim();

        // Vérifie si le texte commence par un élément typique du Markdown
        const markdownPatterns = [
          /^#{1,6}\s/, // Titres Markdown: "# Titre", "## Titre"
          /^\*\s|\-\s/, // Listes "- item", "* item"
          /^\d+\.\s/, // Listes numérotées "1. item"
          /\*\*[^*]+\*\*/, // Gras "**texte**"
          /_[^_]+_/, // Italique "_texte_"
          /\[.+\]\(.+\)/, // Liens "[texte](url)"
          /`{1,3}[^`]+`{1,3}/, // Code inline "`code`"
        ];

        return markdownPatterns.some((pattern) => pattern.test(text));
      },
    },
    async mounted() {
      const router = useRouter();
      const route = useRoute();
      const jobId = route.params.id;
      try {
        this.scrollToTop();
        const response = await getJobofferById(jobId);
        //console.log('getJobofferById', response);
        if (!response) {
          router.replace('/404');
          return;
        }
        this.joboffer = response;



        // Ajout : valeurs par défaut pour les champs critiques si absents
        this.joboffer.title = this.joboffer.title || 'Métier non renseigné';
        this.joboffer.local = this.joboffer.local || 'Non renseigné';
        this.joboffer.contract = this.joboffer.contract || 'Non renseigné';
        this.joboffer.expérience = this.joboffer.expérience || 'Non renseigné';
        this.joboffer.descriptif = this.joboffer.descriptif || 'Aucune description.';

        // Initialiser les tableaux vides si pas de données
        this.joboffer.savoir_etre = this.joboffer.savoir_etre || [];
        this.joboffer.savoir_pro = this.joboffer.savoir_pro || [];
        this.joboffer.formation_recruteur = this.joboffer.formation_recruteur || [];
        this.joboffer.formation_demandee = this.joboffer.formation_demandee || this.joboffer.formation_requise || [];
        this.joboffer.langue_recruteur = this.joboffer.langue_recruteur || [];
        this.joboffer.permis_recruteur = this.joboffer.permis_recruteur || [];
        this.jobofferIsFavorite = isJobFavoriteById(jobId);

        if (this.joboffer.jours_teletravail != null && this.joboffer.jours_teletravail !== undefined) {
          if (this.joboffer.jours_teletravail > 4) {
            this.teletravail = 'Télétravail complet';
          } else if (TELETRAVAIL_FIELDS[this.joboffer.jours_teletravail]) {
            this.teletravail =
              TELETRAVAIL_FIELDS[this.joboffer.jours_teletravail].teletravail;
          } else {
            this.teletravail = 'En présentiel';
          }
        } else {
          this.teletravail = 'En présentiel';
        }

        this.showSkills = this.joboffer.savoir_pro.length > 0;
        this.showSoftSkills = this.joboffer.savoir_etre.length > 0;

        // Récupérer les informations de l'auteur (inclus le logo de l'entreprise)
        if (this.joboffer.author) {
          const user = await getUserById(this.joboffer.author); // Récupère l'info de l'auteur
          this.joboffer.authorInfo = user; // Ajoute les infos de l'auteur à l'offre d'emploi
        }
        // Vérifier si l'offre est déjà dans la liste des candidatures de l'utilisateur connecté
        const isLoggedIn = store.getters.isLoggedIn;
        if (isLoggedIn) {
          this.userHasCandidate = this.checkJobIsInUserCandidatures();
        }
      } catch (error) {
        //console.error(
        //  `Erreur lors de la récupération de l'offre ${jobId}:`,
        //  error
        //);
        router.replace('/404');
      }
    },

    methods: {
      gotoPage,
      getImgPath,
      isJobPriority,
      getPriorityClass,
      checkJobIsInUserCandidatures() {
        if (
          this.user.postulation.find((post) => post.job.id == this.joboffer.id)
        ) {
          return true;
        }
        return false;
      },
      formatDate(dateString) {
        const date = new Date(dateString);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
      },

      getSourceText() {
        if (this.joboffer.source === 'Thanks_boss') {
          return 'Thanks-Boss';
        } else if (this.joboffer.source === 'FT') {
          return 'France Travail';
        } else {
          return '';
        }
      },

      scrollToTop() {
        window.scrollTo(0, 0);
      },

      async handleFavoriteJobClick(jobId) {
        const isLoggedIn = store.getters.isLoggedIn;
        if (!isLoggedIn) {
          toaster.showErrorPopup(
            'Veuillez vous connecter pour ajouter des offres aux favoris.'
          );
        }
        else{
          if (this.jobofferIsFavorite) {
          const response = await removeFavoriteJob(jobId);
          if (response) {
            toaster.showSuccessPopup('Annonce retirée des favoris.');
            this.jobofferIsFavorite = false;
          }
        } else {
          const response = await addFavoriteJob(jobId);
          if (response) {
            toaster.showSuccessPopup('Annonce ajoutée aux favoris.');
            this.jobofferIsFavorite = true;
          }
        }
        }
      
      },

      applicationModal(joboffer) {
        const isLoggedIn = store.getters.isLoggedIn;
        if (!isLoggedIn) {
          toaster.showErrorPopup(
            'Veuillez vous connecter pour ajouter des offres aux favoris.'
          );
        }
        else {
            if (joboffer.source === 'FT') {
              gotoPage(joboffer.url, 'FT');
            } else {
              this.selectedJobForApplication = joboffer;
            }
        }
      
      },

      // Nouvelle méthode pour gérer la candidature (redirection ou modal)
      handleJobApplication(joboffer) {
        // Vérifier si l'utilisateur est connecté
        const isLoggedIn = store.getters.isLoggedIn;
        if (!isLoggedIn) {
          toaster.showErrorPopup(
            'Veuillez vous connecter pour postuler à cette offre.'
          );
          return;
        }

        // Vérifier si l'URL du recruteur existe et est valide
        if (joboffer.recruiter_url && this.isValidUrl(joboffer.recruiter_url)) {
          this.redirectToRecruiterUrl(joboffer.recruiter_url);
        } else {
          // Fallback vers le modal si pas d'URL valide
          this.applicationModal(joboffer);
        }
      },

      // Méthode pour rediriger vers l'URL du recruteur
      redirectToRecruiterUrl(url) {
        this.isApplying = true;

        try {
          // Ouvrir dans un nouvel onglet
          window.open(url, '_blank', 'noopener,noreferrer');

          // Feedback visuel pour l'utilisateur
          toaster.showSuccessPopup(
            'Redirection vers le site du recruteur...'
          );

          // Marquer comme candidature envoyée après un délai
          setTimeout(() => {
            this.userHasCandidate = true;
            this.isApplying = false;
          }, 2000);

        } catch (error) {
          console.error('Erreur lors de la redirection:', error);
          toaster.showErrorPopup(
            'Erreur lors de la redirection. Veuillez réessayer.'
          );
          this.isApplying = false;
        }
      },

      // Méthode pour valider une URL
      isValidUrl(string) {
        try {
          const url = new URL(string);
          return url.protocol === 'http:' || url.protocol === 'https:';
        } catch (_) {
          return false;
        }
      },

      // Méthode pour obtenir le texte du bouton
      getApplyButtonText() {
        if (this.isApplying) {
          return 'Redirection en cours...';
        }
        if (this.userHasCandidate) {
          return 'Candidature envoyée';
        }
        return 'Postuler à l\'offre';
      },

      closeApplicationModal(success) {
        this.selectedJobForApplication = null;
        this.userHasCandidate = success;
      },
    },
  };
</script>

<style scoped>
/* ===== STYLES SELON MAQUETTE ===== */

/* Layout principal */
.job-detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 0;
  margin: 0;
}

.job-detail-page.priority {
  background-color: #f0f8ff;
}



/* Container principal */
.job-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Header avec logo et infos principales */
.job-header-card {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  border: 1px solid #e0e0e0;
}

.job-header-card.priority-job {
  border-color: #2196f3;
  background-color: #f8fcff;
}

.priority-badge {
  background-color: #2196f3;
  color: #ffffff;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 16px;
}

.priority-icon {
  width: 16px;
  height: 16px;
  filter: brightness(0) invert(1);
}

/* Header principal */
.header-main {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.company-logo-section {
  flex-shrink: 0;
}

.company-logo {
  width: 64px;
  height: 64px;
  border-radius: 8px;
  object-fit: contain;
  border: 1px solid #e0e0e0;
  background-color: #ffffff;
  padding: 8px;
}

.job-info-section {
  flex: 1;
}

.job-title {
  font-size: 24px;
  font-weight: 700;
  color: #333333;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.company-name {
  font-size: 16px;
  font-weight: 600;
  color: #666666;
  margin: 0 0 16px 0;
}

.job-actions-top {
  display: flex;
  gap: 12px;
  align-items: center;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: none;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  color: #666666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  border-color: #2196f3;
  color: #2196f3;
}

.action-btn.is-favorite {
  border-color: #f44336;
  color: #f44336;
}

.action-icon {
  width: 16px;
  height: 16px;
}

/* Section informations de base */
.job-info-details {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #e9ecef;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  padding: 8px 0;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-icon {
  width: 20px;
  height: 20px;
  opacity: 0.7;
  flex-shrink: 0;
}

.info-label {
  font-weight: 500;
  color: #495057;
  min-width: 120px;
  font-size: 14px;
}

.info-value {
  color: #212529;
  font-weight: 400;
  font-size: 14px;
}

/* Section compétences requises */
.competences-section {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #e0e0e0;
}

.competences-section .section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin: 0 0 16px 0;
}

.competences-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.competence-chip {
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  display: inline-block;
  border: none;
  cursor: default;
  transition: all 0.2s ease;
}

/* Savoir-faire en bleu selon la maquette */
.competence-chip.savoir-pro {
  background-color: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
}

/* Savoir-être en vert selon la maquette */
.competence-chip.savoir-etre {
  background-color: #e8f5e8;
  color: #388e3c;
  border: 1px solid #c8e6c9;
}

/* Formation en bleu clair selon la maquette */
.competence-chip.formation {
  background-color: #e3f2fd;
  color: #1976d2;
  border: 1px solid #90caf9;
}

/* Formation demandée en rouge clair selon la maquette */
.competence-chip.formation-demandee {
  background-color: #ffebee;
  color: #d32f2f;
  border: 1px solid #ef9a9a;
}

/* Langues en orange selon la maquette */
.competence-chip.langue {
  background-color: #fff3e0;
  color: #f57c00;
  border: 1px solid #ffcc02;
}

/* Permis en violet selon la maquette */
.competence-chip.permis {
  background-color: #f3e5f5;
  color: #7b1fa2;
  border: 1px solid #ce93d8;
}
/* Fallback pour les anciens styles */
.competence-chip.soft-skill {
  background-color: #e8f5e8;
  color: #388e3c;
  border: 1px solid #c8e6c9;
}

/* Boutons d'action principaux */
.main-actions {
  display: flex;
  gap: 16px;
  margin-bottom: 32px;
  align-items: center;
}

.apply-main-btn {
  flex: 1;
  max-width: 200px;
}



/* Section compétences requises */
.competences-section {
  margin-bottom: 24px;
}

.competences-section .section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin: 0 0 16px 0;
}

.competences-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}



/* Boutons d'action principaux */
.main-actions {
  display: flex;
  gap: 16px;
  margin-bottom: 32px;
  align-items: center;
}

.apply-main-btn {
  flex: 1;
  max-width: 200px;
}



/* Sections de contenu */
.content-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.content-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #333333;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #2196f3;
  display: inline-block;
}

.section-content {
  color: #666666;
  line-height: 1.6;
  font-size: 14px;
}

.section-content p {
  margin: 0 0 16px 0;
}

.section-content p:last-child {
  margin-bottom: 0;
}

/* Chips des avantages */
.avantages-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.avantage-chip {
  background-color: #ff9800;
  color: #ffffff;
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 14px;
  font-weight: 500;
}

/* Boutons d'action du bas */
.bottom-actions {
  display: flex;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
  align-items: center;
}

.apply-bottom-btn {
  flex: 1;
  max-width: 200px;
}



/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablettes */
@media screen and (max-width: 768px) {
  .job-container {
    margin: 0;
    padding: 16px;
    box-shadow: none;
  }

  .job-header-card {
    padding: 16px;
    margin-bottom: 16px;
  }

  .header-main {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .company-logo {
    width: 48px;
    height: 48px;
    align-self: center;
  }

  .job-title {
    font-size: 20px;
  }

  .company-name {
    font-size: 14px;
  }

  .job-actions-top {
    justify-content: center;
  }

  .job-info-details {
    padding: 16px;
    margin-bottom: 16px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    margin-bottom: 16px;
  }

  .info-label {
    min-width: auto;
    font-size: 13px;
  }

  .info-value {
    font-size: 13px;
  }

  .main-actions,
  .bottom-actions {
    flex-direction: column;
    gap: 12px;
  }

  .apply-main-btn,
  .apply-bottom-btn {
    max-width: none;
    width: 100%;
  }

  .content-section {
    margin-bottom: 24px;
  }

  .section-title {
    font-size: 18px;
  }

  .competences-chips,
  .avantages-chips {
    justify-content: center;
  }
}

/* Mobiles */
@media screen and (max-width: 480px) {
  .job-container {
    padding: 12px;
  }

  .job-header-card {
    padding: 12px;
  }

  .job-title {
    font-size: 18px;
  }

  .company-name {
    font-size: 13px;
  }

  .action-btn {
    padding: 6px 10px;
    font-size: 12px;
  }

  .action-icon {
    width: 14px;
    height: 14px;
  }

  .section-title {
    font-size: 16px;
  }

  .section-content {
    font-size: 13px;
  }

  .competence-chip,
  .avantage-chip {
    font-size: 12px;
    padding: 4px 8px;
  }
}

/* Très grands écrans */
@media screen and (min-width: 1200px) {
  .job-container {
    max-width: 900px;
    padding: 32px;
  }

  .job-header-card {
    padding: 32px;
  }

  .job-title {
    font-size: 28px;
  }

  .section-title {
    font-size: 22px;
  }

  .section-content {
    font-size: 15px;
  }
}


</style>
