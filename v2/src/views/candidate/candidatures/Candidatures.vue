<template>
  <main class="container">
    <section>
      <div class="mes-candidatures">
        <h1 class="tab-buttons-container">
          <v-btn
            variant="text"
            class="tab-button"
            :class="{ active: !showEntretienPage }"
            @click="showEntretienPage = false"
            elevation="0"
          >
            Mes candidatures
          </v-btn>
          <span class="separator"></span>
          <v-btn
            variant="text"
            class="tab-button"
            :class="{ active: showEntretienPage }"
            @click="showEntretienPage = true"
            elevation="0"
          >
            Mes entretiens
          </v-btn>
        </h1>
        <!-- Affichage conditionnel -->
        <EntretienPage v-if="showEntretienPage" />

        <div v-else>
          <!-- filtres pours les candidatures (grands écrans) -->
          <div class="offer-toggle-container">
            <v-btn-toggle
              v-model="toggle"
              class="offer-toggle desktop-toggle"
              rounded="0"
              color="transparent"
              group
              mandatory
            >
              <v-btn
                v-for="option in filterOptions"
                :key="option.value"
                :value="option.value"
                :ripple="false"
              >
                {{ option.title }}
              </v-btn>
            </v-btn-toggle>

            <v-tooltip location="top">
              <template #activator="{ props }">
                <div v-bind="props" class="tooltip-wrapper">
                  <v-btn
                    class="auto-candidate-btn"
                    @click="gotoPage('/auto-candidature')"
                  >
                    Auto-candidater</v-btn
                  >
                </div>
              </template>
              <span>Fonctionnalité disponible prochainement</span>
            </v-tooltip>
            <!-- filtres pour les candidatures (petits écrans) -->
            <v-select
              v-model="toggle"
              :items="filterOptions"
              class="mobile-select"
              variant="outlined"
            >
            </v-select>
          </div>
          <div class="candidatures-cards-container">
            <!-- Message si aucune candidature -->
            <p v-if="!filteredPostulations.length" class="no-message">
              Vous n'avez aucune candidature.
              <!--Tu n'as aucune candidature pour le moment. <br />Rend-toi sur la
              page des
              <a class="link" @click.stop="gotoPage('/recherche')"
                >Offres d'emploi</a
              >
              pour débuter.-->
            </p>
            <CandidatureCard
              v-for="candidature in filteredPostulations"
              :key="candidature.id"
              :candidature="candidature"
              @decline-candidature="handleDeclineCandidature"
            />
            <!-- ➖ Séparation visuelle -->
            <div v-if="filteredAutoCandidatures.length" class="separator">
              Auto-candidatures
            </div>
            <!-- ➕ Cartes auto-candidatures -->
            <AutoCandidatureCard
              v-for="auto in filteredAutoCandidatures"
              :key="auto.id"
              :candidature="auto"
              @edit="onEditAutoCandidature"
              @delete="onDeleteAutoCandidature"
            />
          </div>
        </div>
      </div>
    </section>
  </main>
</template>

<script>
  import EntretienPage from '@/components/views-models/entretiens/Entretiens.vue';
  import CandidatureCard from '@/components/cards/candidature-card/CandidatureCard.vue';
  import AutoCandidatureCard from '@/components/cards/candidature-card/AutoCandidatureCard.vue';
  import { cancelApplicationToJob } from '@/services/job.service.js';
  import { toaster } from '@/utils/toast/toast';
  import store from '../../../store';

  export default {
    name: 'CandidaturesPage',
    components: {
      EntretienPage,
      CandidatureCard,
      AutoCandidatureCard,
    },
    props: {
      user: {
        type: Object,
        required: true,
      },
    },
    data() {
      return {
        showEntretienPage: false,
        postulations: [],
        autoCandidatures: [],
        modalIsOpen: false,
        toggle: 'Toutes',
        latestArticles: null,
        filterOptions: [
          {
            title: 'Toutes',
            value: 'Toutes',
          },
          {
            title: 'Envoyées',
            value: 'En Cours',
          },
          {
            title: "A l'étude",
            value: "A l'étude",
          },
          {
            title: 'Acceptés',
            value: 'Accepté',
          },
          {
            title: 'Refusées',
            value: 'Refuser',
          },
        ],
      };
    },
    async created() {
      this.parseUserPostulations(this.user);
      this.fetchAutoCandidatures();
      console.log('Auto-candidatures : ', this.autoCandidatures);
    },
    methods: {
      gotoPage(path = '/auto-candidature') {
        console.log('Redirecting to:', path);
        this.$router.push(path);
      },
      parseUserPostulations(user) {
        if (!user) {
          return;
        }
        const currentCandidatures = user?.postulation?.map((postulation) => {
          return {
            id: postulation.id,
            img: postulation.job.logo_url,
            title: postulation.job.title,
            description: postulation.job.descriptif,
            status: postulation.statut,
            isLiked: postulation.job.is_favorite,
            jobOffert: postulation.job,
            createdAt: postulation.date_creation,
          };
        });
        this.postulations = currentCandidatures;
      },

      async fetchAutoCandidatures() {
        try {
          const response = await fetch(
            'https://paris.thanks-boss.com/api/auto-postuler/',
            {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
              },
            }
          );

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();
          this.autoCandidatures = data.results.map((item) => ({
            ...item,
            status: item.status || 'En Cours', // valeur par défaut si manquante
          }));
        } catch (error) {
          console.error('Erreur chargement auto-candidatures:', error);
        }
      },

      async handleDeclineCandidature(jobId) {
        const response = await cancelApplicationToJob(jobId);
        if (response.statusText === 'OK') {
          toaster.showSuccessPopup('Candidature annulée avec succès');
          // Update user data
          const updatedUserData = {
            ...this.user,
            postulation: this.user.postulation.filter(
              (post) => post.job.id !== jobId
            ),
          };
          // Mettre à jour l'utilisateur dans le store
          store.dispatch('handleUserChange', {
            type: null,
            payload: updatedUserData,
          });
          // Mettre à jour la liste des candidatures dans cette page
          this.parseUserPostulations(updatedUserData);
        } else {
          toaster.showErrorPopup(
            "Erreur lors de l'annulation de la candidature"
          );
        }
      },
      onEditAutoCandidature(candidature) {
        console.log('Edit reçu pour :', candidature); // 👈 À vérifier dans la console
        this.$router.push({
          name: 'update-candidature',
          params: { id: candidature.id },
        });
      },

      onDeleteAutoCandidature(id) {
        console.log('Delete reçu pour ID :', id); // 👈 À vérifier dans la console
        this.deleteAutoCandidatureFromServer(id);
      },
      async deleteAutoCandidatureFromServer(id) {
        try {
          const response = await fetch(
            `https://paris.thanks-boss.com/api/auto-postuler/${id}/`,
            {
              method: 'DELETE',
              headers: {
                'Content-Type': 'application/json',
              },
            }
          );

          if (!response.ok) {
            throw new Error(`Erreur HTTP : ${response.status}`);
          }

          // Supprimer la candidature localement
          this.autoCandidatures = this.autoCandidatures.filter(
            (c) => c.id !== id
          );

          toaster.showSuccessPopup('Auto-candidature supprimée avec succès');
        } catch (error) {
          console.error(
            'Erreur lors de la suppression de l’auto-candidature :',
            error
          );
          toaster.showErrorPopup('Erreur lors de la suppression');
        }
      },
    },
    computed: {
      filteredPostulations() {
        if (this.toggle === 'Toutes') {
          return this.postulations;
        }
        return this.postulations.filter((job) => job.status === this.toggle);
      },
      filteredAutoCandidatures() {
        // Affiche uniquement pour les onglets "Toutes" et "Envoyées"
        if (this.toggle === 'Toutes' || this.toggle === 'En Cours') {
          return this.autoCandidatures;
        }
        return [];
      },
    },
  };
</script>

<style scoped>
  .tab-buttons-container {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 20px;
  }

  .tab-button {
    padding: 18px 40px;
    color: #fff;
    background-color: var(--gray-100);
    font-size: 1.3rem !important;
    font-weight: 500 !important;
    border-radius: 8px;
    
    box-shadow: 0 2px 8px rgba(0,0,0,0.07);
    transition: none;
    position: relative;
    height: 100%;
    outline: none !important;
  }

  .tab-button:hover {
    background-color: var(--primary-1);
    border: 2px solid var(--primary-1);
    box-shadow: 0 4px 16px rgba(0,0,0,0.12);
  }

  .tab-button.active {
    background-color: var(--primary-1);
    border: 2px solid var(--primary-1);
    box-shadow: 0 4px 16px rgba(0,0,0,0.18);
  }

  .tab-button:focus {
    outline: none !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.07);
  }

  .separator {
    color: var(--gray-100);
    margin: 0 4px;
  }
  section {
    width: 100%;
  }
  .container section:nth-child(1) {
    min-height: 50vh;
  }
  .container section:nth-child(2) {
    background-color: var(--surface-bg-4);
    padding: 80px 20px;
  }
  .mes-candidatures,
  .articles-section {
    margin: 0 auto;
  }
  .candidatures-cards-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 40px;
  }
  .candidatures-cards-container .no-message {
    font-size: 1.2rem;
    color: #666;
    text-align: center;
    margin-top: 20px;
    grid-column: 1 / -1;
  }
  .candidatures-cards-container .no-message .link {
    color: var(--primary-1);
    font-weight: bold;
    text-decoration: underline;
    cursor: pointer;
  }

  /* ========= filtre pour les candidatures ========= */

  /* hidden sur mobile, displayed à partir de 768px */
  .desktop-toggle {
    display: none !important;
  }
  .offer-toggle-container {
    width: 100%;
    display: flex;
    flex-direction: row-reverse;
  }
  .offer-toggle-container .v-btn__content {
    justify-content: center;
    white-space: normal !important;
  }
  .offer-toggle {
    width: 100%;
    display: flex;
  }
  .offer-toggle button {
    flex: 1;
    height: 48px !important;
    border-bottom: 1px solid var(--surface-bg-4);
    color: var(--text-3);
    background-color: transparent;
  }
  .offer-toggle button:hover {
    color: var(--text-1) !important;
    background-color: var(--surface-bg-4);
  }
  .auto-candidate-btn {
    background-color: var(--primary-1) !important;
    color: var(--text-1) !important;
    border-radius: 10px !important;
    height: 50px;
    margin-left: 10px;
  }

  /* ========= articles section ========= */
  .card-row {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .card-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 10px;
    justify-content: center;
  }
  .card-wrapper p {
    font-size: 1.2rem;
    font-weight: 600;
    margin-left: 10px;
  }

  @media screen and (min-width: 992px) {
    .card-row {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
    }

    .card-wrapper {
      justify-content: start;
    }

    .content-wrapper {
      width: 50%;
    }
    .padding-container-img {
      padding-inline: 0vw;
    }
    .mobile-select {
      display: none;
    }
  }
  @media screen and (min-width: 768px) {
    .candidatures-cards-container {
      justify-content: flex-start;
    }
    .offer-toggle-container {
      flex-direction: row;
      align-items: center;
      justify-content: center;
      margin: 40px 0;
    }
    .desktop-toggle {
      display: flex !important;
    }
    .mobile-select {
      display: none;
      width: 100%;
    }
  }

  @media screen and (max-width: 768px) {
    .tab-buttons-container {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
      margin-bottom: 10px;
      padding: 0 12px;
    }
    .tab-button {
      width: 100%;
      min-width: 0;
      font-size: 1.05rem !important;
      padding: 16px 0;
      border-radius: 8px;
      margin-bottom: 0;
      box-sizing: border-box;
    }
    .tab-button + .tab-button {
      border-radius: 8px;
      margin-top: 0;
    }
    .separator {
      display: none;
    }
  }

  .bg {
    background-color: var(--surface-bg-4);
  }
  .separator {
    margin: 30px 0 10px;
    padding-bottom: 10px;
    border-bottom: 2px dashed #ccc;
    font-weight: bold;
    font-size: 1.1rem;
    color: var(--text-2);
  }
</style>
