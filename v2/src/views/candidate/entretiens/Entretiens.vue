<template>
  <div class="mes-entretiens">
    <h1 class="title">
      <span class="black-text underline">Mes candidatures</span> /
      <span class="grey-text">Mes entretiens</span>
    </h1>

    <div class="tabs-container">
      <div class="tabs-wrapper">
        <div class="tabs">
          <button
            :class="{ active: activeTab === 'toutes' }"
            @click="activeTab = 'toutes'"
          >
            Toutes
          </button>
          <button
            :class="{ active: activeTab === 'envoyées' }"
            @click="activeTab = 'envoyées'"
          >
            Envoyées
          </button>
          <button
            :class="{ active: activeTab === 'à l\'étude' }"
            @click="activeTab = 'à l\'étude'"
          >
            À l'étude
          </button>
          <button
            :class="{ active: activeTab === 'acceptés' }"
            @click="activeTab = 'acceptés'"
          >
            Acceptés
          </button>
          <button
            :class="{ active: activeTab === 'refusés' }"
            @click="activeTab = 'refusés'"
          >
            Refusés
          </button>
        </div>
        <div class="tabs-underline"></div>
        <button class="auto-candidater">Auto-candidater</button>
      </div>
    </div>

    <div class="entretien-list">
      <div
        class="entretien-card"
        v-for="entretien in filteredEntretiens"
        :key="entretien.id"
      >
        <img src="@/assets/logo-tb.svg" alt="Logo TB" class="logo-tb" />
        <div class="entretien-content">
          <div class="entretien-header">
            <div class="entretien-info">
              <h2>{{ entretien.titre }}</h2>
              <p>{{ entretien.entreprise }}</p>
            </div>
            <div class="entretien-meta">
              <span class="date">📅 {{ entretien.date }}</span>
              <span class="status"
                ><i class="info-icon">ℹ️</i> Envoyées
                <i class="heart-icon">❤️</i></span
              >
            </div>
          </div>
          <div class="underline-meta"></div>
          <div class="actions-container">
            <div class="actions">
              <button class="cancel">Annuler</button>
              <button class="view">Voir les détails</button>
              <button class="contact">Contacter le recruteur</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import logoTB from '@/assets/logo-tb.svg';

  export default {
    data() {
      return {
        activeTab: 'toutes',
        entretiens: [
          {
            id: 1,
            titre: 'Dev front test',
            entreprise: 'Dev front',
            date: '25 févr. 2025',
            logo: 'https://via.placeholder.com/50',
          },
        ],
      };
    },
    computed: {
      filteredEntretiens() {
        if (this.activeTab === 'toutes') return this.entretiens;
        return this.entretiens.filter(
          (e) => e.status.toLowerCase() === this.activeTab
        );
      },
    },
  };
</script>

<style scoped>
  .mes-entretiens {
    padding-top: 20px;
    padding-left: 100px;
    padding-right: 100px;
    background-color: #f5f1eb;
  }
  .title {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 35px;
  }
  .black-text {
    color: black;
  }
  .grey-text {
    color: #ccc;
  }
  .underline {
    text-decoration: underline;
    text-decoration-color: #f6b337;
    text-decoration-thickness: 3px;
  }
  .tabs-container {
    position: relative;
    margin-bottom: 10px;
  }
  .tabs-wrapper {
    display: flex;
    align-items: center;
    width: 100%;
    position: relative;
    margin-bottom: 35px;
  }
  .tabs {
    display: flex;
    gap: 85px;
    align-items: center;
    justify-content: flex-start;
    white-space: nowrap;
    position: relative;
  }
  .tabs button {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 10px 15px;
  }
  .tabs button.active {
    background: #ccc;
    color: black;
    padding-left: 65px;
    padding-right: 65px;
  }
  .entretien-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: white;
    padding: 20px;
    margin-bottom: 10px;
    border-radius: 8px;
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
    position: relative; /* Nécessaire pour positionner les boutons */
    width: 1050px;
    height: 200px;
  }
  .entretien-card h2 {
    font-size: 15px;
    font-weight: normal;
    margin-bottom: 25px;
    margin-top: -100px;
  }
  .entretien-card p {
    font-size: 10px;
  }
  .entretien-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    width: 100%;
  }
  .entretien-meta {
    font-size: 13px;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 50px;
    margin-bottom: 85px;
    margin-right: 10px;
    margin-top: -92px;
  }
  .entretien-list {
    padding: 10px;
  }

  .info-icon {
    font-size: 12px;
    color: #757575;
  }
  .entretien-logo {
    width: 50px;
    height: 50px;
    margin-right: 15px;
  }
  .actions button {
    margin-left: 10px;
    padding: 5px 10px;
    cursor: pointer;
    border-radius: 10px;
  }
  .cancel {
    background: white;
    border: 3px solid #f6b337;
    color: black;
  }
  .view {
    background: #f6b337;
    border: 3px solid #f6b337;
    color: black;
  }
  .contact {
    background: #ccc;
    border: 3px solid #ccc;
    color: white;
  }
  button.auto-candidater {
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    padding: 8px 16px;
    background: #f6b337;
    color: black;
    cursor: pointer;
    font-size: 16px;
    border-radius: 5px;
    margin-left: auto;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
  }
  .tabs-underline {
    position: absolute;
    bottom: 0px;
    left: 0;
    width: 100%;
    max-width: 915px;
    height: 1px;
    background: #ccc;
  }
  .actions-container {
    position: absolute;
    bottom: 10px;
    right: 10px;
  }
  .actions {
    display: flex;
    gap: 10px;
    align-items: center;
    padding: 10px;
  }
  .entretien-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
  img.logo-tb {
    height: auto;
    width: 110px;
    border: 1px solid #ccc;
    border-radius: 10px;
    margin-right: 10px;
  }
  .underline-meta {
    width: 100%;
    height: 1px;
    background-color: #ccc;
    margin-top: -80px; /* Colle l'underline au texte au maximum */
    position: relative;
  }
</style>
