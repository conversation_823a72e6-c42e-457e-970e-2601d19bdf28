<template>
  <div class="interviews-container">
    <h1 class="title">Mes simulations d'entretien</h1>

    <div class="actions-container">
      <button class="create-button" @click="createNewInterview">
        <i class="fas fa-plus"></i> Créer une nouvelle simulation
      </button>
    </div>

    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>Chargement des simulations d'entretien...</p>
    </div>

    <div v-else-if="error" class="error-container">
      <p>{{ error }}</p>
      <button @click="fetchInterviews" class="retry-button">Réessayer</button>
    </div>

    <div v-else-if="interviews.length === 0" class="empty-container">
      <p>Vous n'avez pas encore de simulations d'entretien.</p>
      <p>Créez votre première simulation pour vous entraîner !</p>
      <button @click="createNewInterview" class="create-button">
        <PERSON><PERSON>er ma première simulation
      </button>
    </div>

    <div v-else class="interviews-list">
      <div
        v-for="interview in validInterviews"
        :key="interview.id"
        class="interview-card"
      >
        <div class="interview-header">
          <h3>{{ getInterviewTitle(interview) }}</h3>
          <span class="interview-date">{{ formatDate(interview.date) }}</span>
        </div>
        <div class="interview-content">
          <div class="interview-info">
            <p v-if="interview.job && interview.job.title">
              <strong>Poste :</strong> {{ interview.job.title }}
            </p>
            <p v-else-if="interview.job_description">
              <strong>Description :</strong>
              {{ truncateText(interview.job_description, 100) }}
            </p>
            <p>
              <strong>Statut :</strong>
              <span :class="'status-' + interview.status">{{
                getStatusLabel(interview.status)
              }}</span>
            </p>
          </div>
          <div class="interview-actions">
            <button
              v-if="interview.status === 'pending'"
              @click="continueInterview(interview)"
              class="continue-button"
            >
              Continuer
            </button>
            <button
              v-else-if="interview.status === 'passed'"
              @click="viewReport(interview)"
              class="report-button"
            >
              Voir le rapport
            </button>
            <div
              v-else-if="interview.status === 'failed' && interview.report"
              class="interview-actions-multiple"
            >
              <button @click="viewReport(interview)" class="report-button">
                Voir le rapport
              </button>
              <button @click="retakeInterview(interview)" class="retake-button">
                Refaire l'entretien
              </button>
            </div>
            <button
              v-else
              @click="startInterview(interview)"
              class="start-button"
            >
              Démarrer
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { axiosInstance } from '@/services/axios';
  import { toaster } from '@/utils/toast/toast.js';
  import { format } from 'date-fns';
  import { fr } from 'date-fns/locale';
  import { FLASK_API_URL } from '@/services/interview.service';

  export default {
    name: 'InterviewsList',
    data() {
      return {
        interviews: [],
        loading: true,
        error: null,
      };
    },
    computed: {
      validInterviews() {
        // Filtrer les entretiens qui ont un ID valide
        return this.interviews.filter((interview) => interview && interview.id);
      },
    },
    mounted() {
      this.fetchInterviews();
    },
    methods: {
      async fetchInterviews() {
        this.loading = true;
        this.error = null;

        try {
          // Récupérer uniquement les entretiens de simulation
          const response = await axiosInstance.get('/interviews/', {
            params: {
              simulation: true,
            },
          });

          // Vérifier que les données reçues sont valides
          if (response.data) {
            if (Array.isArray(response.data)) {
              // Si c'est un tableau, filtrer les entretiens valides (avec un ID)
              this.interviews = response.data.filter(
                (interview) => interview && interview.id
              );
            } else if (
              response.data.results &&
              Array.isArray(response.data.results)
            ) {
              // Si c'est un objet avec une propriété 'results' qui est un tableau
              this.interviews = response.data.results.filter(
                (interview) => interview && interview.id
              );
            } else if (
              typeof response.data === 'object' &&
              response.data !== null
            ) {
              // Si c'est un objet unique, le mettre dans un tableau
              if (response.data.id) {
                this.interviews = [response.data];
              } else {
                console.warn(
                  "Les données reçues ne contiennent pas d'ID:",
                  response.data
                );
                this.interviews = [];
              }
            } else {
              console.warn('Format de données non reconnu:', response.data);
              this.interviews = [];
            }
          } else {
            console.warn('Aucune donnée reçue');
            this.interviews = [];
          }

          this.loading = false;
        } catch (error) {
          //console.error(
          //  'Erreur lors de la récupération des entretiens:',
          //  error
          //);
          this.error =
            "Impossible de charger les simulations d'entretien. Veuillez réessayer.";
          this.loading = false;
        }
      },

      createNewInterview() {
        this.$router.push('/create-interview');
      },

      async continueInterview(interview) {
        try {
          // Stocker l'ID de l'entretien dans le localStorage
          localStorage.setItem('currentInterviewId', interview.id);

          // Préparer les données pour l'API Flask
          const formData = new FormData();
          formData.append('interview_id', interview.id);

          // Si l'entretien a une description de poste, l'ajouter
          if (interview.job_description) {
            formData.append('description_job', interview.job_description);
          }

          // Appeler l'API Flask pour obtenir la première question
          const response = await fetch(`${FLASK_API_URL}/first_question`, {
            method: 'POST',
            body: formData,
          });

          if (!response.ok) {
            throw new Error(`Erreur HTTP: ${response.status}`);
          }

          const data = await response.json();

          // Stocker les données de l'entretien dans le localStorage pour les utiliser dans Interview.vue
          const interviewData = {
            interview_id: interview.id,
            feedback: data.feedback || null,
            next_question: data.next_question || null,
            audio_url: data.audio_url || null,
            continue_interview: true, // Indiquer qu'il s'agit d'une continuation d'entretien
          };

          localStorage.setItem('interviewData', JSON.stringify(interviewData));

          // Rediriger vers la page d'entretien
          this.$router.push('/candidate/interview');
        } catch (error) {
          //console.error(
          //  "Erreur lors de la continuation de l'entretien:",
          //  error
          //);
          // En cas d'erreur, rediriger quand même vers la page d'entretien
          // L'utilisateur pourra démarrer l'entretien manuellement
          this.$router.push('/candidate/interview');
        }
      },

      async startInterview(interview) {
        try {
          // Stocker l'ID de l'entretien dans le localStorage
          localStorage.setItem('currentInterviewId', interview.id);

          // Préparer les données pour l'API Flask
          const formData = new FormData();
          formData.append('interview_id', interview.id);

          // Si l'entretien a une description de poste, l'ajouter
          if (interview.job_description) {
            formData.append('description_job', interview.job_description);
          }

          // Appeler l'API Flask pour obtenir la première question
          const response = await fetch(`${FLASK_API_URL}/first_question`, {
            method: 'POST',
            body: formData,
          });

          if (!response.ok) {
            throw new Error(`Erreur HTTP: ${response.status}`);
          }

          const data = await response.json();

          // Stocker les données de l'entretien dans le localStorage pour les utiliser dans Interview.vue
          const interviewData = {
            interview_id: interview.id,
            feedback: data.feedback || null,
            next_question: data.next_question || null,
            audio_url: data.audio_url || null,
            continue_interview: true, // Indiquer qu'il s'agit d'une continuation d'entretien
          };

          localStorage.setItem('interviewData', JSON.stringify(interviewData));

          // Rediriger vers la page d'entretien
          this.$router.push('/candidate/interview');
        } catch (error) {
          //console.error("Erreur lors du démarrage de l'entretien:", error);
          // En cas d'erreur, rediriger quand même vers la page d'entretien
          // L'utilisateur pourra démarrer l'entretien manuellement
          this.$router.push('/candidate/interview');
        }
      },

      viewReport(interview) {
        // Rediriger vers la page de rapport avec l'ID de l'entretien
        this.$router.push(`/interview-report/${interview.id}`);
      },

      async retakeInterview(interview) {
        try {
          // Stocker l'ID de l'entretien dans le localStorage
          localStorage.setItem('currentInterviewId', interview.id);

          // Préparer les données pour l'API Flask
          const formData = new FormData();
          formData.append('interview_id', interview.id);

          // Si l'entretien a une description de poste, l'ajouter
          if (interview.job_description) {
            formData.append('description_job', interview.job_description);
          }

          // Appeler l'API Flask pour obtenir la première question
          const response = await fetch(`${FLASK_API_URL}/first_question`, {
            method: 'POST',
            body: formData,
          });

          if (!response.ok) {
            throw new Error(`Erreur HTTP: ${response.status}`);
          }

          const data = await response.json();

          // Stocker les données de l'entretien dans le localStorage pour les utiliser dans Interview.vue
          const interviewData = {
            interview_id: interview.id,
            feedback: data.feedback || null,
            next_question: data.next_question || null,
            audio_url: data.audio_url || null,
            continue_interview: true, // Indiquer qu'il s'agit d'une continuation d'entretien
          };

          localStorage.setItem('interviewData', JSON.stringify(interviewData));

          // Rediriger vers la page d'entretien
          this.$router.push('/candidate/interview');
        } catch (error) {
          //console.error("Erreur lors de la reprise de l'entretien:", error);
          // En cas d'erreur, rediriger quand même vers la page d'entretien
          // L'utilisateur pourra démarrer l'entretien manuellement
          this.$router.push('/candidate/interview');
        }
      },

      formatDate(dateString) {
        try {
          const date = new Date(dateString);
          return format(date, 'dd MMMM yyyy', { locale: fr });
        } catch (error) {
          return dateString;
        }
      },

      getStatusLabel(status) {
        const statusMap = {
          pending: 'En cours',
          passed: 'Terminé',
          failed: 'Échoué',
          cancelled: 'Annulé',
          scheduled: 'Planifié',
        };

        return statusMap[status] || status;
      },

      getInterviewTitle(interview) {
        if (!interview) return "Simulation d'entretien";

        if (interview.job && interview.job.title) {
          return `Simulation pour: ${interview.job.title}`;
        } else if (interview.job_description) {
          // Extraire le titre du poste de la description (premiers mots)
          const words = interview.job_description
            .split(' ')
            .slice(0, 5)
            .join(' ');
          return `Simulation: ${words}...`;
        } else {
          return `Simulation d'entretien #${interview.id || ''}`;
        }
      },

      truncateText(text, maxLength) {
        if (!text) return '';
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
      },
    },
  };
</script>

<style scoped>
  .interviews-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
  }

  .title {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: var(--black-100); /* Changé de var(--primary-1) à var(--black-100) */
  }

  .actions-container {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 2rem;
  }

  .create-button {
    background-color: var(--black-100); /* Changé de var(--primary-1) à var(--black-100) */
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: background-color 0.3s;
  }

  .create-button:hover {
    background-color: var(--primary-1); /* Changé de var(--black-100) à var(--primary-1) */
  }

  .loading-container,
  .error-container,
  .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    text-align: center;
  }

  .spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 4px solid var(--primary-1);
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .retry-button {
    background-color: var(--gray-light);
    color: var(--gray-dark);
    border: none;
    border-radius: 4px;
    padding: 0.5rem 1rem;
    margin-top: 1rem;
    cursor: pointer;
  }

  .interviews-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
  }

  .interview-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition:
      transform 0.3s,
      box-shadow 0.3s;
  }

  .interview-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .interview-header {
    background-color: var(--primary-1); /* Modifié pour correspondre au retake-button */
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .interview-header h3 {
    margin: 0;
    font-size: 1.2rem;
    color: white; /* Assurez-vous que le texte est bien visible sur le fond */
  }

  .interview-date {
    font-size: 0.9rem;
    opacity: 0.9;
    color: white; /* Assurez-vous que la date est bien visible sur le fond */
  }

  /* Ajout d'un effet de survol sur la carte d'entretien entière */
  .interview-card:hover .interview-header {
    background-color: var(--black-100); /* Même effet de survol que le bouton create */
    transition: background-color 0.3s;
  }

  .interview-content {
    padding: 1.5rem;
  }

  .interview-info {
    margin-bottom: 1.5rem;
  }

  .interview-info p {
    margin: 0.5rem 0;
  }

  .interview-actions {
    display: flex;
    justify-content: flex-end;
  }

  .interview-actions-multiple {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
  }

  .continue-button,
  .report-button,
  .start-button,
  .retake-button {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
  }

  .continue-button {
    background-color: var(--primary-1);
    color: white;
  }

  .report-button {
    background-color: var(--primary-1); /* Modifié pour correspondre à l'en-tête et au retake-button */
    color: var(--black-100); /* Maintenu en noir comme demandé */
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    padding: 0.5rem 1rem;
  }

  .report-button:hover {
    background-color: #e67e22; /* Effet hover légèrement plus foncé */
  }

  .start-button {
    background-color: var(--green);
    color: white;
  }

  .retake-button {
    background-color: var(--primary-1);
    color: white;
  }

  .status-pending {
    color: var(--orange);
  }

  .status-passed {
    color: var(--green);
  }

  .status-failed {
    color: var(--red);
  }

  .status-cancelled {
    color: var(--red);
  }

  .status-scheduled {
    color: var(--blue);
  }

  @media (max-width: 768px) {
    .interviews-container {
      padding: 1rem;
    }

    .interviews-list {
      grid-template-columns: 1fr;
    }

    .title {
      font-size: 1.5rem;
    }
  }
</style>
