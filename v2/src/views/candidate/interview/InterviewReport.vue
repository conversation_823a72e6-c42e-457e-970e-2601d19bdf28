<template>
  <div class="report-container">
    <div class="report-overlay"></div>
    <div class="report-header">
      <h1>Entretiens IA - Compte rendu</h1>
      <button class="back-button" @click="goBack">
        <i class="fas fa-arrow-left"></i> Retour
      </button>
    </div>

    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>Chargement du rapport...</p>
    </div>

    <div v-else-if="error" class="error-container">
      <p>{{ error }}</p>
      <button @click="fetchReport" class="retry-button">Réessayer</button>
    </div>

    <div v-else class="report-content">
      <!-- Performance Review Card -->
      <div class="report-card performance-card">
        <div class="performance-section">
          <div class="performance-container">
            <!-- Graphique circulaire à anneaux multiples maintenant à gauche -->
            <div class="circular-chart">
              <svg viewBox="0 0 100 100" class="progress-rings">
                <!-- Anneaux de fond -->
                <circle class="ring-bg" cx="50" cy="50" r="40" />
                <circle class="ring-bg" cx="50" cy="50" r="32" />
                <circle class="ring-bg" cx="50" cy="50" r="24" />

                <!-- Anneaux de progression -->
                <circle
                  class="ring ring-overall"
                  cx="50"
                  cy="50"
                  r="40"
                  :stroke-dasharray="getOverallArcLength()"
                />
                <circle
                  class="ring ring-soft"
                  cx="50"
                  cy="50"
                  r="32"
                  :stroke-dasharray="getSoftSkillsArcLength()"
                />
                <circle
                  class="ring ring-technical"
                  cx="50"
                  cy="50"
                  r="24"
                  :stroke-dasharray="getTechnicalSkillsArcLength()"
                />

                <!-- Bordures de séparation (placées après les anneaux pour être au-dessus) -->
                <circle class="ring-border" cx="50" cy="50" r="36" />
                <circle class="ring-border" cx="50" cy="50" r="28" />

                <!-- Pourcentage au centre -->
                <text x="50" y="55" class="percentage-text">
                  {{ getOverallPerformance() }}%
                </text>
              </svg>
            </div>

            <!-- Métriques détaillées maintenant à droite -->
            <div class="performance-metrics">
              <h3>Évaluation des performances</h3>
              <div class="metric">
                <span class="metric-color overall"></span>
                <span class="metric-label">
                  <strong>Score global: {{ getOverallPerformance() }}%</strong>
                  <div class="metric-description">
                    Basé sur la qualité des réponses et leur pertinence par
                    rapport au poste, la performance globale est de
                    {{ getOverallPerformance() }}%.
                  </div>
                </span>
              </div>

              <div class="metric">
                <span class="metric-color soft"></span>
                <span class="metric-label">
                  <strong
                    >Compétences interpersonnelles et communication:
                    {{ getSoftSkills() }}%</strong
                  >
                  <div class="metric-description">
                    Les compétences en communication et les soft skills sont
                    évaluées à {{ getSoftSkills() }}% sur la base des réponses
                    données lors de l'entretien.
                  </div>
                </span>
              </div>

              <div class="metric">
                <span class="metric-color technical"></span>
                <span class="metric-label">
                  <strong
                    >Compétences techniques: {{ getTechnicalSkills() }}%</strong
                  >
                  <div class="metric-description">
                    Le candidat a démontré des connaissances techniques
                    correspondant à {{ getTechnicalSkills() }}% du niveau
                    attendu pour le poste.
                  </div>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="cards-row">
        <!-- Strengths Card -->
        <div class="report-card">
          <h3>Points forts du candidat</h3>
          <p v-if="interview.report && interview.report.strengths">
            {{ interview.report.strengths }}
          </p>
          <p v-else>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Morbi eu
            libero quis felis imperdiet euismod. Aenean dictum dignissim
            sodales. Class aptent taciti sociosqu ad litora torquent per conubia
            nostra, per inceptos himenaeos. Mauris maximus auctor sem nec
            interdum. Ut efficitur nunc augue, sed faucibus dui malesuada non
          </p>
        </div>

        <!-- Areas for Improvement Card -->
      </div>

      <div class="cards-row">
        <div class="report-card">
          <h3>Points à améliorer</h3>
          <p v-if="interview.report && interview.report.improvements">
            {{ interview.report.improvements }}
          </p>
          <p v-else>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Morbi eu
            libero quis felis imperdiet euismod. Aenean dictum dignissim
            sodales. Class aptent taciti sociosqu ad litora torquent per conubia
            nostra, per inceptos himenaeos. Mauris maximus auctor sem nec
            interdum. Ut efficitur nunc augue, sed faucibus dui malesuada non
          </p>
        </div>
        <!-- Recommendations Card -->
        <!-- <div class="report-card">
          <h3>Prochaines étapes recommandées</h3>
          <p v-if="interview.report && interview.report.recommendations">
            {{ interview.report.recommendations }}
          </p>
          <p v-else>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Morbi eu
            libero quis felis imperdiet euismod. Aenean dictum dignissim
            sodales. per inceptos himenaeos. Mauris maximus auctor sem nec
            interdum. Ut efficitur nunc augue, sed faucibus dui malesuada non
          </p>
        </div> -->

        <!-- Resume Card -->
        <div class="report-card">
          <h3>Améliorations suggérées pour le CV</h3>
          <p v-if="interview.report && interview.report.resume_comments">
            {{ interview.report.resume_comments }}
          </p>
          <p v-else>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Morbi eu
            libero quis felis imperdiet euismod. Aenean dictum dignissim sodales
          </p>
          <div class="resume-button-container">
            <button class="resume-button" @click="modifyProfile">
              Modifier mon profil
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { axiosInstance } from '@/services/axios';
  import { format } from 'date-fns';
  import { fr } from 'date-fns/locale';

  export default {
    name: 'InterviewReport',
    data() {
      return {
        interview: {},
        loading: true,
        error: null,
      };
    },
    mounted() {
      this.fetchReport();
    },
    methods: {
      async fetchReport() {
        this.loading = true;
        this.error = null;

        const interviewId = this.$route.params.id;

        if (!interviewId) {
          this.error =
            "ID d'entretien manquant. Veuillez retourner à la liste des entretiens.";
          this.loading = false;
          return;
        }

        try {
          const response = await axiosInstance.get(
            `/interviews/${interviewId}/`
          );

          // Vérifier que les données reçues sont valides
          if (response.data && response.data.id) {
            this.interview = response.data;
          } else {
            console.warn(
              'Les données reçues ne sont pas valides:',
              response.data
            );
            this.error =
              "Les données de l'entretien sont invalides. Veuillez réessayer.";
          }

          this.loading = false;
        } catch (error) {
          //console.error('Erreur lors de la récupération du rapport:', error);
          this.error =
            "Impossible de charger le rapport d'entretien. Veuillez réessayer.";
          this.loading = false;
        }
      },

      goBack() {
        this.$router.push('/interviews');
      },

      createNewInterview() {
        this.$router.push('/create-interview');
      },

      modifyProfile() {
        // Redirect to profile page - corrected path
        this.$router.push('/profil');
      },

      formatDate(dateString) {
        try {
          const date = new Date(dateString);
          return format(date, 'dd MMMM yyyy à HH:mm', { locale: fr });
        } catch (error) {
          return dateString;
        }
      },

      getInterviewTitle() {
        if (!this.interview) return "Rapport d'entretien";

        if (this.interview.job && this.interview.job.title) {
          return `Simulation pour: ${this.interview.job.title}`;
        } else if (this.interview.job_description) {
          // Extraire le titre du poste de la description (premiers mots)
          const words = this.interview.job_description
            .split(' ')
            .slice(0, 5)
            .join(' ');
          return `Simulation: ${words}...`;
        } else {
          return `Simulation d'entretien #${this.interview.id || ''}`;
        }
      },

      // Nouvelles méthodes pour le rapport visuel
      getOverallPerformance() {
        if (!this.interview || !this.interview.report) return 0;

        const report = this.interview.report;

        // Si c'est un nombre, l'utiliser directement
        if (typeof report.overall_performance === 'number') {
          return report.overall_performance;
        }

        // Si c'est une chaîne de caractères, essayer d'extraire le nombre
        if (typeof report.overall_performance === 'string') {
          const match = report.overall_performance.match(/(\d+)/);
          if (match && match[1]) {
            return parseInt(match[1], 10);
          }
        }

        // Si c'est dans le texte global, essayer de l'extraire
        if (typeof report.overall_text === 'string') {
          const match = report.overall_text.match(/(\d+)%/);
          if (match && match[1]) {
            return parseInt(match[1], 10);
          }
        }

        return 0;
      },

      getTechnicalSkills() {
        if (!this.interview || !this.interview.report) return 0;

        const report = this.interview.report;

        if (typeof report.technical_skills === 'number') {
          return report.technical_skills;
        }

        if (typeof report.technical_skills === 'string') {
          const match = report.technical_skills.match(/(\d+)/);
          if (match && match[1]) {
            return parseInt(match[1], 10);
          }
        }

        if (typeof report.technical_text === 'string') {
          const match = report.technical_text.match(/(\d+)%/);
          if (match && match[1]) {
            return parseInt(match[1], 10);
          }
        }

        return 0;
      },

      getSoftSkills() {
        if (!this.interview || !this.interview.report) return 0;

        const report = this.interview.report;

        if (typeof report.soft_skills === 'number') {
          return report.soft_skills;
        }

        if (typeof report.soft_skills === 'string') {
          const match = report.soft_skills.match(/(\d+)/);
          if (match && match[1]) {
            return parseInt(match[1], 10);
          }
        }

        if (typeof report.soft_text === 'string') {
          const match = report.soft_text.match(/(\d+)%/);
          if (match && match[1]) {
            return parseInt(match[1], 10);
          }
        }

        return 0;
      },

      getOverallDashOffset() {
        const percentage = this.getOverallPerformance();
        // La circonférence totale est 2πr = 2 * π * 40 = 251.2
        return 251.2 * (1 - percentage / 100);
      },

      getTechnicalSkillsDashOffset() {
        const percentage = this.getTechnicalSkills();
        return 251.2 * (1 - percentage / 100);
      },

      getSoftSkillsDashOffset() {
        const percentage = this.getSoftSkills();
        return 251.2 * (1 - percentage / 100);
      },

      formatReport(report) {
        if (!report) return '';

        // Vérifier le type de données du rapport
        if (typeof report === 'string') {
          // Si c'est une chaîne de caractères, convertir les sauts de ligne en balises <br>
          return report.replace(/\n/g, '<br>');
        } else if (typeof report === 'object') {
          // Si c'est un objet, essayer de le convertir en chaîne de caractères
          try {
            // Si l'objet a une propriété 'text' ou 'content', l'utiliser
            if (report.text) {
              return report.text.replace(/\n/g, '<br>');
            } else if (report.content) {
              return report.content.replace(/\n/g, '<br>');
            } else if (report.report) {
              return report.report.replace(/\n/g, '<br>');
            } else {
              // Sinon, convertir l'objet en JSON
              const jsonString = JSON.stringify(report, null, 2);
              return jsonString.replace(/\n/g, '<br>');
            }
          } catch (error) {
            //console.error('Erreur lors du formatage du rapport:', error);
            return 'Erreur lors du formatage du rapport';
          }
        } else {
          // Pour tout autre type de données, le convertir en chaîne de caractères
          return String(report).replace(/\n/g, '<br>');
        }
      },
      getArcPath(percentage, radius) {
        // Convertir le pourcentage en angle (0-100% -> 0-180 degrés)
        const angle = (percentage / 100) * 180;

        // Calculer les coordonnées du point final de l'arc
        const x = 50 + radius * Math.cos(Math.PI * (1 - angle / 180));
        const y = 50 + radius * Math.sin(Math.PI * (1 - angle / 180));

        // Déterminer si l'arc est grand (> 180 degrés) ou petit
        const largeArcFlag = angle > 180 ? 1 : 0;

        // Créer le chemin SVG pour l'arc
        return `M 50 ${50 - radius} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x} ${y}`;
      },
      formatMetricText(text) {
        if (!text) return '';

        // Nettoyer le texte en supprimant les pourcentages à la fin s'ils existent
        let cleanedText = text.replace(/\s*\d+%\s*$/, '');

        // Supprimer les préfixes inutiles comme "Overall: " ou "Technical Skills: "
        cleanedText = cleanedText
          .replace(/^Overall:?\s*/i, '')
          .replace(/^Technical Skills:?\s*/i, '')
          .replace(/^Soft Skills & Communication:?\s*/i, '');

        // Capitaliser la première lettre
        return cleanedText.charAt(0).toUpperCase() + cleanedText.slice(1);
      },
      getGaugePath(percentage) {
        // Convertir le pourcentage en angle (0-100% -> 0-180 degrés)
        const angle = (percentage / 100) * 180;

        // Calculer les coordonnées du point final de l'arc
        const x = 50 + 40 * Math.cos(Math.PI * (1 - angle / 180));
        const y = 50 - 40 * Math.sin(Math.PI * (1 - angle / 180));

        // Créer le chemin SVG pour l'arc
        return `M 10 50 A 40 40 0 ${angle > 90 ? 1 : 0} 1 ${x} ${y}`;
      },
      // Ajouter ces méthodes pour calculer précisément les longueurs d'arc
      calculateArcLength(percentage, radius) {
        // La circonférence complète est 2πr
        const circumference = 2 * Math.PI * radius;
        // La longueur de l'arc est proportionnelle au pourcentage
        return (percentage / 100) * circumference;
      },

      getOverallArcLength() {
        return `${this.calculateArcLength(this.getOverallPerformance(), 40)} ${2 * Math.PI * 40}`;
      },

      getSoftSkillsArcLength() {
        return `${this.calculateArcLength(this.getSoftSkills(), 32)} ${2 * Math.PI * 32}`;
      },

      getTechnicalSkillsArcLength() {
        return `${this.calculateArcLength(this.getTechnicalSkills(), 24)} ${2 * Math.PI * 24}`;
      },
    },
  };
</script>

<style scoped>
  .report-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    background-color: #f5f5f5;
    font-family: Arial, sans-serif;
    position: relative;
    z-index: 1;
  }

  .report-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(
      --surface-bg
    ); /* Utiliser la même variable que report-content */
    z-index: 1;
  }

  .report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    position: relative;
    z-index: 2;
  }

  .report-header h1 {
    font-size: 2rem;
    color: #333;
    margin: 0;
    font-weight: bold;
  }

  .back-button {
    background-color: #f6b337; /* Même couleur que le bouton "Modifier mon profil" */
    color: var(
      --black-100
    ); /* Texte noir comme sur le bouton "Modifier mon profil" */
    border: none;
    border-radius: 20px; /* Coins arrondis comme le bouton "Modifier mon profil" */
    padding: 0.5rem 1.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: bold;
    transition: background-color 0.3s;
  }

  .back-button:hover {
    background-color: #e67e22; /* Même effet hover que le bouton "Modifier mon profil" */
  }

  /* S'assurer que l'icône de flèche est de la bonne couleur */
  .back-button i {
    color: var(--black-100);
  }

  .loading-container,
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    text-align: center;
    position: relative;
    z-index: 2;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4); /* Ombre jaune plus opaque */
    padding: 2rem;
    border: 1px solid rgba(255, 193, 7, 0.2); /* Bordure légèrement jaune */
  }

  .spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 4px solid var(--primary-1);
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .retry-button {
    background-color: var(--gray-light);
    color: var(--gray-dark);
    border: none;
    border-radius: 4px;
    padding: 0.5rem 1rem;
    margin-top: 1rem;
    cursor: pointer;
  }

  .report-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    position: relative;
    z-index: 2;
    background-color: var(
      --surface-bg
    ); /* Utiliser la variable de couleur de fond standard */
  }

  .cards-row {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 0;
  }

  /* Nouvelles classes pour le design du rapport */
  .report-card {
    background-color: white;
    border-radius: 16px;
    border: none;
    padding: 1.8rem;
    flex: 1;
    position: relative;
    z-index: 3;
    box-shadow:
      -2px -2px 0 1.5px #f6b337,
      /* Bordure plus épaisse à gauche et en haut (augmentée) */ 0 0 0 1px
        #f6b337,
      /* Bordure fine de base partout */ 2px 2px 4px rgba(246, 179, 55, 0.2); /* Légère ombre en bas à droite */
  }

  .report-card:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease;
  }

  .performance-card {
    margin-bottom: 1.5rem;
    box-shadow: none; /* Suppression de l'ombre spéciale */
    border: none; /* S'assurer qu'il n'y a pas de bordure */
    background-color: transparent; /* Rendre le fond transparent pour hériter de la couleur parente */
  }

  .performance-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    border: none;
    box-shadow:
      -2px -2px 0 1.5px #e74c3c,
      /* Bordure plus épaisse à gauche et en haut (rouge) */ 0 0 0 1px #e74c3c,
      /* Bordure fine de base partout (rouge) */ 2px 2px 4px
        rgba(231, 76, 60, 0.2); /* Légère ombre en bas à droite */
    display: flex;
    flex-direction: column;
    align-items: flex-start; /* Aligne les éléments à gauche */
  }

  .performance-section h3 {
    margin-bottom: 15px;
    width: 100%;
    text-align: left; /* Aligne le texte à gauche */
  }

  .performance-container {
    display: flex;
    flex-direction: row; /* Disposition horizontale */
    align-items: center; /* Centrer verticalement */
    gap: 20px;
    width: 100%; /* Utiliser toute la largeur disponible */
  }

  .gauge-chart {
    width: 150px;
    height: 50px;
  }

  .gauge {
    width: 100%;
    height: 100%;
  }

  .gauge-background {
    fill: none;
    stroke: #f0f0f0;
    stroke-width: 10;
  }

  .gauge-overall {
    fill: none;
    stroke: #e74c3c;
    stroke-width: 10;
    stroke-linecap: round;
  }

  .gauge-text {
    font-size: 1.5rem;
    font-weight: bold;
    fill: #333;
    text-anchor: middle;
  }

  .performance-metrics {
    display: flex;
    flex-direction: column;
    gap: 15px;
    flex: 1; /* Prendre l'espace disponible */
    max-width: 60%; /* Limiter la largeur pour ne pas écraser le graphique */
  }

  .metric {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .metric-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: inline-block;
  }

  .metric-color.overall {
    background-color: #e74c3c;
  }

  .metric-color.technical {
    background-color: #f39c12;
  }

  .metric-color.soft {
    background-color: #8e44ad;
  }

  .metric-label {
    font-size: 0.9rem;
    color: #555;
  }

  .report-card h3 {
    margin-top: 0;
    margin-bottom: 1.5rem;
    color: #333;
    font-size: 1.25rem;
  }

  .report-card p {
    margin: 0;
    line-height: 1.6;
    color: #555;
    font-size: 0.9rem;
  }

  .resume-button-container {
    display: flex;
    justify-content: center;
    margin-top: 1.5rem;
  }

  .resume-button {
    background-color: #f6b337; /* Orange/jaune du bouton */
    color: var(--black-100);
    border: none;
    border-radius: 20px;
    padding: 0.5rem 1.5rem;
    font-size: 0.9rem;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .resume-button:hover {
    background-color: #e67e22;
  }

  @media (max-width: 768px) {
    .report-container {
      padding: 1rem;
    }

    .report-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }

    .report-header h1 {
      font-size: 1.5rem;
    }

    .cards-row {
      flex-direction: column;
    }

    .performance-section {
      flex-direction: column;
      align-items: center;
      gap: 1rem;
    }

    .chart {
      width: 100px;
      height: 100px;
    }

    .percentage {
      font-size: 1.5rem;
    }
  }

  /* Ajout d'un fond blanc circulaire derrière le pourcentage */
  .chart::after {
    content: '';
    position: absolute;
    width: 60px; /* Taille du cercle blanc */
    height: 60px; /* Taille du cercle blanc */
    background-color: white;
    border-radius: 50%;
    z-index: 1;
  }

  .progress-rings {
    width: 100%;
    height: 100%;
  }

  .skill-arc {
    transition: all 0.5s ease;
  }

  .skill-arc.overall {
    stroke: #e74c3c;
  }

  .skill-arc.technical-skills {
    stroke: #f39c12;
  }

  .skill-arc.soft-skills {
    stroke: #8e44ad;
  }

  .metric-color.overall {
    background-color: #e74c3c;
  }

  .metric-color.technical {
    background-color: #f39c12;
  }

  .metric-color.soft {
    background-color: #8e44ad;
  }

  .performance-details h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    color: #333;
    font-size: 1.25rem;
    border-bottom: none;
  }

  /* Ajouter ces styles pour améliorer l'affichage des métriques */
  .metric {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
  }

  .metric-color {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    margin-right: 10px;
    margin-top: 4px;
    flex-shrink: 0;
  }

  .metric-color.overall {
    background-color: #e74c3c;
  }

  .metric-color.technical {
    background-color: #f39c12;
  }

  .metric-color.soft {
    background-color: #8e44ad;
  }

  .metric-label {
    flex: 1;
    line-height: 1.4;
  }

  .metric-description {
    margin-top: 5px;
    font-size: 0.9em;
    color: #555;
  }

  /* S'assurer que le graphique est toujours visible */
  .chart-container {
    position: relative;
    width: 150px;
    height: 150px;
    margin-right: 20px;
    flex-shrink: 0;
  }

  .chart {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .percentage {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.5rem;
    font-weight: bold;
    z-index: 2;
  }

  .progress-rings {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
  }

  /* Styles pour la section de performance - avec le même effet que performance-card */
  .performance-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    border: none;
    box-shadow:
      -2px -2px 0 1.5px #e74c3c,
      /* Bordure plus épaisse à gauche et en haut (rouge) */ 0 0 0 1px #e74c3c,
      /* Bordure fine de base partout (rouge) */ 2px 2px 4px
        rgba(231, 76, 60, 0.2); /* Légère ombre en bas à droite */
  }

  .performance-container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 30px;
  }

  /* Styles pour le graphique circulaire */
  .circular-chart {
    width: 180px;
    height: 180px;
    position: relative;
  }

  .progress-rings {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
  }

  .ring-bg {
    fill: none;
    stroke: #f5f5f5;
    stroke-width: 8;
  }

  .ring {
    fill: none;
    stroke-width: 8;
    stroke-linecap: round;
    transition: stroke-dasharray 0.8s ease;
  }

  .ring-overall {
    stroke: #e74c3c;
  }

  .ring-soft {
    stroke: #8e44ad;
  }

  .ring-technical {
    stroke: #f39c12;
  }

  .percentage-text {
    fill: #333;
    font-size: 18px;
    font-weight: bold;
    text-anchor: middle;
    transform: rotate(90deg);
    transform-origin: center;
  }

  /* Styles pour les métriques */
  .performance-metrics {
    flex: 1;
    min-width: 300px;
  }

  .metric {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
  }

  .metric-color {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    margin-right: 10px;
    margin-top: 4px;
    flex-shrink: 0;
  }

  .metric-color.overall {
    background-color: #e74c3c;
  }

  .metric-color.soft {
    background-color: #8e44ad;
  }

  .metric-color.technical {
    background-color: #f39c12;
  }

  .metric-label {
    flex: 1;
  }

  .metric-description {
    margin-top: 5px;
    font-size: 0.9em;
    color: #555;
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .performance-container {
      flex-direction: column-reverse; /* Graphique en haut, métriques en bas sur mobile */
    }

    .performance-metrics {
      max-width: 100%; /* Utiliser toute la largeur sur mobile */
    }

    .circular-chart {
      margin: 0 auto; /* Centrer le graphique sur mobile */
    }
  }

  /* Modifier le style pour les bordures de séparation en blanc */
  .ring-border {
    fill: none;
    stroke: #ffffff; /* Blanc */
    stroke-width: 1;
    opacity: 0.8; /* Opacité élevée pour être visible sur toutes les parties */
  }
</style>
