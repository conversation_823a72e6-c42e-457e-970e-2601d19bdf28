<template>
  <div class="create-interview-container">
    <div class="header">
      <h1>Créer un nouvel entretien</h1>
      <p>
        Préparez-vous pour votre prochain entretien en simulant une conversation
        avec notre IA
      </p>
    </div>

    <div class="form-container">
      <div class="cv-section">
        <h2>Sélectionnez votre CV</h2>
        <div class="options-container">
          <div class="option-tabs">
            <button
              :class="['option-tab', { active: cvOption === 'existing' }]"
              @click="cvOption = 'existing'"
            >
              Utiliser un CV existant
            </button>
            <button
              :class="['option-tab', { active: cvOption === 'upload' }]"
              @click="cvOption = 'upload'"
            >
              Télécharger un nouveau CV
            </button>
          </div>

          <div v-if="cvOption === 'existing'" class="existing-cvs">
            <div v-if="loading" class="loading-indicator">
              <p>Chargement de vos CV...</p>
            </div>
            <div v-else-if="cvList.length === 0" class="no-cvs">
              <p>Vous n'avez pas encore de CV enregistré.</p>
              <button @click="cvOption = 'upload'" class="primary-button">
                Télécharger un CV
              </button>
            </div>
            <div v-else class="cv-list">
              <div
                v-for="cv in cvList"
                :key="cv.id"
                :class="['cv-item', { selected: selectedCV === cv.id }]"
                @click="selectCV(cv.id)"
              >
                <div class="cv-info">
                  <h3>{{ cv.title }}</h3>
                  <p v-if="cv.default">CV par défaut</p>
                </div>
                <div class="cv-actions">
                  <button class="select-button" @click.stop="selectCV(cv.id)">
                    {{ selectedCV === cv.id ? 'Sélectionné' : 'Sélectionner' }}
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div v-if="cvOption === 'upload'" class="upload-cv">
            <div class="upload-container">
              <input
                type="file"
                ref="cvFileInput"
                accept=".pdf,.doc,.docx"
                style="display: none"
                @change="handleCVUpload"
              />
              <div 
                class="upload-area" 
                @click="$refs.cvFileInput.click()"
                @dragover.prevent="onDragOver"
                @dragleave.prevent="onDragLeave"
                @drop.prevent="onDrop"
                :class="{ 'drag-over': isDragging }"
              >
                <img
                  src="@/assets/icons/upload.svg"
                  alt="Upload"
                  width="50"
                  height="50"
                />
                <p>Cliquez pour sélectionner un fichier ou glissez-le ici</p>
                <span>Formats acceptés: PDF, DOC, DOCX</span>
              </div>
            </div>
            <div v-if="uploadedCV" class="uploaded-file">
              <p>{{ uploadedCV.name }}</p>
              <button @click="uploadedCV = null" class="remove-button">
                Supprimer
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="job-section">
        <h2>Description du poste</h2>
        <div class="options-container">
          <div class="option-tabs">
            <button
              :class="['option-tab', { active: jobOption === 'existing' }]"
              @click="jobOption = 'existing'"
            >
              Sélectionner un poste
            </button>
            <button
              :class="['option-tab', { active: jobOption === 'custom' }]"
              @click="jobOption = 'custom'"
            >
              Ajouter une description
            </button>
          </div>

          <div v-if="jobOption === 'existing'" class="existing-jobs">
            <div v-if="loadingJobs" class="loading-indicator">
              <p>Chargement des postes...</p>
            </div>
            <div v-else-if="jobList.length === 0" class="no-jobs">
              <p>Aucun poste disponible.</p>
              <button @click="jobOption = 'custom'" class="primary-button">
                Ajouter une description
              </button>
            </div>
            <div v-else class="job-list">
              <div
                v-for="job in jobList"
                :key="job.id"
                :class="['job-item', { selected: selectedJob === job.id }]"
                @click="selectJob(job.id)"
              >
                <div class="job-info">
                  <h3>{{ job.title }}</h3>
                  <p>{{ job.company }}</p>
                </div>
                <div class="job-actions">
                  <button class="select-button" @click.stop="selectJob(job.id)">
                    {{
                      selectedJob === job.id ? 'Sélectionné' : 'Sélectionner'
                    }}
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div v-if="jobOption === 'custom'" class="custom-job">
            <div class="form-group">
              <label for="job-title">Titre du poste</label>
              <input
                type="text"
                id="job-title"
                v-model="customJob.title"
                placeholder="Ex: Développeur Full Stack"
              />
            </div>
            <div class="form-group">
              <label for="job-description">Description du poste</label>
              <textarea
                id="job-description"
                v-model="customJob.description"
                placeholder="Décrivez le poste, les responsabilités, les compétences requises, etc."
                rows="6"
              ></textarea>
            </div>
          </div>
        </div>
      </div>

      <div class="actions">
        <button @click="goBack" class="secondary-button">Annuler</button>
        <button
          @click="createInterview"
          class="primary-button"
          :disabled="!isFormValid"
        >
          Créer l'entretien
        </button>
      </div>
    </div>
  </div>
</template>

<script>
  import { getCvList } from '@/services/profile.service';
  import { getJobList } from '@/services/job.service';
  import { createInterview } from '@/services/interview.service';
  import { toaster } from '@/utils/toast/toast.js';

  export default {
    name: 'CreateInterview',

    data() {
      return {
        cvOption: 'existing',
        jobOption: 'existing',
        selectedCV: null,
        selectedJob: null,
        uploadedCV: null,
        cvList: [],
        jobList: [],
        loading: false,
        loadingJobs: false,
        customJob: {
          title: '',
          description: '',
        },
        isDragging: false,
      };
    },

    computed: {
      isFormValid() {
        // Vérifier si un CV est sélectionné
        const hasCv =
          (this.cvOption === 'existing' && this.selectedCV) ||
          (this.cvOption === 'upload' && this.uploadedCV);

        // Vérifier si une description de poste est fournie
        const hasJob =
          (this.jobOption === 'existing' && this.selectedJob) ||
          (this.jobOption === 'custom' &&
            this.customJob.title &&
            this.customJob.description);

        return hasCv && hasJob;
      },
    },

    mounted() {
      this.loadCVs();
      this.loadJobs();
    },

    methods: {
      async loadCVs() {
        this.loading = true;
        try {
          this.cvList = await getCvList();
          // Si l'utilisateur a un CV par défaut, le sélectionner automatiquement
          const defaultCV = this.cvList.find((cv) => cv.default);
          if (defaultCV) {
            this.selectedCV = defaultCV.id;
          }
        } catch (error) {
          //console.error('Erreur lors du chargement des CV:', error);
          toaster.showErrorPopup('Erreur lors du chargement de vos CV.');
        } finally {
          this.loading = false;
        }
      },

      async loadJobs() {
        this.loadingJobs = true;
        try {
          this.jobList = await getJobList();
        } catch (error) {
          //console.error('Erreur lors du chargement des postes:', error);
          toaster.showErrorPopup('Erreur lors du chargement des postes.');
        } finally {
          this.loadingJobs = false;
        }
      },

      selectCV(id) {
        this.selectedCV = id;
      },

      selectJob(id) {
        this.selectedJob = id;
      },

      handleCVUpload(event) {
        const file = event.target.files[0];
        if (file) {
          this.uploadedCV = file;
        }
      },

      goBack() {
        this.$router.go(-1);
      },

      async createInterview() {
        if (!this.isFormValid) return;

        try {
          // Préparer les données pour l'API
          const formData = new FormData();

          // Ajouter le CV
          if (this.cvOption === 'existing') {
            formData.append('cv_id', this.selectedCV);
          } else {
            formData.append('file', this.uploadedCV);
          }

          // Ajouter la description du poste
          if (this.jobOption === 'existing') {
            formData.append('job_id', this.selectedJob);
          } else {
            formData.append('job_title', this.customJob.title);
            formData.append('description_job', this.customJob.description);
          }

          // Appeler l'API pour créer l'entretien dans le backend Django
          const data = await createInterview(formData);

          if (data && data.interview_id) {
            // Afficher un message de succès
            toaster.showSuccessPopup(
              data.message || 'Entretien créé avec succès !'
            );

            // Rediriger vers la page d'entretien si l'API Flask a été appelée avec succès
            if (data.redirect_to_interview) {
              this.$router.push({ name: 'candidate-interview' });
            } else {
              // Sinon, rediriger vers la liste des entretiens
              this.$router.push({ name: 'interviews-list' });
            }
          } else {
            throw new Error("Aucun ID d'entretien reçu du serveur");
          }
        } catch (error) {
          //console.error("Erreur lors de la création de l'entretien:", error);
          toaster.showErrorPopup(
            "Erreur lors de la création de l'entretien. Veuillez réessayer."
          );
        }
      },
      onDragOver() {
        this.isDragging = true;
      },
      onDragLeave() {
        this.isDragging = false;
      },
      onDrop(event) {
        this.isDragging = false;
        
        // Récupérer le fichier déposé
        const files = event.dataTransfer.files;
        if (files.length > 0) {
          const file = files[0];
          
          // Vérifier le type de fichier
          const acceptedTypes = ['.pdf', '.doc', '.docx'];
          const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
          
          if (acceptedTypes.includes(fileExtension)) {
            // Simuler le changement de l'input file
            this.uploadedCV = file;
          } else {
            // Afficher un message d'erreur pour type de fichier non accepté
            toaster.showErrorPopup('Format de fichier non accepté. Veuillez utiliser PDF, DOC ou DOCX.');
          }
        }
      },
    },
  };
</script>

<style scoped>
  .create-interview-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 30px 20px;
  }

  .header {
    text-align: center;
    margin-bottom: 40px;
  }

  .header h1 {
    font-size: 32px;
    margin-bottom: 10px;
    color: #333;
  }

  .header p {
    font-size: 18px;
    color: #666;
  }

  .form-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 30px;
  }

  .cv-section,
  .job-section {
    margin-bottom: 30px;
  }

  .cv-section h2,
  .job-section h2 {
    font-size: 24px;
    margin-bottom: 20px;
    color: #333;
  }

  .options-container {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
  }

  .option-tabs {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
  }

  .option-tab {
    flex: 1;
    padding: 15px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    color: #666;
    transition: all 0.3s ease;
  }

  .option-tab.active {
    background-color: #f6b337;
    color: #fff;
  }

  .existing-cvs,
  .existing-jobs,
  .upload-cv,
  .custom-job {
    padding: 20px;
  }

  .cv-list,
  .job-list {
    max-height: 300px;
    overflow-y: auto;
  }

  .cv-item,
  .job-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .cv-item:hover,
  .job-item:hover {
    border-color: #f6b337;
  }

  .cv-item.selected,
  .job-item.selected {
    border-color: #f6b337;
    background-color: rgba(246, 179, 55, 0.1);
  }

  .cv-info h3,
  .job-info h3 {
    font-size: 18px;
    margin-bottom: 5px;
  }

  .cv-info p,
  .job-info p {
    font-size: 14px;
    color: #666;
    margin: 0;
  }

  .select-button {
    padding: 8px 15px;
    background-color: #f6b337;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
  }

  .upload-area {
    border: 2px dashed #e0e0e0;
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .upload-area:hover {
    border-color: #f6b337;
  }

  .upload-area.drag-over {
    border-color: #f6b337;
    background-color: rgba(246, 179, 55, 0.1);
  }

  .upload-area img {
    width: 50px;
    margin-bottom: 15px;
  }

  .upload-area p {
    font-size: 16px;
    margin-bottom: 5px;
  }

  .upload-area span {
    font-size: 14px;
    color: #666;
  }

  .uploaded-file {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
    padding: 10px 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
  }

  .remove-button {
    padding: 5px 10px;
    background-color: #ff5252;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }

  .form-group {
    margin-bottom: 20px;
  }

  .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
  }

  .form-group input,
  .form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 16px;
  }

  .form-group textarea {
    resize: vertical;
  }

  .actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 30px;
  }

  .primary-button,
  .secondary-button {
    padding: 12px 25px;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .primary-button {
    background-color: #f6b337;
    color: white;
  }

  .primary-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
  }

  .secondary-button {
    background-color: #f0f0f0;
    color: #333;
  }

  .loading-indicator,
  .no-cvs,
  .no-jobs {
    text-align: center;
    padding: 30px;
  }

  .no-cvs p,
  .no-jobs p {
    margin-bottom: 15px;
    color: #666;
  }
</style>
