<template>
  <div class="page-container">
    <!-- <PERSON><PERSON> intégrée -->
    <div class="sidebar">
      <div class="download-button" @click="downloadConversation">
        <div class="download-text">Télécharger ma conversation</div>
        <button class="download-logo">
          <img src="@/assets/download-icon.png" alt="logo" />
        </button>
      </div>

      <div class="mascotte-container">
        <video
          ref="mascotteVideo"
          class="Talking"
          title="Mascotte"
          playsinline
          muted
          loop
        >
          <source src="@/assets/mascotte/Talking.webm" type="video/webm" />
        </video>
      </div>

      <!-- Commenté temporairement
      <div class="sidebar-nav">
        <div class="sidebar-item">
          Voix 1 <img src="@/assets/stars.png" alt="logo" class="stars-logo" />
        </div>
        <div class="sidebar-item">
          Voix 2 <img src="@/assets/stars.png" alt="logo" class="stars-logo" />
        </div>
      </div>
      -->

      <!-- Commenté temporairement
      <div class="sidebar-nav">
        <div class="sidebar-item">
          Voix 1 <img src="@/assets/stars.png" alt="logo" class="stars-logo" />
        </div>
        <div class="sidebar-item">
          Voix 2 <img src="@/assets/stars.png" alt="logo" class="stars-logo" />
        </div>
      </div>
      -->
    </div>

    <!-- Contenu à droite de la barre -->
    <div class="main-chat">
      <!-- Fenêtre de chat -->
      <div class="chat-container">
        <div class="chat-window" ref="chatWindow">
          <div
            v-for="(msg, index) in messages"
            :key="index"
            :class="['message', msg.sender]"
          >
            <img
              v-if="msg.sender === 'bot'"
              :src="msg.avatar"
              alt="Avatar"
              class="avatar"
            />
            <div class="message-content">
              {{ msg.text }}
              <audio
                v-if="msg.audioUrl"
                :src="msg.audioUrl"
                autoplay
                @play="onAudioPlay(msg.audioUrl)"
                class="audio-player hidden"
              ></audio>
            </div>
            <img
              v-if="msg.sender === 'user'"
              :src="msg.avatar"
              alt="Avatar"
              class="avatar"
            />
          </div>
        </div>
      </div>

      <!-- Zone de saisie + bouton micro -->
      <div class="input-container" v-if="interviewStarted && !interviewEnded">
        <input
          v-model="newMessage"
          @keyup.enter="sendMessage"
          placeholder="Saisis ton message..."
        />
        <button @click="sendMessage">Envoyer</button>
        <button @click="toggleRecording">
          <font-awesome-icon
            :icon="['fas', 'microphone']"
            :class="{ recording: isRecording }"
            :style="{ color: isRecording ? 'red' : 'black' }"
          />
        </button>
      </div>

      <!-- Boutons principaux -->
      <div class="buttons-container">
        <!-- Bouton pour commencer l'entretien -->
        <button
          v-if="!interviewStarted"
          @click="openStartDialog"
          class="start-button"
        >
          Commencer mon entretien
          <img
            src="@/assets/soundwave.png"
            alt="Logo"
            class="button-soundwave"
          />
        </button>

        <!-- Bouton pour terminer l'entretien -->
        <button
          v-if="interviewStarted && !interviewEnded"
          @click="endInterview"
          class="end-button"
        >
          Terminer l'entretien
        </button>

        <!-- Bouton pour générer le rapport -->
        <button
          v-if="interviewEnded && !reportGenerated"
          @click="generateReport"
          class="report-button"
        >
          Générer le rapport
        </button>
      </div>
    </div>

    <!-- Modal pour démarrer l'entretien -->
    <div v-if="showStartDialog" class="modal">
      <div class="modal-content">
        <span class="close" @click="showStartDialog = false">&times;</span>
        <h2>Commencer l'entretien</h2>

        <div class="api-notice">
          <p>
            <strong>Important :</strong> Pour que l'entretien fonctionne
            correctement, assurez-vous que le serveur Flask est en cours
            d'exécution sur <code>{{ FLASK_API_URL }}</code
            >.
          </p>
        </div>

        <div class="form-group">
          <label for="cv-file">Importer votre CV (PDF, DOCX)</label>
          <input
            type="file"
            id="cv-file"
            ref="cvFileInput"
            accept=".pdf,.docx,.doc"
          />
        </div>

        <div class="form-group">
          <label for="job-description">Description du poste</label>
          <textarea
            id="job-description"
            v-model="jobDescription"
            rows="5"
            placeholder="Entrez la description du poste..."
          ></textarea>
        </div>

        <div class="form-actions">
          <button @click="showStartDialog = false">Annuler</button>
          <button @click="startInterview" class="primary-button">
            Commencer
          </button>
        </div>
      </div>
    </div>

    <!-- Nous avons supprimé le modal de rapport car nous redirigeons vers la page InterviewReport.vue -->
  </div>
</template>

<script>
  import {
    sendResponse,
    generateReport,
    startInterview,
    uploadAudio,
    checkFlaskApiAvailability,
    FLASK_API_URL,
  } from '@/services/interview.service';
  import { toaster } from '@/utils/toast/toast.js';
  import { mapGetters } from 'vuex';
  import getImgPath from '@/utils/imgpath.js'; // Importez correctement la fonction

  export default {
    data() {
      return {
        audioChunks: [],
        mediaRecorder: null,
        isRecording: false,
        newMessage: '',
        messages: [],
        // Nouvelles propriétés pour l'entretien
        interviewStarted: false,
        interviewEnded: false,
        reportGenerated: false,
        showStartDialog: false,
        showReportDialog: false,
        jobDescription: '',
        apiBaseUrl: FLASK_API_URL,
        lastPlayedAudioUrl: null, // Pour éviter de jouer le même audio plusieurs fois
        interviewId: null, // ID de l'entretien en cours
        userAvatar: null, // Pour stocker l'avatar de l'utilisateur
      };
    },

    computed: {
      ...mapGetters(['getUser']),
      
      // Récupérer l'URL de l'avatar de l'utilisateur
      userProfileImage() {
        const user = this.getUser;
        if (user && user.photo) {
          return getImgPath(user.photo); // Utilisez la fonction importée
        }
        return require('@/assets/icons/avatar.png'); // Image par défaut
      }
    },

    mounted() {
      console.log('[Mounted] Initialisation du chat en cours...');
      
      // Récupérer l'avatar de l'utilisateur
      this.userAvatar = this.userProfileImage;
      
      // Vérifier si des données d'entretien sont disponibles dans le localStorage
      const interviewData = localStorage.getItem('interviewData');
      if (interviewData) {
        try {
          const data = JSON.parse(interviewData);
          //console.log("[Mounted] Données d'entretien trouvées:", data);

          // Initialiser l'entretien avec les données récupérées
          this.interviewStarted = true;

          // Stocker l'ID de l'entretien
          if (data.interview_id) {
            this.interviewId = data.interview_id;
            //console.log("[Mounted] ID de l'entretien:", this.interviewId);
          } else {
            console.warn(
              "[Mounted] Aucun ID d'entretien trouvé dans les données"
            );
          }

          // Ajouter le message de bienvenue et la première question
          if (data.feedback) {
            this.messages.push({
              text: data.feedback,
              sender: 'bot',
              avatar: require('@/assets/circle.png'),
              audioUrl: null,
            });
          }

          if (data.next_question) {
            this.messages.push({
              text: data.next_question,
              sender: 'bot',
              avatar: require('@/assets/circle.png'),
              audioUrl: data.audio_url
                ? data.audio_url.startsWith('http')
                  ? data.audio_url
                  : `${FLASK_API_URL}${data.audio_url}`
                : null,
            });
          }

          // Si c'est une continuation d'entretien, masquer la boîte de dialogue de démarrage
          if (data.continue_interview) {
            this.showStartDialog = false;
          }

          // Supprimer les données du localStorage pour éviter de les réutiliser
          localStorage.removeItem('interviewData');
        } catch (error) {
          //console.error(
          //  "[Mounted] Erreur lors de la récupération des données d'entretien:",
          //  error
          //);
        }
      } else {
        // Récupérer l'ID de l'entretien du localStorage si disponible
        const currentInterviewId = localStorage.getItem('currentInterviewId');
        if (currentInterviewId) {
          this.interviewId = currentInterviewId;
          //console.log(
          //  "[Mounted] ID de l'entretien récupéré du localStorage:",
          //  this.interviewId
          //);
        }
      }
    },

    updated() {
      this.scrollToBottom(); // Scroll après chaque mise à jour du DOM
    },

    methods: {
      scrollToBottom() {
        this.$nextTick(() => {
          const chatWindow = this.$refs.chatWindow;
          if (chatWindow) {
            chatWindow.scrollTop = chatWindow.scrollHeight;
          }
        });
      },

      playMascotte() {
        const video = this.$refs.mascotteVideo;
        if (video && video.paused) {
          video.play().catch((err) => {
            console.warn('La lecture vidéo a échoué :', err);
          });
        }
      },

      pauseMascotte() {
        const video = this.$refs.mascotteVideo;
        if (video && !video.paused) {
          video.pause();
        }
      },

      downloadConversation() {
        // Créer le contenu du fichier texte
        let content = "Conversation d'entretien\n\n";
        content += 'Date: ' + new Date().toLocaleString() + '\n\n';

        // Ajouter chaque message
        this.messages.forEach((msg) => {
          const sender = msg.sender === 'bot' ? 'Recruteur' : 'Vous';
          content += `${sender}: ${msg.text}\n\n`;
        });

        // Créer un blob et un lien de téléchargement
        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);

        // Créer un élément a pour déclencher le téléchargement
        const a = document.createElement('a');
        a.href = url;
        a.download = `entretien_${new Date().toISOString().slice(0, 10)}.txt`;
        document.body.appendChild(a);
        a.click();

        // Nettoyer
        setTimeout(() => {
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }, 100);
      },

      openStartDialog() {
        this.showStartDialog = true;
        //console.log(
        //  '[openStartDialog] Ouverture de la boîte de dialogue de démarrage'
        //);
      },

      async startInterview() {
        // Vérifier si les champs requis sont remplis
        if (!this.jobDescription.trim()) {
          alert('Veuillez entrer une description du poste.');
          return;
        }

        const cvFile = this.$refs.cvFileInput.files[0];
        if (!cvFile) {
          alert('Veuillez importer votre CV.');
          return;
        }

        //console.log(
        //  "[startInterview] Démarrage de l'entretien avec CV et description du poste"
        //);
        this.showStartDialog = false;

        // Activer la mascotte pendant le chargement
        this.playMascotte();

        // Vérifier si l'API Flask est disponible avant de commencer
        try {
          // Vérifier la disponibilité de l'API Flask
          const isFlaskAvailable = await checkFlaskApiAvailability();
          if (!isFlaskAvailable) {
            throw new Error("L'API Flask n'est pas disponible");
          }

          //console.log('[startInterview] API Flask disponible');
          this.interviewStarted = true;

          // Préparer les données à envoyer
          const formData = new FormData();
          formData.append('file', cvFile);
          formData.append('description_job', this.jobDescription);

          // Ajouter l'ID de l'entretien s'il est disponible dans le localStorage
          const currentInterviewId = localStorage.getItem('currentInterviewId');
          if (currentInterviewId) {
            formData.append('interview_id', currentInterviewId);
            //console.log(
            //  "[startInterview] Ajout de l'ID d'entretien:",
            //  currentInterviewId
            //);
          }

          // Appeler l'API pour démarrer l'entretien en utilisant notre service
          const data = await startInterview(formData);
          //console.log('[startInterview] Première question reçue :', data);

          // Stocker l'ID de l'entretien
          if (data.interview_id) {
            this.interviewId = data.interview_id;
            //console.log(
            //  "[startInterview] ID de l'entretien:",
            //  this.interviewId
            //);
          } else {
            console.warn(
              "[startInterview] Aucun ID d'entretien trouvé dans les données"
            );
          }

          // Ajouter le message de bienvenue et la première question
          if (data.feedback) {
            this.messages.push({
              text: data.feedback,
              sender: 'bot',
              avatar: require('@/assets/circle.png'),
              // Pas d'audio pour le feedback
              audioUrl: null,
            });
          }

          if (data.next_question) {
            this.messages.push({
              text: data.next_question,
              sender: 'bot',
              avatar: require('@/assets/circle.png'),
              // Audio uniquement pour la question
              audioUrl: data.audio_url
                ? data.audio_url.startsWith('http')
                  ? data.audio_url
                  : `${FLASK_API_URL}${data.audio_url}`
                : null,
            });
          }

          // Arrêter la mascotte après un court délai
          setTimeout(() => {
            this.pauseMascotte();
          }, 1000);
        } catch (error) {
          //console.error(
          //  "[startInterview] Erreur lors du démarrage de l'entretien :",
          //  error
          //);

          // Arrêter la mascotte en cas d'erreur
          this.pauseMascotte();

          // Vérifier si l'erreur est liée à la connexion à l'API Flask
          if (
            error.message &&
            (error.message.includes('Failed to fetch') ||
              error.message.includes("L'API Flask n'est pas disponible"))
          ) {
            this.messages.push({
              text: `Erreur : impossible de se connecter à l'API d'IA. Veuillez vérifier que le serveur Flask est en cours d'exécution sur ${FLASK_API_URL}.`,
              sender: 'bot',
              avatar: require('@/assets/circle.png'),
            });
            toaster.showErrorPopup(
              "L'API d'IA n'est pas disponible. Veuillez vérifier que le serveur Flask est en cours d'exécution."
            );
          } else {
            this.messages.push({
              text: "Erreur : impossible de démarrer l'entretien. Veuillez réessayer.",
              sender: 'bot',
              avatar: require('@/assets/circle.png'),
            });
            toaster.showErrorPopup(
              "Erreur lors du démarrage de l'entretien. Veuillez réessayer."
            );
          }

          // Réinitialiser l'état pour permettre une nouvelle tentative
          this.interviewStarted = false;
        }
      },

      async sendMessage() {
        if (this.newMessage.trim() === '') return;

        const fullText = String(this.newMessage).trim();
        //console.log('[sendMessage] Message envoyé :', fullText);

        this.messages.push({
          text: fullText,
          sender: 'user',
          avatar: this.userAvatar, // Utilisez l'avatar de l'utilisateur
        });

        this.newMessage = '';

        // Vérifier si l'utilisateur souhaite terminer l'entretien
        const endKeywords = [
          'fin',
          'terminer',
          'bye',
          'au revoir',
          'arrêter',
          'stop',
          'je veux finir',
          "c'est tout",
        ];
        if (
          endKeywords.some((keyword) =>
            fullText.toLowerCase().includes(keyword)
          )
        ) {
          this.endInterview();
          return;
        }

        try {
          // Activer la mascotte pendant que le bot "réfléchit"
          this.playMascotte();

          // Vérifier si l'ID de l'entretien est disponible
          if (!this.interviewId) {
            console.warn("[sendMessage] Aucun ID d'entretien disponible");
            this.messages.push({
              text: "Erreur : impossible d'identifier l'entretien en cours. Veuillez rafraîchir la page et réessayer.",
              sender: 'bot',
              avatar: require('@/assets/circle.png'),
            });
            this.pauseMascotte();
            return;
          }

          // Utiliser notre service pour envoyer la réponse avec l'ID de l'entretien
          const data = await sendResponse(
            { response: fullText },
            this.interviewId
          );
          //console.log('[sendMessage] Réponse IA reçue :', data);

          // Ajouter le feedback sans audio
          if (data.feedback) {
            this.messages.push({
              text: data.feedback,
              sender: 'bot',
              avatar: require('@/assets/circle.png'),
              audioUrl: null, // Pas d'audio pour le feedback
            });
          }

          // Ajouter la question suivante avec l'audio s'il est disponible
          if (data.next_question) {
            this.messages.push({
              text: data.next_question,
              sender: 'bot',
              avatar: require('@/assets/circle.png'),
              audioUrl: data.audio_url
                ? `${FLASK_API_URL}${data.audio_url}`
                : null,
            });
          }

          // Si un message de fin est reçu, terminer l'entretien
          if (data.message && !data.next_question) {
            this.messages.push({
              text: data.message,
              sender: 'bot',
              avatar: require('@/assets/circle.png'),
              // Conserver l'audio pour le message de fin
              audioUrl: data.audio_url
                ? `${FLASK_API_URL}${data.audio_url}`
                : null,
            });
            this.interviewEnded = true;
          }

          // Arrêter la mascotte après la réponse
          setTimeout(() => {
            this.pauseMascotte();
          }, 1000);
        } catch (error) {
          //console.error(
          //  '[sendMessage] Erreur lors de la réponse du modèle IA :',
          //  error
          //);
          this.messages.push({
            text: "Erreur : impossible de contacter l'IA.",
            sender: 'bot',
            avatar: require('@/assets/circle.png'),
          });
          toaster.showErrorPopup(
            "Erreur lors de l'envoi de votre réponse. Veuillez réessayer."
          );

          // Arrêter la mascotte en cas d'erreur
          this.pauseMascotte();
        }
      },

      endInterview() {
        //console.log("[endInterview] Fin de l'entretien demandée");
        this.interviewEnded = true;

        // Ajouter un message de fin
        this.messages.push({
          text: "Entretien terminé. Vous pouvez maintenant générer un rapport d'analyse.",
          sender: 'bot',
          avatar: require('@/assets/circle.png'),
        });
      },

      async generateReport() {
        //console.log('[generateReport] Génération du rapport demandée');

        try {
          // Vérifier si l'ID de l'entretien est disponible
          if (!this.interviewId) {
            console.warn("[generateReport] Aucun ID d'entretien disponible");
            toaster.showErrorPopup(
              "Erreur : impossible d'identifier l'entretien en cours. Veuillez rafraîchir la page et réessayer."
            );
            return;
          }

          // Afficher un message de chargement
          this.messages.push({
            text: 'Génération du rapport en cours... Veuillez patienter.',
            sender: 'bot',
            avatar: require('@/assets/circle.png'),
          });

          // Utiliser notre service pour générer le rapport avec l'ID de l'entretien
          const data = await generateReport(this.interviewId);
          //console.log('[generateReport] Rapport reçu :', data);

          // Marquer le rapport comme généré
          this.reportGenerated = true;

          // Rediriger vers la page de rapport d'entretien avec l'ID de l'entretien
          this.$router.push(`/interview-report/${this.interviewId}`);
        } catch (error) {
          //console.error(
          //  '[generateReport] Erreur lors de la génération du rapport :',
          //  error
          //);
          toaster.showErrorPopup(
            'Erreur lors de la génération du rapport. Veuillez réessayer.'
          );

          // Ajouter un message d'erreur dans le chat
          this.messages.push({
            text: 'Erreur lors de la génération du rapport. Veuillez réessayer.',
            sender: 'bot',
            avatar: require('@/assets/circle.png'),
          });
        }
      },

      playReportAudio() {
        if (this.$refs.reportAudio) {
          //console.log('[playReportAudio] Lecture du rapport audio');
          this.$refs.reportAudio.play();
        }
      },

      pauseReportAudio() {
        if (this.$refs.reportAudio) {
          //console.log('[pauseReportAudio] Pause du rapport audio');
          this.$refs.reportAudio.pause();
        }
      },

      async onAudioPlay(audioUrl) {
        //console.log('[onAudioPlay] Lecture audio du bot:', audioUrl);
        this.lastPlayedAudioUrl = audioUrl;

        // Démarrer la mascotte
        const video = this.$refs.mascotteVideo;
        if (video) {
          // Réinitialiser la vidéo avant de la jouer pour éviter des problèmes de blocage
          video.currentTime = 0;
          
          // Utiliser une promesse pour s'assurer que la vidéo démarre correctement
          try {
            await video.play();
            //console.log('[onAudioPlay] Mascotte démarrée avec succès');
          } catch (err) {
            console.warn('[onAudioPlay] Erreur lecture mascotte:', err);
            
            // Tentative de récupération en cas d'erreur
            setTimeout(() => {
              video.currentTime = 0;
              video.play().catch(e => console.warn('[onAudioPlay] Nouvelle tentative échouée:', e));
            }, 300);
          }
        }

        // Trouver l'élément audio actuel du bot
        const audioElements = document.querySelectorAll('audio');
        const currentAudio = Array.from(audioElements).find(
          (audio) => audio.src === audioUrl
        );

        if (currentAudio) {
          // Arrêter la mascotte quand l'audio du bot se termine
          currentAudio.addEventListener('ended', () => {
            if (video) {
              video.pause();
              video.currentTime = 0;
            }
          });

          // Gérer aussi la pause si l'audio est interrompu
          currentAudio.addEventListener('pause', () => {
            if (video) {
              video.pause();
              video.currentTime = 0;
            }
          });
          
          // Vérifier périodiquement si l'audio joue toujours
          const checkInterval = setInterval(() => {
            if (currentAudio.paused || currentAudio.ended) {
              clearInterval(checkInterval);
              return;
            }
            
            // Si la vidéo s'est arrêtée mais que l'audio joue encore, redémarrer la vidéo
            if (video && video.paused && !currentAudio.paused) {
              //console.log('[onAudioPlay] Redémarrage de la mascotte');
              video.currentTime = 0;
              video.play().catch(err => console.warn('[onAudioPlay] Erreur redémarrage:', err));
            }
          }, 1000); // Vérifier toutes les secondes
        }
      },

      displayTranscription(transcription) {
        const cleaned = String(transcription).trim();
        const avoidList = ['thank you', 'ok', 'hello', '...', ''];

        if (!avoidList.includes(cleaned.toLowerCase())) {
          //console.log(
          //  '[displayTranscription] Transcription envoyée :',
          //  cleaned
          //);
          this.newMessage = cleaned;
          this.sendMessage();
        } else {
          console.warn(
            '[displayTranscription] Transcription ignorée :',
            cleaned
          );
        }
      },

      toggleRecording() {
        if (!this.isRecording) {
          //console.log("[toggleRecording] Démarrage de l'enregistrement...");
          navigator.mediaDevices
            .getUserMedia({ audio: true })
            .then((stream) => {
              this.mediaRecorder = new MediaRecorder(stream);
              this.audioChunks = [];

              this.mediaRecorder.ondataavailable = (event) => {
                this.audioChunks.push(event.data);
              };

              this.mediaRecorder.onstop = () => {
                //console.log('Enregistrement terminé.');
                if (this.audioChunks.length > 0) {
                  const audioBlob = new Blob(this.audioChunks, {
                    type: 'audio/wav',
                  });
                  //console.log('Audio prêt à être envoyé.');
                  this.sendAudio(audioBlob);
                } else {
                  //console.error('Aucun audio capturé.');
                }
              };

              this.mediaRecorder.start();
              this.isRecording = true;
            })
            .catch((error) => {
              //console.error("Erreur d'accès au micro :", error);
              alert("Impossible d'accéder au microphone.");
            });
        } else {
          //console.log("Arrêt de l'enregistrement.");
          if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
            this.mediaRecorder.stop();
            this.isRecording = false;
          } else {
            //console.error("Le MediaRecorder n'est pas actif.");
          }
        }
      },

      async sendAudio(audioBlob) {
        //console.log('[sendAudio] Envoi du blob audio au back...');

        try {
          // Vérifier si l'ID de l'entretien est disponible
          if (!this.interviewId) {
            console.warn("[sendAudio] Aucun ID d'entretien disponible");
            toaster.showErrorPopup(
              "Erreur : impossible d'identifier l'entretien en cours. Veuillez rafraîchir la page et réessayer."
            );
            return;
          }

          // Utiliser notre service pour envoyer l'audio avec l'ID de l'entretien
          const data = await uploadAudio(audioBlob, this.interviewId);
          //console.log('[sendAudio] Transcription reçue :', data.transcription);

          if (data.transcription) {
            this.displayTranscription(data.transcription);
          }
        } catch (error) {
          //console.error(
          //  "[sendAudio] Erreur lors de l'envoi de l'audio :",
          //  error
          //);
          toaster.showErrorPopup(
            "Erreur lors de l'envoi de l'audio. Veuillez réessayer."
          );
        }
      },
    },
  };
</script>

<style>
  /* === Container principal contenant la sidebar et le contenu === */
  .page-container {
    display: flex;
    flex-direction: row;
    height: 100vh;
    width: 100%;
    background-color: #f0f0f0;
    overflow: hidden;
  }

  /* === Sidebar intégrée === */
  .sidebar {
    width: 280px;
    background-color: #fffaf0;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    padding: 20px 15px;
    height: calc(100vh - 116px); /* Hauteur de référence */
    border-radius: 5px;
    margin-top: 50px;
  }

  .download-button {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    background-color: #ff9800;
    border-radius: 4px;
    padding: 8px 12px;
    margin-bottom: 20px;
  }

  .download-text {
    font-size: 14px;
    color: #090909;
    font-weight: 500;
  }

  .download-logo {
    background: transparent;
    border: none;
    cursor: pointer;
  }

  .mascotte-container {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    margin: 50px 0;
    height: calc(100vh - 250px);
  }

  .Talking {
    width: 80vh;
    height: 60vh;
    object-fit: contain;
    image-rendering: auto;
    border-radius: 8px;
  }

  .sidebar-nav {
    display: flex;
    flex-direction: row;
    gap: 15px;
    justify-content: center;
    margin-bottom: 20px;
    padding: 10px 0;
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
  }

  .sidebar-item {
    width: 110px;
    text-align: center;
    font-size: 12px;
    cursor: pointer;
    padding: 10px;
    border-radius: 8px;
    background-color: #58a09633;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* === Conteneur du chat + input à droite de la sidebar === */
  .main-chat {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding-left: 7px;
    background-color: #f0f0f0;
    border-radius: 5px;
  }

  /* === Fenêtre de chat === */
  .chat-container {
    width: 100%;
    height: calc(100vh - 100px); /* Même hauteur que la sidebar */
    background: white;
    border-radius: 12px;
    box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-top: 50px;
  }

  .chat-window {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    scroll-behavior: smooth;
    margin-bottom: 20px;
  }

  .message {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    gap: 12px;
  }

  .message.bot {
    flex-direction: row;
  }

  .message.user {
    flex-direction: row-reverse;
  }

  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .message-content {
    max-width: 70%;
    padding: 12px 18px;
    border-radius: 18px;
    font-size: 15px;
    word-wrap: break-word;
    line-height: 1.4;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .message.user .message-content {
    background-color: #ffdbac;
    color: #333;
    border-bottom-right-radius: 4px;
  }

  .message.bot .message-content {
    background-color: #e8e8e8;
    color: #333;
    text-align: left;
    border-bottom-left-radius: 4px;
  }

  .input-container {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.08);
    margin-top: 10px;
    border: 1px solid #eaeaea;
  }

  .input-container input {
    flex: 1;
    padding: 12px;
    border: none;
    outline: none;
    background: transparent;
    font-size: 15px;
  }

  .input-container button {
    padding: 12px 18px;
    border: none;
    background: #ff9800;
    color: white;
    cursor: pointer;
    border-radius: 8px;
    margin-left: 8px;
    font-weight: 500;
    transition: background-color 0.2s;
  }

  .input-container button:hover {
    background: #f57c00;
  }

  .buttons-container {
    display: flex;
    justify-content: center;
    width: 100%;
    margin-top: 10px;
    margin-bottom: 10px;
  }

  .start-button,
  .end-button,
  .report-button {
    padding: 12px 24px;
    border: none;
    color: white;
    cursor: pointer;
    border-radius: 30px;
    font-weight: bold;
    font-size: 15px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
  }

  .start-button:hover,
  .end-button:hover,
  .report-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  }

  .start-button {
    background: #26282b;
  }

  .end-button {
    background: #e74c3c;
  }

  .report-button {
    background: #2ecc71;
  }

  .button-soundwave {
    width: 22px;
    height: 22px;
    margin-left: 8px;
    vertical-align: middle;
  }

  .stars-logo {
    width: 14px;
    height: auto;
    vertical-align: middle;
    padding-left: 3px;
  }

  .recording {
    color: red;
    animation: pulse 1.5s infinite;
  }

  @keyframes pulse {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
    100% {
      opacity: 1;
    }
  }

  /* Styles pour les lecteurs audio */
  .audio-player {
    margin-top: 8px;
    width: 100%;
    max-width: 250px;
  }

  .report-audio {
    width: 100%;
  }

  .hidden {
    display: none;
  }

  .audio-controls {
    display: flex;
    gap: 10px;
    margin: 10px 0;
  }

  .audio-button {
    padding: 8px 15px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
  }

  .audio-button:hover {
    background-color: #2980b9;
  }

  /* Styles pour les modales */
  .modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  .modal-content {
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    width: 80%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
  }

  .report-modal {
    max-width: 800px;
  }

  .close {
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
  }

  .form-group {
    margin-bottom: 15px;
  }

  .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
  }

  .form-group input,
  .form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
  }

  .primary-button {
    background-color: #4caf50;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
  }

  /* Styles pour le rapport */
  .report-content {
    margin: 20px 0;
    line-height: 1.6;
    text-align: left;
  }

  .report-content h3 {
    margin-top: 20px;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
  }

  .audio-container {
    margin-top: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 5px;
  }

  .api-notice {
    margin-bottom: 20px;
    padding: 10px 15px;
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
    border-radius: 4px;
  }

  .api-notice p {
    margin: 0;
    color: #856404;
  }

  .api-notice code {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
  }
</style>
