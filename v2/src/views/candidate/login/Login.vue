<template>
  <main class="container">
    <div class="login-header">
      <h1>Connexion</h1>
      <div class="cross-button-container">
        <button class="cross-button" @click="gotoPage('/')">
          <img src="@/assets/icons/cross.svg" alt="close" />
        </button>
      </div>
    </div>
    <section class="login-container">
      <div class="login-ext-buttons-container">
        <!-- google btn -->
        <GoogleButton
          @click="handleGoogleLogin"
          :loading="isGoogleLoading"
          id="Google_button"
        />
      </div>

      <span>ou</span>

      <!-- form inputs -->
      <v-form ref="formLoginRef">
        <!-- email -->
        <label for="email">Email</label>
        <v-text-field
          type="email"
          variant="plain"
          :rules="[...emailRules, ...notEmptyRules]"
          v-model="formData.email"
          placeholder="Votre email"
        />
        <!-- password -->
        <label for="password">Mot de passe</label>
        <v-text-field
          variant="plain"
          placeholder="*********"
          :rules="notEmptyRules"
          v-model="formData.password"
          :type="isPasswordHide ? 'password' : 'text'"
          :append-icon="isPasswordHide ? 'mdi-eye-off' : 'mdi-eye'"
          @click:append="isPasswordHide = !isPasswordHide"
          @keyup.enter="loginSubmit"
        />
        <!-- password forget ? -->
        <span
          class="text-underline cursor-pointer"
          @click="gotoPage('/reinitialisation-mot-de-passe')"
          >Mot de passe oublié ?</span
        >
        <!-- checkbox remember me -->
        <!--
      <div class="remember-container">
        <v-checkbox v-model="rememberMeCheckbox" color="var(--primary-1)" label="Se souvenir de moi." hide-details></v-checkbox>
      </div>
-->
        <!-- form footer, buttons and text -->
        <div class="form-footer">
          <!-- login btn -->
          <div @click="loginSubmit">
            <PrimayNormalButton
              textContent="Je me connecte"
              :isLoading="isLoading"
            />
          </div>

          <!-- other texts -->
          <div class="form-footer-text">
            <p>Vous n'avez pas encore de compte chez thanks-boss ?</p>
            <p
              class="text-underline cursor-pointer"
              @click="gotoPage('/inscription')"
            >
              Inscrivez-vous ici
            </p>
          </div>
        </div>
      </v-form>
    </section>
    <section class="login-footer">
      <!--  go to login-footer css classes when register btn will be displayed
    
    <div>
      <p>Vous êtes recruteur ?</p>
      <p>Venez-vous connecter chez thanks-boss</p>
    </div>
    <div @click="gotoPage('/recruiter')">
      <PrimayNormalButton textContent="Espace recruteur" />
    </div> -->
    </section>
  </main>
</template>

<script setup>
  import { ref } from 'vue';
  const formLoginRef = ref(null);
</script>

<script>
  import { login } from '@/services/account.service';
  import { toaster } from '@/utils/toast/toast';
  import GoogleButton from '@/components/buttons/GoogleLogin.vue';
  import PrimayNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import gotoPage from '@/utils/router.js';
  import {
    validateEmail,
    validateNotEmpty,
  } from '../../../utils/validationRules';
  import { googleLogin } from '../../../services/account.service';
  import { useMessagerieStore } from '@/store/messagerie';

  export default {
    name: 'LoginPage',
    components: {
      GoogleButton,
      PrimayNormalButton,
    },
    data() {
      return {
        formData: {
          email: '',
          password: '',
        },
        isLoading: false,
        isGoogleLoading: false,
        isPasswordHide: true,
        rememberMeCheckbox: false,
        emailRules: [(v) => validateEmail(v) || true],
        notEmptyRules: [(v) => validateNotEmpty(v) || true],
      };
    },
    methods: {
      gotoPage,
      // login with email and password
      async loginSubmit() {
        try {
          const valid = await this.validateInputForm();
          if (!valid) {
            return;
          }
          // set loader in btn
          this.isLoading = true;

          // set email to lowercase
          this.formData.email = this.formData.email.toLowerCase();
          // try to login
          await login(this.formData, this.rememberMeCheckbox, false);

          // Reset Pinia stores après login
          const messagerieStore = useMessagerieStore();
          messagerieStore.clearUsers();
          messagerieStore.clearConversations();

          // and go to home page
          this.$router.push({ path: '/' });
        } catch (error) {
          //console.log(error);
          toaster.showErrorPopup(
            "Le mot de passe ou l'email est incorrect : veuillez saisir un email ou un mot de passe valide."
          );
        } finally {
          this.isLoading = false;
        }
      },
      // login with google
      async handleGoogleLogin() {
        try {
          await googleLogin();
          // Reset Pinia stores après login Google
          const messagerieStore = useMessagerieStore();
          messagerieStore.clearUsers();
          messagerieStore.clearConversations();
          this.$router.push({ path: '/' });
        } catch (error) {
          //console.log({ error });
          return toaster.showErrorPopup(
            'Utilisateur non inscrit. Veuillez vérifier vos informations ou vous inscrire'
          );
        }
      },
      // form validators
      async validateInputForm() {
        // check if form is valid and not the last page
        return (await this.$refs.formLoginRef.validate()).valid;
      },
    },
  };
</script>

<style>
  .login-container input {
    width: 532px;
    border-radius: 2px;
    padding: 5px;
    background-color: var(--surface-bg-2);
  }
  .login-container .v-input__append {
    background-color: var(--surface-bg-2);
    margin-inline-start: 0;
    padding-top: 50% !important;
    padding-right: 5px;
  }
</style>
<style scoped>
  .container {
    height: 100%;
    display: flex;
    flex-direction: column;
    /* justify-content: space-between;*/
    margin-top: inherit;
  }

  .login-header {
    padding-top: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .login-container {
    margin-top: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px;
  }

  .login-ext-buttons-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 20px;
  }

  .remember-container {
    position: relative;
    right: 10px;
  }

  .form-footer {
    margin-top: 10px;
    margin-bottom: 20px;
    gap: 30px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .form-footer-text {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;
  }

  .cross-button-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
  }

  .login-footer {
    min-height: 100px;
    width: 100%;
    gap: 30px;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    /* background-color: var(--text-1); decomment here when register btn will be displayed*/
    color: var(--surface-bg);
  }

  .cross-button {
    width: 32px;
    height: 32px;
    border-radius: 5px;
    background-color: var(--text-1);
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .cross-button img {
    width: 13px;
    height: 13px;
  }

  /* Add media queries to adjust spacing and layout for smaller screens */
  @media screen and (max-width: 768px) {
    .login-container {
      margin-top: 10px;
    }

    .login-ext-buttons-container {
      margin-bottom: 10px;
    }

    .form-footer {
      margin-top: 5px;
    }

    .cross-button-container {
      right: 40px;
    }
  }
</style>
