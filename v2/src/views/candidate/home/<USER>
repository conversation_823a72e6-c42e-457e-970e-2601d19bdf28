<template>
  <main>
    <!-- page visible pour les recruteurs non connectés via le switch -->
    <template v-if="!isLoggedIn && isViewingAsRecruiter">
      <div class="recruiter-landing">
        <RecruiterProcess class="RecruiterProcess" />
        <RecruiterHighlights class="RecruiterHighlights" />
      </div>
    </template>

    <!-- Page visible pour les candidats non connectés -->
    <template v-else-if="!isLoggedIn && !isViewingAsRecruiter">
      <HeroSection />
      <HowDoesItWork />
      <AboutUs />
    </template>

    <!-- Sections pour les recruteurs connectés -->
    <template v-else-if="isLoggedIn && userRole === 'recruiter'">
      <RecruiterProcess class="RecruiterProcess" />
      <RecruiterHighlights class="RecruiterHighlights" />
    </template>

    <!-- Sections pour les candidats connectés -->
    <template v-else-if="isLoggedIn && userRole === 'applicant'">
      <HeroSection />
      <HowDoesItWork />
      <AboutUs />
    </template>
    
    <!--BannerAd/-->

    <!-- Sections communes -->
    <OurPartners />
    <div class="full-width-section">
      <OurValues :userType="userRole" :isLoggedIn="isLoggedIn" />
    </div>
    <LatestNews />
    <TheySpeakOfUs />

    <!--BannerAd/-->
    
    <Feedbacks />
    <!-- Bouton retour en haut -->
    <BackToTopArrow />
  </main>
</template>

<script setup>
  import { useDisplay } from 'vuetify';
  const { mobile } = useDisplay();
</script>

<script>
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import LatestNews from '@/components/views-models/blog/LatestNews.vue';
  import AboutUs from '@/components/views-models/home/<USER>';
  import HeroSection from '@/components/views-models/home/<USER>';
  import HowDoesItWork from '@/components/views-models/home/<USER>';
  import OurPartners from '@/components/views-models/home/<USER>';
  import OurValues from '@/components/views-models/home/<USER>';
  import Feedbacks from '../../../components/views-models/home/<USER>';
  import RecruiterHighlights from '../../../components/views-models/home/<USER>';
  import RecruiterProcess from '../../../components/views-models/home/<USER>';
  import TheySpeakOfUs from '@/components/views-models/home/<USER>';
  import BannerAd from '@/components/google-ad/BannerAd.vue';

  export default {
    name: 'HomePage',

    components: {
      HeroSection,
      RecruiterProcess,
      RecruiterHighlights,
      AboutUs,
      OurValues,
      HowDoesItWork,
      LatestNews,
      OurPartners,
      Feedbacks,
      BackToTopArrow,
      TheySpeakOfUs,
      BannerAd,
    },
    props: {
      isViewingAsRecruiter: {
        type: Boolean,
        default: false,
      },
      user: {
        type: Object,
        required: true,
        default: () => ({ id: null, name: '', type_user: '' }),
      },
    },

    computed: {
      isLoggedIn() {
        return !!this.user.id; // Vérifie si l'utilisateur est connecté
      },
      userRole() {
        return this.user.type_user || 'guest'; // Rôle utilisateur avec valeur par défaut
      },
    },

    watch: {
      user: {
        immediate: true,
        deep: true,
        handler() {
          this.updateTitle();
        },
      },
    },

    mounted() {
      this.updateTitle();

      // Gestion de l'URL
      if (this.isLoggedIn) {
        const currentPath = this.$route.path;
        if (currentPath === '/') {
          if (this.userRole === 'recruiter') {
            this.$router.replace('/recruteur');
          } else if (this.userRole === 'applicant') {
            this.$router.replace('/candidat');
          }
        }
      }
    },

    methods: {
      // la méthode effectue des appels imbriqués
      // on peut l'optimiser avec une API regroupant toutes les données
      updateTitle() {
        if (!this.isLoggedIn) {
          document.title = this.isViewingAsRecruiter
            ? 'Accueil Recruteur Thanks-Boss'
            : 'Accueil Thanks-Boss';
        } else if (this.userRole === 'recruiter') {
          document.title = 'Accueil Recruteur Thanks-Boss';
        } else if (this.userRole === 'applicant') {
          document.title = 'Accueil Candidat Thanks-Boss';
        }
      },
    },
  };
</script>

<style scoped>
  main {
    width: 100%;
    height: fit-content;
  }
  .recruiter-landing {
    display: flex;
    flex-direction: column;
  }
  .RecruiterProcess,
  .RecruiterHighlights {
    margin-bottom: 0;
  }
  .full-width-section {
    width: 100vw;
    display: flex;
    justify-content: center;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
  }
</style>
