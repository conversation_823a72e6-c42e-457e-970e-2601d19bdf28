<template>
    
    <main class="container padding-container">

        <h1 class="title">Formulaire de contact</h1>

        <div class="content-wrapper">

            <!-- contact form -->
            <v-form @submit.prevent :disabled="submitted" ref="contactForm" class="form-container">

                <div class="row1">
                    <div class="field1">
                        <h5>Nom</h5>
                        <v-text-field
                            v-model="formData.nom"
                            :rules="[...nameRules, ...notEmptyRules]"
                            :disabled="submitted"
                            label="Votre nom"
                            variant="solo"
                            flat
                        ></v-text-field>
                    </div>
                    
                    <div class="field1">
                        <h5>Prénom</h5>
                        <v-text-field
                            v-model="formData.prenom"
                            :rules="[...nameRules, ...notEmptyRules]"
                            :disabled="submitted"
                            label="Votre prénom"
                            variant="solo"
                            flat
                        ></v-text-field>
                    </div>
                </div>

                <div class="field2">
                    <h5>Email</h5>
                    <v-text-field
                        v-model="formData.email"
                        :rules="[...emailRules , ...notEmptyRules]"
                        :disabled="submitted"
                        label="Votre email"
                        variant="solo"
                        flat
                    ></v-text-field>
                </div>

                <div class="field2">
                    <h5>Vous-êtes ?</h5>
                    <v-autocomplete
                        clearable
                        chips
                        label="Type d'utilisateur"
                        :items="['Un candidat', 'Un recruteur']"
                        :rules="notEmptyRules"
                        :disabled="submitted"
                        v-model="formData.userType"
                        variant="solo"
                        flat
                    ></v-autocomplete>
                </div>

                <div class="field2">
                    <h5>Quel est l'objet de votre message</h5>
                    <v-autocomplete
                        clearable
                        chips
                        label="Objet du message"
                        :items="selectItems"
                        :rules="notEmptyRules"
                        :disabled="submitted"
                        v-model="formData.options"
                        variant="solo"
                        flat
                    ></v-autocomplete>
                </div>

                <div class="field2">
                    <h5>Votre message</h5>
                    <v-textarea 
                        clearable 
                        label="Champ de texte"
                        :rules="notEmptyRules"
                        :disabled="submitted"
                        v-model="formData.message"
                        variant="solo"
                        flat
                    ></v-textarea>
                </div>
                
                <div class="checkbox-row">
                    <div class="custom-checkbox">
                        <img v-if="checkbox" src="@/assets/search/search-page-filters-checkedbox.svg" @click="toggleCheckbox(index)"/>
                        <img v-else src="@/assets/search/search-page-filters-checkbox.svg" @click="toggleCheckbox(index)"/>
                    </div>
                    <p>J'ai lu et j'accepte la 
                        <u class="custom-link" @click="gotoPage('/politique-confidentialite', 'blank')">politique de confidentialité</u> 
                        et 
                        <u class="custom-link" @click="gotoPage('/cgu', 'blank')">les CGU</u>.</p>
                </div>
                

                <div class="row-btn">
                    <div @click="cancelForm">
                        <PrimaryNormalButton btnColor="secondary" textContent="Annuler"/>
                    </div>

                    <div @click="submitForm">
                        <PrimaryNormalButton textContent="Envoyer"/>
                    </div>          
                </div>  
                
            </v-form>

            <!-- faq link container -->
            <!-- <div class="faq-container">
                <h5>Foire aux questions</h5>
                <p>Vous pouvez également consulter la FAQ de Thanks-Boss qui pourra répondre à vos questions.</p>

                <div class="btn-wrapper">
                    <v-btn append-icon="mdi-arrow-right" class="custom-vbtn" @click="gotoPage('/faq')">Voir la FAQ</v-btn>
                </div>
            </div> -->
        
        </div>

    </main>
    
</template>

<script setup>
import gotoPage from '@/utils/router';
</script>

<script>
import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
import { submitContactForm } from '@/services/contact.service.js';
import { toaster } from '@/utils/toast/toast.js';
import { validateEmail , validateName , validateNotEmpty } from "../../../utils/validationRules"; 

export default {
    name: 'Contact',

    components: {
        PrimaryNormalButton,
    },

    data: () => ({
        submitted: false,                   //  toggle si le formulaire est envoyé
        checkbox: false,                    //  state of checkbox
        selectItems: ['Bug', 'Suggestion', 'Partenariat', 'Autre'],
        formData: {},                       //  datas sent

        /* form rules */
        emailRules: [
        v => validateEmail(v) || true],
        nameRules: [  
        v => validateName(v) || true],
        notEmptyRules: [
        v => validateNotEmpty(v) || true],

    }),

    methods: {
        //  cancel form
        cancelForm() {
            this.formData = {};
        },

        //  validate & submit form
        async submitForm () {
            const validate = await this.$refs.contactForm.validate()
            if (validate.valid && this.checkbox) submitContactForm(this.formData);
            else return toaster.showInfoPopup('Les informations sur le formulaire sont incomplètes.');
        },

        //   toggle between checked and not checked
        toggleCheckbox() {
            this.checkbox = !this.checkbox;
        },
    },
}
</script>

<style scoped>
.container {
    display: flex;
    flex-direction: column;
    gap: 40px;
    height: fit-content;
    padding-bottom: 60px;
}

.title {
    font-size: 40px;
    text-align: center;
}

.content-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 40px;
}

.form-container {
    width: 90%;
    background-color: var(--surface-bg-4);
    box-shadow: 0px 4px 10px 0px rgba(38, 40, 43, 0.1);
    display: flex;
    flex-direction: column;
    gap: 16px;
    border-radius: 10px;
    padding: 16px;
}

.faq-container {
    width: 80%;
    background-color: var(--text-1);
    color: var(--surface-bg-2);
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    padding: 20px;
    gap: 20px;
}

.checkbox-row {
    display: flex;
    gap: 10px;
    margin-left: 6px;
}

.custom-checkbox {
    cursor: pointer;
    width: fit-content;
}

.row1 {
    display: bloc;
    width: 100%;
    justify-content: space-between;
}

.field1 {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.field2 {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.row-btn {
    display: flex;
    width: 100%;
    margin-block: 30px;
    justify-content: space-evenly;
}

.btn-wrapper {
    display: flex;
    width: 100%;
    justify-content: end;
}

.custom-vbtn {
    border: 1px solid #F6B337;
}

.custom-vbtn:hover {
    background-color: var(--yellow-100);
}

.custom-link {
    cursor: pointer;
}


@media screen and (min-width: 992px) {
    .title {
        font-size: 45px;
        
    }

    .row1 {
        display: flex;
        width: 100%;
        justify-content: space-between;
    }

    .field1 {
        width: 49%;
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .form-container {
        width: 60%;
        background-color: var(--surface-bg-4);
        box-shadow: 0px 4px 10px 0px rgba(38, 40, 43, 0.1);
        display: flex;
        flex-direction: column;
        gap: 16px;
        border-radius: 10px;
        padding: 16px;
    }
}

@media screen and (min-width: 2000px) {
  .container {
    padding: 1% 20%;
  }
}

</style>