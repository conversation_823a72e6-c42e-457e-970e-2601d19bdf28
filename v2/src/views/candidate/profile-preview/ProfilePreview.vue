<template>

    <main class="container ">

        <div class="content-wrapper">
            <div class="btn-row padding-container">
                <img src="@/assets/icons/close-btn.svg" alt="bouton fermer" class="close-btn" @click="closePreview"/>
            </div>

            <PreviewProfileSection />
            <PreviewCriteriasSection />
            <PreviewSkillsSection />
            <PreviewExperiencesSection />
            <PreviewFormationsSection />
        </div>


        <BackToTopArrow />

    </main>

</template>

<script>
import PreviewProfileSection from '@/components/views-models/profile-preview/PreviewProfileSection.vue';
import PreviewCriteriasSection from '@/components/views-models/profile-preview/PreviewCriteriasSection.vue';
import PreviewSkillsSection from '@/components/views-models/profile-preview/PreviewSkillsSection.vue';
import PreviewExperiencesSection from '@/components/views-models/profile-preview/PreviewExperiencesSection.vue';
import PreviewFormationsSection from '@/components/views-models/profile-preview/PreviewFormationsSection.vue';
import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';

export default {
    name: 'ProfilePreview',

    components: {
        PreviewProfileSection,
        PreviewCriteriasSection,
        PreviewSkillsSection,
        PreviewExperiencesSection,
        PreviewFormationsSection,
        BackToTopArrow,
    },

    methods: {
        //  toggle preview off
        closePreview() {
            this.$emit("close-preview");
        },
    },
}
</script>

<style scoped>
/* layout */
.content-wrapper {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: fit-content;
    padding-bottom: 40px;
}

.btn-row {
    display: flex;
    justify-content: end;
}

.close-btn {
    width: 32px;
    height: 32px;
    cursor: pointer;
}

/* desktop */
@media screen and (min-width: 992px) {
    .content-wrapper{
        width: 100%;
    }
}

/* large desktop */
@media screen and (min-width: 1800px) {
    .content-wrapper{
        width: 80%;
        margin: 0 auto;
    }
}

/* x-large desktop */
@media screen and (min-width: 2400px) {
    .content-wrapper{
        width: 70%;
        margin: 0 auto;
    }
}

</style>
