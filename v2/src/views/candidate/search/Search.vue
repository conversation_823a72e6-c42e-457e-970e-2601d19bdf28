<template>
  <main class="container padding-container">
    <div v-if="alertPanelOn" class="alert-panel-wrapper">
      <Alert @close-alert-panel="toggleAlertPanel" />
    </div>

    <div v-else class="search-job">
      <SearchBar @search-btn-click="searchJobsFromUrl" />

      <div class="search-content">
        <div class="filters-wrapper">
          <JobSearchFilters @open-alert="toggleAlertPanel" />
        </div>
        <div
          class="loader-container"
          v-if="isLoading && jobOfferList.length === 0"
        >
          <div class="optimized-loader">
            <div class="loader-spinner"></div>
            <p class="loader-text">Recherche en cours...</p>
          </div>
        </div>
        <div v-else class="grid-wrapper">
          <div v-if="jobOfferList.length === 0" class="loader-container">
            <p>Aucun résultat trouvé</p>
          </div>  
          <div v-else>
            <JobOfferGridDisplay
            :jobOfferList="jobOfferList"
            :favoriteJobList="userFavoriteJob"
          />
          </div>
        </div>
      </div>

      <BackToTopArrow />
    </div>
  </main>
</template>

<script>
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import Alert from '@/components/views-models/search/Alert.vue';
  import JobOfferGridDisplay from '@/components/views-models/search/JobOfferGridDisplay.vue';
  import JobSearchFilters from '@/components/views-models/search/JobSearchFilters.vue';
  import SearchBar from '@/components/views-models/search/SearchBar.vue';
  import { getPublishedJobList } from '@/services/job.service';
  import { searchJobWithFilters } from '@/services/search.service.js';
  import { throttle } from '@/utils/performance';
  import { sortJobsWithPriority } from '@/utils/jobNavigation.js';
  import apiOptimizer from '@/services/api-optimizer.service';

  export default {
    name: 'SearchPage',

    components: {
      SearchBar,
      JobSearchFilters,
      JobOfferGridDisplay,
      BackToTopArrow,
      Alert,
    },

    data() {
      return {
        isLoading: false,
        isLoadingMore: false,
        jobOfferList: [], //  Liste des offres d'emploi
        alertPanelOn: false, //  Visibilité du panneau d'alerte
        currentPage: 1, //  Index de la page actuelle des offres chargées
        userFavoriteJob: [], //  Liste des emplois favoris de l'utilisateur
        hasMoreJobs: true,
        pageSize: 8, // Nombre d'offres par page (8 par 8)
        lastScrollPosition: 0, // Dernière position de défilement
      };
    },
    watch: {
      '$route.query': {
        immediate: true,
        handler() {
          if (Object.keys(this.$route.query).length !== 0) {
            this.searchJobsFromUrl();
          } else {
            this.fetchJobOffersWithAuthors();
          }
        },
      },
    },
    mounted() {
      // Initialiser le chargement des données
      if (Object.keys(this.$route.query).length === 0) {
        this.fetchJobOffersWithAuthors();
      }
      // Gestion du scroll infini via window
      window.addEventListener('scroll', this.handleScroll);
    },
    beforeDestroy() {
      window.removeEventListener('scroll', this.handleScroll);
    },
    // Pour Vue 3, utiliser unmounted() { ... } à la place
    methods: {
      // Gestion du scroll infini comme côté recruteur
      handleScroll() {
        if (Object.keys(this.$route.query).length !== 0) {
          return;
        }
        if (!this.hasMoreJobs || this.isLoadingMore) {
          return;
        }
        const scrollPosition = window.innerHeight + window.scrollY;
        const threshold = document.body.offsetHeight - 100;
        if (scrollPosition >= threshold) {
          this.currentPage++;
          this.loadMoreItems();
        }
      },
      // Charger plus d'éléments lorsque l'utilisateur fait défiler vers le bas
      async loadMoreItems() {
        if (!this.hasMoreJobs || this.isLoadingMore) return;
        this.isLoadingMore = true;
        await this.fetchJobOffersWithAuthors(true);
        this.isLoadingMore = false;
      },
      async fetchJobOffersWithAuthors(isLoadingMore = false) {
        if (Object.keys(this.$route.query).length !== 0) {
          return;
        }

        if (!isLoadingMore) {
          this.isLoading = true;
        }

        // Force pageSize à 8 à chaque appel
        this.pageSize = 8;

        try {
          const result = await apiOptimizer.loadPaginatedData(
            '/jobs/',
            {
              page: this.currentPage,
              pageSize: this.pageSize,
            }
          );

          //console.log('Résultat de la requête:', result);

          // Vérifier si le résultat est au format attendu
          if (!result || typeof result !== 'object') {
            //console.error('Format de réponse inattendu:', result);
            this.isLoading = false;
            return;
          }

          // Extraire les jobs et hasNext du résultat
          let jobs, hasNext;

          if (result.jobs && Array.isArray(result.jobs)) {
            // Format { jobs: [...], hasNext: boolean }
            jobs = result.jobs;
            hasNext = result.hasNext;
          } else if (result.results && Array.isArray(result.results)) {
            // Format { results: [...], next: string }
            jobs = result.results;
            hasNext = result.next && result.next.length > 0;
          } else if (Array.isArray(result)) {
            // Format [...]
            jobs = result;
            hasNext = false;
          } else {
            //console.error('Format de données non reconnu:', result);
            this.isLoading = false;
            return;
          }

          //console.log('Jobs extraits:', jobs);
          //console.log('hasNext:', hasNext);

          this.hasMoreJobs = hasNext;

          if (this.currentPage === 1) {
            // Appliquer le tri avec priorité pour la première page
            this.jobOfferList = sortJobsWithPriority(jobs);
          } else {
            // Ajouter les nouvelles offres à la liste existante et re-trier
            const allJobs = [...this.jobOfferList, ...jobs];
            this.jobOfferList = sortJobsWithPriority(allJobs);
          }

          //console.log('jobOfferList après mise à jour:', this.jobOfferList);
        } catch (error) {
          //console.error(
          //  "Erreur lors du chargement des offres d'emploi:",
          //  error
          //);
        } finally {
          this.isLoading = false;
        }
      },
      // Méthode pour gérer les changements des paramètres de recherche
      async searchJobsFromUrl(filterDatas) {
        this.filters = filterDatas;
        const query = this.$route.query;
        if (Object.keys(query).length === 0) {
          return;
        }

        this.isLoading = true;
        this.currentPage = 1; // Réinitialiser la pagination

        // Préparer les paramètres de recherche
        const params = {
          ville: query.ville,
          title: query.title,
          sort: query.sort ? query.sort.replace(/[\[\]]/g, '') : '',
          contrat: query.contract
            ? query.contract.replace(/[\[\]]/g, '').split(',')
            : [],
          teletravail: query.remote
            ? query.remote.replace(/[\[\]]/g, '').split(',')
            : [],
          page: this.currentPage,
          pageSize: this.pageSize,
        };

        let allCombinations = [
          {
            ville: params?.ville || '',
            title: params?.title || '',
            sort: params?.sort || '',
            contrat: '',
            teletravail: '',
          },
        ];

        if (params.contrat?.length > 0) {
          allCombinations = params.contrat.flatMap((contrat) =>
            allCombinations.map((combo) => ({
              ...combo,
              contrat: contrat,
            }))
          );
        }

        if (params.teletravail.length > 0) {
          allCombinations = params.teletravail.flatMap((teletravail) =>
            allCombinations.map((combo) => ({
              ...combo,
              teletravail: teletravail,
            }))
          );
        }

        try {
          const searchResults = await Promise.all(
            allCombinations.map((params) => searchJobWithFilters(params))
          );

          // Vérifier la structure des résultats et extraire les données correctement
          const allResults = searchResults.flatMap((response) => {
            // Vérifier si la réponse a une propriété 'results'
            if (response && response.results) {
              return response.results;
            }
            // Vérifier si la réponse est un tableau
            else if (Array.isArray(response)) {
              return response;
            }
            // Sinon, retourner un tableau vide
            return [];
          });

          //console.log('Résultats de recherche:', allResults);

          // S'assurer que nous avons des résultats valides avant de les traiter
          if (allResults && allResults.length > 0) {
            const uniqueResults = [
              ...new Map(allResults.map((item) => [item.id, item])).values(),
            ];
            // Appliquer le tri avec priorité aux résultats de recherche
            this.jobOfferList = sortJobsWithPriority(uniqueResults);
          } else {
            this.jobOfferList = [];
            console.warn('Aucun résultat trouvé pour la recherche');
          }
          this.isLoading = false;
        } catch (error) {
          //console.error(
          //  'Erreur lors de la recherche avec les filtres :',
          //  error
          //);
          this.isLoading = false;
        }
      },
      // Basculer la visibilité du panneau d'alerte
      toggleAlertPanel() {
        this.alertPanelOn = !this.alertPanelOn;
      },
    },
  };
</script>

<style scoped>
  .container {
    background-color: var(--search-candidate-bg-color);
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    width: 100%;
    height: fit-content;
  }

  .search-content {
    width: 100%;
    height: auto;
    display: flex;
    flex-direction: row;
    overflow-x: hidden;
    box-sizing: border-box;
    padding-right: 0;
  }

  .filters-wrapper {
    overflow-y: auto;
    flex-shrink: 0;
  }

  .grid-wrapper {
    flex: 1 1 0%;
    min-width: 0;
    width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;
    padding-right: 0;
  }

  /* Styles pour la liste virtuelle */
  .virtual-list {
    width: 100%;
    height: 100%;
    min-height: 600px;
  }

  /* Styles pour l'indicateur de chargement */
  .optimized-loader {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
  }

  .loader-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #f6b337;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loader-spinner-small {
    width: 30px;
    height: 30px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #f6b337;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loader-text {
    margin-top: 20px;
    font-size: 16px;
    color: #333;
  }

  .load-more-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0;
    width: 100%;
    gap: 10px;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .alert-panel-wrapper {
    display: flex;
    justify-content: center;
    width: 100%;
    max-width: 800px; /* Augmentation de la largeur maximale pour le panneau d'alerte */
    margin: 0 auto;
  }

  .loader-container {
    min-height: 50vh;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  /* ✅ MOBILE */
  @media screen and (max-width: 480px) and (max-width: 767px) {
    .search-content {
      flex-direction: column;
    }
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }
</style>
