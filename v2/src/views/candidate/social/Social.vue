<template>

  <main class="container padding-container">

    <div class="content-wrapper">

      <p>Lorem ipsum dolor sit amet consectetur. <PERSON><PERSON> et morbi euismod eget ipsum nibh eget suspendisse laoreet. Nec velit vel in metus at varius faucibus cras mattis.</p>

      <div class="main-container">

        <section class="left-section">

          <!-- filters -->
          <div>
            <FilterPost  @selected-filter="getFilterValue"/>
          </div>

          <!-- advertisement -->
          <div>
            <Advertisement />
          </div>

        </section>
        
        <section class="right-section">

          <!-- post loop -->
          <RedactPost />

          <div v-for="(post,index) in postList" class="post-wrapper">
            <Post :content="post"/>
          </div>
          

        </section>

      </div>

    </div>

  </main>

</template>

<script>
import Post from '@/components/views-models/social/Post.vue';
import RedactPost from '@/components/views-models/social/RedactPost.vue';
import Advertisement from '@/components/views-models/social/Advertisement.vue';
import FilterPost from '@/components/buttons/FilterPost.vue';
import { getPosts } from '@/services/post.service';

export default {
  name: 'Social',

  components: {
    Post,
    RedactPost,
    Advertisement,
    FilterPost,
  },

  data() {
    return {
      selectedFilter: null,   //  selected filter
      postList: [                //  list of post
        {  
          "avatarImgPath": null,
          "gallery": ["@/assets/home-section-news-article2.png","@/assets/home-section-news-article1.png","@/assets/home-section-news-article2.png"],
          "commentList": ["Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.","Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.","Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."],
          "title": "Nom Prénom",
          "desc": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
        },
      ], 
    }
  },

  mounted() {
    //  TODO get all posts from server
    //this.postList = this.getPostList();
  },

  methods: {
    //  select a filter 1 = pertinence, 2 = date
    getFilterValue(value) {
      this.selectedFilter = value;
    },

    //  get list of the posts from server
    getPostList() {
      const list = getPosts();
      return list;
    }
  }
};
</script>

<style scoped>
.container {
  width: 100%;
  height: fit-content;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 60px;
}

.main-container {
  margin-top: 52px;
  gap: 16px;
  display: flex;
}

.left-section {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.right-section {
  display: flex;
  flex-direction: column;
  gap: 40px;
}
</style>
