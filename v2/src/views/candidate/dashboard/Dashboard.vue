<template>
  <main class="container padding-container">
    <h1 class="dashboard-h1">Tableau de bord</h1>
    <GraphSection :user="user" :mobile="mobile" />
    <SearchYourJob />
    <RecentJobs :jobs="recentJobs" :mobile="mobile" :userTitle="user.titre" />
    <!-- <SubscriptionSection :subscriptionList="subscriptionList" :mobile="mobile" /> -->
    <HowDoesItWork />
    <LatestNews />

    <BackToTopArrow />
  </main>
</template>

<script setup>
  import { useDisplay } from 'vuetify';
  const { mobile } = useDisplay();
</script>

<script>
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import gotoPage from '@/utils/router';
  import GraphSection from '@/components/views-models/dashboard/GraphSection.vue';
  import SearchYourJob from '@/components/views-models/dashboard/SearchYourJob.vue';
  import RecentJobs from '@/components/views-models/dashboard/RecentJobs.vue';
  import HowDoesItWork from '@/components/views-models/home/<USER>';
  import LatestNews from '@/components/views-models/blog/LatestNews.vue';
  import SubscriptionSection from '@/components/views-models/dashboard/SubscriptionSection.vue';
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';

  export default {
    name: 'Dashboard',
    components: {
      PrimaryNormalButton,
      GraphSection,
      SearchYourJob,
      RecentJobs,
      HowDoesItWork,
      LatestNews,
      SubscriptionSection,
      BackToTopArrow,
    },
    props: {
      user: {
        type: Object,
        required: true,
        default: () => {},
      },
    },
    data() {
      return {
        recentJobs: [
          {
            id: 1,
            title: '1Développeur web',
            company: 'Google',
            local: 'Paris',
            logo_url: 'https://via.placeholder.com/150',
            date: '12/12/2021',
            descriptif:
              'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla nec purus feugiat, molestie ipsum et, consequat nunc. Nulla facilisi. Nullam vel semper turpis. Nulla nec purus feugiat, molestie ipsum et, consequat nunc. Nulla facilisi. Nullam vel semper turpis.',
          },
          {
            id: 2,
            logo_url: 'https://via.placeholder.com/150',
            title: '2Développeur web',
            company: 'Google',
            local: 'Paris',
            date: '12/12/2021',
            descriptif:
              'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla nec purus feugiat, molestie ipsum et, consequat nunc. Nulla facilisi. Nullam vel semper turpis. Nulla nec purus feugiat, molestie ipsum et, consequat nunc. Nulla facilisi. Nullam vel semper turpis.',
          },
          {
            id: 3,
            title: '3Développeur web',
            logo_url: 'https://via.placeholder.com/150',
            company: 'Google',
            local: 'Paris',
            date: '12/12/2021',
            descriptif:
              'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla nec purus feugiat, molestie ipsum et, consequat nunc. Nulla facilisi. Nullam vel semper turpis. Nulla nec purus feugiat, molestie ipsum et, consequat nunc. Nulla facilisi. Nullam vel semper turpis.',
          },
        ],
        subscriptionList: [
          {
            id: 1,
            offer: 'Gratuit',
            price: '0',
            annualPrice: '0',
            description: ['desc 1', 'desc 2', 'desc 3'],
          },
          {
            id: 2,
            offer: '2',
            price: '101',
            annualPrice: '0',
            description: [
              'desc 1',
              'desc 2',
              'desc 3222222 2222222 222222222222 22222222 2 2222222 222222 222222222222',
            ],
          },
          {
            id: 3,
            offer: '3',
            price: '2220',
            annualPrice: '0',
            description: ['desc 1', 'desc 2', 'desc 3'],
          },
        ],
      };
    },
    methods: {
      gotoPage,
      fetchRecentJobs() {
        // fetch recent jobs
      },
    },
  };
</script>

<style scoped>
  main {
    height: fit-content;
    width: 100%;
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }
</style>
