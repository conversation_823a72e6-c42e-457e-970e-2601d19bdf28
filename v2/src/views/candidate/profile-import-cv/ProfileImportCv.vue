<template>
  <main class="container padding-container">
      <h1 class="page-title">Importer mes données CV</h1>

      <div class="container padding-container">
          <div class="section-container">
              <h5>Choisis les informations que tu veux récupérer dans ton CV</h5>
              
              <div class="checkbox-wrapper">
                  <input
                      type="checkbox"
                      v-model="selectAll"
                      @change="toggleAll"
                      class="custom-checkbox"
                      id="custom-checkbox"
                  />
                  <label class="custom-label" for="custom-checkbox">Tout importer</label>
              </div>
          </div>
      </div>

      <div>
          <PreviewProfileSection :checked="selectAll" @updateChecked="updateChecked('profile')"/>
          <PreviewCriteriasSection />
          <PreviewFormationsSection :checked="selectAll" @updateChecked="updateChecked('formations')"/>
          <PreviewSkillsSection :checked="selectAll" @updateChecked="updateChecked('skills')"/>
          <PreviewExperiencesSection :checked="selectAll" @updateChecked="updateChecked('experiences')"/>

      </div>

      <div class="btn-row padding-container">
          <div @click="closeImportPreview">
              <PrimaryNormalButton textContent="Annuler" btnColor="secondary"/>
          </div>

          <div @click="saveImportedContent">
              <PrimaryNormalButton textContent="Enregistrer"/>
          </div>
      </div>

      <BackToTopArrow />
  </main>
</template>
<script>
import PreviewProfileSection from '@/components/views-models/profile-preview/PreviewProfileSection.vue';
import PreviewCriteriasSection from '@/components/views-models/profile-preview/PreviewCriteriasSection.vue';
import PreviewFormationsSection from '@/components/views-models/profile-preview/PreviewFormationsSection.vue';
import PreviewSkillsSection from '@/components/views-models/profile-preview/PreviewSkillsSection.vue';
import PreviewExperiencesSection from '@/components/views-models/profile-preview/PreviewExperiencesSection.vue';
import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
import axiosRequest from '../../../services/axios';
import { toaster } from '@/utils/toast/toast.js';
import { toast } from "vue3-toastify";

export default {
  name: 'ProfilePreview',

  props: {
      defaultCv: {
          type: String,
          required: true,
      }
  },

  components: {
      PreviewProfileSection,
      PreviewFormationsSection,
      PreviewSkillsSection,
      PreviewExperiencesSection,
      PreviewCriteriasSection,
      PrimaryNormalButton,
      BackToTopArrow,
  },

  data() {
      return {
          selectAll: false,
          checkedSections: {
              profile: false,
              formations: false,
              skills: false,
              experiences: false,
          },
          formations: [],
          experiences:[],
          type_extract: [],
      };
  },
  methods: {
      toggleAll(event) {
          this.selectAll = event.target.checked;
          this.updateAllSections();
      },

      updateChecked(section, isChecked) {
          this.checkedSections[section] = isChecked;
      },

      updateAllSections() {
          this.checkedSections = {
              profile: this.selectAll,
              formations: this.selectAll,
              skills: this.selectAll,
              experiences: this.selectAll,
          };
      },

      closeImportPreview() {
          this.$emit('close-import');
      },

      async saveImportedContent() {
          //console.log('Données de la section formations:', this.formations);
  if (this.selectAll) {
      this.type_extract = [ 'experiences', 'formations'];
  } else {
      this.type_extract = []; // Clear array before pushing
      if (this.checkedSections.profile) this.type_extract.push('profile');
      if (this.checkedSections.skills) this.type_extract.push('competences');
      if (this.checkedSections.experiences) this.type_extract.push('experiences');
      if (this.checkedSections.formations) this.type_extract.push('formations');
  }

  if (this.type_extract.length === 0) {
      toaster.showErrorPopup('Aucune donnée à importer');
      return;
  }

  const extractedData = [...this.type_extract]; 
  //console.log('Données extraites:', extractedData); 

  const payload = { type_extract: extractedData };
  //console.log('Payload envoyé:', JSON.stringify(payload)); 

  const toastId = toaster.showInfoPopupCVparser('Parsing du CV en cours...', { autoClose: false });

  try {
      const response = await axiosRequest().post('/user/cvs/parse/', payload, {
          headers: { 'Content-Type': 'application/json' },
      });
      //console.log('API response:', response);
      toast.update(toastId, {
          render: 'CV parsé avec succès!',
          type: toast.TYPE.SUCCESS,
          autoClose: 3000,
      });
      await new Promise(r => setTimeout(r, 5000));
      this.$router.push({ path: '/profil', query: { importerData: true } });
  } catch (error) {
      //console.error('Error during API call:', error);
      toast.update(toastId, {
          render: 'Échec du parsing du CV.',
          type: toast.TYPE.ERROR,
          autoClose: 2000,
      });
  }
}

}
}
</script>



<style scoped>
/* layout */
.container {
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: 20px;
  height: fit-content;
  padding-bottom: 40px;
}   



.section-container {
  background-color: var(--white-200);
  width: 100%;
  border-radius: 2px;
  padding: 16px;
  border-radius: 5px;
}

.checkbox-wrapper {
  width: fit-content;
  margin-top: 20px;
  display: flex;
  align-content: center;
}
label{
  font-weight: 300;
}
.btn-row {
  display: flex;
  width: 100%;
  gap: 80px;
  justify-content: center;
}

.close-btn {
  width: 32px;
  height: 32px;
  cursor: pointer;
}

.page-title {
  align-self: center;
}

.custom-checkbox {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;

}


.custom-label {
  position: relative;
  cursor: pointer;
  font-size: 18px;
  padding-left: 40px; 
}


.custom-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 25px;
  height: 25px;
  background-color: #fff;
  border: 2px solid rgba(246, 179, 55, 1);
  border-radius: 5px;
}


.custom-checkbox:checked + .custom-label::before {
  background-color: rgba(246, 179, 55, 1);
}

.custom-checkbox:checked + .custom-label::after {
  content: '';
  position: absolute;
  left: 8px;
  top: 2px;
  width: 10px;
  height: 16px;
  border: solid black;
  border-width: 0px 3px 2px 0;
  transform: rotate(45deg)
}

</style>