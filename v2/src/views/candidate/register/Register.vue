<template>
  <div class="container">
    <div class="container-content">
      <section class="illustration-section">
        <figure class="illustration">
          <!-- stepper

        <v-btn
          @click="$router.back()"
          size="large"
          color="black"
          class="btn-return"
          prepend-icon="@/assets/ArrowBack.svg"
        >
          <template v-slot:prepend>
            <img src="@/assets/ArrowBack.svg" />
          </template>
          <span class="return-text">Retour</span>
        </v-btn>-->
          <img
            src="@/assets/candidate_register_illustration.webp"
            alt="loupe"
          />
        </figure>
      </section>

      <section class="form-container-register">
        <main class="main-container">
          <!-- header -->
          <div class="form-container-header">
            <h1>Inscription</h1>
            <p>Candidat</p>
          </div>

          <!-- stepper -->
          <div ref="stepperContainer">
            <v-stepper
              v-model="page"
              :items="stepperItems"
              @keydown.enter="validate"
              class="stepper-container-email-register"
              next-text="Suivant"
              prev-text="Retour"
              ref="stepper"
              hide-actions
            >
              <!-- STEPS -->
              <template v-slot:item.1>
                <v-card class="padding-inline-40">
                  <v-form ref="firstForm" @submit.prevent>
                    <div class="first-step-container">
                      <div @click="signUpWithGoogle">
                        <GoogleRegister />
                      </div>
                      <p>OU</p>
                      <v-btn
                        :disabled="page === stepperItems.length"
                        @click="validate"
                        size="large"
                        variant="flat"
                        theme="dark"
                      >
                        Créer un compte
                      </v-btn>

                      <div>
                        <v-checkbox
                          v-model="newslettersIsChecked"
                          color="var(--yellow-100)"
                          label="Je souhaite m'inscrire et recevoir les newsletters."
                          hide-details
                        ></v-checkbox>
                        <v-checkbox
                          v-model="termsIsChecked"
                          color="var(--yellow-100)"
                          hide-details
                          :rules="[(v) => !!v || 'Vous devez accepter les CGU']"
                        >
                          <template #label>
                            <p class="text-regex">
                              J'ai lu et j'accepte&nbsp;<a
                                href="https://thanks-boss.com/politique-confidentialite"
                                target="_blank"
                                class="underline-link"
                                >la politique de confidentialité&nbsp;</a
                              >
                              et les&nbsp;<a
                                href="https://thanks-boss.com/cgu"
                                target="_blank"
                                class="underline-link"
                                >CGU</a
                              >.
                            </p>
                          </template>
                        </v-checkbox>
                      </div>
                    </div>
                  </v-form>
                </v-card>
              </template>
              <template v-slot:item.2>
                <v-card class="padding-inline-40">
                  <!-- <h4 class="center">Informations complémentaires</h4> -->
                  <v-form ref="secondForm">
                    <label for="nom">
                      <h5>Nom</h5>
                      <v-text-field
                        v-model="formData.last_name"
                        placeholder="Votre nom"
                        :rules="notEmptyRules"
                      />
                    </label>

                    <label for="prenom">
                      <h5>Prénom</h5>
                      <v-text-field
                        v-model="formData.first_name"
                        placeholder="Votre prénom"
                        :rules="notEmptyRules"
                      />
                    </label>

                    <label for="metier">
                      <h5>Métier</h5>
                      <v-text-field
                        v-model="formData.metier"
                        placeholder="Votre métier"
                        :rules="notEmptyRules"
                      />
                    </label>
                    <label for="email">
                      <h5>Adresse</h5>
                      <v-text-field
                        class="custom-location-input"
                        textContent="Votre adresse complète"
                        @city-and-postal-code="getCityAndPostalCodeValue"
                        :paramLocation="formData.ville"
                      />
                    </label>

                    <label for="email">
                      <h5>Email</h5>
                      <v-text-field
                        type="email"
                        :rules="[...emailRules, ...notEmptyRules]"
                        v-model="formData.email"
                        placeholder="Votre mail"
                      />
                    </label>
                    <label for="password">
                      <div>
                        <h5 class="mdp">
                          Mot de passe
                          <p class="password-hint">
                            (Le mot de passe doit contenir au moins 8
                            caractères, incluant une majuscule, une minuscule,
                            un chiffre et un caractère spécial.)
                          </p>
                        </h5>
                      </div>
                      <v-text-field
                        autocomplete="new-password"
                        v-model="formData.password"
                        :rules="[...passwordRules, ...notEmptyRules]"
                        placeholder="***********"
                        :type="!showPassword ? 'password' : 'text'"
                        :append-icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
                        @click:append="showPassword = !showPassword"
                      />
                    </label>

                    <!-- password verif -->
                    <label for="password2">
                      <h5>Confirmer le mot de passe</h5>
                      <v-text-field
                        autocomplete="new-password"
                        v-model="formData.password2"
                        :rules="[...password2Rules, ...notEmptyRules]"
                        placeholder="***********"
                        :type="!isPassword2Hide ? 'password' : 'text'"
                        :append-icon="
                          isPassword2Hide ? 'mdi-eye-off' : 'mdi-eye'
                        "
                        @click:append="isPassword2Hide = !isPassword2Hide"
                    /></label>
                  </v-form>

                  <div class="btn-container">
                    <v-btn
                      v-if="page !== 1"
                      class="height"
                      @click="stepper.prev()"
                      size="large"
                      variant="flat"
                      theme="dark"
                    >
                      Retour
                    </v-btn>
                    <PrimaryNormalButton
                      v-if="page !== 1"
                      @click="submitForm"
                      textContent="Continuer"
                    />
                  </div>
                </v-card>
              </template>

              <template v-slot:item.3>
                <v-card class="padding-inline-40">
                  <v-form ref="thirdForm">

                    <label for="state">
                      <h5>Quel est votre état actuel ?</h5>
                      <v-select
                        v-model="formData.currentState"
                        :items="stateOptions"
                        label="Sélection"
                        :rules="notEmptyRules"
                      />
                    </label>

                    <label for="source">
                      <h5>Comment avez-vous connu Thanks-Boss ?</h5>
                      <v-select
                        v-model="formData.sourceState"
                        :items="sourceOptions"
                        label="Sélection"
                        :rules="notEmptyRules"
                      />
                    </label>


                  </v-form>
                  <div class="btn-container">

                    <v-btn
                      v-if="page !== 1"
                      class="height"
                      @click="this.$refs.stepper.prev()"
                      size="large"
                      variant="flat"
                      theme="dark"
                    >
                      Retour
                    </v-btn>
                    <PrimaryNormalButton
                      v-if="page !== 1"
                      @click="submitForm"
                      textContent="Continuer"
                    />
                  </div>
                </v-card>
              </template>

              <template v-slot:item.4>
                <v-card class="padding-inline-40">
                  <v-form ref="fourthForm">
                    <!--h3 class="photo-title"> Une photo de profil augmente tes chances d'être remarqué(e) par les recruteurs.</h3-->
                    <label for="photo">
                        <div class="photo-upload-container">
                            <div class="photo-preview-container">
                                <v-img
                                  :src="previewImage || require('@/assets/search/search-page-jobcard-emptylogo-icon.jpg')"
                                  height="180"
                                  width="180"
                                  class="profile-image-preview"
                                  cover
                                />
                                <!--div class="photo-overlay" v-if="!previewImage">
                                  <v-icon size="large" color="white">mdi-camera</v-icon>
                                </div-->
                            </div>
                            <div class="photo-upload-instructions">
                                <h5>Ajouter une photo de profil</h5>
                                <p class="photo-hint">Une photo professionnelle augmente vos chances d'être contacté.</p>
                                <PrimaryNormalButton
                                   @click="openFileDialog"
                                  textContent="Choisir une photo"
                                  class="upload-button"
                                />
                            </div>
                        </div>

                        <!-- Input pour choisir une image -->
                        <v-file-input
                          ref="fileInput"
                          v-model="formData.profilePicture"
                          accept="image/*"
                          prepend-icon="mdi-camera"
                          style="display: none;"
                          @change="handleImageChange"
                        />
                      </label>
                  </v-form>
                  <div class="btn-container">

                    <v-btn
                      v-if="page !== 1"
                      class="height"
                      @click="this.$refs.stepper.prev()"
                      size="large"
                      variant="flat"
                      theme="dark"
                    >
                      Retour
                    </v-btn>
                    <PrimaryNormalButton
                      v-if="page !== 1"
                      @click="confirmForm"
                      textContent="Je crée mon compte"
                    />
                  </div>
                </v-card>
              </template>

            </v-stepper>
          </div>

          <!-- btn already registered -->
          <div
            v-if="page !== stepperItems.length"
            class="already-registered-container"
          >
            <p>Tu as déjà un compte chez Thanks-Boss ?</p>
            <p
              class="text-underline cursor-pointer"
              @click="gotoPage('/connexion')"
            >
              Connecte-toi ici
            </p>
          </div>
          <div class="stepper-footer">
            <p>
              Vous êtes recruteur ?<br />
              Venez-vous inscrire chez Thanks-Boss
            </p>
            <div @click="gotoPage('/recruteur/inscription')">
              <PrimaryNormalButton textContent="Espace recruteur" />
            </div>
          </div>
        </main>
      </section>
    </div>

    <!-- Modal de confirmation pour création de compte sans photo -->
    <ConfirmationModal
      v-if="showConfirmationModal"
      title="Attention !"
      :description="popupDescription"
      @close="closeConfirmationModal"
      @confirm="handleConfirmation"
      class="centered-modal"
    />
  </div>
</template>

<script setup>
  import { ref } from 'vue';
  const stepper = ref(null);
  const showPassword = ref(false);
</script>

<script>
  import LocationInput from '@/components/inputs/LocationInput.vue';
  import GoogleRegister from '@/components/buttons/GoogleRegister.vue';
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import ConfirmationModal from '@/components/modal/confirmation/ConfirmationModal.vue';
  import { login, register } from '@/services/account.service';
  import gotoPage from '@/utils/router.js';
  import { toaster } from '@/utils/toast/toast';
  import {
    validateEmail,
    validatePassword,
    validatePassword2,
    validateNotEmpty,
    validateMobile,
  } from '@/utils/validationRules';
  import { decodeCredential, googleOneTap } from 'vue3-google-login';

  export default {
    name: 'RegisterCandidatePage',
    components: {
      PrimaryNormalButton,
      LocationInput,
      ConfirmationModal,
      GoogleRegister,
    },
    data() {
      return {
        formData: {
          type_user: 'applicant',
          googleSub: null,
          first_name: '',
          last_name: '',
          metier: '',
          email: '',
          password: '',
          password2: '',
          numberPhone: '',
          ville: '',
          currentState: 'Employé(e)',
          sourceState: 'Facebook',
          profilePicture: '',
          crédit: 60,
          abonnement: 'beta_test',
        },
        showConfirmationModal: false,
        previewImage: null,
        notEmptyRules: [(v) => !!v || 'Ce champ est requis'],
        stateOptions: [
          'Stage/alternance',
          '1er job',
          'Demandeur d\'emploi',
          'Reconversion professionnelle',
          'Autre',
        ],
        sourceOptions: [
          'Facebook',
          'LinkedIn',
          'Entretiens',
          'Youtube',
          'TikTok',
          'Instagram',
        ],
        isPassword1Hide: false,
        isPassword2Hide: false,
        newslettersIsChecked: false,
        termsIsChecked: false,
        page: null,
        stepperItems: ['1', '2','3','4'],
        emailRules: [(v) => validateEmail(v) || true],
        passwordRules: [(v) => validatePassword(v) || true],
        password2Rules: [
          (v) => validatePassword2(v, this.formData.password) || true,
        ],
        mobileRules: [(v) => validateMobile(v) || true],
        notEmptyRules: [(v) => validateNotEmpty(v) || true],
        popupDescription: `Vous n'avez pas ajouté de photo de profil.<br><br><span class='bold-important'>En raison du fonctionnement de l'algorithme, une photo professionnelle augmente vos chances d'être contacté par les recruteurs.</span><br><br>Souhaitez-vous quand même créer votre compte sans photo ?`,
      };
    },
    methods: {
      gotoPage,
      getCityAndPostalCodeValue(cityAndPostalCode) {
        if (cityAndPostalCode) {
          this.formData['ville'] = cityAndPostalCode[0];
          this.formData['code_postal'] = cityAndPostalCode[1];
        }
      },
      // form validators
      async validate() {
        //console.log(this.page);
        const getStepper = this.$refs.stepper;
        let valid = false;

        try {
          switch (this.page) {
            case 1:
              if (this.$refs.firstForm) {
                // //console.log('firstForm', this.formData);
                this.formData.password = '';
                this.formData.password2 = '';
                valid = (await this.$refs.firstForm.validate()).valid;
                break;
              }
            case 2:
              if (this.$refs.secondForm) {
                // //console.log('secondForm', this.formData);
                valid = (await this.$refs.secondForm.validate()).valid;
              }
              break;
            case 3:
              if (this.$refs.thirdForm) {
                  // //console.log('secondForm', this.formData);
                  valid = (await this.$refs.thirdForm.validate()).valid;
                }
              break;
            default:
              break;
          }
        } catch (error) {
          //console.log(error);
        }

        // Vérifiez si le formulaire est valide et n'est pas la dernière page
        if (valid && this.page < getStepper.items.length) {
          getStepper.next();
          const stepperContainer = this.$refs.stepperContainer;
          stepperContainer.scrollTop = 0;
        } else if (valid && this.page === getStepper.items.length) {
          return true; // Dernière page
        } else {
          this.$nextTick(() => {
            const firstInvalidField = this.$el.querySelector('.v-input--error');
            if (firstInvalidField) {
              firstInvalidField.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
              });
            }
          });
          return false;
        }
      },
      async submitForm() {
        const valid = await this.validate();
        if (!valid) return;
        else if (!this.termsIsChecked) {
            toaster.showErrorPopup(
              "Veuillez accepter la politique de confidentialité et les conditions d'utilisation."
            );
            return;
      }
      else{
        const getStepper = this.$refs.stepper;
        getStepper.next();
      }
      // const getStepper = this.$refs.stepper;
      // getStepper.next();

      },
      async confirmForm() {
        try {
          // Vérifier si l'utilisateur a ajouté une photo de profil
          if (!this.formData.profilePicture) {
            this.showConfirmationModal = true;
            return;
          }

          await this.submitRegistration();
        } catch (error) {
          //console.error("Erreur de l'API :", error.response);
          const errorMessage =
            error.message || 'Erreur à la création de votre compte.';
          if (
            errorMessage.includes(
              'Un objet account avec ce champ email existe déjà.'
            )
          ) {
            toaster.showErrorPopup(
              'Un compte avec cette adresse mail existe déjà.'
            );
          } else {
            toaster.showErrorPopup(errorMessage);
          }
        } finally {
          this.isLoading = false;
        }
      },

      async submitRegistration() {
        this.isLoading = true;
        this.formData.email = this.formData.email.toLowerCase();

        // Création du FormData pour inclure la photo
        const formDataToSend = new FormData();
        formDataToSend.append('email', this.formData.email);
        formDataToSend.append('password', this.formData.password);
        formDataToSend.append('password2', this.formData.password2);
        formDataToSend.append('type_user', 'applicant');
        formDataToSend.append('first_name', this.formData.first_name || '');
        formDataToSend.append('last_name', this.formData.last_name || '');
        formDataToSend.append('metier', this.formData.metier || '');
        formDataToSend.append('numberPhone', this.formData.numberPhone);
        formDataToSend.append('recherche_info', this.formData.currentState);
        formDataToSend.append('venu_info', this.formData.sourceState);
        formDataToSend.append('crédit', this.formData.crédit);
        formDataToSend.append('abonnement', this.formData.abonnement);
        if (this.formData.profilePicture) {
          formDataToSend.append('photo', this.formData.profilePicture);
        }

        await register(formDataToSend);

        await login({
          email: this.formData.email,
          password: this.formData.password,
        });
        this.$router.push('/profil');
      },

      handleConfirmation() {
        this.showConfirmationModal = false;
        this.submitRegistration();
      },

      closeConfirmationModal() {
        this.showConfirmationModal = false;
      },
      openFileDialog() {
      this.$refs.fileInput.click(); // Ouvre le sélecteur de fichier
      },
      handleImageChange(event) {
        const file = event.target.files[0];
        if (file) {
          this.previewImage = URL.createObjectURL(file);
          this.formData.profilePicture = file;
        }
      },
      handleFileChange(file) {
      if (file && file[0]) {
        const reader = new FileReader();
        reader.onload = (e) => {
          this.formData.profilePicture = e.target.result; // Set the preview source
        };
        reader.readAsDataURL(file[0]); // Read the file as a data URL for preview
      }
      },
      async signUpWithGoogle() {
        try {
          if (!this.termsIsChecked) {
            toaster.showErrorPopup(
              "Veuillez accepter la politique de confidentialité et les conditions d'utilisation."
            );
            return;
          }
          this.isLoading = true;
          const response = await googleOneTap({
            clientId:
              '499109195466-3cgb1ucg8oon8ncdemjpveuqkv7n0i61.apps.googleusercontent.com',
            context: 'signup',
          });

          const registrationData = decodeCredential(response.credential);
          this.formData.email = registrationData.email;
          this.formData.password = registrationData.sub;
          this.formData.password2 = registrationData.sub;

          this.formData.first_name = registrationData.given_name || '';
          this.formData.last_name = registrationData.family_name || '';
          this.formData.metier = '';

          await register({
            email: this.formData.email,
            password: this.formData.password,
            password2: this.formData.password2,
            googleSub: registrationData.sub,
            type_user: 'applicant',
            first_name: this.formData.first_name,
            last_name: this.formData.last_name,
            metier: this.formData.metier,
            crédit: this.formData.crédit,
            abonnement: this.formData.abonnement,
          });
          await login({
            email: this.formData.email,
            password: this.formData.password,
          });
          this.$router.push('/profil/edition');
        } catch (error) {
          //console.error('Google Register Failed:', error);
          toaster.showErrorPopup(error.message);
          this.errorDetails = 'Google Register Failed';
        } finally {
          this.isLoading = false;
        }
      },
    },
  };
</script>

<style scoped>
  :deep(.custom-location-input .v-field__overlay) {
    background-color: transparent !important; /* Supprime l'overlay */
  }
  :deep(.custom-location-input .v-input__control) {
    background-color: var(--surface-bg-2) !important;
  }

  /* Styles pour la section d'ajout de photo */
  .photo-title {
    font-weight: 700;
    margin-bottom: 30px;
    text-align: center;
    color: var(--text-1);
  }

  .photo-upload-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 30px;
    margin-bottom: 30px;
    padding: 20px;
    background-color: var(--surface-bg-2);
    border-radius: 12px;
  }

  .photo-preview-container {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .profile-image-preview {
    border-radius: 12px;
    transition: all 0.3s ease;
  }

  .photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }

  .photo-upload-instructions {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .photo-hint {
    color: var(--text-2);
    font-size: 14px;
    margin-bottom: 15px;
  }

  .upload-button {
    align-self: flex-start;
  }

  .mdp {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
  }
  h5 {
    margin-bottom: 10px;
  }
  .password-hint {
    margin-left: 1.2rem;
    font-size: 11px;
    color: #6b6b6b;
    margin-top: 0;
    width: 65%;
  }
  section {
    width: 100%;
  }

  label {
    font-size: 16px;
  }

  .container {
    width: 100vw;
    /* Utilise min-height pour éviter le scroll supplémentaire sur mobile */
    min-height: 100dvh;
    display: flex;
    justify-content: center;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
  }
  .container-content {
    width: 100%;
    height: 100%;
    max-width: 1440px;
    display: flex;
    justify-content: space-between;
    /* Ajout pour mobile */
    flex-direction: row;
  }

  .first-step-container {
    display: flex;
    width: 100%;
    height: 100%;
    gap: 20px;
    margin-bottom: 30px;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .text-regex {
    white-space: normal;
    color: var(--text-1);
    font-weight: 500;
    font-size: 1rem;
  }

  .illustration-section {
    width: 55%;
    /* Ajout pour mobile */
    min-width: 0;
  }

  .illustration {
    height: 100%;
    width: 100%;
  }

  .illustration img {
    height: 100%;
    width: 100%;
    object-fit: cover;
  }

  .form-container-register {
    width: 45%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    min-width: 0;
    height: 80vh;
  }

  .form-container-register h1 {
    margin-top: 24px;
  }

  .main-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
    height: calc(100% - 61px);
  }

  .form-container-header {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 50px;
  }
  .form-name-inputs-column {
    display: flex;
    flex-direction: column;
    width: 50%;
  }

  .form-name-inputs {
    display: flex;
    gap: 20px;
  }

  .already-registered-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.3vh;
    margin-bottom: 13px;
  }

  .stepper-footer {
    height: 70px;
    padding: 20px 0;

    width: 100%;
    background-color: #26282bd6;
    color: var(--surface-bg-2);
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    position: relative;
  }

  .stepper-footer p {
    width: 50%;
  }

  .padding-inline-40 {
    padding-inline: 40px;
  }
  .btn-container {
    display: flex;
    width: 100%;
    margin: 0 auto;
    justify-content: space-between;
    margin-bottom: 24px;
  }
  .btn-return {
    position: absolute;
    margin-top: 42px;
    margin-left: 42px;
  }

  @media (max-width: 1024px) {
    .container-content {
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
    }
    .illustration-section {
      width: 100%;
      min-height: 180px;
      order: 2;
      margin-bottom: 24px;
    }
    .form-container-register {
      width: 100%;
      order: 1;
      min-width: 0;
    }
  }

  @media (max-width: 768px) {
    .container {
      /* Supprime height/auto et force min-height à 100dvh pour éviter le scroll supplémentaire */
      min-height: 100dvh;
      max-height: 100dvh;
      height: unset;
      padding: 0;
    }
    .container-content {
      flex-direction: column;
      align-items: stretch;
      max-width: 100vw;
      min-width: 0;
      box-shadow: none;
    }
    .illustration-section {
      width: 100%;
      min-width: 0;
      order: 2;
      margin-bottom: 16px;
      height: 180px;
    }
    .illustration img {
      width: 100%;
      height: 180px;
      object-fit: cover;
    }
    .form-container-register {
      width: 100%;
      min-width: 0;
      order: 1;
      padding: 0 8px;
    }
    .form-container-header {
      padding-top: 24px;
    }
    .main-container {
      height: auto;
      min-height: 0;
      padding-bottom: 16px;
    }
    .photo-upload-container {
      flex-direction: column;
      gap: 16px;
      align-items: flex-start;
      padding: 10px;
    }
    .photo-preview-container {
      margin: 0 auto;
    }
    .profile-image-preview {
      width: 120px !important;
      height: 120px !important;
    }
    .btn-container {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }
    .stepper-footer {
      flex-direction: column;
      height: auto;
      padding: 12px 0;
      font-size: 15px;
      gap: 8px;
    }
    .stepper-footer p {
      width: 100%;
      text-align: center;
    }
    .already-registered-container {
      gap: 0.7vh;
      margin-bottom: 8px;
      font-size: 15px;
    }
    h1 {
      font-size: 2rem;
    }
    h5 {
      font-size: 1rem;
    }
    label {
      font-size: 15px;
    }
    .padding-inline-40 {
      padding-inline: 8px;
    }
  }

  @media (max-width: 480px) {
    .profile-image-preview {
      width: 80px !important;
      height: 80px !important;
    }
    .photo-upload-container {
      gap: 8px;
      padding: 4px;
    }
    .form-container-header {
      padding-top: 12px;
    }
    .main-container {
      padding-bottom: 8px;
    }
    .stepper-footer {
      font-size: 13px;
      padding: 8px 0;
    }
  }

  @media (max-width: 768px) {
    .password-hint {
      margin-left: 0rem;
      width: 100%;
    }
    .mdp {
      flex-direction: column;
    }
    .form-container-register {
      width: 100%;
    }

    .illustration-section {
      width: 0%;
    }
    .btn-return {
      margin-top: 6px;
      margin-left: 8px;
    }
    .v-btn_predent {
      margin-inline: 0;
    }
    .v-btn_predent img {
      margin-left: 11px;
    }
    .illustration img {
      margin-left: 14px;
    }
    .return-text {
      display: none;
    }
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .container-content {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .container-content {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
  }
</style>

<style>
  .form-container-register .location-container input {
    background-color: inherit;
    padding: inherit;
    margin-top: inherit;
  }

  .form-container-register input {
    background-color: var(--surface-bg-2);
    padding: 5px;
  }
  .form-container-register .v-card {
    background-color: transparent;
  }
  .form-container-register .v-stepper-header {
    display: none;
  }
  .form-container-register .v-stepper {
    background-color: transparent;
    box-shadow: none;
  }
  .form-container-register .v-window {
    overflow-y: auto;
    margin: 0;
    margin-bottom: 16px;
  }
  .form-container-register .v-progress-linear__background {
    opacity: unset;
  }
  .form-container-register .v-checkbox .v-selection-control {
    min-height: inherit;
  }

  .form-container-register .v-field__input {
    min-height: max(
      (40px),
      1rem + var(--v-field-input-padding-top) +
        var(--v-field-input-padding-bottom)
    );
  }

  /* Style pour la modal centrée */
  .centered-modal {
    position: fixed !important;
    top: 0;
    left: 0;
    width: 100vw !important;
    height: 100vh !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    z-index: 9999 !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    margin: 0 !important;
  }

  .centered-modal .confirmation-modal-container {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2) !important;
  }

  .confirmation-modal-container {
    font-weight: bold ;
  }

  .confirmation-modal-container, .confirmation-modal-container * {
    font-weight: bold;
  }
</style>
