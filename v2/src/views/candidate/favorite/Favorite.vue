<template>
  <main class="container padding-container">
    <!-- Fond sombre -->
    <div
      v-if="confirmationisOpened"
      class="overlay"
      @click="confirmationisOpened = false"
    ></div>

    <!-- title -->
    <h1>Mes favoris</h1>
    <div class="favorite-container">
      <div class="basic-filter-container border-radius-15">
        <CustomAccordion
          field=""
          title="Trier par nom"
          :fields="['A - Z', 'Z - A']"
          @checkbox-state="sortByName"
          :chips="false"
        />
        <CustomAccordion
          field=""
          title="Trier par date"
          :fields="['Plus récent', 'Plus ancien']"
          @checkbox-state="sortByDate"
        />
      </div>
      <article class="favorite-main-container">
        <!-- Message si aucun favori -->
        <p v-if="!favoritesList.length" class="no-message">
          Tu n'as ajouté aucun job à tes favoris pour le moment. <br />
          Rend-toi sur la page des
          <a class="link" @click.stop="gotoPage('/recherche')"
            >Offres d'emploi</a
          >
          pour débuter.
        </p>
        <CompactJobCard
          v-for="(favorite, index) in favoritesList"
          :key="index"
          :job="favorite.job_offer"
          @heart-click="openConfirmationModal"
        />
      </article>
    </div>
    <ConfirmationModal
      v-if="confirmationisOpened"
      title="Suppression"
      class="modal-container"
      description="Êtes-vous sûr de vouloir supprimer ce job de votre liste de favoris ?"
      @confirm="deleteFavorite"
      @close="confirmationisOpened = false"
    />
  </main>

  <BackToTopArrow />
</template>

<script>
  import { mapActions } from 'vuex';
  import { removeFavoriteJob } from '/src/services/favoriteJob.service.js';
  import CompactJobCard from '@/components/cards/job-cards/CompactJobCard.vue';
  import CustomAccordion from '@/components/buttons/CustomAccordion.vue';
  import ConfirmationModal from '@/components/modal/confirmation/ConfirmationModal.vue';
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import gotoPage from '@/utils/router';

  export default {
    name: 'FavoritePage',
    components: {
      CompactJobCard,
      CustomAccordion,
      ConfirmationModal,
      BackToTopArrow,
    },
    data() {
      return {
        confirmationisOpened: false,
        selectedFavoriteId: null,
      };
    },
    props: {
      user: { Object, required: false, default: () => {} },
    },
    computed: {
      favoritesList() {
        return this.user.favorie_job;
      },
    },
    methods: {
      gotoPage,
      ...mapActions(['handleDeleteFavoris']),
      openConfirmationModal(id) {
        this.confirmationisOpened = true;
        this.selectedFavoriteId = id;
      },
      async deleteFavorite() {
        try {
          // delete from db
          await removeFavoriteJob(this.selectedFavoriteId);
          // delete from store
          this.handleDeleteFavoris(this.selectedFavoriteId);
        } catch (error) {
          //console.log(error);
        } finally {
          this.confirmationisOpened = false;
        }
      },
      sortByDate(el, values) {
        let selectedFilter = '';
        // find the value of the selected filter
        for (const [key, value] of Object.entries(values)) {
          if (value === true) {
            selectedFilter = key;
            break;
          }
        }

        // filter by selected filter
        if (selectedFilter === 'Plus récent') {
          //console.log('Plus récent selected');
          this.favoritesList.sort((a, b) => {
            return (
              new Date(b.job_offer.created_at) -
              new Date(a.job_offer.created_at)
            );
          });
        } else if (selectedFilter === 'Plus ancien') {
          //console.log('Plus ancien selected');
          this.favoritesList.sort((a, b) => {
            return (
              new Date(a.job_offer.created_at) -
              new Date(b.job_offer.created_at)
            );
          });
        }
      },
      sortByName(el, values) {
        let selectedFilter = '';
        // find the value of the selected filter
        for (const [key, value] of Object.entries(values)) {
          if (value === true) {
            selectedFilter = key;
            break;
          }
        }

        // filter by selected filter
        if (selectedFilter === 'A - Z') {
          this.favoritesList.sort((a, b) => {
            return a.job_offer.title.localeCompare(b.job_offer.title);
          });
        } else if (selectedFilter === 'Z - A') {
          this.favoritesList.sort((a, b) => {
            return b.job_offer.title.localeCompare(a.job_offer.title);
          });
        }
      },
    },
  };
</script>

<style scoped>
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Couleur semi-transparente */
    z-index: 999; /* Juste en dessous de la modale */
  }

  .modal-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000; /* Assurez-vous que la modale est au-dessus du contenu */
    width: 100%; /* Cela peut être ajusté selon vos besoins */
    max-width: 500px; /* Ajustez selon la taille de votre modale */
    padding: 20px; /* Optionnel, pour ajouter du padding autour de la modale */
    text-align: center;
  }

  .favorite-container {
    display: flex;
    gap: 27px;
  }

  .basic-filter-container {
    height: fit-content;
    max-width: 231px;
    margin-top: 90px;
    background-color: var(--surface-bg-2);
    padding: 16px;
  }

  .basic-filter-container .container {
    margin-top: 0;
    margin-bottom: 10px;
  }

  main {
    width: 100%;
    margin-bottom: 30px;
  }

  .favorite-main-container {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fill, 258px);
    grid-gap: 27px;
    margin-top: 90px;
  }
  .favorite-main-container .no-message {
    font-size: 1.2rem;
    color: #666;
    text-align: center;
    margin-top: 20px;
    grid-column: 1 / -1;
  }
  .favorite-main-container .no-message .link {
    color: var(--primary-1);
    font-weight: bold;
    text-decoration: underline;
    cursor: pointer;
  }

  /* ✅ MOBILE */
  @media screen and (max-width: 480px) and (max-width: 767px) {
    .favorite-container {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .favorite-main-container {
      justify-content: center;
    }
    .basic-filter-container {
      display: flex;
      width: 100%;
      max-width: 100%;
      gap: 20px;
    }
    .basic-filter-container .container {
      width: 50%;
    }
    main {
      justify-items: center;
    }
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }
</style>
