<template>
  <main class="container padding-container">
    <h1 class="title">Foire aux Questions</h1>

    <div class="content-wrapper">
      <div
        v-for="category in faqCategories"
        :key="category.category"
        class="faq-section"
      >
        <h2 class="subtitle">{{ category.category }}</h2>

        <FaqAccordion :faqs="category.questions" />
      </div>

      <div class="contact-container">
        <h2>Nous contacter</h2>
        <p>
          Vous n'avez pas trouvé de réponse à votre question ? Alors
          contactez-nous via notre formulaire.
        </p>

        <div class="btn-wrapper">
          <PrimaryNormalButton
            append-icon="mdi-arrow-right"
            @click="gotoPage('/contact')"
            textContent="Aller au formulaire"
          />
        </div>
      </div>
    </div>

    <BackToTopArrow />
  </main>
</template>

<script>
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import FaqAccordion from '@/components/buttons/FaqAccordion.vue';
  import gotoPage from '@/utils/router';
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';

  export default {
    name: 'FAQ',
    components: {
      PrimaryNormalButton,
      FaqAccordion,
      BackToTopArrow,
    },
    methods: {
      gotoPage,
    },
    data() {
      return {
        faqCategories: [
          {
            category: 'FAQ Thanks-Boss',
            questions: [
              {
                title: "Qu'est-ce que Thanks-Boss ?",
                text: "Thanks-Boss est une plateforme de recrutement propulsée par l'Intelligence Artificielle. Elle vise à simplifier le processus de recrutement en garantissant un matching parfait entre les compétences des candidats et les attentes des recruteurs.",
              },
              {
                title: 'Comment fonctionne Thanks-Boss ?',
                text: "Grâce à notre technologie d'IA, nous analysons les profils des candidats et les offres d'emploi pour proposer des correspondances optimales. Notre plateforme facilite la recherche et la mise en relation rapide entre talents et employeurs.",
              },
              {
                title:
                  "Quels sont les avantages d'utiliser Thanks-Boss pour les candidats/recruteurs ?",
                text: 'Pour les candidats : des offres pertinentes, un processus de candidature simplifié et des réponses rapides.\nPour les recruteurs : un accès aux meilleurs talents, une gestion facile des candidatures et un gain de temps considérable.',
              },
            ],
          },
          {
            category: 'Inscription et support',
            questions: [
              {
                title: "Comment puis-je m'inscrire à Thanks-Boss ?",
                text: 'Vous pouvez vous inscrire en visitant notre site web</a> et en suivant les instructions pour créer un compte, que vous soyez candidat ou recruteur.',
              },
              {
                title: 'Comment puis-je contacter le support client ?',
                text: "Pour contacter notre support client, envoyez-nous un email à <a href='mailto:<EMAIL>'><EMAIL></a> ou utilisez le <a href='/contact'>formulaire de contact</a> sur notre site web.",
              },
              {
                title:
                  "Le Match Coach peut-il m'aider à améliorer la qualité de mon CV et de ma lettre de motivation ?",
                text: "Oui, notre Match Coach utilise l'IA pour analyser et fournir des recommandations personnalisées afin d'améliorer la qualité de votre CV et de votre lettre de motivation.",
              },
              {
                title: 'Y a-t-il des ressources ou des guides pour aider ?',
                text: 'Oui, nous proposons des guides et des tutoriels disponibles sur notre site web pour vous accompagner dans votre processus de recrutement.',
              },
              {
                title: 'Comment réinitialiser mon mot de passe ?',
                text: 'Cliquez sur  <a href="/reinitialisation-mot-de-passe">"Mot de passe oublié"</a> sur la page de connexion et suivez les instructions pour réinitialiser votre mot de passe.',
              },
              {
                title: 'Comment modifier les informations de mon profil ?',
                text: '   1. Se connecter à votre compte\n   2. Aller dans les paramètres de votre profil\n   3. Modifier les informations que vous souhaitez mettre à jour.',
              },
            ],
          },
          {
            category: 'Sécurité et confidentialité',
            questions: [
              {
                title:
                  'Est-ce que mes données sont bien protégées ? Qui y a accès ?',
                text: 'Vos données sont sécurisées grâce à des mesures de protection avancées. Seuls vous et les recruteurs avec lesquels vous partagez votre profil avez accès à vos informations.',
              },
              {
                title:
                  'Quelle est la politique de confidentialité de Thanks-Boss ?',
                text: "Notre politique de confidentialité est disponible sur notre <a href='/politique-confidentialite'>site web</a. Nous nous engageons à protéger vos données personnelles et à respecter votre vie privée.",
              },
            ],
          },
          {
            category: 'Fonctionnalités',
            questions: [
              {
                title:
                  'Y a-t-il une application mobile disponible pour Thanks-Boss ?',
                text: 'Notre site web est responsive. La sortie de l’application mobile est prévue dans quelques mois, pour vous permettre de mieux gérer vos candidatures et annonces en déplacement.',
              },
              {
                title:
                  'Comment résoudre les problèmes de connexion à mon compte ?',
                text: "Si vous avez des problèmes de connexion, assurez-vous d'utiliser les bons identifiants. Sinon, <a href='/reinitialisation-mot-de-passe'>réinitialisez votre mot de passe</a> ou contactez notre <a href='/contact'>support client</a>.",
              },
              {
                title:
                  'Que faire si une fonctionnalité ne fonctionne pas correctement ?',
                text: "Veuillez signaler toute anomalie à notre support client via <a class='link' href='mailto:<EMAIL>'><EMAIL></a>. Nous nous efforcerons de résoudre le problème rapidement.",
              },
              {
                title: 'Puis-je supprimer/annuler une candidature ?',
                text: 'Oui, vous pouvez annuler une candidature depuis votre tableau de bord en accédant à la section "Mes candidatures".',
              },
              {
                title: 'Comment et où retrouver mes candidatures ?',
                text: 'Vos candidatures sont listées dans la section "Mes candidatures" de votre compte, où vous pouvez suivre leur statut.',
              },
              {
                title: 'Comment configurer mon annonce de recrutement ?',
                text: 'Allez dans la section "Créer une annonce" et remplissez les informations requises pour publier votre annonce de recrutement.',
              },
              {
                title: 'Comment mettre en avant mon annonce de recrutement ?',
                text: "Vous pouvez choisir l'option de mise en avant lors de la création de votre annonce ou dans la gestion de vos annonces existantes pour augmenter leur visibilité.",
              },
              {
                title: 'Puis-je supprimer mon compte ?',
                text: 'Oui, vous pouvez supprimer votre compte depuis les paramètres de votre profil. Une fois supprimé, toutes vos données seront effacées de notre plateforme.',
              },
            ],
          },
          {
            category: 'Abonnement et désabonnement',
            questions: [
              {
                title: 'Comment faire pour se désabonner de Thanks-Boss ?',
                text: "Allez dans les paramètres de votre compte et sélectionnez l'option de désabonnement. Suivez les instructions pour confirmer.",
              },
              {
                title: 'Y a-t-il une période d’essai gratuite ?',
                text: 'Oui, nous offrons une période d’essai gratuite de deux semaines pour vous permettre de tester nos services.',
              },
              {
                title: 'Est-il possible d’obtenir un remboursement ?',
                text: "Les conditions de remboursement sont détaillées dans nos termes et conditions. Veuillez contacter notre <a href='/contact'>support client</a> pour toute demande de remboursement.",
              },
            ],
          },
        ],
      };
    },
  };
</script>

<style scoped>
  .container {
    display: flex;
    flex-direction: column;
    gap: 40px;
    height: fit-content;
    padding-bottom: 60px;
  }

  .title {
    font-size: 40px;
    text-align: center;
  }

  .subtitle {
    margin-top: 20px;
    margin-bottom: 10px;
    text-align: start;
    width: 100%;
  }

  .content-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    gap: 40px;
  }

  .faq-section {
    width: 100%;
    box-sizing: border-box;
  }

  .contact-container {
    min-width: 50%;
    background-color: var(--surface-bg-2);
    color: var(--text-1);
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    padding: 1rem;
    gap: 1rem;
  }

  .btn-wrapper {
    display: flex;
    width: 100%;
    justify-content: end;
  }

  /* @media screen and (min-width: 992px) {
    .title {
      font-size: 45px;
      text-align: start;
    }
  }

  @media screen and (min-width: 1400px) {
    .container {
      padding: 1% 20%;
    }
    .title {
      font-size: 45px;
      text-align: start;
    }
  }

  @media screen and (min-width: 2000px) {
    .container {
      padding: 1% 20%;
    }
    .title {
      font-size: 45px;
      text-align: center;
    }

    .subtitle {
      margin-top: 20px;
      margin-bottom: 10px;
      text-align: center;
      width: 100%;
    }
  } */

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }
</style>
