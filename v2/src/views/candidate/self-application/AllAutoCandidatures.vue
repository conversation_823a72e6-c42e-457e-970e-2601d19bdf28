<template>
  <main class="container">
    <h1>Mes auto candidatures</h1>

    <div class="btn-container">
      <PrimaryNormalButton
        textContent="Nouvelle candidature"
        btnColor="secondary"
        @click="goToForm"
        add
      />
    </div>

    <!--div class="search-bar-container">
      <input
        type="text"
        v-model="searchQuery"
        @input="onSearchInput"
        placeholder="Rechercher une auto-candidature..."
        class="search-bar"
      />
    </div-->

    <div v-if="!candidatures.length" class="no-message">
      Aucune auto-candidature pour le moment.
    </div>

    <div class="cards-wrapper">
      <AutoCandidatureCard
        v-for="(candidature, index) in candidatures"
        :key="index"
        :candidature="candidature"
        @delete="handleDelete"
        @edit="handleEdit"
      />
    </div>
  </main>

  <BackToTopArrow />
</template>

<script>
  import AutoCandidatureCard from '@/components/views-models/self-application/AutoCandidatureCard.vue';
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import { getSelfApplications } from '@/services/profile.service.js';
  import { deleteSelfApplications } from '@/services/profile.service.js';

  export default {
    name: 'AllAutoCandidatures',
    components: {
      AutoCandidatureCard,
      PrimaryNormalButton,
      BackToTopArrow,
    },
    data() {
      return {
        candidatures: [],
        // Ajoute une propriété pour garder toutes les candidatures en mémoire
        allCandidatures: [],
        searchQuery: '',
      };
    },
    mounted() {
      this.fetchCandidatures();
    },
    activated() {
      // Rafraîchit la liste à chaque retour sur la page
      this.fetchCandidatures();
    },
    methods: {
      async fetchCandidatures() {
        try {
          const data = await getSelfApplications();
          this.allCandidatures = data.results || [];
          this.candidatures = [...this.allCandidatures];
          console.log('aaa');
          console.log(this.candidatures);
        } catch (error) {
          console.error('Erreur chargement candidatures', error);
        }
      },
      handleDelete(id) {
        deleteSelfApplications(id);
        this.candidatures = this.candidatures.filter((c) => c.id !== id);
        this.allCandidatures = this.allCandidatures.filter((c) => c.id !== id);
        // Optionnel : appel à une API DELETE ici
      },
      handleEdit(candidature) {
        console.log('Candidature:', candidature);
        this.$router.push({
          name: 'update-candidature',
          params: { id: candidature.id },
        });
      },
      goToForm() {
        this.$router.push({ name: 'auto-candidature' });
      },
      searchCandidatures() {
        const query = this.searchQuery.toLowerCase();
        this.candidatures = this.allCandidatures.filter((candidature) =>
          candidature.position.toLowerCase().includes(query)
        );
      },
      onSearchInput(event) {
        const value = event.target ? event.target.value : event;
        this.searchQuery = value;
        if (!value) {
          // Si la barre est vide, on remet toutes les candidatures
          this.candidatures = [...this.allCandidatures];
        } else {
          // Sinon, on filtre
          this.candidatures = this.allCandidatures.filter((c) => {
            // Adapte le filtre selon les champs pertinents
            return (
              (c.nom_job && c.nom_job.toLowerCase().includes(value.toLowerCase())) ||
              (c.lm && c.lm.toLowerCase().includes(value.toLowerCase()))
            );
          });
        }
      },
    },
  };
</script>

<style scoped>
  .container {
    width: 100%;
    padding: 20px;
  }
  .cards-wrapper {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 30px;
  }
  .btn-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
  .no-message {
    text-align: center;
    margin-top: 30px;
    font-size: 1.1rem;
    color: #666;
  }
  .search-bar-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    margin-bottom: 10px;
  }
  .search-bar {
    width: 100%;
    max-width: 400px;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 6px;
    font-size: 1rem;
  }
</style>
