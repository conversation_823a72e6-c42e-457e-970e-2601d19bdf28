<template>
  <ProfilePreview v-if="isPreviewOn" @close-preview="togglePreview" />

  <ProfileImportCv
    v-else-if="isImportPreviewOn || $route.query.importerData"
    @close-import="toggleImportPreview"
  />
  <main v-else class="container padding-container">
    <div class="content-wrapper">
      <header class="header-title">
        <h1 id="edition-profil">Mon auto-candidature</h1>
        <div class="save-all-changes">
          <PrimaryNormalButton
            textContent="Retour"
            btnColor="secondary"
            back
            @click="$router.go(-1)"
          />
        </div>
      </header>
      <el-progress
        :percentage="this.progress"
        :text-inside="true"
        :stroke-width="20"
        :color="this.color"
      >
        <span>Il vous reste {{ this.nbCookies }} cookies</span>
      </el-progress>
      <MyForm
        :user="user"
        :activeSection="activeSection"
        @toggleSection="setActiveSection"
        @submit_user_infos="submitUserInfos"
        ref="profile"
        @save="handleSave('profile', 'updateUserDatas')"
      />
      <CvPreview
        ref="cv"
        :user="user"
        @cv-status-change="updateCvStatus"
        @submit_cv_infos="submitCvInfos"
        :activeSection="activeSection"
        @toggleSection="setActiveSection"
      />
      <MyMotivations
        ref="motivation"
        :user="user"
        :activeSection="activeSection"
        @toggleSection="setActiveSection"
        @submit_lm_infos="submitLmInfos"
      />

      <div class="save-all-changes-bottom">
        <PrimaryNormalButton
          textContent="Enregistrer et envoyer"
          @click="saveAllChanges"
          class="btn-save-all-changes"
        />
      </div>

      <BackToTopArrow />
    </div>
  </main>
</template>

<script>
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import CvPreview from '@/components/views-models/self-application/CvPreview.vue';
  import MyMotivations from '@/components/views-models/self-application/Motivations.vue';
  import MyForm from '@/components/views-models/self-application/MyForm.vue';

  import { deleteAccount } from '@/services/account.service';
  import { logout } from '@/services/account.service.js';
  import { toaster } from '@/utils/toast/toast.js';
  import { takeUserNotifications } from '@/utils/userUtilities';

  import ProfileImportCv from '@/views/candidate/profile-import-cv/ProfileImportCv.vue';
  import ProfilePreview from '@/views/candidate/profile-preview/ProfilePreview.vue';

  import { insertSelfApplciation } from '@/services/profile.service.js';

  import { ref } from 'vue';
  import 'element-plus/es/components/progress/style/css';
  import { ElProgress } from 'element-plus';

  export default {
    name: 'SettingsPage',
    components: {
      BackToTopArrow,
      PrimaryNormalButton,
      ProfilePreview,
      ProfileImportCv,
      MyForm,
      CvPreview,
      MyMotivations,
    },
    data() {
      return {
        activeSection: null, // Aucun menu ouvert par défaut
        validPassword: false,
        notificationsEnabled: false,
        socialEnabled: false,
        statutCandidatureEnabled: false,
        alertesEnabled: false,
        // datas de la page mon profile
        notifications: 0,
        isPreviewOn: false, //  toggle between user profile input and user profile preview
        isImportPreviewOn: false, //  toggle between user profile input and user profile import preview
        formData: {
          nom_job: '',
          experience: '',
          contrat: '',
          presence: '',
          user: null,
          lm: '',
          cv: null,
        },
        candidature: [],
        color: ref('#f6b337'),
        nbCookies: 30,
        progress: 0,
      };
    },
    props: {
      user: {
        type: Object,
        required: false,
        default: () => {},
      },
    },
    methods: {
      logout,
      deleteAccount,
      setActiveSection(sectionId) {
        if (this.activeSection === sectionId) {
          this.activeSection = null;
        } else {
          this.activeSection = sectionId;
          this.$nextTick(() => {
            this.$forceUpdate(); // 🔥 Force la mise à jour du DOM
          });
        }
      },
      scrollToSection(sectionId, callback) {
        const element = document.getElementById(sectionId);
        if (element) {
          const navbarHeight =
            document.querySelector('.navbar')?.offsetHeight || 80;
          const elementPosition =
            element.getBoundingClientRect().top + window.scrollY;
          window.scrollTo({
            top: elementPosition - navbarHeight - 20,
            behavior: 'smooth',
          });

          // 🔥 Attendre que le scroll soit terminé avant d'ouvrir le menu
          setTimeout(() => {
            if (callback) callback();
          }, 300); // Ajuste le délai si besoin
        }
      },

      async handleSave(component, method) {
        const refComponent = this.$refs[component];
        if (refComponent && typeof refComponent[method] === 'function') {
          try {
            await refComponent[method]();
          } catch (error) {
            console.error(
              `Erreur lors de l'exécution de ${method} dans ${component}:`,
              error
            );
          }
        } else {
          console.warn(
            `Composant ou méthode introuvable : ${component}.${method}`
          );
        }
      },
      submitLmInfos(lettreMotivation) {
        this.formData.lm = lettreMotivation;
      },
      submitCvInfos(cv) {
        this.formData.cv = cv;
      },
      submitUserInfos(userData) {
        this.formData.nom_job = userData.nom_job;
        this.formData.experience = userData.experience;
        this.formData.contrat = userData.contrat;
        this.formData.presence = userData.presence;
      },
      async saveAllChanges() {
        try {
          this.formData.user = this.user.id;
          await this.handleSave('profile', 'updateUserDatas');
          await this.handleSave('cv', 'saveCV');
          await this.handleSave('motivation', 'submitMotivationForm');
          console.log('Payload envoyé :', this.formData);
          await insertSelfApplciation(this.formData);
          // Ne pas rediriger, rester sur la page
          // this.$router.push({ name: 'AllAutoCandidatures' });
        } catch (error) {
          console.error('Erreur lors de la sauvegarde :', error);
          this.$toasted?.error(
            "Erreur lors de l'enregistrement. Veuillez réessayer."
          );
        }
      },

      //  toggle preview screen
      togglePreview() {
        this.isPreviewOn = !this.isPreviewOn;
      },

      //  toggle preview screen
      toggleImportPreview() {
        this.isImportPreviewOn = !this.isImportPreviewOn;
      },

      // Activation/Désactivation des notifications globales
      activateAll() {
        console.log(
          'Notifications générales avant:',
          this.notificationsEnabled
        );
        console.log('Alertes avant :', this.alertesEnabled);
        if (!this.notificationsEnabled || !this.alertesEnabled) {
          this.notificationsEnabled = true;
          this.socialEnabled = true;
          this.statutCandidatureEnabled = true;
          this.alertesEnabled = true;

          this.saveSettings(); // Sauvegarde de l'état après modification
          console.log(
            'toutes les alertes sont activées:',
            this.notificationsEnabled
          );
          console.log(
            'les alertes sociables sont activées:',
            this.socialEnabled
          );
          console.log(
            'les alertes statut candidats sont activées:',
            this.statutCandidatureEnabled
          );
          console.log(
            'les alertes globales sont activées:',
            this.alertesEnabled
          );
        }
      },

      // Activation/Désactivation des alertes
      toggleAlertes(value) {
        console.log('Alertes avant :', this.alertesEnabled);
        this.alertesEnabled = value;
        this.saveSettings();
        console.log('Alertes maintenant :', this.alertesEnabled);
      },

      // Sauvegarde de l'état dans LocalStorage
      saveSettings() {
        localStorage.setItem('notificationsEnabled', this.notificationsEnabled);
        localStorage.setItem('alertesEnabled', this.alertesEnabled);
        console.log('changements enregistrés:', this.notificationsEnabled);
      },

      // Chargement de l'état depuis LocalStorage
      loadSettings() {
        const notificationsState = localStorage.getItem('notificationsEnabled');
        const alertesState = localStorage.getItem('alertesEnabled');

        this.notificationsEnabled = notificationsState === 'true';
        this.alertesEnabled = alertesState === 'true';
      },

      handleLogout() {
        try {
          logout();
        } catch (error) {
          console.log(error);
        }
      },

      async deleteAccountClick() {
        try {
          await deleteAccount();
        } catch (error) {
          console.log(error);
        }
      },

      async submitEmailChange() {
        try {
          const response = await emailchangetRequest(this.formData);
          if (response) {
            toaster.showSuccessPopup('Votre email a été modifié avec succès.');
          }
        } catch (error) {
          console.log(error);
        }
      },

      async desactivate_newsletter() {
        try {
          const response = await desactivate_newletter();
          if (response) {
            toaster.showSuccessPopup(
              'Votre abonnement à la newsletter est désactivé.'
            );
          }
        } catch (error) {
          console.log(error);
        }
      },

      async activate_newsletter() {
        try {
          const response = await activate_newletter();
          if (response) {
            toaster.showSuccessPopup(
              'Votre abonnement à la newsletter est activé.'
            );
          }
        } catch (error) {
          console.log(error);
        }
      },
    },
    async created() {
      if (this.$route.query.section) {
        this.activeSection = this.$route.query.section;

        this.$nextTick(() => {
          this.scrollToSection(this.activeSection, () => {
            this.$forceUpdate(); // Forcer la mise à jour du DOM
          });
        });
      }
    },
    // Hook du cycle de vie pour charger les paramètres à l'initialisation
    mounted() {
      this.progress = (this.nbCookies * 100) / 30;
      // this.candidature = JSON.parse(this.$route.params.candidature);
      this.candidature = history.state.candidature;
      console.log('Candidature:', this.candidature);
      this.loadSettings();
      // this.getProgressBarCompletion();
      this.notifications = takeUserNotifications(this.user, [
        'about',
        'photo',
        'cvs',
        'experience',
        'formation',
        'numberPhone',
        'skill',
        'langue',
        'mobilité',
        'permis',
        'salaire_souhaite',
        'ville',
      ]);
    },
  };
</script>

<style scoped>
  section {
    width: 100%;
    height: fit-content;
    margin-top: 20px;
    margin-bottom: 30px;
    /* display: grid;
    grid-template-columns: 1fr 1fr;
    row-gap: 28px;
    column-gap: 80px; */
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  h5 {
    height: 40px;
  }
  .demo-progress .el-progress--line {
    margin-bottom: 15px;
    max-width: 600px;
  }
  .v-input__slot {
    flex-direction: row-reverse !important;
    justify-content: flex-end;
  }

  .v-input--selection-controls__input {
    margin-right: 0;
    margin-left: 8px;
  }

  .v-input--selection-controls__input {
    margin-left: 0;
    margin-right: 8px;
  }
  .settings-password-container {
    background-color: aquamarine;
  }
  .settings-email-container {
    background-color: blanchedalmond;
  }
  .settings-preference-container {
    grid-column: 1 / 3;
  }

  .settings-container-description {
    padding: 8px 8px 8px 5px;
    margin-block: 4px;
  }

  .settings-container-inputs {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .settings-container-newsletter-buttons {
    text-align: right;
    margin-top: 8px;
  }

  .settings-preference-main-container {
    gap: 25vw;
  }

  .settings-preference-switch-container {
    width: 100%;
    padding-left: 5px;
    padding: 8px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  /* Section des switches (préférences de notifications) */
  .settings-preference-switch {
    display: flex;
    align-items: center;
    justify-content: flex-start; /* Aligne le texte et le switch à gauche */
    width: 100%;
    margin-bottom: 0; /* Supprime tout espacement inférieur entre les éléments */
    gap: 5px; /* Réduit l'écart entre le texte et le bouton */
  }

  .settings-preference-switch p {
    margin: 0; /* Supprime les marges */
    padding-right: 5px; /* Ajoute un petit espace à droite du texte */
    font-size: 14px; /* Ajuste la taille du texte si nécessaire */
  }

  /* Alignement et espacement général */
  .custom-switch {
    display: flex;
    align-items: center;
    gap: 5px; /* Réduit l'écart entre les éléments du switch */
  }

  /* Optionnel : Réduction de l'espacement global entre les sections */
  .settings-preference-main-container {
    margin-top: 0;
    padding-top: 0;
    gap: 10px; /* Réduire l'espace entre les containers */
  }

  .settings-preference-switch-container {
    padding: 0; /* Enlever le padding interne */
    margin: 0; /* Enlever le margin externe */
  }

  /* newsletter btns */
  .settings-preference-newsletter-buttons p {
    margin-block: 10px;
  }
  .settings-preference-secondary-button {
    margin-left: 20px;
  }

  /* email and password btns actions */
  .settings-container-buttons {
    margin-top: 10px;
  }

  /* action btns */
  .settings-preference-actions-buttons {
    width: 100%;
    display: flex;
    justify-content: space-around;
    grid-column: 1 / 3;
  }

  /* container template */
  .settings-container {
    padding: 16px;
    background-color: var(--surface-bg-2);
  }

  .save-all-changes {
    display: flex;
    justify-content: space-between;
    margin: 30px 0;
  }
  .save-all-changes-bottom {
    display: flex;
    justify-content: right;
    margin: 30px 0;
  }
  .btn-save-all-changes {
    width: fit-content;
  }

  .text-regex {
    color: #757472;
    font-size: 11px;
  }
  /* styles de la page mon profil */
  .head-message {
    font-size: 1.1rem;
  }
  .custom-progress {
    border: solid 2px black;
  }
  .content-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin: 0 auto;
    width: 100%;
  }
  a.incomplete-infos-notification {
    display: flex;
    background-color: rgba(254, 106, 106, 0.444);
    width: fit-content;
    padding: 0 10px;
    border: solid 1px red;
    border-radius: 10px;
    font-size: 1rem;
    text-decoration: none;
    color: black;
    margin-top: 10px;
    transition: all 0.3s ease-in-out;
  }
  a.incomplete-infos-notification:hover {
    background-color: rgba(254, 106, 106, 0.273);
  }
  /* TODO : supprimer ? Styles pour donner style de button a un lien... */
  /* .edition-profile-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    background-color: var(--black-100);
    color: var(--white-100);
    font-size: 1rem;
    cursor: pointer;
    transition:
      background-color 0.2s,
      color 0.2s;
  }
  .edition-profile-link img {
    margin-left: 10px;
  } */
  .default-cv {
    padding-inline: 24px;
    padding-block: 16px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    background-color: var(--surface-bg-2);
  }

  .informations {
    background-color: rgba(88, 160, 150, 0.2);
    padding-block: 10px;
    padding-inline: 8px;
    border: 5px;
  }

  /* buttons */
  .import-btn {
    border-radius: 5px;
    border: 1px solid rgba(246, 179, 55, 1);
  }

  /* content */
  .title-import {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }

  /* @media screen and (max-width: 768px) {
    h1 {
      justify-content: center;
      display: flex;
    }
    .settings-preference-main-container {
      flex-direction: column;
      gap: 20px;
    }
    .settings-preference-switch-container {
      width: 100%;
    }

    /* newsletter btns 
    .settings-preference-newsletter-buttons {
      display: flex;
      flex-direction: column;
      width: 150px;
    }

    .settings-preference-secondary-button {
      margin-left: 0;
      margin-top: 20px;
    }

    /* action btns 
    .settings-preference-actions-buttons {
      flex-direction: column;
      gap: 10px;
    }
  } */
</style>
