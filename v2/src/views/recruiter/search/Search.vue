<template>
  <main class="container padding-container">
    <div v-if="alertPanelOn" class="alert-panel-wrapper">
      <CreationAlerteProfiles
        v-if="user.type_user === 'recruiter'"
        @close-alert-panel="toggleAlertPanel"
      />
      <Alert v-else @close-alert-panel="toggleAlertPanel" />
    </div>

    <div v-else class="search-job">
      <div class="search-container">
        <!-- Combined search interface with three textboxes -->
        <section class="combined-search-bar">
          <!-- Recherche field -->
          <div class="field-container">
            <h5>Recherche</h5>
            <v-text-field
              v-model="formData.query"
              label="Saisis un métier, un prénom ou un nom"
              type="text"
              clearable
              variant="outlined"
              density="comfortable"
              hide-details
              @click:clear="handleClearSearch"
              @input="handleSearchInput"
              @keydown.enter="handleCombinedSearch"
            />
          </div>

          <!-- Location field -->
          <div class="field-container">
            <h5>Lieu</h5>
            <!--LocationInput
              :paramLocation="formData.ville"
              textContent="Saisis une ville"
              @city-and-postal-code="getCityAndPostalCodeValue"
            /-->
            <v-text-field
              v-model="formData.ville"
              label="Ex: Paris"
              type="text"
              clearable
              variant="outlined"
              density="comfortable"
              hide-details
              @click:clear="handleClearSearch"
              @input="handleLocationInput"
              @keydown.enter="handleSearch"
            />
          </div>

          <!-- AI Best Profile field -->
          <div class="field-container ai-field">
            <div class="ai-header">
              <h5>Meilleur profil (IA)</h5>
              <img src="@/assets/icons/AI-stars.svg" alt="AI" class="ai-icon" />
            </div>
            <v-text-field
              v-model="bestProfileQuery"
              label="Recherche IA de profil"
              variant="outlined"
              density="comfortable"
              hide-details
              @keyup.enter="handleCombinedSearch"
            ></v-text-field>
          </div>

          <!-- Search button -->
          <div class="btn-container">
            <v-btn
              color="primary"
              class="search-btn"
              @click="handleCombinedSearch"
            >
              <img
                src="@/assets/search/search-page-searchbar-icon.svg"
                alt="magnifier glass"
              />
            </v-btn>
          </div>
        </section>

        <!-- Filters section -->
        <div class="search-content">
          <div class="filters-wrapper">
            <FiltersRecruiter @open-alert="toggleAlertPanel" />
          </div>

          <!-- Results display -->
          <div
            class="loader-container"
            v-if="
              (isLoading && filteredCandidates.length === 0) ||
              (isLoadingBestProfiles && bestProfiles.length === 0)
            "
          >
            <v-progress-circular
              class="progress-circular"
              indeterminate
            ></v-progress-circular>
          </div>
          <div v-else class="grid-wrapper">
            <CandidateGridDisplay
              :candidateList="displayResults"
              :favoriteCandidateList="userFavoriteJob"
              :currentUserId="user.id"
            />
          </div>
        </div>
      </div>

      <BackToTopArrow />
    </div>
  </main>
</template>

<script>
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import Alert from '@/components/views-models/search/Alert.vue';
  import CandidateGridDisplay from '@/components/views-models/search/CandidateGridDisplay.vue';
  import FiltersRecruiter from '@/components/views-models/search/FiltersRecruiter.vue';
  import LocationInput from '@/components/inputs/LocationInput.vue';
  import { axiosInstance } from '@/services/axios';
  import { searchCandidates } from '@/services/search.service.js';
  import CreationAlerteProfiles from '../../../components/modal/alert/recruiter/CreationAlerteProfiles.vue';
  import axios from 'axios';

  export default {
    name: 'SearchPage',
    props: {
      user: {
        type: Object,
        required: true,
      },
    },
    components: {
      LocationInput,
      FiltersRecruiter,
      CandidateGridDisplay,
      BackToTopArrow,
      CreationAlerteProfiles,
      Alert,
    },

    data() {
      return {
        // Form data
        formData: {
          query: null,
          ville: null,
          postalCode: null,
        },
        // Search results
        defaultCandidates: [],
        searchCandidatesList: [],
        filteredCandidates: [],
        currentPage: 1,
        pageSize: 3,
        hasMoreCandidates: true,
        alertPanelOn: false,
        userFavoriteJob: [],
        isLoading: false,
        // Best Profile search data
        bestProfileQuery: '',
        bestProfiles: [],
        isLoadingBestProfiles: false,
        // Combined results
        displayResults: [],

        // Ajouter ces nouvelles propriétés
        showBestProfileModal: false,
        bestProfilesMessage: '',
      };
    },

    mounted() {
      this.loadCandidates();
      window.addEventListener('scroll', this.handleScroll);
    },
    beforeDestroy() {
      window.removeEventListener('scroll', this.handleScroll);
    },

    methods: {
      async loadCandidates() {
        if (Object.keys(this.$route.query).length !== 0) {
          return;
        }
        if (!this.hasMoreCandidates || this.isLoading) {
          return;
        }
        this.isLoading = true;

        try {
          const { data } = await axiosInstance.get(
            `/best_profils/?page=${this.currentPage}&size=${this.pageSize}`
          );

          if (data.next === null) {
            this.hasMoreCandidates = false;
          }

          // Crear un Set con los IDs existentes
          const existingIds = new Set(
            this.defaultCandidates.map((candidate) => candidate.id)
          );

          // Filtrar solo los nuevos candidatos que no existen en la lista actual
          const newCandidates = data.results.filter(
            (candidate) => !existingIds.has(candidate.id)
          );

          this.defaultCandidates = [
            ...this.defaultCandidates,
            ...newCandidates,
          ];
          this.filteredCandidates = this.defaultCandidates;
        } catch (error) {
          //console.error('Error loading candidates:', error);
          this.hasMoreCandidates = false;
        } finally {
          this.isLoading = false;
        }
      },

      handleScroll() {
        if (Object.keys(this.$route.query).length !== 0) {
          return;
        }
        if (!this.hasMoreCandidates || this.isLoading) {
          return;
        }

        const scrollPosition = window.innerHeight + window.scrollY;
        const threshold = document.body.offsetHeight - 100;

        if (scrollPosition >= threshold) {
          this.currentPage++;
          this.loadCandidates();
        }
      },

      async searchCandidatesFromURL() {
        let uniqueResults = [];
        try {
          const query = this.$route.query;

          if (Object.keys(query).length === 0) {
            return;
          }

          this.isLoading = true;

          const params = {
            recherche: query.recherche || '',
            ville: query.ville || '',
            contrat: query.contract
              ? query.contract.replace(/[\[\]]/g, '').split(',')
              : [],
            teletravail: query.remote
              ? query.remote.replace(/[\[\]]/g, '').split(',')
              : [],
          };
          console.log('Search params:', params);
          let allCombinations = [
            {
              recherche: params.recherche,
              ville: params.ville,
              teletravail: '',
              contrat: '',
            },
          ];

          if (params.contrat.length > 0) {
            allCombinations = params.contrat.flatMap((contrat) =>
              allCombinations.map((combo) => ({ ...combo, contrat }))
            );
          }

          if (params.teletravail.length > 0) {
            allCombinations = params.teletravail.flatMap((teletravail) =>
              allCombinations.map((combo) => ({ ...combo, teletravail }))
            );
          }

          const searchResults = await Promise.all(
            allCombinations.map((combination) => searchCandidates(combination))
          );

          const allResults = searchResults.flatMap((response) => response);
          uniqueResults = [
            ...new Map(allResults.map((item) => [item.id, item])).values(),
          ];
        } catch (error) {
          console.error('Erreur lors de la recherche :', error);
        } finally {
          this.isLoading = false; // Ensure isLoading is set to false even on error
        }

        this.filteredCandidates = uniqueResults;
        console.log('Filtered Candidates:', this.filteredCandidates);
        this.displayResults = [...this.filteredCandidates];
        console.log('Display Results:', this.displayResults);
      },

      toggleAlertPanel() {
        this.alertPanelOn = !this.alertPanelOn;
        this.scrollToTop();
      },

      scrollToTop() {
        window.scrollTo({ top: 0, behavior: 'smooth' });
      },

      // Handle search input changes
      handleSearchInput(event) {
        if (event && event.target) {
          this.formData.query = event.target.value;
        } else {
          this.formData.query = event;
        }
      },
      handleClearSearch() {
        this.formData.query = ''; // Vide le champ
        this.$router.replace({ path: '/communaute' }); // Vide l’URL
      },

      // Handle search input changes for 'Lieu'
      handleLocationInput(event) {
        if (event && event.target) {
          this.formData.ville = event.target.value;
        } else {
          this.formData.ville = event;
        }
      },

      // Get city and postal code from LocationInput component
      getCityAndPostalCodeValue(cityAndPostalCode) {
        if (cityAndPostalCode) {
          this.formData.ville = cityAndPostalCode[0];
          this.formData.postalCode = cityAndPostalCode[1];
        }
      },

      // Update URL with search parameters
      updateURL(newParams) {
        const params = { ...this.$route.query };

        Object.keys(newParams).forEach((key) => {
          if (
            newParams[key] === null ||
            newParams[key] === undefined ||
            newParams[key] === ''
          ) {
            delete params[key];
          } else {
            params[key] = newParams[key];
          }
        });

        this.$router.replace({ query: params }).catch((err) => {
          //console.error("Erreur lors de la mise à jour de l'URL:", err);
        });
      },

      // Handle standard search
      handleSearch() {
        const trimmedQuery = this.formData.query?.trim();
        const query = {};

        if (trimmedQuery) {
          query.recherche = trimmedQuery;
        }

        if (this.formData.ville?.trim()) {
          query.ville = this.formData.ville;
          if (this.formData.postalCode) {
            query.cp = this.formData.postalCode;
          }
        }

        console.log('Query constructed for URL:', query);
        this.updateURL(query);
        this.searchCandidatesFromURL();
      },

      // Handle combined search (both standard and AI)
      async handleCombinedSearch() {
        // Si AI search est utilisé, afficher la modale et lancer la recherche
        if (this.bestProfileQuery) {
          this.showBestProfileModal = true;
          this.isLoadingBestProfiles = true;
          this.bestProfilesMessage = '';

          // Effacer les résultats standard pour n'afficher que les résultats AI
          this.filteredCandidates = [];

          // Lancer la recherche AI
          await this.fetchBestProfiles();

          // Mettre à jour le message selon les résultats
          if (this.bestProfiles.length > 0) {
            this.bestProfilesMessage = `${this.bestProfiles.length} profils correspondants trouvés !`;
          } else {
            this.bestProfilesMessage =
              'Aucun profil correspondant trouvé. Essayez de reformuler votre recherche.';
          }

          // Désactiver le loader
          this.isLoadingBestProfiles = false;

          // Fermer automatiquement la modale après un délai
          setTimeout(() => {
            this.showBestProfileModal = false;
          }, 2000);

          return;
        }

        // Sinon, effectuer la recherche standard si query ou location est fourni
        if (this.formData.query || this.formData.ville) {
          this.handleSearch();
        }

        // Combiner les résultats
        this.combineResults();
      },

      // Combine results from both search types
      combineResults() {
        // If AI search is active and has results, show AI results
        if (this.bestProfileQuery && this.bestProfiles.length > 0) {
          this.displayResults = [...this.bestProfiles];
        }
        // If no AI results or no AI query, show standard search results
        else if (this.filteredCandidates.length > 0) {
          this.displayResults = [...this.filteredCandidates];
        }
        // If no results from either search
        else {
          this.displayResults = [];
        }
      },

      async fetchBestProfiles() {
        console.log(
          'Démarrage fetchBestProfiles avec query:',
          this.bestProfileQuery
        );

        if (!this.bestProfileQuery) {
          console.log('Aucune requête, retour aux résultats standards');
          this.bestProfiles = [];
          this.displayResults = [...this.filteredCandidates];
          return;
        }

        this.isLoadingBestProfiles = true;
        this.bestProfiles = [];

        try {
          // Utiliser l'URL depuis les variables d'environnement
          const bestProfileApiUrl = process.env.VUE_APP_BEST_PROFILE_API_URL;

          if (!bestProfileApiUrl) {
            console.error(
              "Variable d'environnement VUE_APP_BEST_PROFILE_API_URL non définie"
            );
            throw new Error("URL de l'API Best Profile non configurée");
          }

          // Construire l'URL complète
          const apiUrl = `${bestProfileApiUrl}/get_best_profile`;
          console.log('URL API utilisée depuis .env:', apiUrl);

          // Préparer les données à envoyer
          const dataToSend = {
            query: this.bestProfileQuery,
          };

          console.log('Envoi requête avec paramètres:', dataToSend);

          // Utiliser axios.post avec seulement les en-têtes autorisés par le serveur
          const response = await axios.post(apiUrl, dataToSend, {
            headers: {
              'Content-Type': 'application/json',
              'x-api-key': 'thanks-boss-team-test',
              // Ne pas inclure l'en-tête Authorization car il n'est pas dans la liste des en-têtes autorisés
            },
          });

          console.log('Réponse reçue:', response);
          const data = response.data;
          console.log('Données extraites:', data);

          if (data && data.result) {
            console.log('Nombre de profils reçus:', data.result.length);
            // Transform the API response to match the expected format
            this.bestProfiles = data.result.map((profile) => ({
              id: profile.id,
              first_name: profile.first_name?.split(' ')[0] || '',
              last_name: profile.last_name?.split(' ').slice(1).join(' ') || '',
              email: profile.email || '',
              ville: profile.ville || '',
              metier: profile.metier || '',
              photo: profile.photo || null,
              phone: profile.phone || '',
              experience: profile.experience || '',
              contrat: profile.contrat || '',
              teletravail: profile.teletravail || '',
              description: profile.description || '',
              competences: profile.competences || [],
              skills: profile.skills || [],
              formation: profile.formation || [],
              cv_file: profile.cv_file || null,
              status: profile.status || 'available',
            }));

            console.log('Profils transformés:', this.bestProfiles);
            // Update display results immediately after transformation
            this.displayResults = [...this.bestProfiles];
            console.log('displayResults mis à jour:', this.displayResults);
          } else {
            console.log('Aucun résultat dans la réponse ou format incorrect');
            this.displayResults = [];
          }
        } catch (error) {
          console.error(
            'Erreur complète lors de la récupération des meilleurs profils:',
            error
          );
          console.error("Message d'erreur:", error.message);
          if (error.response) {
            console.error("Données de réponse d'erreur:", error.response.data);
            console.error("Statut d'erreur:", error.response.status);
          }
          this.displayResults = [...this.filteredCandidates];
        } finally {
          this.isLoadingBestProfiles = false;
          console.log(
            'Fin de fetchBestProfiles, isLoadingBestProfiles:',
            this.isLoadingBestProfiles
          );
        }
      },
    },
    watch: {
      '$route.query': {
        immediate: true,
        handler() {
          if (Object.keys(this.$route.query).length !== 0) {
            this.searchCandidatesFromURL();

            // Update form data from URL
            const query = this.$route.query;
            if (query.query) {
              this.formData.query = query.query;
            } else if (query.metier) {
              this.formData.query = query.metier;
            } else if (query.first_name || query.last_name) {
              this.formData.query =
                `${query.first_name || ''} ${query.last_name || ''}`.trim();
            }

            if (query.ville) {
              this.formData.ville = query.ville;
            }
            if (query.cp) {
              this.formData.postalCode = query.cp;
            }
          } else {
            this.loadCandidates();
          }

          // Update display results
          this.combineResults();
        },
      },
      filteredCandidates: {
        handler() {
          // Toujours mettre à jour displayResults sauf si recherche IA active
          if (!this.bestProfileQuery) {
            this.displayResults = [...this.filteredCandidates];
          }
        },
        deep: true,
        immediate: true, // Ajouté pour que displayResults soit mis à jour dès le début
      },
      bestProfiles: {
        handler() {
          // If using AI search, only show AI results
          if (this.bestProfileQuery && this.bestProfiles.length > 0) {
            this.displayResults = [...this.bestProfiles];
          }
        },
        deep: true,
      },
      bestProfileQuery(newValue) {
        // Clear display results when AI query is cleared
        if (!newValue) {
          this.bestProfiles = [];
          this.displayResults = [...this.filteredCandidates];
        }
      },
    },
  };
</script>

<style scoped>
  .container {
    background-color: var(--search-candidate-bg-color);
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    width: 100%;
    height: fit-content;
    box-sizing: border-box;
  }

  .search-content {
    width: 100%;
    height: auto;
    display: flex;
    flex-direction: column;
    align-items: stretch; /* Permet au contenu de s'étendre sur toute la largeur disponible */
  }

  .filters-wrapper {
    width: 100%;
    max-width: 250px;
  }

  .grid-wrapper {
    flex: 1;
  }

  .alert-panel-wrapper {
    display: flex;
    justify-content: center;
    width: 100%;
    max-width: 800px; /* Augmentation de la largeur maximale pour le panneau d'alerte */
    margin: 0 auto;
  }

  .loader-container {
    min-height: 50vh;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  /* Search container styling */
  .search-container {
    width: 100%;
    padding: 20px 0;
  }

  /* Combined search bar styling */
  .combined-search-bar {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 30px;
    width: 100%;
  }

  .field-container {
    padding: 20px;
    background-color: var(--search-candidate-searchfield-bg-color);
    border-radius: 15px;
    width: 100%;
  }

  .field-container h5 {
    margin-bottom: 10px;
  }

  /* AI field styling */
  .ai-field {
    border: 1px solid #a287e9;
    background-color: rgba(162, 135, 233, 0.05);
  }

  .ai-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
  }

  .ai-icon {
    width: 20px;
    height: 20px;
  }

  /* Search button styling */
  .btn-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .search-btn {
    width: 100%;
    height: 56px;
    border-radius: 15px;
  }

  /* Responsive layout for desktop */
  @media screen and (min-width: 992px) {
    .combined-search-bar {
      flex-direction: row;
      align-items: flex-start;
    }

    .field-container {
      width: 30%;
    }

    .btn-container {
      width: 10%;
      align-self: flex-end;
      margin-bottom: 20px;
    }

    .search-btn {
      height: 56px;
    }
  }

  @media screen and (max-width: 768px) {
    .filters-wrapper {
      max-width: inherit;
    }

    .search-input-wrapper {
      flex-direction: column;
    }

    .search-btn {
      width: 100%;
    }
  }

  @media screen and (min-width: 992px) {
    .content-wrapper {
      width: 100%;
    }
    .search-content {
      flex-direction: row;
    }
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2560px) {
    .container {
      max-width: 1920px;
      margin: 0 auto;
    }

    .search-content {
      flex-direction: row;
      padding: 40px 0;
    }

    .filters-wrapper {
      position: sticky;
      top: 20px;
      height: calc(100vh - 40px);
      overflow-y: auto;
      padding-right: 10px;
    }

    .grid-wrapper {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .loader-container {
      min-height: 60vh;
    }

    /* Combined search bar adjustments */
    .combined-search-bar {
      gap: 30px;
    }

    .field-container {
      padding: 25px;
    }

    /* AI field adjustments */
    .ai-field {
      border-width: 2px;
    }

    /* Search button adjustments */
    .search-btn {
      height: 60px;
      font-size: 1.1rem;
    }
  }

  /* TABLETTE EN MODE PAYSAGE */
  @media screen and (min-width: 768px) and (max-width: 992px) and (orientation: landscape) {
    .container {
      padding: 0 20px;
    }

    .search-content {
      flex-direction: row;
      padding: 30px 0;
    }

    .filters-wrapper {
      position: sticky;
      top: 10px;
      height: calc(100vh - 20px);
      overflow-y: auto;
      padding-right: 10px;
    }

    .grid-wrapper {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .loader-container {
      min-height: 50vh;
    }

    /* Combined search bar adjustments */
    .combined-search-bar {
      gap: 20px;
    }

    .field-container {
      padding: 15px;
    }

    /* AI field adjustments */
    .ai-field {
      border-width: 1.5px;
    }

    /* Search button adjustments */
    .search-btn {
      height: 50px;
      font-size: 0.9rem;
    }
  }
</style>
