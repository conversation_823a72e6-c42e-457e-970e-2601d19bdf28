<template>
  <div class="messagerie-wrapper">
    <div class="messagerie-container">
      <!-- <h1 class="page-title">Messagerie</h1> -->
      <div class="content-wrapper">
        <!-- Chat list and search section -->
        <aside class="messaging-chat-history-section">
          <!-- Search bar -->
          <div class="search-bar-container border-radius-5">
            <input placeholder="Recherche" v-model="searchConversationValue" @keyup.enter="handleSearchConversation" />
            <v-icon icon="mdi-magnify" class="search-icon border-radius-5 cursor-pointer"
              @click="handleSearchConversation"></v-icon>
          </div>
          <div class="chat-container">
            <!-- Tabs for "Toutes" and "Non lues" -->
            <v-tabs v-model="selectedTab">
              <v-tab> Toutes </v-tab>
              <v-tab> Non lues </v-tab>
            </v-tabs>

            <!-- Chat history list -->
            <v-list class="messaging-chat-history-list border-radius-5">

              <!-- Conversation list -->
              <v-list-item v-for="conversation in filteredConversations" :key="conversation.conversation_id"
                @click="selectConversation(conversation)"
                :class="{'unread-conversation': !conversation.isRead, 'v-list-item--active': conversation.conversation_id === selectedConversation.conversation_id}">
                <template v-slot:prepend>
                  <img src='@/assets/icons/avatar.png' alt="User Avatar" class="avatar-icon" />
                  <!-- <img :src="conversation.isCompany ? '@/assets/icons/company-logo.png' : '/assets/icons/avatar.png'"
                    alt="Avatar" class="avatar-icon" /> -->
                </template>
                <template v-slot:default>
                  <!-- Only show the title once -->
                  <span class="conversation-title">{{ conversation.titre }}</span>
                </template>
              </v-list-item>
            </v-list>
          </div>
        </aside>

        <!-- Main chat section -->
        <section class="chat-section">
          <!-- Chat header with avatar and buttons -->
          <div class="chat-header">
            <div class="chat-header-left">
              <!-- Company avatar and name -->
              <img src='@/assets/icons/company-logo.png' alt="Company Avatar" class="company-avatar" />
              <!-- Partie dynamique possible: -->
              <!-- <img :src="selectedConversation.isCompany ? '@/assets/icons/company-logo.png' : '/path/to/user-avatar.png'"
                alt="Company Avatar" class="company-avatar" /> -->
              <p class="company-name">Company Name</p>
            </div>

            <div class="chat-header-right">
              <!-- Icons (with images) -->
              <img src="@/assets/viseo.svg" alt="Video Call" class="chat-header-icon yellow-border" />
              <img src="@/assets/call.svg" alt="Call" class="chat-header-icon yellow-border" />
              <img src="@/assets/dots-black.svg" alt="More Options" class="chat-header-icon black-bg" />
            </div>
          </div>

          <!-- Dummy messages -->
          <div class="chat-messages">
            <div class="message user-message">
              <p>Lorem ipsum dolor sit amet con sectetur. Adipiscing elit ullamcorper eget mi tellus ultricies dui
                aenean.</p>
            </div>
            <div class="message company-message">
              <p>Lorem ipsum dolor sit amet con sectetur. Adipiscing elit ullamcorper eget mi tellus ultricies dui
                aenean.</p>
            </div>
            <div class="message user-message">
              <p>Lorem ipsum dolor sit amet con sectetur. Adipiscing elit ullamcorper eget mi tellus ultricies dui
                aenean.</p>
            </div>
            <div class="message company-message">
              <p>Lorem ipsum dolor sit amet con sectetur. Adipiscing elit ullamcorper eget mi tellus ultricies dui
                aenean.</p>
            </div>
          </div>

          <!-- Chat input section -->
          <div class="chat-input-section">
            <!-- Textarea above the icons -->
            <div class="textarea-container">
              <textarea v-model="newMessage" placeholder="Saisis ton message"></textarea>
            </div>

            <!-- Icons row and send button -->
            <div class="icons-and-send">
              <div class="icons-row">
                <img src="@/assets/text-black.svg" alt="Text Icon" class="input-icon" />
                <img src="@/assets/more-black.svg" alt="Plus Icon" class="input-icon" />
                <img src="@/assets/at-black.svg" alt="Mention Icon" class="input-icon" />
                <img src="@/assets/pj-black.svg" alt="Attach Icon" class="input-icon" />
                <img src="@/assets/emoji-black.svg" alt="Smile Icon" class="input-icon" />
              </div>

              <!-- Send button -->
              <button class="send-button" @click="onSendMessage">
                <img src="@/assets/icons/icon-send-msg-primary1.svg" alt="Send Icon" />
              </button>
            </div>
          </div>

        </section>


        <!-- Recruiter details section -->
        <aside class="recruiter-details">
          <img src='@/assets/icons/company-logo.png' alt="Company Avatar" class="company-avatar-image" />
          <!-- Partie dynamique possible: -->
          <!-- <img :src="selectedConversation.isCompany ? '@/assets/icons/company-logo.png' : '@/assets/icons/company-logo.png'"
            alt="Company Avatar" class="company-avatar-image" /> -->
          <p class="recruiter-name">Nom</p>
          <p class="recruiter-description">Description</p>
          <p class="recruiter-description">Description</p>

          <v-btn class="profile-button" @click="onViewProfile">
            View Profile
            <img class="profile-icon" src="@/assets/icons/eye.svg" alt="Eye Icon" />
          </v-btn>


        </aside>

      </div>
    </div>
  </div>

   
  <BackToTopArrow />

</template>

<script>
import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';


export default {
  components: {
        BackToTopArrow,
    },

  data() {
    return {
      searchConversationValue: '',
      selectedTab: 0,
      allConversations: [
        { conversation_id: 1, titre: "Conversation 1", isRead: true, isCompany: false },
        { conversation_id: 2, titre: "Conversation 2", isRead: false, isCompany: true },
        { conversation_id: 3, titre: "Conversation 3", isRead: false, isCompany: false },
      ],
      selectedConversation: { conversation_id: 1, titre: "Conversation 1", messages: [] },
      newMessage: '',
    };
  },

  computed: {
    filteredConversations() {
      if (this.selectedTab === 0) {
        return this.allConversations;
      } else {
        return this.allConversations.filter(conversation => !conversation.isRead);
      }
    }
  },

  methods: {
    handleSearchConversation() {
      //console.log("Search clicked");
    },
    selectConversation(conversation) {
      this.selectedConversation = conversation;
    },
    onSendMessage() {
      if (this.newMessage.trim()) {
        //console.log("Sending message:", this.newMessage);
        this.newMessage = ''; // Clear input after sending
      }
    },
    onViewProfile() {
      //console.log("View profile clicked");
    }
  }
};
</script>

<style scoped>
/* Title aligned to the left */
.page-title {
  margin-bottom: 24px;
  text-align: left;
  padding-left: 16px;
}

/* Tabs styles */
.v-tabs {
  background-color: #fff;
  border-bottom: 1px solid var(--gray-300); 
  border-radius: 5px 5px 0px 0;
  display: flex;
  justify-content: center; 
  align-items: center;
  margin: auto 15px;
}

.v-tab--active {
  border-bottom: 2px solid var(--yellow-100);
  font-weight: bold;
}

/* Styles for conversation avatar */
.avatar-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 10px;
}
/* Conversation title styling */
.conversation-title {
  font-size: 16px;
  font-weight: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Adjust the padding and spacing */
.v-list-item {
  display: flex;
  align-items: center;
  padding: 10px;
  margin: 5px;
  border-bottom: 1px solid var(--gray-300); 
  transition: border-color 0.3s ease;
}

/* Wrapper to center content */
.messagerie-wrapper {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100vh;
  background-color: #f0f0f0;
  padding: 40px 16px;
}

/* Main container */
.messagerie-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
  max-width: 1200px;
}

.chat-container {
  background-color: #fff;
  height: auto;
  border-radius: 5px;
}

/* Wrapper for chat and list */
.content-wrapper {
  display: grid;
  grid-template-columns: 275px 550px 179px;
  gap: 24px;
  justify-content: center;
  width: 100%;
  align-items: start;
  grid-template-areas: 
    "page-title page-title page-title"
    "chat-history-section chat-section recruiter-details";
}

/* Chat list and search section */
.messaging-chat-history-section {
  grid-area: chat-history-section;
  width: 100%;
  max-width: 275px;
  padding-left: 16px;
}

.search-bar-container {
  height: 56px;
  margin-bottom: 24px;
  padding: 8px;
  gap: 8px;
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 5px;
}

.search-bar-container input {
  height: 100%;
  width: 80%;
  background-color: #f5f5f5;
  border: none;
  padding-left: 8px;
  border-radius: 5px;
}

.search-bar-container v-icon {
  cursor: pointer;
}

.search-icon {
  background-color: var(--yellow-100);
  padding: 8px;
  border-radius: 5px;
  color: black;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
}

.search-icon:hover, .send-button:hover {
   
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
}

.messaging-chat-history-list {
  background-color: #fff;
  overflow-y: auto;
  border-radius: 5px;
}

.messaging-chat-history-list-title {
  text-align: center;
  padding: 8px 0;
  border-bottom: 1px solid #ccc;
}

.messaging-chat-history-list .v-list-item {
  padding: 10px 16px;
  border-bottom: 1px solid var(--yellow-100);
}

.v-list-item--active {
  border-bottom: 1px solid var(--yellow-100); 
  background-color: transparent;
}

.unread-conversation {
  font-weight: bold;
}

.messaging-add-conv-btn {
  display: flex;
  justify-content: center;
  padding: 16px 0;
}

.messaging-add-conv-btn button {
  background-color: var(--yellow-100);
  border: none;
  padding: 8px 16px;
  cursor: pointer;
  border-radius: 10px;
  color: black;
}

/* Main chat section */
.chat-section {
  grid-area: chat-section;
  background-color: #fff;
  height: auto;
  padding: 16px;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid #ccc;
}

/* Left part of the header (avatar and name) */
.chat-header-left {
  display: flex;
  align-items: center;
}

.company-avatar {
  display: flex;
  align-content: center;
  width: 48px;
  height: 48px;
  border-radius: 5px;
  border: 1px solid #DFDBD6; 
  opacity: 1;
  margin: 15px;
}

.company-name {
  font-size: 18px;
  font-weight: bold;
  color: black;
}

/* Right part of the header (icons) */
.chat-header-right {
  display: flex;
  gap: 10px;
}

.chat-header-icon {
  width: 32px;
  height: 32px;
  border-radius: 5px;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.chat-header-icon:hover {
  background-color: var(--yellow-100);
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
}

.black-bg {
  background-color: black;
  color: white;
}

/* Gray separator */
.gray-separator {
  border: 0;
  height: 1px;
  background-color: #ccc;
  margin: 16px 0;
}

/* Conversation title below the separator */
.chat-title p {
  font-size: 16px;
  font-weight: bold;
  text-align: left;
  padding-left: 16px;
}

.chat-messages {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 16px;
}

.message {
  max-width: 60%;
  padding: 10px 16px;
  border-radius: 10px;
  margin-bottom: 10px;
  display: inline-block;
  word-wrap: break-word;
}

.user-message {
  align-self: flex-start; 
  background-color: #f1f1f1; 
  color: black;
  border-radius: 10px 10px 10px 0; 
}

.company-message {
  align-self: flex-end; 
  background-color: var(--yellow-100); 
  color: black;
  border-radius: 10px 10px 0 10px; 
}

/* Chat input section styling */
.chat-input-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  border-top: 1px solid #ccc;
}

/* Textarea container styling */
.textarea-container {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

/* Textarea styling */
.chat-input-section textarea {
  width: 100%;
  padding: 12px;
  border-radius: 5px;
  border: 1px solid var(--gray-300);
  background-color: #fdf5e6;
  font-size: 14px;
  resize: none;
  min-height: 80px;
}

/* Icons and send button styling */
.icons-and-send {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

/* Icons row */
.icons-row {
  display: flex;
  gap: 10px;
}

.input-icon {
  width: 32px;
  height: 32px;
  gap: 6px;
  background-color: #fff;
  border-radius: 5px;
  cursor: pointer;
  transition: 0.3s ease;
}

.input-icon:hover {
  background-color: var(--yellow-100);
}

/* Send button styling */
.send-button {
  width: 32px;
  height: 32px;
  background-color: var(--yellow-100);
  border: none;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

/* Recruiter details section */
.recruiter-details {
  grid-area: recruiter-details;
  background-color: #fff;
  border-radius: 5px;
  height: auto;
}

.company-avatar-image {
  display: flex;
  align-content: center;
  width: 92px;
  height: 92px;
  border-radius: 5px;
  border: 1px solid #DFDBD6; 
  opacity: 1;
  margin: 15px auto;
}

.recruiter-name {
  display: flex;
  align-content: center;
  margin: 15px;
}

.recruiter-description {
  display: flex;
  margin: 15px;
}

/* Styles for the "View Profile" button */
.profile-button {
  width: 147px; 
  height: 40px; 
  padding: 16px; 
  margin: 15px auto;
  gap: 8px; 
  background-color: #26282B;  
  border-radius: 5px;
  color: white;
  display: flex; 
  align-items: center; 
  justify-content: center; 
  cursor: pointer; 
  border: none; 
  opacity: 1; 
}

/* Icon inside the "View Profile" button */
.profile-icon {
  margin-left: 8px; 
  font-size: 16px;
  color: var(--yellow-100);
}

/* Responsive layout for mobile */
@media (max-width: 1024px) {
  .content-wrapper {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
    grid-template-areas:
      "page-title"
      "chat-section"
      "chat-history-section"
      "recruiter-details";
  }

  .messaging-chat-history-section {
    grid-area: chat-history-section;
    width: 100%;
    max-width: none;
    padding: 0;
  }
}

</style>
