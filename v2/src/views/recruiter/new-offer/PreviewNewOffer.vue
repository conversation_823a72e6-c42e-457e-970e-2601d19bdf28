<template>
  <main class="container padding-container">
    <div class="content-wrapper">
      <div class="btn-row">
        <img
          src="@/assets/icons/close-btn.svg"
          alt="bouton fermer"
          class="close-btn"
          @click="closePreview"
        />
      </div>

      <!-- job title, job company, location etc -->
      <section class="top-container">
        <div class="section-header">
          <div class="company-title-wrapper">
            <div class="company-logo-wrapper">
              <img
                :src="
                  joboffer.logo_url
                    ? getImgPath(joboffer.logo_url)
                    : defaultLogo
                "
                class="logo"
                alt="logo de l'entreprise"
              />
            </div>

            <div class="title-wrapper">
              <h2>{{ joboffer.title }}</h2>
              <h5>
                <u>{{ joboffer.nom_recruteur }}</u>
              </h5>
            </div>
          </div>
        </div>

        <div class="section-body">
          <div class="tag">
            <img
              src="@/assets/icons/localisation-mini.svg"
              alt="icône de localisation"
            />
            <p class="fs12">Localisation : {{ joboffer.local }}</p>
          </div>

          <div class="tag">
            <img src="@/assets/icons/list-mini.svg" alt="icône de liste" />
            <p class="fs12">Type de contrat : {{ joboffer.contract }}</p>
          </div>

          <div class="tag">
            <img src="@/assets/icons/pc-mini.svg" alt="icône d'ordinateur" />
            <p class="fs12">
              Type de travail : {{ joboffer.jours_teletravail }}
            </p>
          </div>

          <div class="tag">
            <img src="@/assets/icons/suitcase-mini.svg" alt="icône de valise" />
            <p class="fs12">Niveau d'expérience : {{ joboffer.expérience }}</p>
          </div>

          <div class="tag">
            <img src="@/assets/icons/calendly.svg" alt="icône de calendrier" />
            <p class="fs12">
              Publiée le : {{ formatDate(joboffer.created_at) }}
            </p>
          </div>
        </div>
      </section>

      <!-- job description -->
      <section class="section-container">
        <h5>Poste à pourvoir</h5>
        <div class="custom-p-background" v-html="formattedJobDescription"></div>
      </section>

      <!-- job skills & soft skills -->
      <section
        v-if="joboffer.savoir_pro && joboffer.savoir_pro.length"
        class="section-container"
      >
        <h5>Savoirs-faire</h5>
        <div class="packet-chips">
          <div
            v-for="(savoir_pro, index) in offer.savoir_pro"
            :key="index"
            class="chip-wrapper"
          >
            <Chip
              v-if="savoir_pro && savoir_pro.libelle"
              :textContent="savoir_pro.libelle"
            />
          </div>
        </div>

        <h5 v-if="showSoftSkills">Savoirs-être</h5>
        <div class="packet-chips">
          <div
            v-for="(savoir_etre, index) in offer.savoir_etre"
            :key="index"
            class="chip-wrapper"
          >
            <Chip
              v-if="savoir_etre && savoir_etre.libelle"
              :textContent="savoir_etre.libelle"
            />
          </div>
        </div>
      </section>

      <!-- company description -->
      <section class="section-container">
        <h5>Qui sommes-nous ?</h5>
        <p class="custom-p-background">{{ joboffer.company_details }}</p>

        <h5>Pourquoi nous rejoindre ?</h5>
        <p class="custom-p-background">{{ joboffer.why_join_us }}</p>
      </section>

      <!-- company bonus -->
      <section class="section-container">
        <h5>Nos avantages</h5>
        <div class="packet-chips">
          <div
            v-for="(advantage, index) in formatAdvantages(joboffer.advantages)"
            :key="index"
            class="chip-wrapper"
          >
            <Chip :textContent="advantage" />
          </div>
        </div>

        <h5>Le processus de recrutement</h5>
        <p class="custom-p-background">{{ joboffer.recruitment_process }}</p>
      </section>

      <!-- question for candidate 
      <section class="section-container">
        <h5>Les questions recrutement</h5>
        <p>Explication pour le recruteur, qu'il peut poser jusqu'à 5 questions en lien avec l'offre rédiger.</p>
        <div v-for="question in joboffer.questions" 
          class="question-wrapper">
          <h6 class="custom-p-background">{{ question }}</h6>
          <br>
          <v-text-field
            label="Votre réponse ici."
            type="text"
          />
        </div>
      </section> -->
    </div>

    <BackToTopArrow />
  </main>
</template>

<script>
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import Chip from '@/components/chips/Chip.vue';
  import defaultLogo from '@/assets/logo-tb.svg';
  import getImgPath from '@/utils/imgpath.js';

  export default {
    name: 'PreviewNewOffer',
    components: {
      BackToTopArrow,
      // PrimaryNormalButton,
      Chip,
    },

    props: {
      joboffer: {
        type: Object,
        required: false,
        default: () => ({}), // Initialisation avec un objet vide pour éviter undefined
      },
    },

    data() {
      return {
        offer: {}, // Initialiser un objet vide
        showSoftSkills: true,
        showSkills: true,
      };
    },

    computed: {
      formattedJobDescription() {
        if (!this.joboffer?.descriptif) return '';

        let formattedText = this.joboffer?.descriptif;

        // 🔹 Convertir "#### Sous-Titre" en <h4>
        formattedText = formattedText.replace(/####\s(.*)/g, (match, p1) => {
          return `<h4 style="text-decoration: underline; margin: 20px 0 10px 0;">${p1}</h4>`;
        });

        // 🔹 Convertir "### Titre" en <h3>
        formattedText = formattedText.replace(/###\s(.*)/g, (match, p1) => {
          return `<h3 style="font-weight: bold;">${p1}</h3>`;
        });

        // 🔹 Convertir "**Texte en gras**" en <b>
        formattedText = formattedText.replace(/\*\*(.*?)\*\*/g, (match, p1) => {
          return `<b>${p1}</b>`;
        });

        // 🔹 Convertir les numéros "1." en pastilles alignées
        formattedText = formattedText.replace(
          /(\d+)\.\s(.+)/g,
          (match, p1, p2) => {
            return `<div style="display: flex; align-items: center; margin: 10px 0;">
            <span style=" display: flex; justify-content: center; align-items: center; font-weight: bold; font-size: 16px; color: #f7bc53; width: 25px; height: 25px; border: 2px solid #f7bc53; border-radius: 50%; flex-shrink: 0; text-align: center; line-height: 30px;">${p1}</span>
            <span style="margin-left: 10px;">${p2}</span>
          </div>`;
          }
        );

        // 🔹 Convertir les listes non numérotées "- Texte" en puces "• Texte"
        formattedText = formattedText.replace(/^\s*-\s(.+)/gm, (match, p1) => {
          return `<div style="display: flex; align-items: flex-start; margin-left: 20px; margin-top: 10px;">
            <span style="font-size: 18px; color: black; margin-right: 10px;">•</span>
            <span>${p1}</span>
          </div>`;
        });

        // 🔹 Convertir les liens email Markdown [texte](mailto:email) en liens HTML
        formattedText = formattedText.replace(
          /\[([^\]]+)\]\(mailto:([^)]+)\)/g,
          (match, p1, p2) => {
            return `<a href="mailto:${p2}" style="color: #007bff; text-decoration: underline;">${p1}</a>`;
          }
        );

        // 🔹 Supprimer les <br> entre les <div> (évite les sauts de ligne entre éléments de liste)
        formattedText = formattedText.replace(
          /<\/div>\s*<br>\s*<div/g,
          '</div><div>'
        );

        // 🔹 Supprimer les <br> qui suivent immédiatement un <div> (évite les espaces vides)
        formattedText = formattedText.replace(/<div[^>]*>\s*<br>/g, '<div>');

        // 🔹 Convertir les retours à la ligne (\n) en <br>, sauf après <h4> et entre les <div>
        formattedText = formattedText.replace(/\n(?!<\/h4>|<\/div>)/g, '<br>');

        // 🔹 Nettoyer les <br> après <h4> pour éviter un saut de ligne supplémentaire
        formattedText = formattedText.replace(/<\/h4><br>/g, '</h4>');

        return formattedText;
      },
    },

    mounted() {
      //console.log(
      //  'Tentative de récupération des données de prévisualisation...'
      //);

      // Récupérer les données stockées
      const storedOffer = localStorage.getItem('previewOffer');

      if (storedOffer) {
        try {
          this.offer = JSON.parse(storedOffer);

          // Assurer que savoir_pro et savoir_etre sont bien des tableaux d'objets
          this.offer.savoir_pro = Array.isArray(this.offer.savoir_pro)
            ? this.offer.savoir_pro.map((item) =>
                typeof item === 'string' ? { libelle: item } : item
              )
            : [];

          this.offer.savoir_etre = Array.isArray(this.offer.savoir_etre)
            ? this.offer.savoir_etre.map((item) =>
                typeof item === 'string' ? { libelle: item } : item
              )
            : [];

          //console.log('Offer récupéré avec succès :', this.offer);
        } catch (e) {
          //console.error('Erreur lors du parsing des données stockées :', e);
          this.offer = {};
        }
      } else {
        console.warn('Aucune donnée trouvée dans localStorage.');
        this.offer = {};
      }
    },

    methods: {
      getImgPath,

      formatAdvantages(advantages) {
        if (!advantages) return [];
        return typeof advantages === 'string'
          ? advantages.split(',')
          : advantages;
      },

      created() {
        this.offerData = this.$route.state.offerData || {};
        //console.log('this.offerData', this.offerData);
      },

      //  toggle preview off
      closePreview() {
        this.$emit('close-preview');
      },

      getTodayDate() {
        const today = new Date();
        return today.toLocaleDateString('fr-FR', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        });
      },

      formatDate(dateString) {
        if (!dateString) return this.getTodayDate(); // Si la date est absente, retourne la date actuelle.
        const date = new Date(dateString);
        return date.toLocaleDateString('fr-FR', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        });
      },
    },
  };
</script>

<style scoped>
  /* layout */
  .container .content-wrapper {
    display: flex;
    flex-direction: column;
    gap: 30px;
    height: fit-content;
    padding-bottom: 50px;
  }

  .btn-row {
    display: flex;
    width: 100%;
    justify-content: end;
  }

  .close-btn {
    width: 32px;
    height: 32px;
    cursor: pointer;
  }

  .top-container {
    display: flex;
    flex-direction: column;
    padding-bottom: 50px;
  }

  .section-container {
    background-color: var(--surface-bg-2);
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: 100%;
    border-radius: 5px;
  }
  .section-container .custom-p-background {
    background-color: var(--yellow-20);
    min-height: 60px;
    padding: 20px 40px;
    border-radius: 5px;
  }

  .section-header {
    background-color: var(--surface-bg-2);
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    padding-inline: 16px;
    padding-block: 40px;
    display: flex;
    justify-content: space-between;
    width: 100%;
  }

  .section-body {
    background-color: var(--text-1);
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    padding: 8px;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 10px;
    width: 100%;
  }

  .section-footer {
    display: flex;
    justify-content: center;
    gap: 30px;
    width: 100%;
    padding-top: 40px;
  }

  /* wrappers */
  .company-title-wrapper {
    display: flex;
    flex-direction: column;
    gap: 40px;
    align-items: center;
    margin-left: 60px;
  }

  .company-logo-wrapper {
    height: fit-content;
  }

  .title-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .view-wrapper {
    width: fit-content;
    height: fit-content;
  }

  .btn-wrapper {
    width: fit-content;
    height: fit-content;
  }

  /* utilities */
  .logo {
    border: 1px solid rgba(223, 219, 214, 1);
    height: 92px;
    border-radius: 8px;
    object-fit: contain;
  }

  .tag {
    background-color: var(--surface-bg-2);
    padding: 2px;
    border-radius: 5px;
    display: flex;
    justify-content: start;
    align-items: center;
    gap: 5px;
  }

  .fs12 {
    font-size: 10px;
  }

  .favicon {
    cursor: pointer;
  }

  .packet-chips {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  @media screen and (min-width: 992px) {
    .company-title-wrapper {
      flex-direction: row;
      align-items: center;
    }

    .title-wrapper {
      text-align: initial;
    }

    .align-start {
      align-self: baseline;
    }

    .section-body {
      display: flex;
      padding: 16px;
      justify-content: space-between;
      gap: 10px;
    }

    .tag {
      padding: 4px;
      gap: 10px;
    }

    .fs12 {
      font-size: 12px;
    }

    .section-footer {
      gap: 80px;
    }

    .section-container {
      gap: 32px;
      padding-inline: 91px;
      padding-block: 24px;
    }
  }

  @media screen and (min-width: 450px) {
    .section-container {
      gap: 32px;
      padding-inline: 60px;
      padding-block: 24px;
    }
  }

  @media screen and (max-width: 450px) {
    /* wrappers */
    .company-title-wrapper {
      margin-left: inherit;
    }

    /* layout */
    .section-container {
      gap: 32px;
      padding-inline: 20px;
      padding-block: 12px;
    }

    .section-container .chip-wrapper .chip {
      text-wrap: wrap;
      height: fit-content;
      padding-block: 4px;
    }
  }

  /* desktop */
  @media screen and (min-width: 992px) {
    .content-wrapper {
      width: 100%;
    }
  }

  /* large desktop */
  @media screen and (min-width: 1800px) {
    .content-wrapper {
      width: 80%;
      margin: 0 auto;
    }
  }

  /* x-large desktop */
  @media screen and (min-width: 2400px) {
    .content-wrapper {
      width: 70%;
      margin: 0 auto;
    }
  }
</style>
