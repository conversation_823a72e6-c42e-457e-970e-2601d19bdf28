<template>
  <OfferPreview
    v-if="isPreviewOn"
    :joboffer="formData"
    @close-preview="togglePreview"
  />

  <main v-else class="container padding-container">
    <!-- Fond sombre -->
    <div
      v-if="showConfirmationModal"
      class="overlay"
      @click="showConfirmationModal = false"
    ></div>
    <div class="content-wrapper">
      <!-- title -->
      <h1>Rédaction de l'offre</h1>
      <!-- Button to navigate back to the offer page-->
      <div class="back-btn-container">
        <div class="btn-wrapper" @click="gotoPage('/recruteur/offres')">
          <PrimaryNormalButton textContent="Retour" btnColor="secondary" back />
        </div>
      </div>

      <!-- job title, job company, preview button -->
      <section class="section-container-top">
        <div class="section-header">
          <div class="company-wrapper">
            <div class="company-logo-wrapper">
              <img
                v-if="formData.logo_img"
                :src="getFullImageUrl()"
                class="logo"
                alt="logo de l'entreprise"
              />
              <img
                v-else
                src="@/assets/icons/company-logo.png"
                alt="company logo"
                class="logo"
              />
            </div>

            <div class="title-wrapper">
              <h2 class="align-start">
                <v-text-field
                  v-model="formData.title"
                  label="Intitulé du poste à pourvoir"
                  type="text"
                ></v-text-field>
              </h2>
              <h5 class="align-start">
                <u>{{ formData.nom_recruteur }}</u>
              </h5>
            </div>
          </div>

          <div class="view-wrapper">
            <v-btn flat class="preview-btn" @click="togglePreview">
              <template v-slot:append>
                <img src="@/assets/icons/preview-icon.svg" class="favicon" />
              </template>
            </v-btn>
          </div>
        </div>
      </section>

      <!-- job description -->
      <section class="section-container">
        <h5>Poste à pourvoir</h5>
        <v-textarea
          v-model="formData.descriptif"
          label="Détails de la mission : "
          type="text"
          fast-fail
        ></v-textarea>
        <div class="btn-magic">
          <PrimaryRoundedButton
            textContent="Améliorer avec l'IA"
            btnColor="ai"
            ai
            @click="improvingWithAI"
          />
        </div>
      </section>

      <!-- job location -->
      <section class="section-container">
        <h5>Localisation</h5>
        <v-text-field
          v-model="formData.local"
          textContent="Sélectionne ou ajoute la localisation"
        ></v-text-field>
      </section>

      <!-- job contract -->
      <section class="section-container">
        <h5>Type de contrat</h5>
        <v-combobox
          v-model="formData.contract"
          label="Sélectionne ou ajoute le type de contrat"
          :items="contractOptionsInputList"
          chips
          clearable
          hide-selected
        ></v-combobox>
      </section>

      <!-- job remote -->
      <section class="section-container">
        <h5>Type de travail</h5>
        <v-combobox
          v-model="formData.jours_teletravail"
          label="Sélectionne ou ajoute le type de travail"
          :items="remoteOptionsInputList"
          chips
          clearable
          hide-selected
        ></v-combobox>
      </section>

      <!-- job experience -->
      <section class="section-container">
        <h5>Niveau d'expérience</h5>
        <v-combobox
          v-model="formData.expérience"
          label="Sélectionne ou ajoute le niveau d'expérience"
          :items="experienceOptionsInputList"
          chips
          clearable
          hide-selected
        ></v-combobox>
      </section>

      <!-- job skills & soft skills -->
      <section class="section-container">
        <h5>Savoirs-faire</h5>
        <!-- <p class="custom-p-background">Description</p> -->
        <v-combobox
          v-model="formData.savoir_pro"
          label="Sélectionne ou ajoute les savoirs-faire"
          :items="skillsOptionsInputList"
          multiple
          chips
          clearable
          hide-selected
          item-title="libelle"
          item-value="libelle"
          return-object
        >
        </v-combobox>

        <h5>Savoirs-être</h5>
        <!-- <p class="custom-p-background">Description</p> -->
        <v-combobox
          v-model="formData.savoir_etre"
          label="Sélectionne ou ajoute les savoirs-être"
          :items="softSkillsOptionsInputList"
          multiple
          chips
          clearable
          hide-selected
          item-title="libelle"
          item-value="libelle"
          return-object
        >
        </v-combobox>
      </section>

      <!-- company description -->
      <section class="section-container">
        <h5>Qui sommes-nous ?</h5>
        <p class="custom-p-background">{{ formData.company_details }}</p>

        <h5>Pourquoi nous rejoindre ?</h5>
        <p class="custom-p-background">{{ formData.why_join_us }}</p>
      </section>

      <!-- company bonus -->
      <section class="section-container avantages">
        <h5>Nos avantages</h5>
        <div class="custom-p-background packet-chips">
          <div
            v-for="(advantage, index) in formatAdvantages(formData.advantages)"
            :key="index"
            class="chip-wrapper"
          >
            <Chip :textContent="advantage" />
          </div>
        </div>

        <h5>Le processus de recrutement</h5>
        <p class="custom-p-background">{{ formData.recruitment_process }}</p>
      </section>

      <!-- question for candidate 
      <section class="section-container">
        <h5>Les questions recrutement</h5>
        <p v-if="!formData.publie">Explication pour le recruteur, qu'il peut poser jusqu'à 5 questions en lien avec
          l'offre rédiger.</p>
        <div v-for="(question, index) in formData.question" :key="index">
          <h6>Question {{ index + 1 }} :</h6>
          <v-text-field v-model="formData.question[index]" label="Question" type="text"></v-text-field>
        </div>
      </section> -->

      <!-- buttons -->
      <div class="section-footer">
        <div class="btn-wrapper">
          <PrimaryNormalButton
            textContent="Enregistrer le brouillon"
            btnColor="secondary"
            @click="saveDraft"
          />
        </div>
        <div class="btn-wrapper">
          <PrimaryNormalButton
            textContent="Publier l'offre"
            @click="publishOffer"
          />
        </div>
      </div>
    </div>

    <BackToTopArrow />

    <ConfirmationModal
      v-if="showConfirmationModal"
      class="confirmation-modal"
      title="Amélioration de la description"
      description="Avez-vous bien renseigné tous les champs pour une amélioration optimale ?"
      @close="showConfirmationModal = false"
      @confirm="confirmImprovingWithAI"
    />

    <AIImprovementModal
      :show="showModal"
      :isLoading="isLoading"
      :aiDescription="aiDescription"
      @update:show="showModal = $event"
      @confirm="applyImprovedDescription"
    />
  </main>
</template>

<script>
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import Chip from '@/components/chips/Chip.vue';
  import LocationInput from '@/components/inputs/LocationInput.vue';
  import { baseUrl, AIbaseUrlOptimizer } from '@/services/axios';
  import {
    createJobOffers,
    modifyJobOffers,
  } from '@/services/offer.service.js';
  import { AVANTAGE_FIELDS } from '@/utils/base/avantage';
  import gotoPage from '@/utils/router.js';
  import { toaster } from '@/utils/toast/toast.js';
  import OfferPreview from '@/views/recruiter/new-offer/PreviewNewOffer.vue';
  import AIImprovementModal from '../../../components/modal/ai/AIImprovementModal.vue';
  import axios from 'axios';
  import ConfirmationModal from '../../../components/modal/confirmation/ConfirmationModal.vue';

  export default {
    name: 'NewOffer',

    components: {
      BackToTopArrow,
      OfferPreview,
      PrimaryNormalButton,
      PrimaryRoundedButton,
      // LocationInput,
      Chip,
      AIImprovementModal,
      ConfirmationModal,
    },

    data() {
      return {
        isPreviewOn: false, //  toggle between offer input and offer preview
        showModal: false,
        isLoading: false,
        aiDescription: '',

        remoteOptionsInputList: [
          'Télétravail complet',
          'Télétravail partiel',
          'En présentiel',
          'Hybride',
        ],
        experienceOptionsInputList: ['0-1 an', '1-3 ans', '3-5 ans', '> 5 ans'],
        contractOptionsInputList: ['CDI', 'CDD', 'Stage', 'Intérim', 'VIE'],
        skillsOptionsInputList: [
          'JavaScript',
          'Python',
          'Django',
          'Vue.js',
          'API REST',
          'SQL',
          'HTML',
          'CSS',
          'Git',
          'TypeScript',
          'Angular',
          'React.js',
          'Node.js',
          'Express.js',
          'Ruby on Rails',
          'PHP',
          'Laravel',
          'Flask',
          'Svelte',
          'jQuery',
          'GraphQL',
          'MongoDB',
          'PostgreSQL',
          'MySQL',
          'NoSQL',
          'Docker',
          'Kubernetes',
          'AWS',
          'Azure',
          'Google Cloud Platform',
          'CI/CD',
          'Jenkins',
          'GitLab',
          'GitHub',
          'Webpack',
          'Babel',
          'Sass',
          'Less',
          'Bootstrap',
          'Tailwind CSS',
          'TensorFlow',
          'PyTorch',
          'Keras',
          'Scikit-learn',
          'Pandas',
          'NumPy',
          'Matplotlib',
          'Seaborn',
          'OpenCV',
          'NLP',
          'Deep Learning',
          'Neural Networks',
          'Data Science',
          'Data Analysis',
          'Big Data',
          'Machine Learning',
          'Artificial Intelligence',
          'Selenium',
          'Cypress',
          'Jest',
          'Mocha',
          'Chai',
          'Jasmine',
          'WebSockets',
          'OAuth',
          'JWT',
          'RESTful API',
          'Microservices',
          'GraphQL',
          'Redux',
          'MobX',
          'Webpack',
          'Vite',
          'Rollup',
          'Gulp',
          'Grunt',
          'Postman',
          'Figma',
          'Adobe XD',
          'InVision',
          'Sketch',
          'Photoshop',
          'Illustrator',
          'Blender',
          '3D Modeling',
          'Unity',
          'Unreal Engine',
          'Game Development',
          'Agile',
          'Scrum',
          'Kanban',
          'Project Management',
          'JIRA',
          'Confluence',
          'Notion',
          'Trello',
          'Asana',
          'Slack',
          'Zoom',
          'Teams',
          'Miro',
          'Figma',
          'Google Analytics',
          'SEO',
          'Content Management',
          'WordPress',
          'Shopify',
          'Magento',
          'Drupal',
          'Salesforce',
          'CRM',
          'SAP',
          'Oracle',
          'SQL Server',
          'Power BI',
          'Tableau',
          'Looker',
          'Microsoft Excel',
          'Advanced Excel',
          'VBA',
          'Macros',
          'Power Query',
          'Power Pivot',
          'Pivot Tables',
          'Data Visualization',
          'Statistics',
          'R',
          'MATLAB',
          'Simulink',
          'LabVIEW',
          'AutoCAD',
          'SolidWorks',
          '3D Printing',
          'Digital Marketing',
          'Social Media Marketing',
          'Email Marketing',
          'Copywriting',
          'Content Creation',
          'UX/UI Design',
          'Human-Computer Interaction',
          'Accessibility',
          'User Research',
          'Prototyping',
          'Testing',
          'QA',
          'DevOps',
          'Cloud Computing',
          'Network Security',
          'Cybersecurity',
          'Penetration Testing',
          'Ethical Hacking',
          'Cryptography',
          'Blockchain',
          'Bitcoin',
          'Ethereum',
          'Smart Contracts',
          'IoT (Internet of Things)',
          'Raspberry Pi',
          'Arduino',
          'Embedded Systems',
          'Robotics',
          'Automation',
          'Control Systems',
          'PLC Programming',
          'SCADA',
          'DCS',
          'Mechatronics',
        ],
        softSkillsOptionsInputList: [
          'Communication',
          'Adaptabilité',
          "Esprit d'équipe",
          'Résolution de problèmes',
          'Gestion du temps',
          'Leadership',
          'Pensée critique',
          'Créativité',
          'Intelligence émotionnelle',
          'Prise de décision',
          'Gestion du stress',
          'Empathie',
          'Persuasion',
          'Flexibilité',
          'Compétences interpersonnelles',
          'Négociation',
          'Gestion des conflits',
          'Écoute active',
          'Capacité à travailler sous pression',
          'Orientation client',
          "Esprit d'initiative",
          'Curiosité',
          'Motivation personnelle',
          'Collaboration',
          'Capacité à apprendre rapidement',
          'Compétences organisationnelles',
          "Esprit d'analyse",
          'Fiabilité',
          'Confiance en soi',
          'Respect des délais',
          'Ponctualité',
          'Éthique professionnelle',
          'Gestion des priorités',
          'Compétences en facilitation',
          'Vision stratégique',
          'Savoir déléguer',
          'Gestion des relations',
          'Résilience',
          'Capacité à motiver les autres',
          'Sens du détail',
          'Gestion de la diversité',
          'Optimisme',
          "Ouverture d'esprit",
          'Sens des responsabilités',
          'Autonomie',
          "Esprit d'innovation",
          'Capacité de persuasion',
          'Esprit orienté solution',
          'Gestion de projets',
        ],

        advantages: AVANTAGE_FIELDS.map((advantage) => advantage.nom),

        joboffer: null,
        formData: {
          publie: false,
        },
        showConfirmationModal: false,
      };
    },

    mounted() {
      // Récupérer les données stockées dans localStorage
      const offerDataFromStorage = localStorage.getItem('offerData');

      // Si des données sont trouvées dans le localStorage
      if (offerDataFromStorage) {
        try {
          // Désérialiser les données stockées
          this.formData = JSON.parse(offerDataFromStorage);

          // Conversion explicite du nombre en texte pour le combobox
          if (this.formData.jours_teletravail !== undefined) {
            this.formData.jours_teletravail = this.mapRemoteWorkNumberToLabel(
              this.formData.jours_teletravail
            );
          }

          // Conversion de savoir_pro en tableau de libellés
          if (Array.isArray(this.formData.savoir_pro)) {
            this.formData.savoir_pro = this.formData.savoir_pro
              .map((skill) => {
                if (typeof skill === 'object' && skill.libelle) {
                  return { libelle: skill.libelle }; // Conserve l'objet
                } else if (typeof skill === 'string') {
                  return { libelle: skill }; // Si déjà transformé en string, reconvertit en objet
                }
                return null;
              })
              .filter(Boolean);
          }
          if (Array.isArray(this.formData.savoir_etre)) {
            this.formData.savoir_etre = this.formData.savoir_etre
              .map((skill) => {
                if (typeof skill === 'object' && skill.libelle) {
                  return { libelle: skill.libelle };
                } else if (typeof skill === 'string') {
                  return { libelle: skill };
                }
                return null;
              })
              .filter(Boolean);
          }
        } catch (e) {
          //console.error(
          //  'Erreur lors de la désérialisation des données depuis localStorage:',
          //  e
          //);
        }
      } else {
        //console.log("Aucune donnée d'offre trouvée dans localStorage");
      }

      // Mettre à jour formData avec les données du profil utilisateur
      const userConnected = this.$store.getters.getUser;
      this.formData.nom_recruteur = userConnected.company;
      this.formData.company_details = userConnected.company_details;
      this.formData.why_join_us = userConnected.rejoindre;
      this.formData.advantages = userConnected.avantages;
      this.formData.recruitment_process = userConnected.processus_recrutement;
      this.formData.logo_img = userConnected.logo_img;
      this.formData.logo_url = userConnected.logo_img;
    },

    methods: {
      gotoPage,
      getFullImageUrl() {
        if (this.formData.logo_img) {
          if (this.formData.logo_img.name) {
            if (typeof this.formData.logo_img.name === 'string') {
              return URL.createObjectURL(this.formData.logo_img);
            }
          }
          return baseUrl + this.formData.logo_img;
        } else {
          return '@/assets/icons/company-logo.png';
        }
      },

      formatAdvantages(advantages) {
        if (!advantages) return [];
        return typeof advantages === 'string'
          ? advantages.split(',')
          : advantages;
      },

      // Convertir le nombre en texte
      mapRemoteWorkNumberToLabel(number) {
        switch (number) {
          case 5:
            return 'Télétravail complet';
          case 0:
            return 'En présentiel';
          case 2:
            return 'Hybride';
          case 3:
            return 'Hybride';
          case 1:
            return 'Télétravail partiel';
          case 4:
            return 'Télétravail partiel';
          default:
            return 'En présentiel';
        }
      },
      // Convertir le texte en nombre
      mapRemoteWorkLabelToNumber(label) {
        switch (label) {
          case 'Télétravail complet':
            return 5;
          case 'Hybride':
            return 2;
          case 'Télétravail partiel':
            return 1;
          case 'En présentiel':
            return 0;
          default:
            return null;
        }
      },

      async improvingWithAI() {
        // Afficher une modale de confirmation avant d'exécuter la fonction
        this.showConfirmationModal = true;
      },
      async confirmImprovingWithAI() {
        // Fermer la modale de confirmation
        this.showConfirmationModal = false;

        // Ouvrir immédiatement la modale et activer le loader
        this.showModal = true;
        this.isLoading = true;

        // Convertir tous les champs de formData en string avec des noms spécifiques
        const dataToSend = {
          titre_poste: String(this.formData.title ?? ''),
          description_recruteur: String(this.formData.descriptif ?? ''),
          localisation: String(this.formData.local ?? ''),
          contract: String(this.formData.contract ?? ''),
          jours_teletravail: String(this.formData.jours_teletravail ?? ''),
          experience: String(this.formData.expérience ?? ''),
          savoir_etre: Array.isArray(this.formData.savoir_etre)
            ? this.formData.savoir_etre.map((item) => String(item)).join(', ')
            : String(this.formData.savoir_etre ?? ''),
          savoir_pro: Array.isArray(this.formData.savoir_pro)
            ? this.formData.savoir_pro.map((item) => String(item)).join(', ')
            : String(this.formData.savoir_pro ?? ''),
          nom_recruteur: String(this.formData.nom_recruteur ?? ''),
          about: String(this.formData.company_details ?? ''),
          why_join_us: String(this.formData.why_join_us ?? ''),
          advantages: String(this.formData.advantages ?? ''),
          recruitment_process: String(this.formData.recruitment_process ?? ''),
        };

        try {
          const response = await axios.post(AIbaseUrlOptimizer, dataToSend, {
            headers: {
              'x-api-key': 'thanks-boss-team-test',
              'Content-Type': 'multipart/form-data',
            },
          });

          // Vérifier si la réponse est un objet et contient la description
          let responseText = '';
          if (typeof response.data === 'object') {
            responseText = response.data.message || 'Aucune amélioration';
          } else {
            responseText = String(response.data); // Convertir en string si nécessaire
          }

          //console.log('this.aiDescription', responseText);
          this.aiDescription = responseText;

          // Désactiver le loader une fois la réponse reçue
          this.isLoading = false;
        } catch (error) {
          //console.error(`Erreur API :`, error.toJSON());
        }
      },
      applyImprovedDescription(text) {
        this.formData.descriptif = text;
        this.showModal = false;
      },

      // Méthode pour publier l'offre
      async publishOffer() {
        // Mettre à jour la valeur de 'publie' à true
        this.formData.publie = true;
        // Appeler la méthode de soumission
        await this.submitForm();
        this.$router.push('/recruteur/offres');
      },

      // Méthode pour sauvegarder le brouillon
      async saveDraft() {
        // Assurez-vous que 'publie' est false pour un brouillon
        this.formData.publie = false;
        // Appeler la méthode de soumission
        await this.submitForm();
        this.$router.push('/recruteur/offres');
      },

      async submitForm() {
        try {
          // Vérifier et transformer savoir_pro
          if (Array.isArray(this.formData.savoir_pro)) {
            this.formData.savoir_pro = this.formData.savoir_pro.map(
              (skill) => ({
                libelle: skill?.libelle ? skill.libelle : skill,
              })
            );
          } else {
            this.formData.savoir_pro = []; // Initialiser à un tableau vide si undefined
          }

          // Vérifier et transformer savoir_etre
          if (Array.isArray(this.formData.savoir_etre)) {
            this.formData.savoir_etre = this.formData.savoir_etre.map(
              (trait) => ({
                libelle: trait?.libelle ? trait.libelle : trait,
              })
            );
          } else {
            this.formData.savoir_etre = [];
          }

          // Mapper et formater `jours_teletravail` si applicable
          if (this.formData.jours_teletravail) {
            this.formData.jours_teletravail = this.mapRemoteWorkLabelToNumber(
              this.formData.jours_teletravail
            );
          }

          // Joindre les tableaux `contract` et `expérience` en chaînes
          if (Array.isArray(this.formData.contract)) {
            this.formData.contract = this.formData.contract.join(', ');
          }
          if (Array.isArray(this.formData.expérience)) {
            this.formData.expérience = this.formData.expérience.join(', ');
          }

          // Stocker les données avant la navigation
          localStorage.setItem('previewOffer', JSON.stringify(this.formData));

          // Envoyer les données via API
          let response;
          if (this.formData.id) {
            // Mettre à jour une offre existante
            response = await modifyJobOffers(this.formData.id, this.formData);
            toaster.showSuccessPopup('Offre mise à jour.');
          } else {
            // Créer une nouvelle offre
            response = await createJobOffers(this.formData);
            toaster.showSuccessPopup('Offre enregistrée.');
          }

          //console.log('this.formData', this.formData);
          //console.log('this.joboffer = response;', response);
          // Mettre à jour l'offre locale après soumission
          this.joboffer = response;
        } catch (error) {
          //console.error("Erreur lors de l'envoi des données :", error);
          toaster.showErrorPopup("Erreur lors de l'enregistrement.");
        }
      },

      //  toggle preview screen
      togglePreview() {
        this.isPreviewOn = !this.isPreviewOn;
      },
    },
  };
</script>

<style scoped>
  /* layout */
  .container,
  .content-wrapper {
    display: flex;
    flex-direction: column;
    gap: 30px;
    height: fit-content;
    padding-bottom: 50px;
  }

  .back-btn-container {
    display: flex;
  }

  .hidden {
    display: none;
  }

  .section-container-top {
    display: flex;
    flex-direction: column;
  }

  .section-header {
    background-color: var(--surface-bg-2);
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    padding-inline: 16px;
    padding-block: 40px;
    display: flex;
    justify-content: space-between;
    width: 100%;
  }

  .section-container {
    background-color: var(--surface-bg-2);
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: 100%;
    border-radius: 5px;
  }
  .section-container .custom-field-background {
    margin-bottom: -40px;
  }

  .section-container .custom-field-background .v-field,
  .section-container .custom-p-background {
    background-color: var(--secondary-2b2);
    min-height: 60px;
    padding-inline: 16px;
    padding-block: 20px;
    border-radius: 5px;
    display: flex;
    align-items: center;
  }

  .btn-magic {
    display: flex;
    justify-content: center;
  }

  .section-footer {
    display: flex;
    justify-content: center;
    gap: 30px;
    width: 100%;
    padding-top: 40px;
  }

  /* wrappers */
  .company-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    gap: 20px;
  }
  .company-logo-wrapper {
    display: flex;
    height: fit-content;
  }
  .title-wrapper {
    display: flex;
    flex-direction: column;
    width: 100%;
  }
  .title-wrapper h2 {
    width: 100%;
  }

  .btn-wrapper {
    width: fit-content;
    height: fit-content;
  }

  /* utilities */
  .logo {
    border: 1px solid rgba(223, 219, 214, 1);
    height: 92px;
    border-radius: 8px;
    object-fit: contain;
  }

  .favicon {
    cursor: pointer;
  }

  .packet-chips {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }
  .avantages {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 10px;
  }

  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Couleur semi-transparente */
    z-index: 999; /* Juste en dessous de la modale */
  }
  .confirmation-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000; /* Assurez-vous que la modale est au-dessus du contenu */
    width: 100%; /* Cela peut être ajusté selon vos besoins */
    max-width: 500px; /* Ajustez selon la taille de votre modale */
    padding: 20px; /* Optionnel, pour ajouter du padding autour de la modale */
    text-align: center;
  }

  @media screen and (min-width: 992px) {
    .section-footer {
      gap: 80px;
    }

    .section-container {
      gap: 32px;
      padding-inline: 91px;
      padding-block: 24px;
    }

    .align-start {
      align-self: baseline;
    }
  }

  @media screen and (min-width: 450px) {
    .section-container {
      gap: 32px;
      padding-inline: 60px;
      padding-block: 24px;
    }
  }

  @media screen and (max-width: 450px) {
    /* layout */
    .section-container {
      gap: 32px;
      padding-inline: 20px;
      padding-block: 12px;
    }

    .section-container .chip-wrapper .chip {
      text-wrap: wrap;
      height: fit-content;
      padding-block: 4px;
    }
  }
</style>
