<template>
  <main class="container">
    <div class="content-wrapper">
      <!-- Fond sombre -->
      <div
        v-if="modalCandidate"
        class="overlay"
        @click="modalCandidate = false"
      ></div>
      <!-- Title -->
      <h1>Les candidatures</h1>

      <div>
        <!-- Back Button -->
        <div class="back-btn-container">
          <div class="btn-wrapper">
            <PrimaryNormalButton
              @click="gotoPage('/recruteur/offres')"
              textContent="Retour"
              btnColor="secondary"
              back
            />
          </div>
        </div>

        <!-- Job Infos + Candidates -->
        <section v-if="jobOffer" class="content-container">
          <div class="job-container">
            <div class="section-header">
              <div class="company-title-wrapper">
                <div class="img-wrapper">
                  <img
                    :src="
                      jobOffer.logo_url
                        ? getImgPath(jobOffer.logo_url)
                        : defaultLogo
                    "
                    class="logo"
                    alt="logo de l'entreprise"
                  />
                </div>
                <div class="title-wrapper">
                  <h2 class="align-start">{{ jobOffer.title }}</h2>
                  <h5 class="align-start">
                    <u>{{ this.user.company }}</u>
                  </h5>
                </div>
              </div>
            </div>

            <div class="section-body">
              <div class="tag">
                <img src="@/assets/icons/localisation-mini.svg" alt="" />
                <p class="fs12">{{ jobOffer.local }}</p>
              </div>

              <div class="tag">
                <img
                  src="@/assets/icons/list-mini.svg"
                  alt="icône de localisation"
                />
                <p class="fs12">{{ jobOffer.contract }}</p>
              </div>

              <div class="tag">
                <img src="@/assets/icons/pc-mini.svg" alt="icône de liste" />
                <p class="fs12">{{ remoteWorkLabel(jobOffer) }}</p>
              </div>

              <div class="tag">
                <img
                  src="@/assets/icons/suitcase-mini.svg"
                  alt="icône d'ordinateur"
                />
                <p class="fs12">{{ jobOffer.expérience }}</p>
              </div>

              <div class="tag">
                <img
                  src="@/assets/icons/calendly.svg"
                  alt="icône de calendrier"
                />
                <p class="fs12">{{ formatDate(jobOffer.created_at) }}</p>
              </div>
            </div>

            <!-- v-select pour le filtre (mobile uniquement) -->
            <div class="mobile-only">
              <v-select
                v-model="toggle"
                :items="statusOptions"
                item-text="label"
                item-value="value"
                label="Filtrer les candidatures"
                dense
                outlined
              />
            </div>

            <!-- Toggle button for offer filters (visible sur desktop) -->
            <div class="desktop-only">
              <v-btn-toggle
                v-model="toggle"
                class="candidature-toggle"
                rounded="0"
                color="transparent"
                group
                mandatory
              >
                <v-btn value="all" :ripple="false">Toutes</v-btn>
                <v-btn value="received" :ripple="false">Non traitées</v-btn>
                <v-btn value="hold" :ripple="false">En attente</v-btn>
                <v-btn value="true" :ripple="false">Acceptées</v-btn>
                <v-btn value="closed" :ripple="false">Refusées</v-btn>
              </v-btn-toggle>
            </div>
          </div>

          <!-- candidates -->
          <section v-if="isDataReady" class="candidates-container">
            <FullCandidateCard
              v-for="(candidate, index) in filteredCandidatesList"
              :key="index"
              :candidate="candidate"
              :jobOffer="jobOffer"
              :recruteur="user"
              @open-entretien-modal="openConfirmationModal"
              @go-to-entretien="goToEntretienPage"
            />
          </section>
        </section>
      </div>
    </div>

    <ConfirmationModalEntretien
      v-if="modalCandidate"
      class="entretien-modal"
      :candidateName="
        modalCandidate.user_first_name + ' ' + modalCandidate.user_last_name
      "
      :candidatePicture="
        getImgPath(modalCandidate.avatar || modalCandidate.user_photo)
      "
      @close="modalCandidate = null"
      @confirm="handleConfirmation"
    />

    <BackToTopArrow class="arrow-container" />
  </main>
</template>

<script>
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import FullCandidateCard from '@/components/cards/candidate-card/FullCandidateCard.vue';
  import getImgPath from '@/utils/imgpath.js';
  import gotoPage from '@/utils/router.js';
  import ConfirmationModalEntretien from '@/components/modal/confirmation/ConfirmationModalEntretien.vue';
  import { acceptCandidateStatus } from '@/services/search.service.js';
  import { toaster } from '@/utils/toast/toast';

  export default {
    name: 'OfferApplications',

    components: {
      FullCandidateCard,
      BackToTopArrow,
      PrimaryNormalButton,
      ConfirmationModalEntretien,
    },

    props: {
      user: {
        type: Object,
        required: true,
      },
    },

    data() {
      return {
        jobOffer: null,
        recruiterJobOffers: [],
        candidatesList: [],
        toggle: 'all',
        favoritesList: [],
        isDataReady: false,
        statusOptions: [
          {
            title: 'Toutes',
            value: 'all',
            tooltip: 'affiche toutes les offres',
          },
          {
            title: 'Non traitées',
            value: 'received',
            tooltip: 'affiche uniquement les offres non traitées',
          },
          {
            title: 'En attente',
            value: 'hold',
            tooltip: 'affiche uniquement en attente',
          },
          {
            title: 'Acceptées',
            value: 'true',
            tooltip: 'affiche uniquement les offres acceptées',
          },
          {
            title: 'Refusées',
            value: 'closed',
            tooltip: 'affiche uniquement les offres Refusées',
          },
        ],
        modalCandidate: null,
        confirmAction: null,
      };
    },

    computed: {
      filteredCandidatesList() {
        if (!this.jobOffer || !this.candidatesList) return [];
        return this.candidatesList.filter((candidate) => {
          switch (this.toggle) {
            case 'all':
              return true;
            case 'received':
              return candidate.user_status === 'En Cours';
            case 'hold':
              return candidate.user_status === "A l'étude";
            case 'true':
              return candidate.user_status === 'Accepté';
            case 'closed':
              return candidate.user_status === 'Refuser';
            default:
              return true;
          }
        });
      },
    },

    methods: {
      getImgPath,
      gotoPage,

      openConfirmationModal(candidate) {
        this.modalCandidate = candidate;
      },

      async handleConfirmation() {
        try {
          const offerId = this.jobOffer.id;
          const candidateId = this.modalCandidate.user_id;

          const firstName = this.modalCandidate.user_first_name;
          const lastName = this.modalCandidate.user_last_name;

          await acceptCandidateStatus(offerId, candidateId);

          this.modalCandidate = null;
          await this.fetchData();

          toaster.showSuccessPopup(
            `Candidature acceptée et proposition d'entretien envoyée à ${firstName} ${lastName}`
          );
        } catch (error) {
          //console.error('Erreur confirmation entretien:', error);
          toaster.showErrorPopup(
            "Erreur lors de la confirmation de l'entretien."
          );
        }
      },

      async fetchData() {
        const offerId = this.$route.params.offerId;

        if (!offerId || !this.user || !this.user.id) return;

        const favoriteIds = new Set();
        this.user.recruteurFavorites.forEach((item) => {
          if (item.apply_user && item.apply_user.length > 0) {
            favoriteIds.add(item.apply_user[0].id);
          }
        });

        try {
          const { jobOffer, candidatesList } = await this.$store.dispatch(
            'fetchJobOffers',
            {
              offerId,
              favoriteIds,
            }
          );

          //console.log('getUser', this.user);
          //console.log('jobOffer', jobOffer);
          this.jobOffer = jobOffer;
          this.candidatesList = candidatesList;
        } catch (error) {
          this.$router.push('/404');
          //console.error('Erreur lors de la récupération des données:', error);
        } finally {
          this.isDataReady = true;
        }
      },

      getTodayDate() {
        const today = new Date();
        return today.toLocaleDateString('fr-FR', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        });
      },

      formatDate(dateString) {
        if (!dateString) return this.getTodayDate();
        const date = new Date(dateString);
        return date.toLocaleDateString('fr-FR', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        });
      },

      remoteWorkLabel(jobOffer) {
        if (!jobOffer || jobOffer.jours_teletravail === undefined) {
          return 'En présentiel'; // Valeur par défaut si jours_teletravail est absent
        }

        switch (jobOffer.jours_teletravail) {
          case 5:
            return 'Télétravail complet';
          case 0:
            return 'En présentiel';
          case 2:
          case 3:
            return 'Hybride';
          case 1:
          case 4:
            return 'Télétravail partiel';
          default:
            return 'En présentiel'; // Valeur par défaut
        }
      },
    },

    async mounted() {
      await this.fetchData();
    },
  };
</script>

<style scoped>
  .mobile-only {
    display: none;
  }

  h1 span {
    cursor: pointer;
    padding: 5px 10px;
    color: var(--gray-100);
  }

  h1 span.active {
    color: var(--text-1);
    border-bottom: 2px solid var(--primary-1);
  }

  /* Layout */
  .container .content-wrapper {
    display: flex;
    flex-direction: column;
    gap: 30px;
    height: fit-content;
    padding-bottom: 50px;
  }

  .content-container {
    margin-top: 30px;
    width: 100%;
  }

  .section-header {
    background-color: var(--surface-bg-2);
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    padding-inline: 16px;
    padding-block: 40px;
    display: flex;
    justify-content: space-between;
    width: 100%;
  }

  .section-body {
    background-color: var(--text-1);
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    justify-content: space-between;
    padding: 8px;
    width: 100%;
  }

  /* Wrappers */
  .company-title-wrapper {
    display: flex;
    gap: 40px;
    align-items: left;
    margin-left: 60px;
  }

  .img-wrapper {
    width: cover;
    height: fit-content;
  }

  .title-wrapper {
    display: flex;
    flex-direction: column;
    align-items: left;
    text-align: left;
  }

  /* Utilities */
  .logo {
    border: 1px solid rgba(223, 219, 214, 1);
    height: 92px;
    border-radius: 8px;
    object-fit: contain;
  }

  .tag {
    background-color: var(--surface-bg-2);
    padding: 2px;
    border-radius: 5px;
    display: flex;
    justify-content: start;
    align-items: center;
    gap: 5px;
    width: 110px;
    height: 35px;
  }

  .fs12 {
    font-size: 10px;
  }

  section.candidates-container {
    width: 100%;
    height: fit-content;
    margin-top: 30px;
    margin-bottom: 30px;
  }

  .blur-content {
    filter: blur(5px);
  }

  .candidature-toggle {
    width: 100%;
    margin-top: 40px;
    display: flex;
  }

  .candidature-toggle button {
    flex: 1;

    height: 48px !important;

    border-bottom: 1px solid var(--surface-bg-4);

    color: var(--text-3);

    background-color: transparent;
  }

  .candidature-toggle button:hover {
    color: var(--text-1) !important;

    background-color: var(--surface-bg-4);
  }

  /* ----------- MODALE ----------- */
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Couleur semi-transparente */
    z-index: 999; /* Juste en dessous de la modale */
  }
  .entretien-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
  }

  @media (max-width: 785px) {
    .section-body {
      flex-wrap: wrap;
      justify-content: center;
      gap: 10px;
      padding: 10px;
      margin-bottom: 10px;
    }
  }

  /* ✅ MOBILE */

  @media screen and (max-width: 480px) and (max-width: 767px) {
    .mobile-only {
      display: block;
    }

    .desktop-only {
      display: none;
    }

    .section-body {
      flex-wrap: wrap;

      justify-content: center;

      gap: 10px;

      padding: 10px;

      margin-bottom: 10px;
    }
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }
</style>
