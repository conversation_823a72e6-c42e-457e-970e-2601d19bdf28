<template>
  <main class="container padding-container">
    <h1 class="">Tableau de bord</h1>

    <GraphSection v-if="user" :user="user" :mobile="mobile" />
    <SearchProfile class="search-profile-background" />
    <RecentCandidates
      v-if="recentCandidates.length > 0"
      :candidates="recentCandidates"
      class="recent-candidates-background"
    />
    <!--  <SubscriptionSection
      :subscriptionList="subscriptions"
      class="subscription-section-background"
    />-->
    <HowDoesItWorkRecruiter class="how-does-it-work-recruiter-background" />
    <LatestNews />
    <BackToTopArrow class="arrow-container d-none" />
    <div v-if="showBackToTopText" class="arrow-text">Retour en haut</div>
  </main>
</template>

<script setup>
  import { useDisplay } from 'vuetify';
  const { mobile } = useDisplay();
</script>

<script>
  import { getUserById } from '@/services/account.service.js';
  import {
    getJobOffers,
    getCandidatesForOffer,
  } from '../../../services/search.service';
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import gotoPage from '@/utils/router';
  import GraphSection from '@/components/views-models/dashboard/GraphSectionRecruiter.vue';
  import SearchProfile from '@/components/views-models/dashboard/SearchProfile.vue';
  import RecentCandidates from '@/components/views-models/dashboard/RecentCandidates.vue';
  import LatestNews from '@/components/views-models/blog/LatestNewsRecruiter.vue';
  import SubscriptionSection from '@/components/views-models/dashboard/SubscriptionSection.vue';
  import HowDoesItWorkRecruiter from '@/components/views-models/home/<USER>';
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import { axiosInstance } from '../../../services/axios';

  export default {
    name: 'Dashboard',
    props: {
      user: {
        type: Object,
        required: true,
      },
    },
    components: {
      PrimaryNormalButton,
      GraphSection,
      SearchProfile,
      RecentCandidates,
      LatestNews,
      SubscriptionSection,
      HowDoesItWorkRecruiter,
      BackToTopArrow,
    },
    data() {
      return {
        recentCandidates: [],
        subscriptions: [],
      };
    },
    methods: {
      gotoPage,
      async fetchRecentCandidates() {
        const jobOffers = await getJobOffers(); // Récupérer les offres d'emploi
        this.recruiterJobOffers = jobOffers.results.filter(
          (offer) => offer.author === this.user.id
        );

        // Initialise un tableau temporaire pour stocker les candidats
        let allCandidates = [];

        // Récupérer les candidatures pour chaque offre
        for (const offer of this.recruiterJobOffers) {
          const candidates = await getCandidatesForOffer(offer.id);
          if (candidates && candidates.postulants) {
            // Ajoutez `offer_id` à chaque candidat pour la correspondance
            const candidatesWithOfferId = candidates.postulants.map(
              (candidate) => ({
                ...candidate,
                offer_id: offer.id, // Ajout de l'identifiant de l'offre
                offer_title: offer.title, // Ajout du titre de l'offre
              })
            );
            allCandidates = [...allCandidates, ...candidatesWithOfferId];
          }
        }

        // Récupérer les informations complètes pour chaque candidat
        const candidatesWithDetails = await Promise.all(
          allCandidates.slice(0, 3).map(async (candidate) => {
            const fullCandidate = await getUserById(candidate.user_id);
            return { ...candidate, ...fullCandidate }; // Combine les données
          })
        );

        this.recentCandidates = candidatesWithDetails;
      },
      async getProducts() {
        try {
          const { data } = await axiosInstance.get(`/facture/type_produit`);
          this.subscriptions = data.results;
        } catch (error) {
          //console.error(
          //  'Erreur lors de la récupération des abonnements:',
          //  error
          //);
        }
      },
    },
    mounted() {
      this.fetchRecentCandidates();
      this.getProducts();
    },
  };
</script>

<style scoped>
  main {
    height: fit-content;
    width: 100%;
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }
</style>
