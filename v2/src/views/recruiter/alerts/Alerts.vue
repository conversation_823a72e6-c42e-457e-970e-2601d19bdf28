<template>

    <main class="container padding-container">

        <div class="content-wrapper">
            <h1>Mes alertes</h1>

            <!-- alert panel creation -->
            <div v-if="alertPanelOn" class="alert-panel-wrapper">
                <Alert :alertId="alertId" @close-alert-panel="toggleAlertPanel" :alertIndex="alertIndex"/>
            </div>

            <!-- alerts panel browsing-->
            <div v-else>
                <div class="btn-container">
                    <PrimaryNormalButton 
                        textContent="Ajouter une alerte" 
                        btnColor="secondary" 
                        @click="toggleAlertPanel"
                        add 
                    />
                </div>

                <div class="content-container" :class="{ 'blur-content': confirmationModalIsOpened }">

                    <!-- filter panel -->
                    <div class="left-content border-radius-15">
                        <CustomAccordion 
                            field="" 
                            title="Trier par nom" 
                            :fields="['A - Z', 'Z - A']" 
                            @checkbox-state="sortByName" 
                            :chips="false" 
                        />
                        <CustomAccordion 
                            field="" 
                            title="Trier par date" 
                            :fields="['Plus récent', 'Plus ancien']" 
                            @checkbox-state="sortByDate" 
                        />
                    </div>

                    <div class="right-content">

                        <AlertCard 
                            v-for="(alert, index) in alertList"
                            :alert="alert" 
                            @toggle-alert-panel="toggleAlertPanel"
                            :alertIndex="index"
                        />
                    </div>

                </div>
            </div>
        
            <ConfirmationModal 
                v-if="confirmationModalIsOpened" 
                class="modal-container"
                title="Suppression" 
                description="Êtes-vous sûr de vouloir supprimer ce job de votre liste de favoris ?" 
                @confirm="deleteAlert"
                @close="confirmationModalIsOpened = false" 
            />
        </div>

    </main>

    <BackToTopArrow />
      
</template>
  
<script>
//import { mapActions } from 'vuex';
//import { removeFavoriteJob } from '/src/services/favoriteJob.service.js';
import AlertCard from '@/components/cards/AlertCard.vue';
import CustomAccordion from '@/components/buttons/CustomAccordion.vue';
import ConfirmationModal from '@/components/modal/confirmation/ConfirmationModal.vue';
import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
import Alert from '@/components/modal/alert/recruiter/Alert.vue';
import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';

export default {
    name: 'Alerts',

    components: {
        AlertCard,
        CustomAccordion,
        ConfirmationModal,
        PrimaryNormalButton,
        Alert,
        BackToTopArrow
    },

    props: {
        user: Object,
        required: false,
        default: () => {}
    },

    data() {
        return {
            confirmationModalIsOpened: false,   //  toggle for the modal on / off
            selectedFavoriteId: null,           //  contain favorite id for deletion
            alertPanelOn: false,                //  if an alert is in the stagging area for creation
            alertId: null,                      //  current opened alert
            alertIndex: null,                   //  current index of opened alert
        }
    },

    computed: {
        alertList() {
            return this.user.alerte_profils;
        }
    },

    /*mounted() {
        //console.log(this.alertList);
    },*/

    methods: {
        //...mapActions(['handleDeleteFavoris']),

        //  toggle alert panel visibility and refresh alert list
        toggleAlertPanel(alertId, alertIndex) {
            if (alertId) {
                this.alertId = alertId;
                this.alertIndex = alertIndex;
            } else {
                this.alertId = null;
                this.alertIndex = null;
            }
            this.alertPanelOn = !this.alertPanelOn;
        },

        //  open delete favorite confirmation modal
        openConfirmationModal(id) {
            this.confirmationModalIsOpened = true;
            this.selectedFavoriteId = id;
        },

        //  delete favorite from database and store
        async deleteAlert() {
            try {
                //await removeFavoriteJob(this.selectedFavoriteId);
                //this.handleDeleteFavoris(this.selectedFavoriteId);
            } catch (error) {
                //console.log(error);
            } finally {
                this.confirmationModalIsOpened = false;
            }
        },

        //  sort by date
        sortBy(el, values, test) {
            //console.log("sort by", el, values, test);
            let selectedFilter = '';

            // find the value of the selected filter
            for (const [key, value] of Object.entries(values)) {
                if (value === true) {
                    selectedFilter = key;
                    break;
                }
            }

            // filter by selected filter
            if (selectedFilter === 'Plus récent') {
                ////console.log('Plus récent selected');
                //console.log('HUUU')
                this.favoritesList.sort((a, b) => {
                    return new Date(b.job_offer.created_at) - new Date(a.job_offer.created_at);
                });
            } else if (selectedFilter === 'Plus ancien') {
                //console.log('DDD')

                ////console.log('Plus ancien selected');
                this.favoritesList.sort((a, b) => {
                    return new Date(a.job_offer.created_at) - new Date(b.job_offer.created_at);
                });
            }
        },

        getSelectedFilter(values) {
        let selectedFilter = '';

                // Parcours l'objet pour trouver la valeur qui est `true`
                for (const [key, value] of Object.entries(values)) {
                    //console.log(value)
                    if (value === true) {
                        selectedFilter = key;
                        break;
                    }
                }

                return selectedFilter;
            },

        sortByDate(e,values) {
            const order = this.getSelectedFilter(values);

        if (order === 'Plus récent') {
            this.alertList.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        } else if (order === 'Plus ancien') {
            this.alertList.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
        }
          },

        sortByName(e,values) {
            const order = this.getSelectedFilter(values);
            if (order === 'A - Z') {
                this.alertList.sort((a, b) => a.nom.localeCompare(b.nom));
            } else if (order === 'Z - A') {
                this.alertList.sort((a, b) => b.nom.localeCompare(a.nom));
            }
        },
    },
};
</script>
  
<style scoped>
.modal-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000; /* Assurez-vous que la modale est au-dessus du contenu */
  width: 100%; /* Cela peut être ajusté selon vos besoins */
  max-width: 500px; /* Ajustez selon la taille de votre modale */
  padding: 20px; /* Optionnel, pour ajouter du padding autour de la modale */
}

.btn-container {
    display: flex;
    justify-content: end;
    margin-bottom: 20px;
}

.content-container {
    display: flex;
    gap: 27px;
}

.left-content {
    height: fit-content;
    max-width: 231px;
    margin-top: 90px;
    background-color: var(--surface-bg-2);
    padding: 16px;
    border-radius: 15px;
}

.left-content .container {
    margin-top: 0;
    margin-bottom: 10px;
}
main {
    width: 100%;
    height: fit-content;
    margin-top: 50px;
    margin-bottom: 30px;
}
.right-content {
    width: 80%;
    display: grid;
    grid-template-columns: repeat(auto-fill, 258px);  
    grid-gap: 27px;
}

@media screen and (max-width: 768px) {
    main {
        justify-items: center;
    }

    h1 {
        justify-content: center;
        display: flex;
    }
    .btn-container {
        justify-content: center;
        margin-top: 30px;
    }

    .content-container {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .right-content {
        justify-content: center;
    }

    .left-content {
        display: flex;
        width: 100%;
        max-width: 100%;
        gap: 20px;
    }

    .left-content .container {
        width: 50%;
    }
}


.blur-content {
    filter: blur(5px);
}


/* desktop */
@media screen and (min-width: 992px) {
    .content-wrapper{
        width: 100%;
    }
}

/* large desktop */
@media screen and (min-width: 1800px) {
    .content-wrapper{
        width: 80%;
        margin: 0 auto;
    }
}

/* x-large desktop */
@media screen and (min-width: 2400px) {
    .content-wrapper{
        width: 70%;
        margin: 0 auto;
    }
}
</style>
  