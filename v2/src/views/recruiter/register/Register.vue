<template>
  <div class="container">
    <div class="container-content">
      <section class="illustration-section">
        <figure class="illustration">
          <!-- stepper 

        <v-btn
          @click="$router.back()"
          size="large"
          color="black"
          class="btn-return"
          prepend-icon="@/assets/ArrowBack.svg"
        >
          <template v-slot:prepend>
            <img src="@/assets/ArrowBack.svg" />
          </template>
          <span class="return-text">Retour</span>
        </v-btn>-->
          <img
            src="@/assets/candidate_register_illustration.webp"
            alt="loupe"
          />
        </figure>
      </section>

      <section class="form-container-register">
        <main class="main-container">
          <!-- header -->
          <div class="form-container-header">
            <h1>Inscription</h1>
            <p>Recruteur</p>
          </div>

          <!-- stepper -->
          <div ref="stepperContainer">
            <v-stepper
              v-model="page"
              :items="stepperItems"
              @keydown.enter="validate"
              class="stepper-container-email-register"
              next-text="Suivant"
              prev-text="Retour"
              ref="stepper"
              hide-actions
            >
              <!-- STEPS -->
              <template v-slot:item.1>
                <v-card class="padding-inline-40">
                  <v-form ref="firstForm" @submit.prevent>
                    <div class="first-step-container">
                      <div @click="signUpWithGoogle">
                        <GoogleRegister />
                      </div>
                      <p class="google-password-hint">
                        Google génère automatiquement un mot de passe sécurisé
                        pour vous.
                      </p>
                      <p>OU</p>
                      <v-btn
                        :disabled="page === stepperItems.length"
                        @click="validate"
                        size="large"
                        variant="flat"
                        theme="dark"
                      >
                        Créer un compte
                      </v-btn>

                      <div>
                        <v-checkbox
                          v-model="newslettersIsChecked"
                          color="var(--yellow-100)"
                          label="Vous souhaitez vous inscrire et recevoir les newsletters."
                          hide-details
                        ></v-checkbox>
                        <v-checkbox
                          v-model="termsIsChecked"
                          color="var(--yellow-100)"
                          hide-details
                          :rules="[(v) => !!v || 'Vous devez accepter les CGU']"
                        >
                          <template #label>
                            <p class="text-regex">
                              Vous avez lu et acceptez&nbsp;<a
                                href="https://thanks-boss.com/politique-confidentialite"
                                target="_blank"
                                >la politique de confidentialité&nbsp;</a
                              >
                              et les&nbsp;<a
                                href="https://thanks-boss.com/cgu"
                                target="_blank"
                                >CGU</a
                              >.
                            </p>
                          </template>
                        </v-checkbox>
                      </div>
                    </div>
                  </v-form>
                </v-card>
              </template>
              <template v-slot:item.2>
                <v-card class="padding-inline-40">
                  <h4 class="center">Informations sur votre entreprise</h4>
                  <v-form ref="secondForm" @submit.prevent>
                    <label for="Entreprise">
                      <h5>Nom de l'entreprise</h5>
                      <v-text-field
                        v-model="formData.company"
                        type="text"
                        :rules="notEmptyRules"
                      />
                    </label>

                    <label for="adress">
                      <h5>Adresse du siège social</h5>
                      <v-text-field
                        v-model="formData.adress"
                        type="text"
                        :rules="notEmptyRules"
                      />
                    </label>
                    <label for="siret">
                      <h5>Numéro de SIRET</h5>
                      <v-text-field
                        v-model="formData.siret"
                        type="text"
                        :rules="noSpecialCharRules"
                      />
                    </label>
                    <h4 class="center">
                      Informations complémentaires sur vous
                    </h4>
                    <label for="nom">
                      <h5>Nom</h5>
                      <v-text-field
                        v-model="formData.last_name"
                        placeholder="Votre nom"
                        :rules="notEmptyRules"
                      />
                    </label>

                    <label for="prenom">
                      <h5>Prénom</h5>
                      <v-text-field
                        v-model="formData.first_name"
                        placeholder="Votre prénom"
                        :rules="notEmptyRules"
                      />
                    </label>

                    <label for="metier">
                      <h5>Métier</h5>
                      <v-text-field
                        v-model="formData.metier"
                        placeholder="Votre métier"
                        :rules="notEmptyRules"
                      />
                    </label>
                    <label for="email">
                      <h5>Localisation</h5>
                      <LocationInput
                        class="custom-location-input"
                        textContent="Votre ville"
                        @city-and-postal-code="getCityAndPostalCodeValue"
                        :paramLocation="formData.ville"
                      />
                    </label>
                    <label for="email">
                      <h5>Email</h5>
                      <v-text-field
                        type="email"
                        :rules="[...emailRules, ...notEmptyRules]"
                        v-model="formData.email"
                        placeholder="Votre mail"
                      />
                    </label>
                    <label for="password">
                      <div>
                        <h5 class="mdp">
                          Mot de passe
                          <p class="password-hint">
                            (Le mot de passe doit contenir au moins 8
                            caractères, incluant une majuscule, une minuscule,
                            un chiffre et un caractère spécial.)
                          </p>
                        </h5>
                      </div>
                      <v-text-field
                        autocomplete="new-password"
                        v-model="formData.password"
                        :rules="[...passwordRules, ...notEmptyRules]"
                        placeholder="***********"
                        :type="!showPassword ? 'password' : 'text'"
                        :append-icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
                        @click:append="showPassword = !showPassword"
                      />
                    </label>

                    <!-- password verif -->
                    <label for="password2">
                      <h5>Confirmer le mot de passe</h5>
                      <v-text-field
                        autocomplete="new-password"
                        v-model="formData.password2"
                        :rules="[...password2Rules, ...notEmptyRules]"
                        placeholder="***********"
                        :type="!isPassword2Hide ? 'password' : 'text'"
                        :append-icon="
                          isPassword2Hide ? 'mdi-eye-off' : 'mdi-eye'
                        "
                        @click:append="isPassword2Hide = !isPassword2Hide"
                    /></label>
                  </v-form>

                  <div class="btn-container">
                    <PrimaryNormalButton
                      v-if="page !== 1"
                      @click="submitForm"
                      textContent="Je crée mon compte"
                    />
                    <v-btn
                      v-if="page !== 1"
                      class="height"
                      @click="stepper.prev()"
                      size="large"
                      variant="flat"
                      theme="dark"
                    >
                      Retour
                    </v-btn>
                  </div>
                </v-card>
              </template>
            </v-stepper>
          </div>

          <!-- btn already registered -->
          <div
            v-if="page !== stepperItems.length"
            class="already-registered-container"
          >
            <p>Vous avez déjà un compte chez Thanks-Boss ?</p>
            <p
              class="text-underline cursor-pointer"
              @click="gotoPage('/connexion')"
            >
              Connectez-vous ici
            </p>
          </div>
          <div class="stepper-footer">
            <p>
              Tu es candidat ?<br />
              Viens t'inscrire chez Thanks-Boss
            </p>
            <div @click="gotoPage('/inscription')">
              <PrimaryNormalButton textContent="Espace candidat" />
            </div>
          </div>
        </main>
      </section>
    </div>
  </div>
</template>
<script setup>
  import { ref } from 'vue';
  const stepper = ref(null);
  const showPassword = ref(false);
</script>

<script>
  import LocationInput from '@/components/inputs/LocationInput.vue';
  import GoogleRegister from '@/components/buttons/GoogleRegister.vue';
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import { register } from '@/services/account.service';
  import gotoPage from '@/utils/router.js';
  import { toaster } from '@/utils/toast/toast';
  import {
    validateEmail,
    validateNotEmpty,
    validatePassword,
    validatePassword2,
    validateMobile,
  } from '@/utils/validationRules';
  import { decodeCredential, googleOneTap } from 'vue3-google-login';
  import { login } from '../../../services/account.service';

  export default {
    name: 'RegisterRecruiterPage',
    components: {
      PrimaryNormalButton,
      LocationInput,
    },
    data() {
      return {
        formData: {
          type_user: 'recruiter',
          googleSub: null,
          password: '',
          password2: '',
          siret: '',
          company: '',
          adress: '',
          numberPhone: '',
          first_name: '',
          last_name: '',
          metier: '',
          email: '',
          ville: '',
        },
        isPassword1Hide: false,
        isPassword2Hide: false,
        newslettersIsChecked: false,
        termsIsChecked: false,
        page: null,
        stepperItems: ['1', '2'],
        emailRules: [(v) => validateEmail(v) || true],
        passwordRules: [(v) => validatePassword(v) || true],
        password2Rules: [
          (v) => validatePassword2(v, this.formData.password) || true,
        ],
        notEmptyRules: [(v) => validateNotEmpty(v) || true],
        mobileRules: [(v) => validateMobile(v) || true],
      };
    },
    methods: {
      gotoPage,
      getCityAndPostalCodeValue(cityAndPostalCode) {
        if (cityAndPostalCode) {
          this.formData['ville'] = cityAndPostalCode[0];
          this.formData['code_postal'] = cityAndPostalCode[1];
        }
      },
      // form validators
      async validate() {
        const getStepper = this.$refs.stepper;
        let valid = false;

        try {
          switch (this.page) {
            case 1:
              if (this.$refs.firstForm) {
                // //console.log('firstForm', this.formData);
                this.formData.password = '';
                this.formData.password2 = '';
                valid = (await this.$refs.firstForm.validate()).valid;
                break;
              }
            case 2:
              if (this.$refs.secondForm) {
                // //console.log('secondForm', this.formData);
                valid = (await this.$refs.secondForm.validate()).valid;
              }
              break;
            default:
              break;
          }
        } catch (error) {
          //console.log(error);
        }

        // Vérifiez si le formulaire est valide et n'est pas la dernière page
        if (valid && this.page < getStepper.items.length) {
          getStepper.next();
          const stepperContainer = this.$refs.stepperContainer;
          stepperContainer.scrollTop = 0;
        } else if (valid && this.page === getStepper.items.length) {
          return true; // Dernière page
        } else {
          this.$nextTick(() => {
            const firstInvalidField = this.$el.querySelector('.v-input--error');
            if (firstInvalidField) {
              firstInvalidField.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
              });
            }
          });
          return false;
        }
      },
      async submitForm() {
        try {
          const valid = await this.validate();
          if (!valid) return;

          if (!this.termsIsChecked) {
            toaster.showErrorPopup(
              "Veuillez accepter la politique de confidentialité et les conditions d'utilisation."
            );
            return;
          }

          this.isLoading = true;
          this.formData.email = this.formData.email.toLowerCase();
          await register({
            email: this.formData.email,
            password: this.formData.password,
            password2: this.formData.password2,
            type_user: 'recruiter',
            company: this.formData.company || '',
            adress: this.formData.adress || '',
            siret: this.formData.siret || '',
            first_name: this.formData.first_name || '',
            last_name: this.formData.last_name || '',
            metier: this.formData.metier || '',
            numberPhone: this.formData.numberPhone,
            ville: this.formData.ville,
          });
          await login({
            email: this.formData.email,
            password: this.formData.password,
          });
          this.$router.push('/profil');
        } catch (error) {
          //console.error("Erreur de l'API :", error.response);
          const errorMessage =
            error.message || 'Erreur à la création de votre compte.';
          if (
            errorMessage.includes(
              'Un objet account avec ce champ email existe déjà.'
            )
          ) {
            toaster.showErrorPopup(
              'Un compte avec cette adresse mail existe déjà.'
            );
          } else {
            toaster.showErrorPopup(errorMessage);
          }
        } finally {
          this.isLoading = false;
        }
      },
      async signUpWithGoogle() {
        try {
          if (!this.termsIsChecked) {
            toaster.showErrorPopup(
              "Veuillez accepter la politique de confidentialité et les conditions d'utilisation."
            );
            return;
          }
          this.isLoading = true;
          const response = await googleOneTap({
            clientId:
              '************-3cgb1ucg8oon8ncdemjpveuqkv7n0i61.apps.googleusercontent.com',
            context: 'signup',
          });

          const registrationData = decodeCredential(response.credential);
          this.formData.email = registrationData.email;
          this.formData.password = registrationData.sub;
          this.formData.password2 = registrationData.sub;

          this.formData.first_name = registrationData.given_name || '';
          this.formData.last_name = registrationData.family_name || '';
          this.formData.metier = '';

          await register({
            email: this.formData.email,
            password: this.formData.password,
            password2: this.formData.password2,
            googleSub: registrationData.sub,
            type_user: 'recruiter',
            first_name: this.formData.first_name,
            last_name: this.formData.last_name,
            metier: this.formData.metier,
          });
          await login({
            email: this.formData.email,
            password: this.formData.password,
          });
          this.$router.push('/profil/edition');
        } catch (error) {
          //console.error('Google Register Failed:', error);
          toaster.showErrorPopup(error.message);
          this.errorDetails = 'Google Register Failed';
        } finally {
          this.isLoading = false;
        }
      },
    },
  };
</script>

<style scoped>
  :deep(.custom-location-input .v-field__overlay) {
    background-color: transparent !important; /* Supprime l'overlay */
  }
  :deep(.custom-location-input .v-input__control) {
    background-color: var(--surface-bg-2) !important;
  }
  .google-password-hint {
    text-align: center;
    font-size: 14px;
    color: #666666bd;
  }
  .mdp {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
  }
  h5 {
    margin-bottom: 10px;
  }
  .password-hint {
    margin-left: 1.2rem;
    font-size: 11px;
    color: #6b6b6b;
    margin-top: 0;
    width: 65%;
  }
  section {
    width: 100%;
  }

  label {
    font-size: 16px;
  }

  .container {
    width: 100vw;
    height: calc(100vh - 80px);
    display: flex;
    justify-content: center;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
  }
  .container-content {
    width: 100%;
    height: 100%;
    max-width: 1440px;
    display: flex;
    justify-content: space-between;
  }

  .first-step-container {
    display: flex;
    width: 100%;
    height: 100%;
    gap: 20px;
    margin-bottom: 30px;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .text-regex {
    white-space: normal;
    color: var(--text-1);
    font-weight: 500;
    font-size: 1rem;
  }

  .illustration-section {
    /* height: 100%; */
    width: 55%;
  }

  .illustration {
    height: 100%;
    width: 100%;
  }

  .illustration img {
    height: 100%;
    width: 100%;
    object-fit: cover;
  }

  .form-container-register {
    width: 45%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
  }

  .form-container-register h1 {
    margin-top: 24px;
  }

  .main-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
    height: calc(100% - 61px);
  }

  .form-container-header {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 50px;
  }
  .form-name-inputs-column {
    display: flex;
    flex-direction: column;
    width: 50%;
  }

  .form-name-inputs {
    display: flex;
    gap: 20px;
  }

  .already-registered-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.3vh;
    margin-bottom: 13px;
  }

  .stepper-footer {
    height: 70px;
    padding: 20px 0;

    width: 100%;
    background-color: #26282bd6;
    color: var(--surface-bg-2);
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    position: relative;
  }

  .stepper-footer p {
    width: 50%;
  }

  .padding-inline-40 {
    padding-inline: 40px;
  }
  .btn-container {
    display: flex;
    width: 100%;
    margin: 0 auto;
    justify-content: space-between;
    margin-bottom: 24px;
  }
  .btn-return {
    position: absolute;
    margin-top: 42px;
    margin-left: 42px;
  }

  @media (max-width: 768px) {
    .password-hint {
      margin-left: 0rem;
      width: 100%;
    }
    .mdp {
      flex-direction: column;
    }
    .form-container-register {
      width: 100%;
    }

    .illustration-section {
      width: 0%;
    }
    .btn-return {
      margin-top: 6px;
      margin-left: 8px;
    }
    .v-btn_predent {
      margin-inline: 0;
    }
    .v-btn_predent img {
      margin-left: 11px;
    }
    .illustration img {
      margin-left: 14px;
    }
    .return-text {
      display: none;
    }
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .container-content {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .container-content {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
  }
</style>

<style>
  .form-container-register .location-container input {
    background-color: inherit;
    padding: inherit;
    margin-top: inherit;
  }

  .form-container-register input {
    background-color: var(--surface-bg-2);
    padding: 5px;
  }
  .form-container-register .v-card {
    background-color: transparent;
  }
  .form-container-register .v-stepper-header {
    display: none;
  }
  .form-container-register .v-stepper {
    background-color: transparent;
    box-shadow: none;
  }
  .form-container-register .v-window {
    overflow-y: auto;
    margin: 0;
  }
  .form-container-register .v-progress-linear__background {
    opacity: unset;
  }

  .form-container-register .v-checkbox .v-selection-control {
    min-height: inherit;
  }

  .form-container-register .v-field__input {
    min-height: max(
      (40px),
      1rem + var(--v-field-input-padding-top) +
        var(--v-field-input-padding-bottom)
    );
  }
</style>
