<template>
  <main class="container padding-container">
    <div class="second-container">
      <!-- Button to navigate back to the offer page-->
      <div class="back-btn-container">
        <div class="btn-wrapper">
          <PrimaryNormalButton
            @click="
              gotoPage(
                `/recruteur/offre/${this.$route.params.offerId}/candidatures`
              )
            "
            textContent="Retour"
            btnColor="secondary"
            back
          />
        </div>
      </div>

      <OffersBasicsInfo :offerId="offerId" />
      <div v-if="candidate" class="btns-candidate-actions">
        <CandidatureButtons
          v-if="candidate && jobOffer"
          :jobOffer="jobOffer"
          :candidate="candidate"
          :offerId="offerId"
          :onUpdateStatus="updateCandidateStatus"
        />
        <div class="cv-contact-btns">
          <PrimaryRoundedButton
            email
            textContent="Contacter le candidat"
            :href="`mailto:${this.candidate.email}`"
            target="_blank"
          />
          <PrimaryRoundedButton
            textContent="Télécharger le CV"
            @click="handleDownloadCvTemplate"
          />
        </div>
      </div>
      <!-- Composant CV custom thanks-boss -->
      <CvTemplatePrintVersion
        v-if="candidate"
        ref="CvTemplate"
        :user="candidate"
        :isFriend="isFriend"
      />
      <!-- <CvTemplateCandidate v-if="candidate" :user="candidate" /> -->
      <MyContactLinks v-if="contactLinks.length > 0" :links="contactLinks" />
      <CandidateMotivations
        v-if="candidate"
        :candidate="candidate"
        :jobofferId="offerId"
      />
      <section class="avis-and-pdf-container">
        <CvPreview
          class="cv-preview"
          v-if="candidate && candidate.default_cv && candidate.default_cv.file"
          :pdfUrl="getImgPath(candidate.default_cv.file)"
        />
        <Myrecommendations
          class="my-recommendations"
          v-if="candidate"
          :user="candidate"
        />
      </section>
    </div>

    <!-- Button to scroll back to the top of the page -->
    <BackToTopArrow />
  </main>
</template>

<script>
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import CvPreview from '@/components/views-models/profil/CvPreview.vue';
  import CvTemplateCandidate from '@/components/views-models/profil/CvTemplateCandidate.vue';
  import CvTemplatePrintVersion from '@/components/views-models/profil/CvTemplatePrintVersion.vue';
  import MyContactLinks from '@/components/views-models/profil/MyContactLinks.vue';
  import Myrecommendations from '@/components/views-models/profil/MyRecomendations.vue';
  import CandidatureButtons from '@/components/views-models/recruiter/candidature/CandidatureButtons.vue';
  import OffersBasicsInfo from '@/components/views-models/recruiter/candidature/OffersBasicsInfo.vue';
  import {
    acceptCandidateStatus,
    holdCandidateStatus,
    refuseCandidateStatus,
  } from '@/services/search.service.js';
  import getImgPath from '@/utils/imgpath.js';
  import gotoPage from '@/utils/router.js';
  import { downloadCvTemplate } from '@/utils/userUtilities.js';
  import { axiosInstance } from '@/services/axios.js';

  export default {
    name: 'CandidaturePage',

    components: {
      PrimaryRoundedButton,
      PrimaryNormalButton,
      CandidatureButtons,
      BackToTopArrow,
      OffersBasicsInfo,
      // CvTemplateCandidate,
      CvTemplatePrintVersion,
      CvPreview,
      MyContactLinks,
      Myrecommendations,
    },

    data() {
      return {
        offerId: this.$route.params.offerId,
        candidateId: this.$route.params.candidateId,
        candidate: null,
        jobOffer: null,
        contactLinks: [],
        isFriend: false,
      };
    },

    methods: {
      getImgPath,
      downloadCvTemplate,
      gotoPage,
      async handleDownloadCvTemplate() {
        const cvComponent = this.$refs.CvTemplate;
        if (!cvComponent) {
          //console.error('Référence du CV introuvable !');
          return;
        }

        const cvElement = cvComponent.$refs.cvContainer;
        if (!cvElement) {
          //console.error("L'élément du CV est introuvable !");
          return;
        }

        // Appliquer la classe temporaire pour le format PDF
        cvElement.classList.add('pdf-mode');

        try {
          // Attendre la fin de la génération du PDF
          await downloadCvTemplate(this.candidate, cvElement);
        } catch (err) {
          //console.error('Erreur lors de la génération du PDF:', err);
        } finally {
          // Supprimer la classe temporaire
          cvElement.classList.remove('pdf-mode');
        }
      },
      buildContactLinks(user) {
        return [
          user.linkedin,
          user.instagram,
          user.facebook,
          user.site_url,
          user.autre_url,
          user.porfolio_url,
          user.tiktok,
        ].filter((link) => link && link.trim() !== '');
      },

      async fetchJobOfferAndCandidate() {
        try {
          const { offerId, candidateId } = this.$route.params;
          const { jobOffer, candidate } = await this.$store.dispatch(
            'fetchCandidatureData',
            {
              offerId,
              candidateId,
            }
          );
          this.jobOffer = jobOffer;
          this.candidate = candidate;
          this.contactLinks = this.buildContactLinks(this.candidate);
          await this.checkIfCandidateIsFriend();
          //console.log({ jobOffer, candidate });
        } catch (error) {
          this.$router.push('/404');
          //console.error(
          //  "Erreur lors de la récupération de l'offre et du candidat :",
          //  error
          //);
        }
      },

      /**
       * Vérifie si le candidat est un ami de l'utilisateur connecté
       */
      async checkIfCandidateIsFriend() {
        try {
          // Utiliser directement l'API /friends
          const response = await axiosInstance.get('/friends');
          const friends = response.data.friends;

          if (Array.isArray(friends)) {
            // Vérifier si l'utilisateur est déjà ami
            this.isFriend = friends.some((friend) => {
              // Convertir les IDs en nombres pour la comparaison
              const friendId = Number(friend.id);
              const candidateId = Number(this.candidate.id);

              return friendId === candidateId;
            });
          } else {
            this.isFriend = false;
          }
        } catch (error) {
          this.isFriend = false;
        }
      },

      async updateCandidateStatus(status) {
        try {
          const { offerId, candidateId } = this;

          if (status === 'Accepté') {
            await acceptCandidateStatus(offerId, candidateId, 'accepted');
            this.candidate.status = 'Candidature acceptée';
          } else if (status === 'Refuser') {
            await refuseCandidateStatus(offerId, candidateId, 'rejected');
            this.candidate.status = 'Candidature rejetée';
          } else if (status === 'pending') {
            await holdCandidateStatus(offerId, candidateId, 'pending');
            this.candidate.status = "A l'étude";
          }
        } catch (error) {
          //console.error('Erreur lors de la mise à jour du statut :', error);
        }
      },
    },

    async mounted() {
      await this.fetchJobOfferAndCandidate();
    },
  };
</script>

<style scoped>
  .hidden {
    display: none;
  }
  .v-field__input {
    height: 2em !important;
  }
  section.avis-and-pdf-container {
    width: 100%;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    gap: 30px;
  }
  .cv-preview,
  .my-recommendations {
    width: 50%;
  }
  /* @media screen and (min-width: 800px) {
    section.avis-and-pdf-container {
      flex-direction: row;
    }
  }

  @media screen and (min-width: 992px) {
    .v-field__input {
      height: 0 !important;
    }
  } */

  /* layout */
  .container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
    padding-bottom: 50px;
    margin-bottom: 4rem;
  }

  .second-container {
    width: 100%;
    max-width: 1200px;
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .back-btn-container {
    align-self: flex-start;
    margin: 1.2rem 0rem;
  }
  .btns-candidate-actions {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 10px;
  }
  .cv-contact-btns {
    display: flex;
    gap: 10px;
  }
</style>
