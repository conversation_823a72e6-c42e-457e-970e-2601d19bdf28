<template>
  <main class="container padding-container">
    <div class="content-wrapper">
      <h1>Mon profil</h1>
      <div class="preview-progress">
        <div class="progress-wrapper">
          <ProgressProfileBar :user="user" />
          <a
            v-if="notifications > 0 && progressBar < 100"
            href="/profil/edition"
            class="incomplete-infos-notification"
          >
            {{
              'Vous avez ' +
              notifications +
              ' informations incomplètes dans votre profil !'
            }}
          </a>
        </div>
      </div>

      <p class="head-message">
        Voici un aperçu de votre profil tel qu'il apparaît aux autres membres.
        Complétez votre profil pour maximiser vos opportunités et enrichir votre
        réseau !
      </p>

      <RecrutierPresentationCard :user="user" :owner="true" />

      <CvTemplateCandidate
        ref="CvTemplate"
        :user="user"
        :owner="true"
        :isFriend="true"
        @download-cv="handleDownloadCvTemplate"
      />
      <MyContactLinks v-if="contactLinks.length > 0" :links="contactLinks" />
      <!-- Preview of the candidate's cv -->
      <section class="avis-and-pdf-container">
        <CvPreview
          class="cv-preview"
          v-if="user.default_cv && user.default_cv.file"
          :pdfUrl="getImgPath(user.default_cv.file)"
          :candidate="user"
        />
        <MyRecommendations class="my-recommendations" :user="user" />
      </section>
      <!-- <ReviewEntreprise /> -->
      <BackToTopArrow />
    </div>
  </main>
</template>

<script>
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import RecrutierPresentationCard from '@/components/cards/recruiter/RecrutierPresentationCard.vue';
  import CvPreview from '@/components/views-models/profil/CvPreview.vue';
  import CvTemplateCandidate from '@/components/views-models/profil/CvTemplateCandidate.vue';
  import MyContactLinks from '@/components/views-models/profil/MyContactLinks.vue';
  import MyRecommendations from '@/components/views-models/profil/MyRecomendations.vue';
  import ProgressProfileBar from '@/components/views-models/profil/ProgressProfileBar.vue';
  import getImgPath from '@/utils/imgpath.js';
  import { takeUserNotifications, getProgressBarCompletion, requiredFieldsRecruteur } from '@/utils/userUtilities';
  import { downloadCvTemplate } from '@/utils/userUtilities.js';
  import ReviewEntreprise from '@/components/views-models/recruiter/profil/ReviewEntreprise.vue';

  export default {
    name: 'ProfilEntreprise',
    props: {
      user: {
        type: Object,
        required: true,
      },
    },
    components: {
      MyContactLinks,
      CvPreview,
      MyRecommendations,
      BackToTopArrow,
      RecrutierPresentationCard,
      CvTemplateCandidate,
      ProgressProfileBar,
      // ReviewEntreprise,
    },
    data() {
      return {
        contactLinks: [],
        notifications: 0,
        progressBar: 0,
      };
    },
    mounted() {
      this.contactLinks = this.buildContactLinks(this.user);
      this.notifications = takeUserNotifications(this.user);
      this.progressBar = getProgressBarCompletion(this.user, requiredFieldsRecruteur);
    },

    methods: {
      getImgPath,
      buildContactLinks(user) {
        return [
          user.linkedin,
          user.instagram,
          user.facebook,
          user.site_url,
          user.autre_url,
          user.porfolio_url,
          user.tiktok,
        ].filter((link) => link && link.trim() !== '');
      },

      downloadCvTemplate,
      async handleDownloadCvTemplate() {
        const cvComponent = this.$refs.CvTemplate;
        if (!cvComponent) {
          //console.error('Référence du CV introuvable !');
          return;
        }

        const cvElement = cvComponent.$refs.cvContainer;
        if (!cvElement) {
          //console.error("L'élément du CV est introuvable !");
          return;
        }

        // Cacher temporairement les icônes d'édition
        const editIcons = document.querySelectorAll(
          '.edit-icon, .edit-icon-profile'
        );
        editIcons.forEach((icon) => (icon.style.display = 'none'));

        // Appliquer la classe temporaire pour le format PDF
        cvElement.classList.add('pdf-mode');

        try {
          // Attendre la fin de la génération du PDF
          await downloadCvTemplate(this.user, cvElement);
        } catch (err) {
          //console.error('Erreur lors de la génération du PDF:', err);
        } finally {
          // Réafficher les icônes après la génération du PDF
          editIcons.forEach((icon) => (icon.style.display = ''));

          // Supprimer la classe temporaire
          cvElement.classList.remove('pdf-mode');
        }
      },
    },
  };
</script>

<style scoped>
  .head-message {
    font-size: 1.1rem;
  }
  .custom-progress {
    border: solid 3px var(--primary-1b);
  }
  .content-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 50px;
    width: 100%;
  }
  a.incomplete-infos-notification {
    display: flex;
    background-color: rgba(254, 106, 106, 0.444);
    width: fit-content;
    padding: 0 10px;
    border: solid 1px red;
    border-radius: 10px;
    font-size: 1rem;
    text-decoration: none;
    color: black;
    margin-top: 10px;
    transition: all 0.3s ease-in-out;
  }
  a.incomplete-infos-notification:hover {
    background-color: rgba(254, 106, 106, 0.273);
  }
  section.avis-and-pdf-container {
    width: 100%;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    gap: 30px;
  }
  .cv-preview,
  .my-recommendations {
    width: 50%;
  }
  .edition-profile-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    background-color: var(--black-100);
    color: var(--white-100);
    font-size: 1rem;
    cursor: pointer;
    transition:
      background-color 0.2s,
      color 0.2s;
  }
  .edition-profile-link img {
    margin-left: 10px;
  }
  .default-cv {
    padding-inline: 24px;
    padding-block: 16px;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .informations {
    background-color: rgba(88, 160, 150, 0.2);
    padding-block: 10px;
    padding-inline: 8px;
    border: 5px;
  }

  .preview-progress {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
  }

  .btn-wrapper button:hover {
    opacity: 0.6;
  }

  .progress-wrapper {
    width: 60%;
  }

  /* buttons */
  .import-btn {
    border-radius: 5px;
    border: 1px solid rgba(246, 179, 55, 1);
  }

  .preview-btn {
    background-color: var(--black-100);
    color: var(--white-100);
  }

  .checkbox {
    display: flex;
    gap: 20px;
    align-items: center;
  }

  .custom-switch {
    display: flex;
    gap: 20px;
    align-items: center;
  }

  /* content */
  .title-import {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }
  @media only screen and (max-width: 768px) {
    .btn-wrapper {
      width: 100%;
      display: flex;
      justify-content: center;
      margin: 15px;
    }
    .btn-wrapper .preview-btn {
      width: 100%;
    }
    a.incomplete-infos-notification {
      width: 100%;
    }

    .progress-wrapper {
      width: 100%;
      margin-top: 10px;
    }

    .preview-progress {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      flex-direction: column;
      gap: 10px;
    }
    .date-field {
      display: flex;
      align-items: flex-start;
      gap: 10px;
      flex-direction: column;
    }
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }
</style>
