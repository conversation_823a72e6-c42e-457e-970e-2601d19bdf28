<template>
  <main class="container padding-container">
    <!-- Fond sombre -->
    <div
      v-if="showRecruiterCalendar"
      class="overlay"
      @click="showRecruiterCalendar = false"
    ></div>
    <h1>
      <span
        class="clickable"
        @click="showEntretienPage = false"
        :class="{ active: !showEntretienPage }"
        >Mes offres</span
      >
      <span
        class="clickable"
        @click="showEntretienPage = true"
        :class="{ active: showEntretienPage }"
        >/ Mes entretiens</span
      >
    </h1>
    <!-- Affichage conditionnel -->
    <EntretienPage v-if="showEntretienPage" />

    <div v-else>
      <!-- Button to add a new offer -->
      <div class="btn-container">
        <PrimaryNormalButton
          textContent="Ajouter une offre"
          btnColor="secondary"
          @click="addNewOffer"
          add
        />
      </div>

      <!-- Toggle button for offer filters -->
      <div class="offer-toggle-container">
        <!-- Grand écran (Desktop) : v-btn-toggle pour les filtres -->
        <v-btn-toggle
          v-model="toggle"
          class="offer-toggle desktop-toggle"
          rounded="0"
          color="transparent"
          group
          mandatory
        >
          <v-btn value="all" :ripple="false">Toutes</v-btn>
          <v-btn value="true" :ripple="false">Offres publiées</v-btn>
          <v-btn value="false" :ripple="false">Offres non publiées</v-btn>
          <v-btn value="closed" :ripple="false">Offres archivées</v-btn>
        </v-btn-toggle>

        <!-- Mobile : v-select pour les filtres -->
        <v-select
          v-model="toggle"
          :items="filterOptions"
          class="mobile-select"
          variant="outlined"
        >
        </v-select>
      </div>

      <!-- Displaying the list of offers based on the selected filter -->
      <main class="offer-main-container">
        <p class="message">
          <img src="@/assets/icons/lightBulb.svg" alt="" /> Complétez votre
          <a class="link" href="#" @click.prevent="redirectToEntretien">
            calendrier
          </a>
          pour pouvoir planifier des entretiens d'embauche avec les profils qui
          vous interessent
        </p>

        <!-- Message si aucune alerte -->
        <p v-if="!paginatedOffers.length" class="no-message">
          Tu n'as aucune offre pour le moment. <br />
          Ajoute ta première offre
          <a class="link" @click="addNewOffer">ici</a>
          pour débuter.
        </p>
        <OfferCard
          v-for="offer in paginatedOffers"
          :key="offer.id"
          :offer="offer"
          class="tool-tip"
          data-title="Cliquez pour voir les détails de cette offre"
        />
      </main>

      <!-- Pagination Controls -->
      <div v-if="paginatedOffers === 1" class="pagination-controls">
        <v-icon
          @click="prevPage"
          :disabled="currentPage === 1"
          data-title="Page précédente"
          class="tool-tip"
        >
          mdi-chevron-left
        </v-icon>
        <span>Page {{ currentPage }} sur {{ totalPages }}</span>
        <v-icon
          @click="nextPage"
          :disabled="currentPage === totalPages"
          data-title="Page suivante"
          class="tool-tip"
        >
          mdi-chevron-right
        </v-icon>
      </div>
    </div>
  </main>
  <BackToTopArrow />
</template>

<script>
  import { mapGetters, mapActions } from 'vuex';
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import gotoPage from '@/utils/router.js';
  import OfferCard from '../../../components/views-models/recruiter/offer/OfferCard.vue';
  import EntretienPage from '@/components/views-models/entretiens/Entretiens.vue';

  export default {
    name: 'OfferPage',
    components: {
      OfferCard,
      BackToTopArrow,
      PrimaryNormalButton,
      EntretienPage,
    },
    data() {
      return {
        showEntretienPage: false,
        currentPage: 1,
        itemsPerPage: 5,
        toggle: 'true',
        filterOptions: [
          {
            title: 'Toutes',
            value: 'all',
          },
          {
            title: 'Offres non publiées',
            value: 'false',
          },
          {
            title: 'Offres publiées',
            value: 'true',
          },
          {
            title: 'Offres archivées',
            value: 'closed',
          },
        ],
      };
    },

    mounted() {
      if (this.$route.query.entretien === 'true') {
        this.showEntretienPage = true;
      }

      window.addEventListener(
        'redirectToEntretienPage',
        this.goToEntretienPage
      );
    },

    computed: {
      ...mapGetters(['getUser', 'getJobOffers']),

      filteredOffers() {
        return this.getJobOffers.filter((offer) => {
          switch (this.toggle) {
            case 'true':
              return offer.publie === true;
            case 'false':
              return offer.publie === false && offer.closed === false;
            case 'closed':
              return offer.closed === true;
            default:
              return true;
          }
        });
      },

      paginatedOffers() {
        const start = (this.currentPage - 1) * this.itemsPerPage;
        return this.filteredOffers.slice(start, start + this.itemsPerPage);
      },

      totalPages() {
        return Math.ceil(this.filteredOffers.length / this.itemsPerPage);
      },

      emptyMessage() {
        // fournis un msg spécifique en fonction de la section sélectionnée
        switch (this.toggle) {
          case 'true':
            return "Aucune offre publiée n'est disponible.";
          case 'false':
            return "Aucune offre non publiée n'est disponible.";
          case 'closed':
            return "Aucune offre archivée n'est disponible.";
          default:
            return 'Aucune offre disponible.';
        }
      },
    },

    methods: {
      ...mapActions(['fetchUser']),

      gotoPage,

      goToEntretienPage() {
        this.showEntretienPage = true;
      },

      addNewOffer() {
        const emptyOffer = {
          title: '',
          description: '',
          published: false,
          closed: false,
        };

        // Store the empty offer in localStorage
        localStorage.setItem('offerData', JSON.stringify(emptyOffer));

        // Navigate to the new offer page
        this.$router.push('/recruteur/offre/redaction');
      },

      nextPage() {
        if (this.currentPage < this.totalPages) this.currentPage++;
      },
      prevPage() {
        if (this.currentPage > 1) this.currentPage--;
      },
    },

    async created() {
      await this.fetchUser();
      //console.log('User chargé:', this.getUser);
      //console.log('Offres chargées:', this.getJobOffers);
    },
  };
</script>

<style scoped>
  h1 span {
    cursor: pointer;
    padding: 5px 10px;
    color: var(--gray-100);
    border: 1px solid transparent;
    border-radius: 4px;
    transition: all 0.3s ease;
  }

  h1 span:hover {
    border: 1px solid var(--gray-100);
  }

  h1 span.active {
    color: var(--text-1);
    border-bottom: 2px solid var(--primary-1);
    border: 1px solid var(--primary-1);
    border-bottom-width: 2px;
  }

  .pagination-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
  }

  .pagination-controls v-icon {
    cursor: pointer;
    font-size: 24px;
    color: #f6b336 !important;
  }

  .pagination-controls v-icon[disabled] {
    color: #f6b336;
    cursor: not-allowed;
  }

  .offer-toggle-container .v-btn__content {
    justify-content: center;
    white-space: normal !important;
  }

  .container {
    width: 100%;
    margin-bottom: 30px;
  }

  /* toggle btn */
  .offer-toggle-container {
    width: 100%;
    margin-top: 40px;
    overflow: hidden;
  }

  .offer-toggle {
    width: 100%;
    display: flex;
  }

  .offer-toggle button {
    flex: 1;
    height: 48px !important;
    border-bottom: 1px solid var(--surface-bg-4);
    color: var(--text-3);
    background-color: transparent;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    border-bottom: 1px solid var(--surface-bg-4);
  }

  .offer-toggle button:hover {
    color: var(--text-1) !important;
    background-color: var(--surface-bg-4);
    border: 1px solid var(--gray-100);
    border-bottom: 1px solid var(--surface-bg-4);
    cursor: pointer;
  }

  .offer-toggle button.v-btn--active {
    border: 1px solid var(--primary-1);
    border-bottom: 2px solid var(--primary-1);
  }

  .offer-main-container {
    margin-top: 30px;
    gap: 38px;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .offer-main-container .no-message {
    font-size: 1.2rem;
    color: #666;
    text-align: center;
    margin-top: 20px;
    grid-column: 1 / -1;
  }
  .offer-main-container .no-message .link {
    color: var(--primary-1);
    font-weight: bold;
    text-decoration: underline;
    cursor: pointer;
  }
  .offer-main-container .message {
    display: flex;
    align-items: center;
    color: #666;
    text-align: center;
    margin-top: 20px;
    grid-column: 1 / -1;
  }
  .offer-main-container .message .link {
    color: var(--primary-1);
    font-weight: bold;
    text-decoration: underline;
    cursor: pointer;
    padding: 0 4px;
  }

  .d-none {
    display: flex !important;
  }

  .btn-container {
    display: flex;
    justify-content: end;
    margin-bottom: 20px;
  }

  .mobile-select {
    display: none;
  }

  /* ----------- MODALE ----------- */
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Couleur semi-transparente */
    z-index: 999; /* Juste en dessous de la modale */
  }
  .calendar-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    width: 1080px;
    padding: 20px 100px;
  }

  /* ✅ MOBILE */
  @media screen and (max-width: 480px) and (max-width: 767px) {
    .desktop-toggle {
      display: none;
    }

    .mobile-select {
      display: block;
    }
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }
</style>
