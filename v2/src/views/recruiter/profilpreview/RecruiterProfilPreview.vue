<template>
  <main class="container padding-container">
    <div class="content-wrapper">
      <!--Close button-->
      <div class="btn-row">
        <img
          src="@/assets/icons/close-btn.svg"
          alt="bouton fermer"
          class="close-btn"
          @click="closePreview"
        />
      </div>

      <!-- Header Top -->
      <section class="top-container">
        <div class="section-header">
          <div class="company-title-wrapper">
            <!-- Company logo -->
            <div class="company-logo-wrapper">
              <img
                v-if="formData.photo || previewImage"
                :src="previewImage || getFullImageUrl()"
                class="avatar-icon"
                alt="avatar image"
              />
              <img
                v-else
                src="@/assets/icons/avatar.png"
                alt="avatar image"
                class="avatar-icon"
              />
            </div>

            <!-- Company title, website, social networks -->
            <div class="title-wrapper align-start">
              <h2>{{ formData.company }}</h2>
              <h5 class="align-start">
                <u>{{ formData.website }}</u>
              </h5>

              <div class="align-start">
                <img :src="linkedinImage" alt="icon linkedin" />
                <img :src="facebookImage" alt="icon facebook" />
              </div>
            </div>
          </div>

          <!-- Notation and opinions-->
          <div class="opinions-container">
            <div class="stars">
              <img
                v-for="index in 5"
                :key="index"
                :src="index <= formData.rating ? starFill : starEmpty"
                :alt="
                  index <= formData.rating ? 'étoile pleine' : 'étoile vide'
                "
                class="star"
              />
            </div>

            <div class="btn-wrapper">
              <v-btn flat class="opinions-btn">
                Voir les avis
                <template v-slot:append @click="toggleReviewEnterprise">
                  <img src="@/assets/icons/arrow-bottom-yellow.svg" />
                </template>
              </v-btn>
              <ReviewEntreprise v-if="showReviewEnterprise" />
            </div>
          </div>
        </div>

        <!--Header bottom-->
        <div class="section-body">
          <div class="tag">
            <img
              src="@/assets/icons/localisation-mini.svg"
              alt="icône de localisation"
            />
            <p class="fs12">{{ formData.city }}</p>
          </div>
          <div class="tag">
            <img src="@/assets/icons/suitcase-mini.svg" alt="icône de valise" />
            <p class="fs12">{{ formData.activitySector }}</p>
          </div>
          <div class="tag">
            <img
              src="@/assets/icons/firm-size-mini.svg"
              alt="icône d'entreprises"
            />
            <p class="fs12">Taille : {{ formData.taille }}</p>
          </div>

          <div class="tag">
            <img src="@/assets/icons/calendly.svg" alt="icône de calendrier" />
            <p class="fs12">Fondée en : {{ formData.foundationYear }}</p>
          </div>
        </div>
      </section>

      <section class="section-container">
        <div class="presentation">
          <h5>Qui sommes-nous ?</h5>
          <p class="text-block">
            {{ formData.aboutme }}
          </p>
        </div>
        <div class="why-join-us">
          <h5>Pourquoi nous rejoindre ?</h5>
          <p class="text-block">
            {{ formData.whyJoinUs }}
          </p>
        </div>
      </section>

      <section class="section-container">
        <div class="advantages">
          <h5>{{ formData.avantage }}</h5>
        </div>

        <div class="recruitment-process">
          <h5>Le processus de recrutement</h5>
          <p class="text-block">
            {{ formData.process }}
          </p>
        </div>
      </section>

      <section class="section-container">
        <div class="opinion-section">
          <h5>Section avis</h5>
          <p class="text-block">
            {{ formData.avis }}
          </p>
        </div>
      </section>

      <div class="opinion-post">
        <OpinionPost />
      </div>
    </div>

    <BackToTopArrow />
  </main>
</template>

<script>
  import facebookImage from '@/assets/facebook.svg';
  import starEmpty from '@/assets/icons/star-empty.svg';
  import starFill from '@/assets/icons/star-filled.svg';
  import linkedinImage from '@/assets/linkedinBlack.svg';
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import CompanyAdvantages from '@/components/views-models/recruiter/profil-preview/CompanyAdvantages.vue';
  import OpinionPost from '@/components/views-models/recruiter/profil-preview/OpinionPost.vue';
  import ReviewEntreprise from '@/components/views-models/recruiter/profil/ReviewEntreprise.vue';
  import { baseUrl } from '@/services/axios';
  import {
    getUserProfilSectionDatas,
    updateUserInformations,
  } from '@/services/profile.service';

  export default {
    name: 'Profil-preview',
    components: {
      BackToTopArrow,
      OpinionPost,
      CompanyAdvantages,
      ReviewEntreprise,
    },
    data() {
      return {
        formData: {},
        linkedinImage: linkedinImage,
        facebookImage: facebookImage,
        starFill: starFill,
        starEmpty: starEmpty,
        previewImage: '',
        showReviewEnterprise: false,
      };
    },

    mounted() {
      this.formData = getUserProfilSectionDatas();
    },
    methods: {
      //  toggle preview off
      closePreview() {
        this.$emit('close-preview');
      },
      toggleReviewEnterprise() {
        this.showReviewEnterprise = !this.showReviewEnterprise;
      },
      updateUserDatas() {
        updateUserInformations(this.formData);
      },

      // get photo url
      getFullImageUrl() {
        if (this.formData.photo) {
          if (this.formData.photo.name) {
            if (typeof this.formData.photo.name === 'string') {
              return URL.createObjectURL(this.formData.photo);
            }
          }

          return baseUrl + this.formData.photo;
        } else if (this.previewImage) {
          return this.previewImage;
        } else {
          return '';
        }
      },
    },
  };
</script>

<style scoped>
  /* layout */
  .container .content-wrapper {
    display: flex;
    flex-direction: column;
    height: fit-content;
    align-items: center;
    gap: 1.5rem;
  }

  .btn-row {
    display: flex;
    width: 100%;
    justify-content: end;
  }

  .close-btn {
    width: 32px;
    height: 32px;
    cursor: pointer;
    margin-inline: 0.9rem;
  }

  .top-container {
    display: flex;
    flex-direction: column;
    padding-bottom: 0rem;
    width: 100%;
  }

  .section-container {
    background-color: var(--surface-bg-2);
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
    border-radius: 5px;
    padding: 1.5rem 4rem;
  }

  .section-header {
    background-color: var(--surface-bg-2);
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    padding-inline: 4rem;
    padding-block: 1rem;
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-top: 2rem;
  }

  .section-body {
    background-color: var(--text-1);
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    justify-content: space-between;
    padding: 0.6rem 3rem;
  }

  .section-footer {
    display: flex;
    justify-content: center;
    gap: 30px;
    width: 100%;
    padding-top: 40px;
  }

  .opinions-container {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    gap: 0.7rem;
    margin-bottom: 0.9rem;
    padding-right: 4rem;
  }

  .text-block {
    margin: 1.6rem 0rem;
  }

  .opinion-post {
    width: 85%;
    margin-block: 1.5rem;
  }

  .advantages {
    margin-block: 0.3rem 1.3rem;
  }

  .top-container,
  .section-container,
  .opinion-post {
    pointer-events: none;
  }

  /* wrappers */
  .company-title-wrapper {
    display: flex;
    flex-direction: row;
    gap: 40px;
    align-items: center;
  }

  .company-logo-wrapper {
    width: fit-content;
    height: fit-content;
  }

  .title-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: initial;
  }

  .align-start {
    align-self: baseline;
    margin: 0.4rem 0rem;
  }

  .view-wrapper {
    width: fit-content;
    height: fit-content;
  }

  .btn-wrapper {
    width: fit-content;
    height: fit-content;
  }

  .opinions-btn {
    background-color: var(--black-100);
    color: var(--white-100);
  }

  /* components */
  .advantages-component {
    margin: 0.7rem 0rem;
  }

  /* utilities */
  .logo {
    border: 1px solid rgba(223, 219, 214, 1);
    width: 85px;
    height: 85px;
    border-radius: 2px;
  }

  .tag {
    background-color: var(--surface-bg-2);
    padding: 2px;
    border-radius: 5px;
    display: flex;
    justify-content: start;
    align-items: center;
    gap: 5px;
    width: 10.5%;
  }

  .fs12 {
    font-size: 10px;
  }

  .star {
    margin-inline: 0.15rem;
  }

  .avatar-icon {
    width: auto;
    height: 92px;
    cursor: pointer;
  }

  /* Media Queries */
  @media (max-width: 1210px) {
    .tag {
      width: 85px;
    }

    .opinions-container {
      padding-right: 0rem;
    }

    .redact-post {
      width: 100%;
    }

    .opinion-post {
      width: 100%;
    }
  }

  @media (max-width: 727px) {
    .section-header {
      flex-direction: column;
    }

    .redact-post-btn {
      width: auto;
    }
  }

  @media (max-width: 560px) {
    .section-header {
      padding-inline: 1rem;
    }

    .company-title-wrapper {
      justify-content: space-between;
    }

    .section-body {
      padding-inline: 1rem;
      flex-wrap: wrap;
      justify-content: center;
    }

    .tag {
      margin: 0.3rem 0.3rem;
    }
  }

  /* desktop */
  @media screen and (min-width: 992px) {
    .content-wrapper {
      width: 100%;
    }
  }

  /* large desktop */
  @media screen and (min-width: 1800px) {
    .content-wrapper {
      width: 80%;
      margin: 0 auto;
    }
  }

  /* x-large desktop */
  @media screen and (min-width: 2400px) {
    .content-wrapper {
      width: 70%;
      margin: 0 auto;
    }
  }
</style>
