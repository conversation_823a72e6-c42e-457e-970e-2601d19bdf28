<template>
    <main class="container">
        <div class="login-header">
            <h1>Connexion</h1>
            <div class="cross-button-container">
                <button class="cross-button" @click="gotoPage('/')">
                    <img src="@/assets/icons/cross.svg" alt="close" />
                </button>
            </div>
        </div>
        <section class="login-container">
            <div class="login-ext-buttons-container">
                <!-- google btn -->
                <GoogleButton @click="signInWithGoogle" :loading="isGoogleLoading" />
                <!-- ft btn -->
                <!-- <FranceTravailLogin /> -->
            </div>

            <span>ou</span>

            <!-- form inputs -->
            <v-form ref="formLoginRef">
                <label for="siret">Numéro SIRET</label>
                <v-text-field
                    type="text"
                    variant="plain"
                    :rules="siretRules"
                    v-model="formData.siret"
                    placeholder="Votre numéro SIRET"
                />
                <!-- email -->
                <label for="email">Email</label>
                <v-text-field
                    type="email"
                    variant="plain"
                    :rules="[...emailRules , ...notEmptyRules]"
                    v-model="formData.email"
                    placeholder="Votre email"
                />
                <!-- password -->
                <label for="password">Mot de passe</label>
                <v-text-field
                    variant="plain"
                    placeholder="*********"
                    :rules="notEmptyRules"
                    v-model="formData.password"
                    :type="isPasswordHide ? 'password' : 'text'"
                    :append-icon="isPasswordHide ? 'mdi-eye-off' : 'mdi-eye'"
                    @click:append="isPasswordHide = !isPasswordHide"
                    @keyup.enter="loginSubmit"
                />
                <!-- password forget ? -->
                <span class="text-underline cursor-pointer" @click="gotoPage('/reinitialisation-mot-de-passe')">Mot de passe oublié ?</span>
                <!-- checkbox remember me -->
                <div class="remember-container">
                    <v-checkbox v-model="rememberMeCheckbox" color="var(--primary-1)" label="Se souvenir de moi." hide-details></v-checkbox>
                </div>

                <!-- form footer, buttons and text -->
                <div class="form-footer">
                    <!-- login btn -->
                    <div @click="loginSubmit">
                        <PrimayNormalButton textContent="Je me connecte" :isLoading="isLoading" />
                    </div>

                    <!-- other texts -->
                    <div class="form-footer-text">
                        <p>Vous n'avez pas encore de compte chez thanks-boss ?</p>
                        <p class="text-underline cursor-pointer" @click="gotoPage('/inscription')">Inscrivez-vous ici</p>
                    </div>
                </div>
            </v-form>
        </section>
        <section class="login-footer">
            <div>
                <p>Vous êtes à la recherche d'un job ? <br> Venez-vous connecter chez thanks-boss</p>
            </div>
            <div @click="gotoPage('/connexion')" class="btn-espace-candidat">
                <PrimayNormalButton textContent="Espace candidat" />
            </div>
        </section>
    </main>
</template>

<script setup>
import { ref } from 'vue';
const formLoginRef = ref(null);
</script>

<script>
import { login } from '@/services/account.service';
import { toaster } from '@/utils/toast/toast';
import GoogleButton from '@/components/buttons/GoogleLogin.vue';
import FranceTravailLogin from '@/components/buttons/FranceTravailLogin.vue';
import PrimayNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
import gotoPage from '@/utils/router.js';
import { validateEmail, validateNotEmpty } from "../../../utils/validationRules";
import { decodeCredential, googleOneTap } from "vue3-google-login";


export default {
    name: 'Login',
    components: {
        GoogleButton,
        FranceTravailLogin,
        PrimayNormalButton,
    },
    data() {
        return {
            formData: {
                siret: '',
                email: '',
                password: '',
            },
            isLoading: false,
            isGoogleLoading: false,
            isPasswordHide: true,
            rememberMeCheckbox: false,
            emailRules: [
                v => validateEmail(v) || true
            ],
            notEmptyRules: [
                v => validateNotEmpty(v) || true
            ],
        };
    },
    methods: {
        gotoPage,
        // login with email and password
        async loginSubmit() {
            try {
                const valid = await this.validateInputForm();
                if (!valid) {
                    return;
                }
                // set loader in btn
                this.isLoading = true;

                // set email to lowercase
                this.formData.email = this.formData.email.toLowerCase();

                // try to login
                await login(this.formData, this.rememberMeCheckbox);

                // and go to home page
                this.$router.push({ path: "/" });
            } catch (error) {
                //console.log(error);
                toaster.showErrorPopup("Erreur lors de la connexion");
            } finally {
                this.isLoading = false;
            }
        },
        // login with google
        async signInWithGoogle() {
            try {
                const response = await googleOneTap({
                    clientId: "499109195466-3cgb1ucg8oon8ncdemjpveuqkv7n0i61.apps.googleusercontent.com",
                    context: "signin",
                });

                const reg = decodeCredential(response.credential);
                const email = reg.email;
                const password = reg.sub;

                const regData = { email, password };

                // try to login
                await login(regData);

                // and go to home page
                this.$router.push({ path: "/" });
            } catch (error) {
                return toaster.showErrorPopup("Utilisateur non inscrit. Veuillez vérifier vos informations ou vous inscrire");
            }
        },
        // form validators
        async validateInputForm() {
            // check if form is valid and not the last page
            return (await this.$refs.formLoginRef.validate()).valid;
        },
    },
};
</script>

<style>
.login-container input {
    width: 532px;
    border-radius: 2px;
    padding: 5px;
    background-color: var(--surface-bg-2);
}

.login-container .v-input__append {
    background-color: var(--surface-bg-2);
    margin-inline-start: 0;
    padding-top: 50% !important;
    padding-right: 5px;
}
</style>

<style scoped>
.container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-top: inherit;
}

.login-header {
    padding-top: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.login-container {
    margin-top: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px;

}

.login-ext-buttons-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 30px;
}

.remember-container {
    position: relative;
    right: 10px;
}

.form-footer {
    margin-top: 30px;
    margin-bottom: 20px;
    gap: 30px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.form-footer-text {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;
}

.cross-button-container {
    position: absolute;
    right: 120px;
}

.login-footer {
    min-height: 100px;
    width: 100%;
    gap: 30px;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #26282B;
    color: var(--surface-bg);
}

.cross-button {
    width: 32px;
    height: 32px;
    border-radius: 5px;
    background-color: var(--text-1);
    display: flex;
    justify-content: center;
    align-items: center;
}

.cross-button img {
    width: 13px;
    height: 13px;
}

@media (max-width: 768px) {
    .login-header {
        padding-top: 20px;
    }
    .login-container {
        margin-top: 30px;
        padding: 0 20px;
    }
    .login-container input {
        width: 100%;
    }
    .cross-button-container {
        right: 20px;
    }
    .form-footer {
        gap: 20px;
    }
    .login-footer {
        gap: 5px;
        flex-direction: column;
        text-align: center;
        margin-bottom: 40%; /* Ajouté pour remonter le bouton */
    }
    .login-footer .btn-espace-candidat {
        margin-top: center;
    }
    .login-container .v-input__append {
        padding-right: 0;
    }
    .login-container .v-input__append .v-icon {
        padding-right: 5px;
    }
}
</style>