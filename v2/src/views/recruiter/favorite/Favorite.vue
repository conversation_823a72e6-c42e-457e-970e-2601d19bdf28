<template>
  <main class="container padding-container">
    <!-- Fond sombre -->
    <div
      v-if="confirmationisOpened"
      class="overlay"
      @click="confirmationisOpened = false"
    ></div>

    <!-- title -->
    <h1>Mes favoris</h1>
    <div class="favorite-container">
      <div class="basic-filter-container border-radius-15">
        <CustomAccordion
          field=""
          title="Trier par métier"
          :fields="['Tous les métiers', ...getUniqueTitles()]"
          @checkbox-state="sortByTitleType"
          :chips="false"
        />
        <CustomAccordion
          field=""
          title="Trier par nom"
          :fields="['A - Z', 'Z - A']"
          @checkbox-state="sortByName"
          :chips="false"
        />
      </div>

      <aside class="favorite-main-container">
        <!-- Message si aucun favoris -->
        <p
          v-if="!favoritesList.length && !filteredfavoritesList.length"
          class="no-message"
        >
          Tu n'as ajouté aucun profil à tes favoris pour le moment. <br />
          Rend-toi sur la page
          <a class="link" @click.stop="gotoPage('/communaute')">Communauté</a>
          pour débuter.
        </p>
        <CompactProfilCard
          v-for="(candidate, index) in filteredfavoritesList.length
            ? filteredfavoritesList
            : favoritesList"
          :key="index"
          :candidate="candidate"
          :isFavorite="true"
          @heart-click="openConfirmationModal"
          @remove-favorite="removeFavoriteFromList"
        />
      </aside>
    </div>

    <ConfirmationModal
      v-if="confirmationisOpened"
      title="Suppression"
      class="modal-container"
      description="Êtes-vous sûr de vouloir supprimer ce candidat de votre liste de favoris ?"
      @confirm="deleteFavorite"
      @close="confirmationisOpened = false"
    />
  </main>

  <BackToTopArrow />
</template>

<script>
  import {
    removeFavoriteProfil,
    isCandidateFavoriteById,
  } from '/src/services/favoriteProfil.service.js';
  import CompactProfilCard from '@/components/cards/candidate-card/CompactProfilCard.vue';
  import CustomAccordion from '@/components/buttons/CustomAccordion.vue';
  import ConfirmationModal from '@/components/modal/confirmation/ConfirmationModal.vue';
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import gotoPage from '@/utils/router';

  export default {
    name: 'RecruiterFavorite',

    components: {
      CompactProfilCard,
      CustomAccordion,
      ConfirmationModal,
      BackToTopArrow,
    },

    data() {
      return {
        confirmationisOpened: false,
        selectedFavoriteId: null,
        filteredfavoritesList: [], // Liste filtrée des candidats
        favoritesList: [],
      };
    },

    methods: {
      gotoPage,

      removeFavoriteFromList(candidateId) {
        this.favoritesList = this.favoritesList.filter(
          (candidate) => candidate.id !== candidateId
        );
        this.filteredfavoritesList = this.filteredfavoritesList.filter(
          (candidate) => candidate.id !== candidateId
        );
      },

      // Ouverture de la modale de confirmation pour la suppression
      openConfirmationModal(id) {
        //console.log('Heart clicked for candidate with ID:', id);
        this.confirmationisOpened = true;
        this.selectedFavoriteId = id;
      },

      // Suppression d'un favori
      async deleteFavorite() {
        try {
          await removeFavoriteProfil(this.selectedFavoriteId);
          this.removeFavoriteFromList(this.selectedFavoriteId); // Met à jour l'affichage
        } catch (error) {
          //console.error(error);
        } finally {
          this.confirmationisOpened = false;
        }
      },

      // Filtrer par titre
      getUniqueTitles() {
        return [
          ...new Set(this.favoritesList.map((candidate) => candidate.metier)),
        ];
      },

      sortByTitleType(el, selectedTitles) {
        // Si "Tous les métiers" est sélectionné, réinitialiser la liste
        if (
          selectedTitles === 'Tous les métiers' ||
          (Array.isArray(selectedTitles) &&
            selectedTitles.includes('Tous les métiers'))
        ) {
          this.filteredfavoritesList = this.favoritesList;
          return;
        }

        // Si selectedTitles est un objet, extraire les titres sélectionnés
        if (typeof selectedTitles === 'object') {
          selectedTitles = Object.keys(selectedTitles).filter(
            (key) => selectedTitles[key] === true
          );
        }

        // Filtrer la liste des candidats en fonction du métier sélectionné
        this.filteredfavoritesList = this.favoritesList.filter((candidate) =>
          selectedTitles.includes(candidate.metier)
        );
      },

      sortByName(el, values) {
        const selectedFilter = Object.keys(values).find(
          (key) => values[key] === true
        );
        if (selectedFilter === 'A - Z') {
          this.favoritesList.sort((a, b) =>
            a.last_name.localeCompare(b.last_name)
          );
        } else if (selectedFilter === 'Z - A') {
          this.favoritesList.sort((a, b) =>
            b.last_name.localeCompare(a.last_name)
          );
        }
      },

      /* sortByDate(el, values) {
        const selectedFilter = Object.keys(values).find(
          (key) => values[key] === true
        );
        if (selectedFilter === 'Plus récent') {
          this.favoritesList.sort(
            (a, b) => new Date(b.created_at) - new Date(a.created_at)
          );
        } else if (selectedFilter === 'Plus ancien') {
          this.favoritesList.sort(
            (a, b) => new Date(a.created_at) - new Date(b.created_at)
          );
        }
      }, */

      // Charger les favoris de l'utilisateur
      async loadFavorites() {
        try {
          const favoriteProfiles = await isCandidateFavoriteById();
          //console.log('favoriteProfiles', favoriteProfiles);

          this.favoritesList = favoriteProfiles
            .filter((item) => item.apply_user && item.apply_user.length > 0) // Filtre les entrées sans données dans apply_user
            .map((item) => {
              const candidate = item.apply_user[0];
              return {
                id: candidate.id, // ID du candidat
                first_name: candidate.first_name, // Prénom
                last_name: candidate.last_name, // Nom
                metier: candidate.metier, // Métier
                photo: candidate.photo, // Photo
                about: candidate.about, // À propos
                connected: candidate.connected, // Statut de connexion
              };
            });
        } catch (error) {
          //console.error('Erreur lors de la récupération des favoris', error);
        }
      },
    },

    mounted() {
      this.loadFavorites();
      //console.log(this.favoritesList);
    },
  };
</script>

<style scoped>
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Couleur semi-transparente */
    z-index: 999; /* Juste en dessous de la modale */
  }

  .modal-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000; /* Assurez-vous que la modale est au-dessus du contenu */
    width: 100%; /* Cela peut être ajusté selon vos besoins */
    max-width: 500px; /* Ajustez selon la taille de votre modale */
    padding: 20px; /* Optionnel, pour ajouter du padding autour de la modale */
    text-align: center;
  }

  .favorite-container {
    display: flex;
    gap: 27px;
  }

  .basic-filter-container {
    height: fit-content;
    max-width: 231px;
    margin-top: 90px;
    background-color: var(--surface-bg-2);
    padding: 16px;
  }

  .basic-filter-container .container {
    margin-top: 0;
    margin-bottom: 10px;
  }

  aside {
    width: 100%;
    margin-bottom: 30px;
  }

  .favorite-main-container {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fill, 258px);
    grid-gap: 27px;
    margin-top: 90px;
  }
  .favorite-main-container .no-message {
    font-size: 1.2rem;
    color: #666;
    text-align: center;
    margin-top: 20px;
    grid-column: 1 / -1;
  }
  .favorite-main-container .no-message .link {
    color: var(--primary-1);
    font-weight: bold;
    text-decoration: underline;
    cursor: pointer;
  }

  .blur-content {
    filter: blur(5px);
  }

  /* ✅ MOBILE */
  @media screen and (max-width: 480px) and (max-width: 767px) {
    .favorite-container {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .favorite-main-container {
      justify-content: center;
    }
    .basic-filter-container {
      display: flex;
      width: 100%;
      max-width: 100%;
      gap: 20px;
    }
    .basic-filter-container .container {
      width: 50%;
    }
    main {
      justify-items: center;
    }
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }
</style>
