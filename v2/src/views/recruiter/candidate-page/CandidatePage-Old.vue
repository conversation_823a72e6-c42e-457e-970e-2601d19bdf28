<template>
    <main class="container padding-container">

      <div class="content-wrapper">
  
        <!-- Section displaying candidate details -->
        <section class="top-container">
          <!-- Button to navigate back to the search page -->
        <div class="back-btn-container">
          <div class="btn-wrapper" @click="gotoPage('/communaute')">
            <PrimaryNormalButton textContent="Retour" btnColor="secondary" back />
          </div>
        </div>
          <div class="section-header">
            <div class="company-title-wrapper">
            
              <div class="title-wrapper">
                <h2>Profil du candidat</h2>
              </div>
            </div>
  
            <!-- Tags with candidate details like location, contract type, and telecommuting -->
            <div class="tags-wrapper">
              <div class="tag2">
                <img src="@/assets/icons/localisation-mini.svg" alt="Localisation Icon" />
                <p class="fs12">{{ candidate.ville }}</p>
              </div>
  
              <div class="tag2">
                <img src="@/assets/icons/list-mini.svg" alt="Contract Type Icon" />
                <p class="fs12">{{ candidate.contrat }}</p>
              </div>
  
              <div class="tag2">
                <img src="@/assets/icons/pc-mini.svg" alt="Telecommuting Icon" />
                <p class="fs12">{{ candidate.teletravail }}</p>
              </div>
            </div>
          </div>
  
          <!-- Section with buttons for contact the candidate -->
          <div class="section-footer">
  
          <div class="btn-wrapper">
            <PrimaryRoundedButton textContent="Contacter le candidat" @click="$router.push(`/utilisateur/${candidate.id}`)" />
          </div>

          </div>
        </section>
  
        <!-- Sections for Candidateing candidate's profile, criteria, skills, experiences, and formations -->
        <CandidateProfileSection />
        <CandidateCriteriasSection />
        <CandidateSkillsSection />
        <CandidateExperiencesSection />
        <CandidateFormationsSection />
        <!-- 
        LA SECTION MOTIVATION EST PLUTÔT À METTRE SUR LA PAGE CANDIDATURE D'UN JOB
        <CandidateMotivationSection />
        -->

        <!-- Section with buttons for contact the candidate -->
        <section class="section-footer">
          <!-- button -->
          <div class="btn-wrapper">
            <PrimaryRoundedButton textContent="Contacter le candidat" @click="$router.push(`/utilisateur/${candidate.id}`)" />
          </div>
        </section>
      </div>
  
      <!-- Button to scroll back to the top of the page -->
      <BackToTopArrow />
    </main>
</template>
  
<script>
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import Chip from '@/components/chips/Chip.vue';
  import gotoPage from '@/utils/router.js';
  import imgpath from '@/utils/imgpath.js';
  import { getUserById } from '@/services/account.service.js';
  import CandidateProfileSection from '@/components/views-models/recruiter/candidate-page/CandidateProfileSectionRecruiter.vue';
  import CandidateCriteriasSection from '@/components/views-models/recruiter/candidate-page/CandidateCriteriasSectionRecruiter.vue';
  import CandidateSkillsSection from '@/components/views-models/recruiter/candidate-page/CandidateSkillsSectionRecruiter.vue';
  import CandidateExperiencesSection from '@/components/views-models/recruiter/candidate-page/CandidateExperiencesSectionRecruiter.vue';
  import CandidateFormationsSection from '@/components/views-models/recruiter/candidate-page/CandidateFormationsSectionRecruiter.vue';
  import CandidateMotivationSection from '@/components/views-models/recruiter/candidate-page/CandidateMotivationSectionRecruiter.vue';

  export default {
    name: 'CandidatePage',
  
    components: {
      BackToTopArrow,
      PrimaryNormalButton,
      PrimaryRoundedButton,
      Chip,
      CandidateProfileSection,
      CandidateCriteriasSection,
      CandidateSkillsSection,
      CandidateExperiencesSection,
      CandidateFormationsSection,
      CandidateMotivationSection,
    },
  
    props: {
      // Indicates if the user is in recruiter mode
      userIsRecruter: {
        type: Boolean,
        default: false,
      },
    },
  
    data() {
      return {
        jobofferIsFavorite: false,
        showSkills: true,
        showSoftSkills: true,
        showLogo: true,
        candidate: {},
      };
    },
  
    async mounted() {
      this.scrollToTop();
      this.candidate = await getUserById(this.$route.params.id);
    },
  
    methods: {
      gotoPage,
      imgpath,
  
      // Scrolls to the top of the page
      scrollToTop() {
        window.scrollTo(0, 0);
      },

    },
  }
</script>
  
<style scoped>
  .v-text-field {
    height: auto; /* Ajuste la hauteur automatiquement en fonction du contenu */
}
  .v-text-field .v-input__control {
    white-space: normal; /* Ou 'nowrap' si vous préférez */
    overflow-wrap: break-word; /* Permet de casser les mots longs */
    word-break: break-word; /* S'assure que les mots longs sont bien cassés à la ligne */
}
  .padding-container {
    padding-inline: 3vw;
}
  /* Grid layout for items */
  .grid-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 40px;
  }
  
  .grid-item {
    display: flex;
    flex-direction: column;
  }
  
  .location {
    grid-column: span 2; /* Spans across both columns */
  }
  
  /* Main container layout */
  .container {
    height: fit-content;
    padding-bottom: 50px;
  }
  .container .content-wrapper {
    display: flex;
    flex-direction: column;
    gap: 30px;
  }
  
  /* Back button container */
  .back-btn-container {
    display: flex;
    margin-bottom: 20px;
  }
  
  /* Top section layout */
  .top-container {
    display: flex;
    flex-direction: column;
    padding-inline: 3vw;
  }
  
  /* Container for section with background and padding */
  .section-container {
    background-color: var(--surface-bg-2);
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: 100%;
    border-radius: 5px;
    padding-inline: 10px;
    padding-block: 24px;
  }
  
  /* Header section with spacing and alignment */
  .section-header {
    background-color: var(--surface-bg-2);
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    padding-inline: 16px;
    padding-block: 18px;
    display: flex;
    justify-content: space-between;
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
  }
  
  /* Body section with grid layout */
  .section-body {
    background-color: var(--text-1);
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    padding: 8px;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 10px;
    width: 100%;
  }
  
  /* Footer section with centered buttons */
  .section-footer {
    display: flex;
    justify-content: center;
    gap: 30px;
    width: 100%;
    padding-top: 60px;
  }
  
  /* Wrapper styles for different sections */
  .company-title-wrapper {
    display: flex;
    flex-direction: column;
    gap: 40px;
    align-items: center;
    margin-left: 26px;
  }
  
  .company-logo-wrapper {
    width: fit-content;
    height: fit-content;
  }
  
  .title-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .favorite-wrapper {
    width: fit-content;
    height: fit-content;
  }
  
  .btn-wrapper {
    width: fit-content;
    height: fit-content;
  }
  
  .chip-wrapper {
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    gap: 5px;
  }
  
  .question-wrapper {
    display: flex;
    flex-direction: column;
  }
  
  .tags-wrapper {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    padding: 16px;
    display: flex;
    gap: 10px;
    width: 100%;
    justify-items: start;
    flex-direction: row;
    justify-content: space-between;
  }
  
  /* Utility styles for tags */
  .logo {
    border: 1px solid rgba(223, 219, 214, 1);
    width: 92px;
    height: 92px;
    border-radius: 2px;
  }
  
  .tag {
    background-color: var(--surface-bg-2);
    padding: 2px;
    border-radius: 5px;
    display: flex;
    justify-content: start;
    align-items: center;
    gap: 5px;
    width: 110px;
    height: 35px;
  }
  
  .tag2 {
    padding: 2px;
    border-radius: 5px;
    display: flex;
    justify-content: start;
    align-items: center;
    gap: 10px;
    width: 110px;
    height: 35px;
  }
  
  .fs12 {
    font-size: 10px;
  }
  
  .favicon {
    cursor: pointer;
  }
  
  /* Responsive styles */
  @media screen and (min-width: 992px) {
    .company-title-wrapper {
      flex-direction: row;
      align-items: center;
    }
  
    .title-wrapper {
      text-align: initial;
    }
  
    .align-start {
      align-self: baseline;
    }
  
    .section-body,
    .tags-wrapper {
      display: flex;
      padding: 16px;
      justify-content: space-between;
      gap: 0px;
    }
  
    .tag {
      padding: 4px;
      gap: 10px;
      width: 124px;
      height: 42px;
    }
  
    .tag2 {
      padding: 4px;
      gap: 10px;
      width: 124px;
      height: 42px;
    }
  
    .fs12 {
      font-size: 12px;
    }
  
    .section-footer {
      gap: 80px;
    }
  
    .section-container {
      gap: 32px;
      padding-inline: 91px;
      padding-block: 24px;
    }
  
    .chip-wrapper {
      gap: 16px;
    }
  }

/* desktop */
@media screen and (min-width: 992px) {
    .content-wrapper{
        width: 100%;
    }
}

/* large desktop */
@media screen and (min-width: 1800px) {
    .content-wrapper{
        width: 80%;
        margin: 0 auto;
    }
}

/* x-large desktop */
@media screen and (min-width: 2400px) {
    .content-wrapper{
        width: 70%;
        margin: 0 auto;
    }
}

</style>
  