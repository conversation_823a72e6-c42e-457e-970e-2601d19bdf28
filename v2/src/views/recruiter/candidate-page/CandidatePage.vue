<template>
  <main class="container padding-container">
    <div class="btns-top-container">
      <PrimaryNormalButton
        textContent="Retour"
        btnColor="secondary"
        back
        @click="$router.go(-1)"
      />
      <div class="btn-wrapper">
        <PrimaryRoundedButton
          v-if="candidate.type_user === 'recruiter'"
          textContent="Contacter le recruteur"
          @btn-click="startChat(candidate)"
        />
        <PrimaryRoundedButton
          v-else
          textContent="Contacter le candidat"
          @btn-click="startChat(candidate)"
        />
        <PrimaryRoundedButton
          textContent="Appel vidéo"
          @btn-click="startVideoCall(candidate)"
          btnColor="secondary"
        />
      </div>
    </div>
    <RecrutierPresentationCard
      v-if="candidate.type_user === 'recruiter'"
      :user="candidate"
    />

    <!-- Section with the candidate's cv -->
    <div class="cvs-container">
      <!-- Custom Thanks-Boss cv -->
      <CvTemplatePrintVersion ref="CvTemplate" :user="candidate" :isFriend="isFriend" @download-cv="handleDownloadCvTemplate" />
      <MyContactLinks v-if="contactLinks.length > 0" :links="contactLinks" />
      <!-- Preview of the candidate's cv -->
      <section class="avis-and-pdf-container">
        <CvPreview
          class="cv-preview"
          v-if="candidate.default_cv && candidate.default_cv.file"
          :pdfUrl="getImgPath(candidate.default_cv.file)"
          :candidate="candidate"
        />
        <MyRecommendations
          class="my-recommendations"
          v-if="candidate && candidate.id"
          :user="candidate"
        />
      </section>
    </div>
    <!-- Button to scroll back to the top of the page -->
    <BackToTopArrow />
    <!-- Version pour l'export du CV en format PDF -->
  </main>
</template>

<script>
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import RecrutierPresentationCard from '@/components/cards/recruiter/RecrutierPresentationCard.vue';
  import CvPreview from '@/components/views-models/profil/CvPreview.vue';
  import CvTemplatePrintVersion from '@/components/views-models/profil/CvTemplatePrintVersion.vue';
  import MyContactLinks from '@/components/views-models/profil/MyContactLinks.vue';
  import MyRecommendations from '@/components/views-models/profil/MyRecomendations.vue';
  import { getUserById } from '@/services/account.service.js';
  import getImgPath from '@/utils/imgpath.js';
  import gotoPage from '@/utils/router.js';
  import { downloadCvTemplate } from '@/utils/userUtilities.js';
  import { initiateVideoCall } from '@/services/video-call.service';
  import { axiosInstance } from '@/services/axios.js';

  export default {
    name: 'CandidatePage',

    components: {
      RecrutierPresentationCard,
      BackToTopArrow,
      PrimaryNormalButton,
      PrimaryRoundedButton,
      CvTemplatePrintVersion,
      CvPreview,
      MyContactLinks,
      MyRecommendations,
    },

    props: {
      // Indicates if the user is in recruiter mode
      userIsRecruter: {
        type: Boolean,
        default: false,
      },
    },

    data() {
      return {
        jobofferIsFavorite: false,
        showSkills: true,
        showSoftSkills: true,
        showLogo: true,
        candidate: {},
        contactLinks: [],
        isFriend: false,
      };
    },

    async mounted() {
      this.scrollToTop();
      this.candidate = await getUserById(this.$route.params.id);
      this.contactLinks = this.buildContactLinks(this.candidate);

      // Vérifier si le candidat est un ami
      await this.checkIfCandidateIsFriend();

      console.log('CandidatePage - mounted - isFriend:', this.isFriend);
    },

    methods: {
      getImgPath,
      gotoPage,
      downloadCvTemplate,

      /**
       * Démarre un chat avec l'utilisateur
       * @param {Object} user - L'utilisateur avec qui démarrer un chat
       */
      startChat(user) {
        if (!user || !user.id) return;

        // Rediriger vers la page de messagerie avec l'ID de l'utilisateur
        this.$router.push({
          path: '/messagerie',
          query: { chat: user.id },
        });
      },

      /**
       * Démarre un appel vidéo avec l'utilisateur
       * @param {Object} user - L'utilisateur à appeler
       */
      startVideoCall(user) {
        if (!user || !user.id) return;

        // Utiliser le service d'appel vidéo pour initier l'appel
        initiateVideoCall(user);
      },
      async handleDownloadCvTemplate() {
        const cvComponent = this.$refs.CvTemplate;
        if (!cvComponent) {
          //console.error('Référence du CV introuvable !');
          return;
        }

        const cvElement = cvComponent.$refs.cvContainer;
        if (!cvElement) {
          //console.error("L'élément du CV est introuvable !");
          return;
        }

        // Appliquer la classe temporaire pour le format PDF
        cvElement.classList.add('pdf-mode');

        try {
          // Attendre la fin de la génération du PDF
          await downloadCvTemplate(this.candidate, cvElement);
        } catch (err) {
          //console.error('Erreur lors de la génération du PDF:', err);
        } finally {
          // Supprimer la classe temporaire
          cvElement.classList.remove('pdf-mode');
        }
      },
      scrollToTop() {
        window.scrollTo(0, 0);
      },
      buildContactLinks(user) {
        return [
          user.linkedin,
          user.instagram,
          user.facebook,
          user.site_url,
          user.autre_url,
          user.porfolio_url,
          user.tiktok,
        ].filter((link) => link && link.trim() !== '');
      },

      /**
       * Vérifie si le candidat est un ami de l'utilisateur connecté
       */
      async checkIfCandidateIsFriend() {
        try {
          // Utiliser directement l'API /friends
          const response = await axiosInstance.get('/friends');
          const friends = response.data.friends;

          if (Array.isArray(friends)) {
            // Vérifier si l'utilisateur est déjà ami
            this.isFriend = friends.some((friend) => {
              // Convertir les IDs en nombres pour la comparaison
              const friendId = Number(friend.id);
              const candidateId = Number(this.candidate.id);

              return friendId === candidateId;
            });
          } else {
            this.isFriend = false;
          }
        } catch (error) {
          this.isFriend = false;
        }
      },
    },
  };
</script>

<style scoped>
  .hidden {
    display: none;
  }
  .cvs-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    width: 100%;
  }
  section.avis-and-pdf-container {
    width: 100%;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    gap: 30px;
  }
  .cv-preview,
  .my-recommendations {
    width: 50%;
  }
  .v-text-field {
    height: auto; /* Ajuste la hauteur automatiquement en fonction du contenu */
  }
  .v-text-field .v-input__control {
    white-space: normal; /* Ou 'nowrap' si vous préférez */
    overflow-wrap: break-word; /* Permet de casser les mots longs */
    word-break: break-word; /* S'assure que les mots longs sont bien cassés à la ligne */
  }
  .padding-container {
    padding-inline: 3vw;
  }
  /* Grid layout for items */
  .grid-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 40px;
  }

  .grid-item {
    display: flex;
    flex-direction: column;
  }

  .location {
    grid-column: span 2; /* Spans across both columns */
  }

  /* Main container layout */
  .container {
    padding-bottom: 50px;
    display: flex;
    flex-direction: column;
    gap: 30px;
  }
  .container .content-wrapper {
    display: flex;
    flex-direction: column;
    gap: 30px;
    margin: 0 auto;
    width: 100%;
  }

  /* Back button container */
  .btns-top-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    width: 100%;
    margin: 0 auto;
    justify-content: space-between;
  }
  .buttons {
    display: flex;
    margin-bottom: -20px;
  }
  .btn-wrapper {
    width: fit-content;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
  .btn-wrapper button {
    width: fit-content;
  }
  /* Container for section with background and padding */
  .section-container {
    background-color: var(--surface-bg-2);
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: 100%;
    border-radius: 5px;
    padding-inline: 10px;
    padding-block: 24px;
  }

  /* Header section with spacing and alignment */
  .section-header {
    background-color: var(--surface-bg-2);
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    padding-inline: 16px;
    padding-block: 18px;
    display: flex;
    justify-content: space-between;
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
  }

  /* Body section with grid layout */
  .section-body {
    background-color: var(--text-1);
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    padding: 8px;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 10px;
    width: 100%;
  }

  /* Footer section with centered buttons */
  .section-footer {
    display: flex;
    justify-content: center;
    gap: 30px;
    width: 100%;
    padding-top: 60px;
  }

  /* Wrapper styles for different sections */
  .company-title-wrapper {
    display: flex;
    flex-direction: column;
    gap: 40px;
    align-items: center;
    margin-left: 26px;
  }

  .company-logo-wrapper {
    width: fit-content;
    height: fit-content;
  }

  .title-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .favorite-wrapper {
    width: fit-content;
    height: fit-content;
  }

  .chip-wrapper {
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    gap: 5px;
  }

  .question-wrapper {
    display: flex;
    flex-direction: column;
  }

  .tags-wrapper {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    padding: 16px;
    display: flex;
    gap: 10px;
    width: 100%;
    justify-items: start;
    flex-direction: row;
    justify-content: space-between;
  }

  /* Utility styles for tags */
  .logo {
    border: 1px solid rgba(223, 219, 214, 1);
    width: 92px;
    height: 92px;
    border-radius: 2px;
  }

  .tag {
    background-color: var(--surface-bg-2);
    padding: 2px;
    border-radius: 5px;
    display: flex;
    justify-content: start;
    align-items: center;
    gap: 5px;
    width: 110px;
    height: 35px;
  }

  .tag2 {
    padding: 2px;
    border-radius: 5px;
    display: flex;
    justify-content: start;
    align-items: center;
    gap: 10px;
    width: 110px;
    height: 35px;
  }

  .fs12 {
    font-size: 10px;
  }

  .favicon {
    cursor: pointer;
  }

  /* Responsive styles */
  @media screen and (min-width: 992px) {
    .company-title-wrapper {
      flex-direction: row;
      align-items: center;
    }

    .title-wrapper {
      text-align: initial;
    }

    .align-start {
      align-self: baseline;
    }

    .section-body,
    .tags-wrapper {
      display: flex;
      padding: 16px;
      justify-content: space-between;
      gap: 0px;
    }

    .tag {
      padding: 4px;
      gap: 10px;
      width: 124px;
      height: 42px;
    }

    .tag2 {
      padding: 4px;
      gap: 10px;
      width: 124px;
      height: 42px;
    }

    .fs12 {
      font-size: 12px;
    }

    .section-footer {
      gap: 80px;
    }

    .section-container {
      gap: 32px;
      padding-inline: 91px;
      padding-block: 24px;
    }

    .chip-wrapper {
      gap: 16px;
    }
  }
</style>
