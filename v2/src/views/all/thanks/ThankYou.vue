<template>   
    <div class="thank-you-page">
        <img src="@/assets/logo-tb.svg" alt="logo" class="img"/>
    
      <h1>Merci pour votre inscription chez Thanks-Boss !</h1>
      <p>Vous serez redirigé vers la page de connexion dans quelques secondes...</p>
    </div>
  </template>
  
  <script>
  export default {
    name: 'ThankY<PERSON>',
    mounted() {        
      setTimeout(() => {
        this.$router.push('/connexion');
      }, 10000); 
    },
  };
  </script>
  
  <style>
  .img{
      width: 10%;
      margin-bottom: 3rem;
  }
  .thank-you-page {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    text-align: center;
  }
  </style>
  