<template>
  <main class="container padding-container">
    <!-- Fond sombre -->
    <div
      v-if="confirmationModalIsOpened"
      class="overlay"
      @click="confirmationModalIsOpened = false"
    ></div>

    <h1>Mes alertes</h1>

    <!-- alert panel creation -->
    <div v-if="alertPanelOn" class="alert-panel-wrapper">
      <CreationAlerteProfiles
        v-if="user.type_user === 'recruiter'"
        @close-alert-panel="toggleAlertPanel"
        :alert="selectedProfileAlert"
        :alertIndex="alertIndex"
      />
      <Alert
        v-else
        :alertId="alertId"
        @close-alert-panel="toggleAlertPanel"
        :alertIndex="alertIndex"
      />
    </div>

    <!-- alerts panel browsing-->
    <div v-else>
      <div class="btn-container">
        <PrimaryNormalButton
          textContent="Ajouter une alerte"
          btnColor="secondary"
          @click="toggleAlertPanel"
          add
        />
      </div>

      <div class="content-container">
        <!-- filter panel -->
        <div class="left-content border-radius-15">
          <CustomAccordion
            field=""
            title="Trier par nom"
            :fields="['A - Z', 'Z - A']"
            @checkbox-state="sortByName"
            :chips="false"
          />
          <CustomAccordion
            field=""
            title="Trier par date"
            :fields="['Plus récent', 'Plus ancien']"
            @checkbox-state="sortByDate"
          />
        </div>
        <!-- TODO: verifier si c'est necesaire de faire des cartes des alertes specific pour recruiter -->
        <aside v-if="user.type_user === 'recruiter'" class="right-content">
          <ProfileAlertCard
            v-for="(alert, index) in alertList"
            :alert="alert"
            :key="index"
            @toggle-alert-panel="toggleProfilesAlertPanel"
            @toggle-alert-activation="toggleAlertProfileActivation"
            :alertIndex="index"
          />
        </aside>
        <aside v-else class="right-content">
          <!-- Message si aucune alerte -->
          <p v-if="!alertList.length" class="no-message">
            Tu n'as aucune alerte pour le moment. <br />
            Ajoute ta première alerte
            <a class="link" @click="toggleAlertPanel">ici</a>
            pour débuter.
          </p>
          <AlertCard
            v-for="(alert, index) in alertList"
            :alert="alert"
            :key="index"
            @toggle-alert-panel="toggleAlertPanel"
            :alertIndex="index"
          />
        </aside>
      </div>
    </div>

    <ConfirmationModal
      v-if="confirmationModalIsOpened"
      class="modal-container"
      title="Suppression"
      description="Êtes-vous sûr de vouloir supprimer ce job de votre liste de favoris ?"
      @confirm="deleteAlert"
      @close="confirmationModalIsOpened = false"
    />
  </main>

  <BackToTopArrow />
</template>

<script>
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import CustomAccordion from '@/components/buttons/CustomAccordion.vue';
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import AlertCard from '@/components/cards/AlertCard.vue';
  import ProfileAlertCard from '@/components/cards/ProfileAlertCard.vue';
  import CreationAlerteProfiles from '@/components/modal/alert/recruiter/CreationAlerteProfiles.vue';
  import ConfirmationModal from '@/components/modal/confirmation/ConfirmationModal.vue';
  import Alert from '@/components/views-models/search/Alert.vue';
  import { modifyProfileAlert } from '@/services/alert-recruiter.service';

  export default {
    name: 'AlertsPage',

    components: {
      AlertCard,
      ProfileAlertCard,
      CustomAccordion,
      ConfirmationModal,
      PrimaryNormalButton,
      CreationAlerteProfiles,
      Alert,
      BackToTopArrow,
    },

    props: {
      user: { Object, required: false, default: () => {} },
    },

    data() {
      return {
        confirmationModalIsOpened: false, //  toggle for the modal on / off
        selectedFavoriteId: null, //  contain favorite id for deletion
        alertPanelOn: false, //  if an alert is in the stagging area for creation
        alertId: null, //  current opened alert
        alertIndex: null, //  current index of opened alert
        selectedProfileAlert: null,
      };
    },

    computed: {
      alertList() {
        if (this.user.type_user === 'recruiter') {
          return this.user.alerte_profils;
        } else {
          return this.user.alerte;
        }
      },
    },
    methods: {
      toggleAlertProfileActivation(alert) {
        const updatedAlert = { ...alert, active: !alert.active };

        modifyProfileAlert(alert.id, updatedAlert);

        const updatedAlerteProfils = this.user.alerte_profils.map(
          (existingAlert) => {
            if (existingAlert.id === alert.id) {
              return updatedAlert;
            }
            return existingAlert;
          }
        );

        //console.log('updatedAlerteProfils', updatedAlerteProfils);

        const userUpdated = {
          ...this.user,
          alerte_profils: updatedAlerteProfils,
        };

        this.$store.dispatch('handleUserChange', {
          type: null,
          payload: userUpdated,
        });
      },
      toggleProfilesAlertPanel(alert) {
        this.selectedProfileAlert = alert;
        this.alertPanelOn = !this.alertPanelOn;
      },
      //  toggle alert panel visibility and refresh alert list
      toggleAlertPanel(alertId, alertIndex) {
        if (alertId) {
          this.alertId = alertId;
          this.alertIndex = alertIndex;
        } else {
          this.alertId = null;
          this.alertIndex = null;
        }
        this.alertPanelOn = !this.alertPanelOn;
        if (!this.alertPanelOn) {
          window.scrollTo({ top: 0, behavior: 'smooth' });
          this.selectedProfileAlert = null;
        }
      },

      //  open delete favorite confirmation modal
      openConfirmationModal(id) {
        this.confirmationModalIsOpened = true;
        this.selectedFavoriteId = id;
      },

      //  delete favorite from database and store
      async deleteAlert() {
        try {
          //await removeFavoriteJob(this.selectedFavoriteId);
          //this.handleDeleteFavoris(this.selectedFavoriteId);
        } catch (error) {
          //console.log(error);
        } finally {
          this.confirmationModalIsOpened = false;
        }
      },

      //  sort by date
      sortBy(el, values, test) {
        //console.log('sort by', el, values, test);
        let selectedFilter = '';

        // find the value of the selected filter
        for (const [key, value] of Object.entries(values)) {
          if (value === true) {
            selectedFilter = key;
            break;
          }
        }

        // filter by selected filter
        if (selectedFilter === 'Plus récent') {
          ////console.log('Plus récent selected');
          this.favoritesList.sort((a, b) => {
            return (
              new Date(b.job_offer.created_at) -
              new Date(a.job_offer.created_at)
            );
          });
        } else if (selectedFilter === 'Plus ancien') {
          ////console.log('Plus ancien selected');
          this.favoritesList.sort((a, b) => {
            return (
              new Date(a.job_offer.created_at) -
              new Date(b.job_offer.created_at)
            );
          });
        }
      },
      getSelectedFilter(values) {
        let selectedFilter = '';

        // Parcours l'objet pour trouver la valeur qui est `true`
        for (const [key, value] of Object.entries(values)) {
          //console.log(value);
          if (value === true) {
            selectedFilter = key;
            break;
          }
        }

        return selectedFilter;
      },

      sortByDate(e, values) {
        const order = this.getSelectedFilter(values);
        // On determine la valeur de la date à utiliser en fonction du type d'utilisateur
        // car les alertes de recruteur ont une nom de cle pour la date différente
        const dateStringValue =
          this.user.type_user === 'recruiter' ? 'updated_at' : 'created_at';

        if (order === 'Plus récent') {
          this.alertList.sort(
            (a, b) =>
              new Date(b[dateStringValue]) - new Date(a[dateStringValue])
          );
        } else if (order === 'Plus ancien') {
          this.alertList.sort(
            (a, b) =>
              new Date(a[dateStringValue]) - new Date(b[dateStringValue])
          );
        }
      },

      sortByName(e, values) {
        const order = this.getSelectedFilter(values);
        if (order === 'A - Z') {
          this.alertList.sort((a, b) =>
            a.nom
              ? a.nom.localeCompare(b.nom)
              : a.nom_alerte.localeCompare(b.nom_alerte)
          );
        } else if (order === 'Z - A') {
          this.alertList.sort((a, b) =>
            b.nom
              ? b.nom.localeCompare(a.nom)
              : b.nom_alerte.localeCompare(a.nom_alerte)
          );
        }
      },
    },
  };
</script>

<style scoped>
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Couleur semi-transparente */
    z-index: 999; /* Juste en dessous de la modale */
  }

  .modal-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000; /* Assurez-vous que la modale est au-dessus du contenu */
    width: 100%; /* Cela peut être ajusté selon vos besoins */
    max-width: 500px; /* Ajustez selon la taille de votre modale */
    padding: 20px; /* Optionnel, pour ajouter du padding autour de la modale */
    text-align: center;
  }

  .btn-container {
    display: flex;
    justify-content: end;
  }

  .content-container {
    display: flex;
    gap: 27px;
  }

  .left-content {
    height: fit-content;
    max-width: 231px;
    margin-top: 50px;
    background-color: var(--surface-bg-2);
    padding: 16px;
  }

  .left-content .container {
    margin-top: 0;
    margin-bottom: 10px;
  }
  aside {
    width: 100%;
    margin-top: 50px;
    margin-bottom: 30px;
  }
  .right-content {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fill, 258px);
    grid-gap: 27px;
  }
  .right-content .no-message {
    font-size: 1.2rem;
    color: #666;
    text-align: center;
    margin-top: 20px;
    grid-column: 1 / -1;
  }
  .right-content .no-message .link {
    color: var(--primary-1);
    font-weight: bold;
    text-decoration: underline;
    cursor: pointer;
  }

  /* ✅ MOBILE : petits smartphones (iPhone SE, etc.) */
  @media screen and (max-width: 480px) and (max-width: 767px) {
    .content-container {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .right-content {
      justify-content: center;
    }
    .left-content {
      display: flex;
      width: 100%;
      max-width: 100%;
      gap: 20px;
    }
    .left-content .container {
      width: 50%;
    }
    main {
      justify-items: center;
    }
    .btn-container {
      justify-content: center;
      margin-top: 30px;
    }
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }
</style>
