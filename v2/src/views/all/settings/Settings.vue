<template>
  <main class="container padding-container">
    <div class="content-wrapper">
      <header class="header-title">
        <h1 id="edition-profil">Mes param<PERSON>tres</h1>
        <PrimaryNormalButton
          textContent="Retour"
          btnColor="secondary"
          back
          @click="$router.go(-1)"
        />
      </header>

      <section>
        <!-- Password Section -->
        <div class="settings-container settings-password-container">
          <v-form v-model="validPassword">
            <h5>Mon mot de passe</h5>
            <p class="settings-container-description">
              Modifier mon mot de passe
              <span class="text-regex"
                >(Au moins 4 caractères, incluant une majuscule, une minuscule,
                un chiffre, et un caractère spécial)</span
              >
            </p>
            <div class="settings-container-inputs">
              <!-- Nouveau champ pour l'ancien mot de passe -->
              <v-text-field
                label="Ancien mot de passe"
                :type="showOldPassword ? 'password' : 'text'"
                :rules="[...passwordRules, ...notEmptyRules]"
                v-model="formData.old_password"
                :append-inner-icon="showOldPassword ? 'mdi-eye' : 'mdi-eye-off'"
                @click:append-inner="showOldPassword = !showOldPassword"
                hide-details
                required
              />

              <v-text-field
                label="Nouveau mot de passe"
                :type="showPassword ? 'password' : 'text'"
                :rules="[...passwordRules, ...notEmptyRules]"
                v-model="formData.new_password1"
                :append-inner-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
                @click:append-inner="showPassword = !showPassword"
                hide-details
                required
              />

              <v-text-field
                label="Confirmer mot de passe"
                :type="showPassword2 ? 'password' : 'text'"
                :rules="[...passwordRules, ...notEmptyRules]"
                v-model="formData.new_password2"
                :append-inner-icon="showPassword2 ? 'mdi-eye' : 'mdi-eye-off'"
                @click:append-inner="showPassword2 = !showPassword2"
                hide-details
                required
              />
            </div>
            <div class="settings-container-buttons">
              <PrimaryNormalButton
                textContent="Enregistrer"
                btnColor="secondary"
                @click="submitPasswordChange"
              />
            </div>
          </v-form>
        </div>

        <!-- Email update section -->
        <div class="settings-container settings-email-container">
          <v-form>
            <h5>Mon email</h5>
            <p class="settings-container-description">Modifier mon email</p>
            <div class="settings-container-inputs">
              <v-text-field
                :label="`Ancien email : ${user.email}`"
                :value="user.email"
                hide-details
                disabled
                required
              />
              <v-text-field
                label="Nouveau email"
                :rules="[...emailRules, ...notEmptyRules]"
                v-model="formData.new_email"
                hide-details
                required
              />
            </div>
          </v-form>
          <div class="settings-container-buttons">
            <PrimaryNormalButton
              textContent="Enregistrer"
              btnColor="secondary"
              @click="submitEmailChange"
            />
          </div>
        </div>

        <!-- Notifications section -->
        <div class="settings-container settings-preference-container">
          <h5>Préférences de notifications par email</h5>
          <div class="d-flex settings-preference-main-container">
            <!-- left container -->
            <div class="settings-preference-switch-container d-flex a-center">
              <div class="settings-preference-switch custom-switch">
                <p>Tout activer</p>
                <CustomSwitch
                  v-model="notificationsEnabled"
                  class="d-flex a-center"
                  label=""
                  :value="true"
                  @click="activateAll"
                />
              </div>

              <!--   <div class="settings-preference-switch custom-switch">
              <p>Réseau social</p>
              <CustomSwitch 
              v-model="socialEnabled"
              class="d-flex a-center"
              label=""
              value="1"  
              @click="activateSocial"                  
              />
            </div>-->
            </div>

            <!-- right container -->
            <div
              class="settings-preference-switch-container d-flex f-col a-center"
            >
              <!-- <div class="settings-preference-switch">
              <p>Changement de statut candidature</p>
              <CustomSwitch 
              v-model="statutCandidatureEnabled"
              class="d-flex a-center"
              label=""
              value="1"
              @click="activatestatutCandidature"
              />
            </div>-->
              <div class="settings-preference-switch">
                <p>Alertes</p>
                <CustomSwitch
                  class="d-flex a-center"
                  label=""
                  :value="alertesEnabled"
                  v-model="alertesEnabled"
                  @change="toggleAlertes"
                />
              </div>
            </div>
          </div>

          <!-- newsletter buttons -->
          <div class="settings-preference-newsletter-buttons">
            <p>Mon abonnement à la newsletter</p>
            <PrimaryNormalButton
              textContent="Je suis abonné"
              @click="activate_newsletter"
            />
            <PrimaryNormalButton
              textContent="Je me désabonne"
              btnColor="secondary"
              class="settings-preference-secondary-button"
              @click="desactivate_newsletter"
            />
          </div>
        </div>

        <!-- Action buttons -->
        <div class="settings-preference-actions-buttons">
          <PrimaryNormalButton
            textContent="Supprimer mon compte"
            btnColor="secondary"
            @click="toggleConfirmationModal"
          />
        </div>

        <BackToTopArrow />
      </section>
    </div>
    <div v-if="modalOpen" class="confirmation-modal-container">
      <ConfirmationModal
        @close="toggleConfirmationModal"
        @confirm="handleDeleteAccount"
        :title="'Confirmation de suppression de compte'"
        :description="'Êtes-vous sûr de vouloir supprimer votre compte ? '"
      />
    </div>
  </main>
</template>

<script>
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import ConfirmationModal from '@/components/modal/confirmation/ConfirmationModal.vue';
  import CustomSwitch from '@/components/switch/CustomSwitch.vue';
  import { deleteAccount } from '@/services/account.service';
  import {
    activate_newletter,
    desactivate_newletter,
  } from '@/services/newsletter.service';
  import {
    emailchangetRequest,
    passworchangetRequest,
  } from '@/services/password.service';
  import { toaster } from '@/utils/toast/toast.js';
  import {
    validateEmail,
    validateNotEmpty,
    validatePassword,
  } from '@/utils/validationRules';

  export default {
    name: 'SettingsPage',
    components: {
      BackToTopArrow,
      CustomSwitch,
      PrimaryNormalButton,
      CustomSwitch,
      ConfirmationModal,
    },
    data() {
      return {
        modalOpen: false,
        formData: {},
        validPassword: false,
        showPassword: true,
        showPassword2: true,
        showOldPassword: true,
        notificationsEnabled: false,
        socialEnabled: false,
        statutCandidatureEnabled: false,
        alertesEnabled: false,
        emailRules: [(v) => validateEmail(v) || true],
        passwordRules: [(v) => validatePassword(v) || true],
        notEmptyRules: [(v) => validateNotEmpty(v) || true],
      };
    },
    props: {
      user: {
        type: Object,
        required: false,
        default: () => {},
      },
    },
    methods: {
      deleteAccount,
      toggleConfirmationModal() {
        this.modalOpen = !this.modalOpen;
      },
      async handleDeleteAccount() {
        try {
          await deleteAccount();
        } catch (error) {
          //console.log(error);
        }
      },
      // Activation/Désactivation des notifications globales
      activateAll() {
        //console.log(
        //  'Notifications générales avant:',
        //  this.notificationsEnabled
        //);
        //console.log('Alertes avant :', this.alertesEnabled);
        if (!this.notificationsEnabled || !this.alertesEnabled) {
          this.notificationsEnabled = true;
          this.socialEnabled = true;
          this.statutCandidatureEnabled = true;
          this.alertesEnabled = true;

          this.saveSettings(); // Sauvegarde de l'état après modification
          //console.log(
          //  'toutes les alertes sont activées:',
          //  this.notificationsEnabled
          //);
          ////console.log(
          //  'les alertes sociables sont activées:',
          //  this.socialEnabled
          //);
          ////console.log(
          //  'les alertes statut candidats sont activées:',
          //  this.statutCandidatureEnabled
          //);
          ////console.log(
          //  'les alertes globales sont activées:',
          //  this.alertesEnabled
          //);
        }
      },

      // Activation/Désactivation des alertes
      toggleAlertes(value) {
        //console.log('Alertes avant :', this.alertesEnabled);
        this.alertesEnabled = value;
        this.saveSettings();
        //console.log('Alertes maintenant :', this.alertesEnabled);
      },

      // Sauvegarde de l'état dans LocalStorage
      saveSettings() {
        localStorage.setItem('notificationsEnabled', this.notificationsEnabled);
        localStorage.setItem('alertesEnabled', this.alertesEnabled);
        //console.log('changements enregistrés:', this.notificationsEnabled);
      },

      // Chargement de l'état depuis LocalStorage
      loadSettings() {
        const notificationsState = localStorage.getItem('notificationsEnabled');
        const alertesState = localStorage.getItem('alertesEnabled');

        this.notificationsEnabled = notificationsState === 'true';
        this.alertesEnabled = alertesState === 'true';
      },

      /*activateAll() {
        this.notificationsEnabled = true;
        this.socialEnabled = true;        
        this.statutCandidatureEnabled = true;
        this.alertesEnabled = true;
        //console.log('toutes les alertes sont activées:',this.notificationsEnabled);
        //console.log('les alertes sociables sont activées:',this.socialEnabled);
        //console.log('les alertes statut candidats sont activées:',this.statutCandidatureEnabled);
        //console.log('les alertes globales sont activées:',this.alertesEnabled);
      },*/

      /* activateactivateAlerte() {
        
        this.alertesEnabled = true;
        //console.log('les alertes globales sont activées:',this.alertesEnabled);
      },*/
      resetPasswordForm() {
        this.formData.old_password = '';
        this.formData.new_password1 = '';
        this.formData.new_password2 = '';
        this.validPassword = false;
      },

      async submitPasswordChange() {
        if (this.validPassword) {
          try {
            const response = await passworchangetRequest(this.formData);
            //console.log(response);
            if (response) {
              this.resetPasswordForm(); // Réinitialiser les champs après la soumission réussie
              toaster.showSuccessPopup(
                'Votre mot de passe a été modifié avec succès.'
              );
            }
          } catch (error) {
            //console.log(error);
          }
        }
      },

      async submitEmailChange() {
        try {
          const response = await emailchangetRequest(this.formData);
          if (response) {
            toaster.showSuccessPopup('Votre email a été modifié avec succès.');
          }
        } catch (error) {
          //console.log(error);
        }
      },

      async desactivate_newsletter() {
        try {
          const response = await desactivate_newletter();
          if (response) {
            toaster.showSuccessPopup(
              'Votre abonnement à la newsletter est désactivé.'
            );
          }
        } catch (error) {
          //console.log(error);
        }
      },

      async activate_newsletter() {
        try {
          const response = await activate_newletter();
          if (response) {
            toaster.showSuccessPopup(
              'Votre abonnement à la newsletter est activé.'
            );
          }
        } catch (error) {
          //console.log(error);
        }
      },
    },
    mounted() {
      this.loadSettings();
    },
  };
</script>
<style scoped>
  .confirmation-modal-container {
    position: fixed;
    inset: 0;
    z-index: 999;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.5);
  }
  header.header-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  section {
    width: 100%;
    height: fit-content;
    margin-top: 20px;
    margin-bottom: 30px;
    /* display: grid;
    grid-template-columns: 1fr 1fr;
    row-gap: 28px;
    column-gap: 80px; */
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  h5 {
    height: 40px;
  }
  .v-input__slot {
    flex-direction: row-reverse !important;
    justify-content: flex-end;
  }

  .v-input--selection-controls__input {
    margin-right: 0;
    margin-left: 8px;
  }

  .v-input--selection-controls__input {
    margin-left: 0;
    margin-right: 8px;
  }
  .settings-password-container {
    background-color: aquamarine;
  }
  .settings-email-container {
    background-color: blanchedalmond;
  }
  .settings-preference-container {
    grid-column: 1 / 3;
  }

  .settings-container-description {
    padding: 8px 8px 8px 5px;
    margin-block: 4px;
  }

  .settings-container-inputs {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .settings-container-newsletter-buttons {
    text-align: right;
    margin-top: 8px;
  }

  .settings-preference-main-container {
    gap: 25vw;
  }

  .settings-preference-switch-container {
    width: 100%;
    padding-left: 5px;
    padding: 8px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  /* Section des switches (préférences de notifications) */
  .settings-preference-switch {
    display: flex;
    align-items: center;
    justify-content: flex-start; /* Aligne le texte et le switch à gauche */
    width: 100%;
    margin-bottom: 0; /* Supprime tout espacement inférieur entre les éléments */
    gap: 5px; /* Réduit l'écart entre le texte et le bouton */
  }

  .settings-preference-switch p {
    margin: 0; /* Supprime les marges */
    padding-right: 5px; /* Ajoute un petit espace à droite du texte */
    font-size: 14px; /* Ajuste la taille du texte si nécessaire */
  }

  /* Alignement et espacement général */
  .custom-switch {
    display: flex;
    align-items: center;
    gap: 5px; /* Réduit l'écart entre les éléments du switch */
  }

  /* Optionnel : Réduction de l'espacement global entre les sections */
  .settings-preference-main-container {
    margin-top: 0;
    padding-top: 0;
    gap: 10px; /* Réduire l'espace entre les containers */
  }

  .settings-preference-switch-container {
    padding: 0; /* Enlever le padding interne */
    margin: 0; /* Enlever le margin externe */
  }

  /* newsletter btns */
  .settings-preference-newsletter-buttons p {
    margin-block: 10px;
  }
  .settings-preference-secondary-button {
    margin-left: 20px;
  }

  /* email and password btns actions */
  .settings-container-buttons {
    margin-top: 10px;
  }

  /* action btns */
  .settings-preference-actions-buttons {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    grid-column: 1 / 3;
  }

  /* container template */
  .settings-container {
    padding: 16px;
    background-color: var(--surface-bg-2);
  }

  .text-regex {
    color: #757472;
    font-size: 11px;
  }
  /* styles de la page mon profil */
  .head-message {
    font-size: 1.1rem;
  }
  .custom-progress {
    border: solid 2px black;
  }
  .content-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%;
  }
  a.incomplete-infos-notification {
    display: flex;
    background-color: rgba(254, 106, 106, 0.444);
    width: fit-content;
    padding: 0 10px;
    border: solid 1px red;
    border-radius: 10px;
    font-size: 1rem;
    text-decoration: none;
    color: black;
    margin-top: 10px;
    transition: all 0.3s ease-in-out;
  }
  a.incomplete-infos-notification:hover {
    background-color: rgba(254, 106, 106, 0.273);
  }
  /* TODO : supprimer ? Styles pour donner style de button a un lien... */
  /* .edition-profile-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    background-color: var(--black-100);
    color: var(--white-100);
    font-size: 1rem;
    cursor: pointer;
    transition:
      background-color 0.2s,
      color 0.2s;
  }
  .edition-profile-link img {
    margin-left: 10px;
  } */
  .default-cv {
    padding-inline: 24px;
    padding-block: 16px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    background-color: var(--surface-bg-2);
  }

  .informations {
    background-color: rgba(88, 160, 150, 0.2);
    padding-block: 10px;
    padding-inline: 8px;
    border: 5px;
  }

  /* buttons */
  .import-btn {
    border-radius: 5px;
    border: 1px solid rgba(246, 179, 55, 1);
  }

  /* content */
  .title-import {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }
</style>
