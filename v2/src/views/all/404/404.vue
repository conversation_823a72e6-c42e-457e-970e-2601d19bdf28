<template>

    <main class="container">

        <div class="top-section">
            <div class="title-wrapper">
                <h1 class="title">404</h1>
            </div>
        </div>

        <div class="bot-section">

            <div class="content-wrapper">
                <p class="text">La page que vous cherchez semble introuvable.</p>

                <div @click="gotoPage('/')">
                    <PrimaryNormalButton textContent="Retour à l'accueil" />
                </div>
            </div>

        </div>

    </main>
    
</template>

<script setup>
import gotoPage from '@/utils/router';
</script>

<script>
import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';

export default {
    name: '404',

    components: {
        PrimaryNormalButton,
    },
}
</script>

<style scoped>
.container {
    position: absolute;
    top: 0;
    left: 0;
    padding: 0;
    margin: 0;
    height: 100vh;
    z-index: 200;
    overflow: hidden;
}

.top-section {
    height: 50%;
}

.title-wrapper {
    display: flex;
    justify-content: center;
    align-items: end;
    height: 100%;
}

.title {
    font-size: 200px;
}

.bot-section {
    margin-top: -70px;
    width: 100%;
    height: 60%;
    background-color: var(--black-100);
}

.content-wrapper {
    display: flex;
    height: 100%;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 80px;
}

.text {
    color: var(--white-200);
}
</style>
