<template>
  <main>
    <section class="hero">
      <img
        v-if="sectionType == 'candidate'"
        src="@/assets/blog/section-candidate-header-clipped.png"
        alt="header image"
        class="header-img"
      />
      <img
        v-else
        src="@/assets/blog/section-recruter-clipped.png"
        alt="header image"
        class="header-img"
      />

      <div class="padding-container hero-content">
        <h1 v-if="sectionType == 'candidate'">Section candidat</h1>
        <h1 v-else>Section recruteur</h1>

        <div class="text-container">
          <div class="text-wrapper">
            <p>
              Découvrez des conseils d'experts, des stratégies éprouvées et des
              ressources innovantes pour vous épanouir et atteindre vos
              objectifs professionnels. Avec "La vitrine IA", prenez le contrôle
              de votre carrière. Explorez nos articles pour trouver votre voie,
              celle qui correspond réellement à vos attentes et à vos
              aspirations. Nous vous aidons à construire une vie professionnelle
              plus épanouissante et alignée avec vos ambitions.
            </p>
          </div>
        </div>
      </div>
    </section>

    <section class="padding-container main-content">
      <div class="btn-container">
        <PrimaryNormalButton
          btnColor="secondary"
          textContent="Voir toutes les actus"
          prepend-icon="mdi-arrow-left"
          @click="gotoPage('/actualites')"
        />

        <p>
          Voici les dernières nouveautés pour vous aider dans votre recherche !
        </p>
      </div>

      <div v-for="(title, index) in titles" class="category">
        <h2>{{ title }}</h2>

        <div class="grid-section">
          <div class="card-wrapper" v-for="(article, index2) in news[index]">
            <NewsCardMini v-if="article != ''" :article="article" />
            <p v-else>Aucun article</p>
          </div>
        </div>
      </div>
    </section>

    <NewsLetter />

    <BackToTopArrow />
  </main>
</template>

<script setup>
  import gotoPage from '@/utils/router';
</script>

<script>
  import NewsCardMini from '@/components/cards/NewsCardMini.vue';
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import NewsLetter from '@/components/layout/newsletter/NewsLetter.vue';
  import { getArticleList } from '@/services/blog.service';

  export default {
    name: 'BlogSectionCandidate',

    components: {
      NewsCardMini,
      PrimaryNormalButton,
      NewsLetter,
      BackToTopArrow,
    },

    data() {
      return {
        sectionType: null, //  type of user from parameters
        news: [], //  list of lists of articles
        titles: [], //  section titles
      };
    },

    beforeMount() {
      this.scrollToTop();
    },

    async mounted() {
      //  get user type parameter
      this.sectionType = this.$route.params.sectiontype;

      //  static section titles for each user type
      if (this.sectionType == 'candidate')
        this.titles = ['Réussir son embauche', 'Décryptage', 'Success stories'];
      else this.titles = ['Tendances RH', `J'aime l'IA`, 'Recruter autrement'];

      //  fetch articles for each section
      for (let i = 0; i < this.titles.length; i++) {
        this.news[i] = await getArticleList('search', this.titles[i]);
      }

      //console.log(this.titles);
      //console.log(this.news);
    },

    methods: {
      //  scroll to top of the page
      scrollToTop() {
        window.scrollTo(0, 0);
      },
    },
  };
</script>

<style scoped>
  main {
    height: fit-content;
  }

  /* hero section with clipped banner */
  .hero {
    height: 450px;
  }

  .header-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .hero-content {
    position: relative;
    margin-top: -429px; /* adjustement for clipped banner */
    display: flex;
    flex-direction: column;
    gap: 40px;
  }

  .text-container {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .text-wrapper {
    background-color: rgba(255, 253, 252, 0.85);
    border-radius: 20px;
    padding: 16px;
    width: 80%;
  }

  /* main content, cards articles etc*/
  .main-content {
    background-color: var(--white-100);
    display: flex;
    flex-direction: column;
    gap: 60px;
    padding-block: 30px;
  }

  .btn-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .category {
    display: flex;
    flex-direction: column;
    gap: 40px;
  }

  .card-wrapper {
    width: 100%;
    display: flex;
  }

  @media screen and (min-width: 992px) {
    .hero {
      height: 336px;
    }

    .hero-content {
      margin-top: -315px; /* adjustement for clipped banner */
    }

    .header-img {
      object-fit: fill;
    }

    .btn-container {
      flex-direction: row;
      gap: 30px;
      align-items: center;
    }

    .grid-section {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 20px;
    }
  }

  @media screen and (min-width: 1800px) {
    .hero {
      height: 400px;
    }

    .hero-content {
      margin-top: -315px; /* adjustement for clipped banner */
    }

    .header-img {
      object-fit: fit;
    }

    h1 {
      text-align: center;
    }

    .text-container {
      max-width: 70%;
      margin: auto;
    }

    .main-content {
      max-width: 80%;
      margin: auto;
    }

    .btn-container {
      flex-direction: row;
      gap: 30px;
      align-items: center;
    }

    .grid-section {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 20px;
    }
  }
</style>
