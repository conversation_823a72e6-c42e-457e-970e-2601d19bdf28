<template>
  <main class="container">
    <div class="purchase-header">
      <h1>Votre commande</h1>
    </div>
    <PrimaryRoundedButton
      class="btn-retour"
      @click="previousStep"
      textContent="Retour"
      btnColor="secondary"
      back
    />
    <section class="purchase-container">
      <AchatRecap
        v-if="currentStep === 1"
        :activeSubscription="activeSubscription"
        :currentDate="currentDate"
        :isApplicant="isApplicant"
      />
      <AchatInfo
        v-else-if="currentStep === 2"
        :currentUser="currentUser"
        :isApplicant="isApplicant"
      />
      <AchatDevis
        v-else-if="currentStep === 3 && !isApplicant"
        :activeSubscription="activeSubscription"
        :currentUser="currentUser"
        :factures="factures"
        @modify="modify"
      />
      <AchatPaiement
        v-else-if="currentStep === 4"
        :currentUser="currentUser"
        :isApplicant="isApplicant"
        @modify="modify"
        @submit-stripe="submitStripe"
        @submit-paypal="submitPaypal"
      />

      <div class="purchase-container-right">
        <PurchaseCard :subscription="activeSubscription" />

        <div class="next-step-btn" v-if="currentStep < 4">
          <PrimaryRoundedButton
            textContent="Étape suivante"
            @click="nextStep"
          />
        </div>
      </div>
    </section>
    <PurchaseFooter />
  </main>
</template>

<script>
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import PurchaseCard from '@/components/cards/purchase-cards/PurchaseCard.vue';
  import PurchaseFooter from '@/components/cards/purchase-cards/PurchaseFooter.vue';
  import AchatDevis from '@/components/views-models/tunnel-achat/AchatDevis.vue';
  import AchatInfo from '@/components/views-models/tunnel-achat/AchatInfo.vue';
  import AchatPaiement from '@/components/views-models/tunnel-achat/AchatPaiement.vue';
  import AchatRecap from '@/components/views-models/tunnel-achat/AchatRecap.vue';
  import { submitStripeSubscription } from '@/services/subscription.service.js';
  import gotoPage from '@/utils/router.js';
  import { mapGetters, mapActions } from 'vuex';

  export default {
    components: {
      PurchaseCard,
      PurchaseFooter,
      PrimaryRoundedButton,
      AchatRecap,
      AchatInfo,
      AchatDevis,
      AchatPaiement,
    },

    data() {
      return {
        currentStep: 1,
        currentDate: new Date().toISOString(), // ex: "2025-03-24T13:25:43.511Z"
        nomAbonnement: '',
        loading: false,
      };
    },

    computed: {
      ...mapGetters(['getUser', 'userRole', 'isLoggedIn']),
      ...mapGetters('subscription', ['getFacture']),

      currentUser() {
        return this.getUser || {};
      },
      isApplicant() {
        return this.currentUser?.type_user === 'applicant';
      },
      activeSubscription() {
        return this.$store.getters['subscription/getActiveSubscription'] || {};
      },
      factures() {
        return this.getFacture?.results || [];
      },
    },

    mounted() {
      if (this.$route.query.resume === 'true') {
        this.currentStep = this.isApplicant ? 4 : 3;
      }
    },

    methods: {
      ...mapActions({
        createFacture: 'subscription/createFacture',
        fetchFactures: 'subscription/fetchFactures',
      }),

      previousStep() {
        if (this.currentStep === 1) {
          // Si on est à la première étape, on retourne à la page précédente
          this.gotoPage('/tarifs');
        } else {
          // Sinon on recule d'une étape dans le stepper
          if (this.isApplicant && this.currentStep === 4) {
            this.currentStep = 2; // Retourner à l'étape 2 pour les candidats
          } else {
            this.currentStep--;
          }
        }
      },
      modify() {
        this.currentStep = 2;
      },
      async nextStep() {
        if (this.currentStep === 2) {
          if (this.isApplicant) {
            this.currentStep = 4; // pour candidat, on saute le devis
          } else {
            // 👇 On est entre l'étape 2 et 3 pour recruteur → enregistrer la facture
            await this.saveInvoice();
            this.currentStep = 3;
          }
          return;
        }

        // Passer à l'étape suivante normalement
        if (this.currentStep < 4) {
          this.currentStep++;
          //console.log('this.currentStep', this.currentStep);
        }
      },
      async saveInvoice() {
        const invoiceData = {
          user: this.currentUser.id, // ID de l'utilisateur
          date: this.currentDate, // Date actuelle
          type_produit: this.activeSubscription.id, // Provenant de l'abonnement
          entreprise: this.currentUser.company, // Nom de l'entreprise
          prix_total_ht: this.activeSubscription.prix_ht, // Prix HT de l'abonnement
          prix_total_ttc: this.activeSubscription.prix_ttc, // Prix TTC de l'abonnement
          payer: false,
        };
        //console.log('Facture invoiceData :', invoiceData);

        try {
          await this.createFacture(invoiceData);
          //console.log('Facture enregistrée avec succès !');

          // 🔁 Rafraîchir les factures depuis le backend
          await this.fetchFactures();
          //console.log('Factures mises à jour !');
        } catch (error) {
          //console.error(
          //  "Erreur lors de l'enregistrement de la facture :",
          //  error
          //);
        }
      },
      gotoPage,
      // submitPaypal() {
      //   //console.log('PayPal submit called');
      // },
      async submitStripe() {
        try {
          //console.log(
          //  'Stripe submit called with active subscription:',
          //  this.activeSubscription.stripe_id
          //);

          const url = await submitStripeSubscription(
            this.activeSubscription.stripe_id
          );
          window.location.href = url; // Redirection vers Stripe
        } catch (error) {
          //console.error('Erreur lors du paiement avec Stripe :', error);
        }
      },
    },
  };
</script>

<style scoped>
  .container {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .purchase-header {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-bottom: 32px;
  }

  .btn-retour {
    width: fit-content;
  }

  .purchase-container {
    display: flex;
    flex-direction: row;
    justify-content: flex-start; /* Assurez-vous que les éléments se placent en ligne */
    width: 100%;
  }

  .purchase-container-left {
    flex-grow: 1; /* Permet à cette section de prendre toute la largeur disponible */
    margin-top: 55px;
    margin-right: 55px;
  }

  .purchase-container-right {
    flex-grow: 0.5;
    display: flex;
    flex-direction: column;
    margin-top: 55px;
  }

  .purchase-recap-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
  }

  .product-name {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .purchase-card-header {
    display: none;
  }

  hr {
    margin: 8px 0;
  }

  .info-display {
    display: flex;
    justify-content: space-between;
  }

  .hr-white {
    padding: 1px;
    background: var(--white-200);
    border: 0;
  }

  .purchase-container-right {
    width: 95%;
    display: flex;
    flex-direction: column;
    margin-top: 55px;
  }

  .info-link,
  .next-step-btn {
    align-self: center;
  }

  @media screen and (min-width: 992px) {
    .container {
      width: 80%;
      height: 100%;
      display: flex;
      flex-direction: column;
      margin-top: inherit;
      margin: auto;
    }

    .purchase-container {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: flex-start;
    }

    .purchase-header {
      width: 75%;
      margin: 16px auto;
    }

    .purchase-header-btn {
      align-self: flex-start;
    }

    .product-name {
      flex-direction: row;
    }

    .purchase-card-header {
      display: flex;
    }

    .purchase-card-header p,
    .purchase-card-header div {
      margin-right: 12px;
    }

    .purchase-card-header-title {
      margin: 8px;
      display: flex;
      align-items: center;
    }

    .purchase-card-header-number {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 8px;
      height: 24px;
      width: 24px;
      border: 1px solid black;
      border-radius: 50%;
      text-align: center;
    }

    .purchase-card-header-number-valid {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 8px;
      height: 24px;
      width: 24px;
      color: var(--white-200);
      background-color: var(--black-200);
      border-radius: 50%;
      text-align: center;
    }

    .purchase-container-right {
      width: 30%;
    }

    .info-link,
    .next-step-btn {
      align-self: flex-end;
      margin-right: 0;
      margin-top: 24px;
    }
  }
</style>
