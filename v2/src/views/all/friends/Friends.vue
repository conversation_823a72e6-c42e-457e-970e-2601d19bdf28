<template>
  <main class="container padding-container">
    <div class="content-wrapper">
      <!-- title -->
      <h1><PERSON></h1>
      <div class="friend-container">
        <div class="basic-filter-container border-radius-15">
          <!-- Groupe de boutons pour le filtre (catégories) en premier -->
          <div class="filter-group">
            <span class="filter-title">Afficher</span>
            <button
              v-for="option in filterOptions"
              :key="optionKey(option)"
              :class="[
                'filter-btn',
                { active: selectedTab === optionLabel(option) },
              ]"
              @click="setTab(option)"
            >
              <template v-if="typeof option === 'object'">
                {{ option.label }}
                <span v-if="option.count !== undefined" class="filter-count">{{
                  option.count
                }}</span>
              </template>
              <template v-else>
                {{ option }}
              </template>
            </button>
          </div>
          <!-- Groupe de boutons pour le tri, grisé et désactivé si 'Tous' -->
          <div
            class="filter-group"
            :class="{ 'disabled-group': selectedTab === 'Tous' }"
          >
            <span class="filter-title">Trier par nom</span>
            <button
              v-for="option in ['A - Z', 'Z - A']"
              :key="option"
              :class="['filter-btn', { active: sortOption === option }]"
              @click="selectedTab === 'Tous' ? null : setSortOption(option)"
              :disabled="selectedTab === 'Tous'"
            >
              {{ option }}
            </button>
          </div>
        </div>

        <aside>
          <template v-if="selectedTab === 'Tous'">
            <div class="see-all-profiles-bar grid-aligned-bar">
              <button class="see-all-profiles-btn" @click="goToCommunaute">
                Voir tous les profils
              </button>
            </div>
            <div class="friend-main-container">
              <CandidateGridDisplay :candidateList="allCandidates" />
              <div v-if="isLoadingMore" class="loader-container">
                <span class="loader-spinner"></span>
                <span style="margin-left: 8px">Chargement...</span>
              </div>
              <div
                v-if="!hasMoreCandidates && allCandidates.length > 0"
                class="end-message"
              >
                <span>Plus de profils à charger.</span>
              </div>
            </div>
          </template>
          <template v-else>
            <div
              v-if="!isLoading && filteredList.length === 0"
              class="empty-friends-message"
            >
              Vous n'avez pas encore d'amis dans votre réseau.
            </div>
            <div class="friend-main-container grid-wrapper grid-wrapper--fixed">
              <CompactProfilCard
                v-for="(friend, index) in filteredList"
                :key="index"
                :candidate="friend"
                :class="getCardBackgroundClass(friend.type)"
                @friend-removed="handleFriendRemoved"
              >
                <template v-if="friend.type === 'received'" #actions>
                  <div class="action-buttons">
                    <PrimaryRoundedButton
                      btnColor="secondary"
                      textContent="Ignorer"
                      class="decline-button"
                      @click="handleDeclineInvitation(friend.invitationId)"
                    />
                    <PrimaryRoundedButton
                      textContent="Accepter"
                      class="accept-button"
                      @click="handleAcceptInvitation(friend.invitationId)"
                    />
                  </div>
                </template>
              </CompactProfilCard>
            </div>
          </template>
        </aside>
      </div>
    </div>
  </main>

  <BackToTopArrow />
</template>

<script>
  import {
    friendsList,
    friendsInvitations,
    removeFriend,
    acceptFriendInvitation,
    declinedFriendInvitation,
  } from '/src/services/friend.service.js';
  import { getUserById } from '@/services/account.service';
  import { fetchChatUsers } from '@/services/messagerieApi';
  import CompactProfilCard from '@/components/cards/candidate-card/CompactProfilCard.vue';
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import CandidateGridDisplay from '@/components/views-models/search/CandidateGridDisplay.vue';
  import { searchCandidates } from '@/services/search.service.js';
  import { mapActions } from 'vuex';
  import { useMessagerieStore } from '@/store/messagerie';
  import { storeToRefs } from 'pinia';
  import { axiosInstance } from '@/services/axios';

  export default {
    name: 'FriendsPage',

    props: {
      user: { type: Object, default: null },
      isViewingAsRecruiter: { type: Boolean, default: false },
    },

    components: {
      CompactProfilCard,
      BackToTopArrow,
      PrimaryRoundedButton,
      CandidateGridDisplay,
    },

    data() {
      return {
        allFriends: [],
        filteredList: [],
        allCandidates: [],
        filterType: 'Mes amis',
        selectedTab: 'Mon réseau',
        isLoading: true,
        showAllCandidates: false,
        sortOption: 'A - Z',
        filterOptions: [
          'Tous',
          'Mon réseau',
          { label: 'Invitations reçues', count: this.receivedInvitationsCount },
          'Invitations envoyées',
        ],
        // Ajout pour lazy loading
        currentPage: 1,
        hasMoreCandidates: true,
        isLoadingMore: false,
        pageSize: 30,
      };
    },

    computed: {
      receivedInvitationsCount() {
        return this.allFriends.filter((friend) => friend.type === 'received')
          .length;
      },
      // Ajout d'une propriété calculée pour synchroniser l'accordéon avec l'URL
      checkedTab() {
        return [this.selectedTab];
      },
    },

    methods: {
      ...mapActions(['updateReceivedInvitationsCount']),

      // Retourne la classe CSS en fonction du type d'ami
      getCardBackgroundClass(type) {
        switch (type) {
          case 'received':
            return 'bg-received';
          case 'sent':
            return 'bg-sent';
          default:
            return '';
        }
      },

      // Charger toutes les données uniquement depuis le store Pinia
      async loadFriendsFromStore() {
        const messagerieStore = useMessagerieStore();
        const { usersLoaded, users } = storeToRefs(messagerieStore);
        if (!usersLoaded.value) {
          await messagerieStore.loadUsers();
        }
        this.allFriends = users.value.map((u) => ({ ...u, type: 'confirmed' }));
        const invitations = await friendsInvitations();
        // Invitations reçues : récupérer l'utilisateur expéditeur
        const received = await Promise.all(
          (invitations.received_invitations || []).map(async (inv) => {
            const userId = inv.sender || (inv.from_user && inv.from_user.id);
            let user = null;
            try {
              user = userId ? await getUserById(userId) : {};
            } catch (e) {
              user = {};
            }
            return Object.assign({}, inv, {
              id: userId,
              first_name: user.first_name,
              last_name: user.last_name,
              avatar: user.avatar,
              type: 'received',
              invitationId: inv.id,
              userId: userId,
              userData: user,
            });
          })
        );
        // Invitations envoyées : récupérer l'utilisateur destinataire
        const sent = await Promise.all(
          (invitations.sent_invitations || []).map(async (inv) => {
            const userId = inv.recipient || (inv.to_user && inv.to_user.id);
            let user = null;
            try {
              user = userId ? await getUserById(userId) : {};
            } catch (e) {
              user = {};
            }
            return Object.assign({}, inv, {
              id: userId,
              first_name: user.first_name,
              last_name: user.last_name,
              avatar: user.avatar,
              type: 'sent',
              invitationId: inv.id,
              userId: userId,
              userData: user,
            });
          })
        );
        this.allFriends = [...this.allFriends, ...received, ...sent];
        this.filteredList = this.allFriends;
        this.updateReceivedInvitationsCount();
        this.isLoading = false;
        this.applyFilter();
      },

      async loadAllCandidates(reset = false) {
        if (reset) {
          this.currentPage = 1;
          this.hasMoreCandidates = true;
          this.allCandidates = [];
        }
        if (!this.hasMoreCandidates || this.isLoadingMore) return;
        this.isLoadingMore = true;
        try {
          const { data } = await axiosInstance.get(
            `/best_profils/?page=${this.currentPage}&size=${this.pageSize}`
          );
          const results = data.results || [];
          if (!data.next) this.hasMoreCandidates = false;
          if (this.currentPage === 1) {
            this.allCandidates = results;
          } else {
            // Ajoute seulement les nouveaux
            const existingIds = new Set(this.allCandidates.map((c) => c.id));
            this.allCandidates = [
              ...this.allCandidates,
              ...results.filter((c) => !existingIds.has(c.id)),
            ];
          }
        } catch (error) {
          this.hasMoreCandidates = false;
        } finally {
          this.isLoadingMore = false;
          this.isLoading = false;
        }
      },

      // Appliquer un filtre
      async applyFilter(forceReload = false) {
        if (this.filterType === 'Tous') {
          // Affiche la CandidateGridDisplay, cache la liste d'amis/invitations
          this.showAllCandidates = true;
          this.filteredList = [];
          // Fetch all candidates if not already loaded or if forceReload
          if (forceReload || this.allCandidates.length === 0) {
            await this.loadAllCandidates();
          }
        } else {
          this.showAllCandidates = false;
          if (this.filterType === 'Mes amis') {
            this.filteredList = this.allFriends.filter(
              (friend) => friend.type === 'confirmed'
            );
          } else if (this.filterType === 'Invitations reçues') {
            this.filteredList = this.allFriends.filter(
              (friend) => friend.type === 'received'
            );
          } else if (this.filterType === 'Invitations envoyées') {
            this.filteredList = this.allFriends.filter(
              (friend) => friend.type === 'sent'
            );
          }
        }
      },

      // Utilitaires pour les boutons de filtre
      optionLabel(option) {
        return typeof option === 'object' ? option.label : option;
      },
      optionKey(option) {
        return typeof option === 'object' ? option.label : option;
      },
      setTab(option) {
        const label = this.optionLabel(option);
        this.selectedTab = label;
        if (label === 'Mon réseau') {
          this.filterType = 'Mes amis';
        } else if (label === 'Tous') {
          this.filterType = 'Tous';
          // Réinitialise la pagination et recharge la première page
          this.currentPage = 1;
          this.hasMoreCandidates = true;
          this.allCandidates = [];
          this.loadAllCandidates(true);
          this.$nextTick(() => this.tryLoadUntilScrollable());
        } else if (label === 'Invitations envoyées') {
          this.filterType = 'Invitations envoyées';
        } else if (label === 'Invitations reçues') {
          this.filterType = 'Invitations reçues';
        }
        if (label !== 'Tous') {
          this.applyFilter(true);
        }
      },
      setSortOption(option) {
        this.sortOption = option;
        if (option === 'A - Z') {
          this.filteredList.sort((a, b) =>
            a.last_name.localeCompare(b.last_name)
          );
        } else if (option === 'Z - A') {
          this.filteredList.sort((a, b) =>
            b.last_name.localeCompare(a.last_name)
          );
        }
      },

      async handleAcceptInvitation(invitationId) {
        try {
          // Accepter l'invitation via l'API
          await acceptFriendInvitation(invitationId);
          // 2. Trouver l’invitation
          const invitation = this.allFriends.find(
            (friend) => friend.invitationId === invitationId
          );

          // Retirer l'invitation de allFriends
          this.allFriends = this.allFriends.filter(
            (friend) => friend.invitationId !== invitationId
          );
          this.filteredList = this.filteredList.filter(
            (friend) => friend.invitationId !== invitationId
          );

          // Ajouter l'ami accepté aux amis confirmés dans allFriends
          if (invitation) {
            const confirmedFriend = { ...invitation, type: 'confirmed' };
            this.allFriends.push(confirmedFriend);

            // Si on est sur l'onglet "Mon réseau", on ajoute à la liste affichée
            if (this.filterType === 'Mes amis') {
              this.filteredList.push(confirmedFriend);
            }
          }

          // Mettre à jour le compteur d'invitations reçues
          this.updateReceivedInvitationsCount();
        } catch (error) {
          console.error(
            "Erreur lors de l'acceptation de l'invitation :",
            error
          );

          //console.error(
          //  "Erreur lors de l'acceptation de l'invitation :",
          //  error
          //);
        }
      },

      async handleDeclineInvitation(invitationId) {
        try {
          // Refuser l'invitation via l'API
          await declinedFriendInvitation(invitationId);

          // Mettre à jour allFriends pour retirer l'invitation
          this.allFriends = this.allFriends.filter(
            (friend) => friend.invitationId !== invitationId
          );

          // Mettre à jour filteredList
          this.filteredList = this.filteredList.filter(
            (friend) => friend.invitationId !== invitationId
          );

          // Mettre à jour le compteur d'invitations reçues
          this.updateReceivedInvitationsCount();
          this.loadFriendsFromStore();
        } catch (error) {
          //console.error("Erreur lors du refus de l'invitation :", error);
        }
      },

      updateReceivedInvitationsCount() {
        // Mettre à jour le compteur global via l'action Vuex
        this.$store.dispatch(
          'updateReceivedInvitationsCount',
          this.allFriends.filter((friend) => friend.type === 'received').length
        );
      },

      // Cette méthode sera appelée lorsqu'un ami est retiré
      async handleFriendRemoved(removedFriendId) {
        try {
          // 1. Appeler l'API pour supprimer l'ami
          await removeFriend(removedFriendId);

          // 2. Supprimer localement des deux listes
          this.allFriends = this.allFriends.filter(
            (friend) => friend.id !== removedFriendId
          );

          this.filteredList = this.filteredList.filter(
            (friend) => friend.id !== removedFriendId
          );

          // 3. Mettre à jour compteur si besoin
          this.updateReceivedInvitationsCount();
        } catch (error) {
          console.error('Erreur lors du retrait de l’ami :', error);
        }
      },
      goToCommunaute() {
        this.$router.push('/communaute');
      },
      handleScroll() {
        if (this.selectedTab !== 'Tous') return;
        if (!this.hasMoreCandidates || this.isLoadingMore) return;
        const scrollPosition = window.innerHeight + window.scrollY;
        const threshold = document.body.offsetHeight - 100;
        if (scrollPosition >= threshold) {
          this.currentPage++;
          this.loadAllCandidates();
        }
      },
      tryLoadUntilScrollable() {
        // Charge tant qu'il y a de la place à l'écran et qu'il reste des candidats
        this.$nextTick(() => {
          const isScrollable =
            document.body.scrollHeight > window.innerHeight + 50;
          if (
            !isScrollable &&
            this.hasMoreCandidates &&
            !this.isLoadingMore &&
            this.selectedTab === 'Tous'
          ) {
            this.currentPage++;
            this.loadAllCandidates();
            setTimeout(() => this.tryLoadUntilScrollable(), 200); // Relance après chargement
          }
        });
      },
      goToCommunaute() {
        this.$router.push('/communaute');
      },
      handleScroll() {
        if (this.selectedTab !== 'Tous') return;
        if (!this.hasMoreCandidates || this.isLoadingMore) return;
        const scrollPosition = window.innerHeight + window.scrollY;
        const threshold = document.body.offsetHeight - 100;
        if (scrollPosition >= threshold) {
          this.currentPage++;
          this.loadAllCandidates();
        }
      },
      tryLoadUntilScrollable() {
        // Charge tant qu'il y a de la place à l'écran et qu'il reste des candidats
        this.$nextTick(() => {
          const isScrollable = document.body.scrollHeight > window.innerHeight + 50;
          if (!isScrollable && this.hasMoreCandidates && !this.isLoadingMore && this.selectedTab === 'Tous') {
            this.currentPage++;
            this.loadAllCandidates();
            setTimeout(() => this.tryLoadUntilScrollable(), 200); // Relance après chargement
          }
        });
      },
    },

    mounted() {
      this.selectedTab = 'Mon réseau';
      this.filterType = 'Mes amis';
      this.loadFriendsFromStore();
      this.$emit(
        'updateReceivedInvitationsCount',
        this.receivedInvitationsCount
      );
      // Toujours brancher le scroll infini
      window.addEventListener('scroll', this.handleScroll);
      // Si on arrive sur l'onglet "Tous" directement, charger la première page
      if (this.selectedTab === 'Tous') {
        this.loadAllCandidates(true);
      }
      // Correction : si la page n'est pas assez longue, charger plus de candidats
      this.$nextTick(() => {
        if (this.selectedTab === 'Tous') {
          this.tryLoadUntilScrollable();
        }
      });
    },
    beforeUnmount() {
      window.removeEventListener('scroll', this.handleScroll);
    },
    watch: {
      // Suppression du watcher sur $route.params.tab, on ne synchronise plus avec l'URL
      // Met à jour dynamiquement le compteur dans le bouton "Invitations reçues"
      receivedInvitationsCount(newCount) {
        const idx = this.filterOptions.findIndex(
          (opt) => typeof opt === 'object' && opt.label === 'Invitations reçues'
        );
        if (idx !== -1) {
          this.filterOptions[idx] = {
            label: 'Invitations reçues',
            count: newCount,
          };
          this.filterOptions = [...this.filterOptions];
        }
      },
    },
  };
</script>

<style scoped>
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Couleur semi-transparente */
    z-index: 999; /* Juste en dessous de la modale */
  }

  .modal-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000; /* Assurez-vous que la modale est au-dessus du contenu */
    width: 100%; /* Cela peut être ajusté selon vos besoins */
    max-width: 500px; /* Ajustez selon la taille de votre modale */
    padding: 20px; /* Optionnel, pour ajouter du padding autour de la modale */
    text-align: center;
  }

  .friend-container {
    display: flex;
    gap: 27px;
  }

  .basic-filter-container {
    height: fit-content;
    max-width: 260px;
    min-width: 220px;
    margin-top: 90px;
    background-color: var(--surface-bg-2);
    padding: 16px;
  }

  .basic-filter-container .container {
    margin-top: 0;
    margin-bottom: 10px;
  }

  aside {
    width: 100%;
    margin-bottom: 30px;
  }

  .friend-main-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 32px;
    padding-left: 16px;
    padding-right: 16px;
  }

  .grid-wrapper {
    width: 100%;
    max-width: 1400px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    grid-gap: 16px;
    justify-content: center;
    margin: 0 auto;
    box-sizing: border-box;
    overflow-x: visible;
  }

  .grid-wrapper--fixed {
    grid-template-columns: repeat(4, 1fr);
  }

  /* Pour éviter que la grille soit coupée à droite */
  .content-wrapper,
  .friend-main-container,
  .grid-wrapper {
    box-sizing: border-box;
    overflow-x: visible;
  }

  .bg-received {
    background-color: var(--yellow-20);
  }

  .bg-sent {
    opacity: 0.5;
  }

  .action-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
  }

  .blur-content {
    filter: blur(5px);
  }

  .no-friends-message {
    text-align: center;
    color: var(--text-secondary, #888);
    font-size: 1.2em;
    margin-top: 40px;
  }

  .empty-friends-message {
    text-align: center;
    color: var(--text-secondary, #888);
    font-size: 1.1rem;
    margin-top: 40px;
    grid-column: 1/-1;
  }

  .filter-group {
    margin-bottom: 24px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-height: 90px; /* Fixe la hauteur pour éviter le décalage visuel */
    justify-content: flex-start;
  }
  .filter-title {
    font-weight: bold;
    margin-bottom: 6px;
    font-size: 1rem;
  }
  .filter-btn {
    background: none;
    border: 1px solid var(--surface-bg-3, #e0e0e0);
    border-radius: 8px;
    padding: 8px 16px;
    margin-bottom: 4px;
    cursor: pointer;
    transition:
      background 0.2s,
      color 0.2s;
    font-size: 1rem;
    text-align: left;
  }
  .filter-btn.active {
    background: var(--primary, #f7c873);
    color: #222;
    border-color: var(--primary, #f7c873);
    font-weight: bold;
  }
  .filter-btn.see-all {
    background: var(--primary, #f7c873);
    color: #222;
    border-color: var(--primary, #f7c873);
    font-weight: bold;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.07);
    margin-top: 8px;
    margin-bottom: 8px;
    text-align: center;
    width: 100%;
  }
  .filter-count {
    background: var(--primary, #f7c873);
    color: #222;
    border-radius: 10px;
    padding: 2px 8px;
    margin-left: 8px;
    font-size: 0.9em;
  }

  .disabled-group {
    opacity: 0.5;
    pointer-events: none;
    filter: grayscale(1);
  }

  .see-all-profiles-bar {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    margin-bottom: 24px;
    margin-top: 0;
  }
  .see-all-profiles-bar--side {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    margin-bottom: 24px;
    margin-top: 0;
  }
  .see-all-profiles-btn {
    background: var(--primary, #f7c873);
    color: #222;
    border: 1px solid var(--primary, #f7c873);
    border-radius: 8px;
    font-weight: bold;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.07);
    padding: 10px 24px;
    font-size: 1rem;
    cursor: pointer;
    transition:
      background 0.2s,
      color 0.2s;
    width: auto;
    max-width: 100%;
    white-space: nowrap;
    margin-left: 32px;
  }
  .see-all-profiles-btn:hover {
    background: #ffd98a;
    color: #111;
  }

  /* Pour que les cartes soient plus larges comme dans Search.vue */
  :deep(.candidate-card),
  :deep(.compact-profil-card) {
    min-width: 0 !important;
    max-width: 100% !important;
    width: 100% !important;
  }

  /* Ajout d'une barre alignée à la grille pour le bouton "Voir tous les profils" */
  .grid-aligned-bar {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto 24px auto;
    display: flex;
    justify-content: flex-start;
    overflow-x: auto;
  }

  .loader-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 24px 0;
  }
  .loader-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary, #f7c873);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: inline-block;
  }
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  .end-message {
    text-align: center;
    color: #888;
    margin: 16px 0 0 0;
    font-size: 1rem;
  }

  @media screen and (max-width: 768px) {
    .friend-container {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .friend-main-container {
      justify-content: center;
    }

    .basic-filter-container {
      width: 100%;
      max-width: 100%;
    }

    aside {
      justify-items: center;
    }

    h1 {
      justify-content: center;
      display: flex;
    }

    .see-all-profiles-bar--side {
      justify-content: center;
      align-items: center;
    }
    .grid-wrapper {
      grid-template-columns: 1fr;
      max-width: 100%;
    }

    .grid-aligned-bar {
      justify-content: center;
      max-width: 100%;
    }
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }
</style>
