<template>

    <main class="container padding-container">
        <h1>Conditions générales de vente</h1>

        <div class="content-container">
            <!-- preamble -->
            <h5>Sous-titre</h5>
            <p>Lorem ipsum dolor sit amet consectetur. Imperdiet quis suspendisse ac aliquam egestas rhoncus dignissim. Vitae donec sed sodales id ornare eget in euismod quis. Fames elit et et sit. Dictum gravida massa eget amet in lobortis congue viverra. Sed mauris tempus sem fusce vel bibendum. Nec amet mi ut sed enim. Et sollicitudin volutpat rhoncus convallis nec pretium at. In eleifend nunc ullamcorper in porttitor. Purus arcu tincidunt scelerisque eget felis varius morbi proin risus. Et porta id nibh neque donec eget massa mattis. Morbi gravida diam pharetra accumsan.

Vehicula eget pulvinar lectus risus adipiscing diam ut. Accumsan nulla nisl amet arcu nec luctus donec lorem suspendisse. Et rhoncus et odio massa integer volutpat venenatis. At justo amet facilisis bibendum. Non pulvinar ornare diam blandit vel velit.


Arcu porttitor quisque sem ut vitae. Fames risus ultricies egestas varius ac vestibulum auctor. Rhoncus vitae ac amet adipiscing amet adipiscing. Ullamcorper elit turpis vitae non turpis nunc neque. Arcu maecenas pellentesque ultricies faucibus amet. Fermentum proin iaculis volutpat sit ipsum. Neque egestas id odio est est lectus nisi. Mattis ullamcorper ut tellus diam porttitor fermentum nunc id.

In arcu sit odio in vel nisl. Eu scelerisque mattis tortor dolor tincidunt. Rutrum egestas elementum rhoncus enim etiam accumsan quis congue. Ultricies cras sagittis tristique in egestas sed. Aliquam convallis malesuada non mauris nascetur. Netus aliquam aliquam pretium ultrices tellus proin vel morbi fusce. Mattis diam metus sed arcu venenatis. Quam massa augue tristique semper mattis aliquet ut laoreet. Nunc blandit euismod tincidunt sed pellentesque facilisis mollis ultrices. Massa commodo pretium facilisi at eu nullam vel. Accumsan nullam ipsum dui quis. Posuere felis venenatis sapien eu ut vehicula at sit. Diam faucibus pulvinar accumsan lacus pretium. Sit orci in in nisl.

Pharetra tristique blandit venenatis diam nibh habitant ullamcorper amet. <br><br>

Porttitor adipiscing lobortis faucibus ipsum. Turpis amet vitae sed nisl ullamcorper mollis pulvinar euismod consequat. Sed proin diam tellus arcu nunc suspendisse. Mi ut dui turpis viverra sit vitae ultricies. Id habitant dolor mauris enim massa urna aliquet mauris. Justo lacus sed eget suspendisse ultrices lacinia risus vitae. Commodo dictum cursus augue id bibendum amet curabitur vel elit.

Vitae mus etiam urna aenean quis nunc lacus. At vel turpis elit consequat nulla massa dictumst. Convallis nisl nulla consequat non aliquet ut. Eget tincidunt tellus et ac. Purus in eget mauris vitae in egestas. Quis phasellus fermentum sed sollicitudin auctor sit et diam. Nec enim elementum sed diam condimentum. In blandit nec nec facilisi feugiat pharetra. Eu lobortis nibh sem mattis nisi commodo. Posuere nibh volutpat dictum enim accumsan. Amet gravida mauris auctor etiam in. Cursus praesent mauris sociis nibh interdum velit elementum duis. Sed consectetur id turpis mi tortor semper. Eget accumsan tortor phasellus interdum nisl auctor orci. Ipsum donec augue proin vitae integer fermentum justo cursus in.

Sit convallis aliquam congue mauris diam est commodo in. <br><br>

Aliquam viverra et ullamcorper sociis eu senectus. Varius massa platea elementum ac. Amet purus aliquam ante tincidunt cursus sit lectus aliquet malesuada. Tortor est aliquet facilisis lacus semper magna. Sed molestie elit felis ut mus ut enim. Sollicitudin et nisl pellentesque platea odio in facilisis donec odio. Suscipit nunc sapien nisl lorem cras et dignissim sem.

Nullam in vivamus consectetur vitae quis faucibus cursus. Varius sit gravida congue euismod commodo sed maecenas ultricies. Proin ornare sit orci eu suspendisse ut integer id nunc. Gravida consequat vitae in maecenas. Dictumst euismod in commodo nibh in eleifend amet gravida commodo. At consequat amet turpis massa. Scelerisque varius feugiat cum eget nibh.

Amet orci rhoncus venenatis ac elit velit tortor. Imperdiet convallis quis lorem sed sed sit. <br><br>


Auctor consequat in cras massa sed vitae suspendisse. In feugiat quam volutpat integer purus at bibendum diam neque.

In vitae vestibulum egestas amet sed. Id lobortis elementum vehicula eu eu pharetra dictum. Erat pellentesque viverra natoque magna sit commodo pulvinar nunc tristique. Amet sodales feugiat id volutpat lectus iaculis. Cras vulputate quisque diam senectus. Arcu sit luctus risus tincidunt eu gravida viverra fringilla donec. Tristique morbi sit leo in senectus. Cursus eros ut integer convallis amet. Sed et elit dui egestas tincidunt. Et gravida aliquet augue vestibulum consequat enim sed elit.

Eget metus ultrices nulla auctor in arcu. Praesent sed volutpat vivamus volutpat metus lacinia. Ultricies nibh pretium ipsum tristique donec luctus maecenas. Vel fermentum sapien facilisi risus.



Tincidunt semper ultrices vitae vel diam est arcu elit. Sed aliquet adipiscing erat augue eu blandit tortor id. Amet varius purus fermentum turpis phasellus eget fermentum lectus. Et ultrices et velit fames rutrum pharetra nulla sit. Nisi consequat consequat eu magna adipiscing. Aliquet lorem porta et malesuada ut egestas faucibus. Nam mi mi proin cursus semper pellentesque. Nunc nibh euismod eget justo.

Ullamcorper enim vel id id accumsan. Lacus leo sollicitudin euismod aliquam ut cras velit. Quisque ut hac mauris integer mi lorem faucibus tellus auctor. Platea ultricies nullam dictumst viverra et.</p>
        </div>

        <BackToTopArrow />
    </main>

</template>

<script>
import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';

export default {
    name: 'CGU',

    components: {
        BackToTopArrow,
    }
}
</script>

<style scoped>
.container {
    height: fit-content;
    margin-bottom: 100px;
}

.content-container {
    margin-top: 5%;
    display: flex;
    flex-direction: column;
    align-items: baseline;
    gap: 30px;
}

ol li {
    margin-left: 40px;
}

ul li {
    margin-left: 40px;
}

@media screen and (min-width: 1400px) {
    h1 {
        margin: auto;
        width: 80%;
    }

    .content-container {
        margin: auto;
        width: 80%;
    }

    .conditions {
        padding: 16px;
        line-height: 1.2;
    }
}

@media screen and (min-width: 2000px) {
    h1 {
        margin: auto;
        width: 70%;
    }

    .content-container {
        margin: auto;
        width: 70%;
    }

}

</style>