<template>
  <main class="main-article">
    <div class="content-wrapper">
      <!-- ✅ <PERSON><PERSON><PERSON> (Article avec Scroll) -->
      <div class="article-container">
        <!-- Barre de navigation -->
        <div class="p-back">
          <div class="header-container">
            <div class="header-content">
              <PrimaryNormalButton
                btnColor="secondary"
                prepend-icon="mdi-arrow-left"
                textContent="Retour"
                @click="gotoPage('/actualites')"
                class="back-button"
                back
              />
              <p class="p-header">
                {{
                  $route.params.sectiontype === 'candidats'
                    ? 'Candidat'
                    : 'Recruteur'
                }}
                / {{ article.theme }} / {{ article.titre }}
              </p>
            </div>
            <div class="mid-band-bot-container">
              <Chip v-for="chip in article.tags" :textContent="chip.text" />
            </div>
            <div class="article-header">
              <h1 class="title">{{ article.titre }}</h1>
            </div>
            <div class="mid-band">
              <div class="mid-band-top-container">
                <p class="article-name">
                  {{ article.redacteur }},
                  {{ article.date_publication.slice(0, 10) }}
                </p>
              </div>
            </div>
            <div class="img-wrapper">
              <img
                :src="getImgPath(article.photo)"
                alt="preview photo"
                class="photo"
              />
            </div>
          </div>
        </div>

        <!-- Article -->
        <article>
          <div class="article-body">
            <h5>{{ article.sous_titre }}</h5>
            <div class="content-wrapper">
              <p v-html="article.contenu"></p>
            </div>
          </div>
        </article>
      </div>

      <!-- ✅ Colonne Droite (Sidebar Fixe) -->
      <aside class="sidebar">
        <div class="sidebar-section">
          <h2>Découvrez aussi :</h2>
          <h5>{{ article.theme }}</h5>
          <div class="card-row">
            <div class="card-wrapper" v-for="(article, index) in sameNews">
              <NewsCardMini
                :key="article.id"
                :article="article"
                :theme="article.theme"
              />
            </div>
          </div>
        </div>
      </aside>
    </div>

    <BackToTopArrow />
  </main>
</template>

<script setup>
  import gotoPage from '@/utils/router';
</script>

<script>
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import Chip from '@/components/chips/Chip.vue';
  import NewsCardMini from '@/components/cards/NewsCardMini.vue';
  import {
    getMostPopularArticlesByTheme,
    getArticleList,
    getArticleById,
  } from '@/services/blog.service';
  import getImgPath from '@/utils/imgpath.js';

  export default {
    name: 'Article',

    components: {
      PrimaryNormalButton,
      Chip,
      NewsCardMini,
      BackToTopArrow,
    },

    data() {
      return {
        sameNews: [],
        news: [],
        article: {
          categorie: '',
          theme: '',
          date_publication: '2001-01-01',
        },
      };
    },

    beforeMount() {
      this.scrollToTop();
    },

    async mounted() {
      const fullId = this.$route.params.articleid;
      const articleId = fullId.split('/')[0];
      this.article = await getArticleById(articleId);
      this.sameNews = await getMostPopularArticlesByTheme(this.article.theme);
      this.news = await getArticleList('search', 'Décryptage');
    },

    methods: {
      scrollToTop() {
        window.scrollTo(0, 0);
      },
      getImgPath,
    },
  };
</script>

<style scoped>
  .article-body h5 {
    font-size: 30px;
  }

  .text-align {
    text-align: center;
  }

  /* ✅ Conteneur Principal en Deux Colonnes */
  .content-wrapper {
    display: flex;
    gap: 40px; /* Espacement entre l'article et la sidebar */
    max-width: 1200px;
    margin: auto;
    padding: 40px 5px;
    min-height: 100vh; /* ✅ pour garantir que la page remplit l’écran */
    font-size: larger;
  }

  /* ✅ Colonne de Gauche : Article avec Scroll */
  .article-container {
    padding-right: 10px;
    scrollbar-width: thin; /* Rendre la barre plus fine */
    scrollbar-color: #fffdfc transparent; /* Couleur de la barre et fond transparent */
  }

  /* ✅ Colonne de Droite : Sidebar Fixe */
  .sidebar {
    padding: 20px;
    border-radius: 10px;
    height: 100%;
    position: sticky;
    margin-top: -33px;
    top: auto;
  }

  .sidebar h2 {
    font-size: 15px;
    font-style: normal;
  }

  /* ✅ Header */
  .header-content {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 50px;
    padding: 20px;
    margin-bottom: 25px;
    margin-top: -20px;
    margin-left: -15px;
  }

  /* ✅ Bouton Retour */
  .back-button {
    display: flex;
    align-items: center;
    justify-content: center;
    background: black;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    gap: 5px;
  }

  /* ✅ Modifier la couleur de la flèche */
  :deep(.back-button img) {
    filter: brightness(0) saturate(100%) invert(79%) sepia(70%) saturate(300%)
      hue-rotate(0deg) !important;
  }

  /* ✅ Image de l'article */
  .photo {
    width: 100%;
    border-radius: 35px;
    margin-top: 25px;
    margin-bottom: 25px;
  }

  /* ✅ Style des cartes */
  .card-row {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  .card {
    width: 291px;
  }

  .chip {
    background-color: lightgray;
  }

  .mid-band-bot-container {
    margin-bottom: 25px;
  }

  h1 {
    font-size: 33px;
    font-weight: 100;
  }

  h5 {
    margin-bottom: 15px;
  }

  .card-wrapper {
    margin-bottom: 15px;
    width: 144px;
  }

  .p-header {
    font-size: 13px;
  }

  .article-name {
    margin-bottom: 35px;
  }

  /* ✅ Responsive */
  @media screen and (max-width: 992px) {
    .content-wrapper {
      flex-direction: column;
    }

    .sidebar {
      width: 100%;
      padding: 15px;
      position: relative;
    }

    .photo {
      width: 100%;
    }

    .article-container {
      height: auto; /* Suppression du scroll sur mobile */
      overflow-y: visible;
    }
  }
</style>
