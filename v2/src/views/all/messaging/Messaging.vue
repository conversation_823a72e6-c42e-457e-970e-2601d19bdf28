<template>
  <div class="container padding-container">
    <!-- Bannière promotionnelle -->
    <!-- <div class="promo-banner">
      <p>
        Donne un nouvel élan à ta carrière !
        <strong>Discute avec notre Match Coach</strong>
      </p>
      <p>pour des conseils personnalisés, du choix du job parfait jusqu'à</p>
      <p>l'entretien et même au-delà. 🚀</p>
      <div class="highlight-text">
        <span class="normal-weight"
          >Fais partie de notre club des testeurs et gagne</span
        >
        <strong>
          un mois gratuit<br />
          sur les fonctionnalités premium !</strong
        >
      </div>
      <button class="promo-button" @click="redirectToForm">
        Cela m'intéresse
      </button>
    </div> -->
    <!-- Fin bannière promotionnelle -->

    <h1>Bienvenue sur le chat IA 🚀</h1>

    <!-- BannerAd style="margin-bottom: 125px;" /-->

    <main class="messaging-main-container">
      <!-- Par où commencer (bulles) AU-DESSUS du chat IA -->
      <BubbleSelector
        class="predefined-messages"
        :bubbles="predefinedMessages"
        @bubble-clicked="handleBubbleMessage"
      />
      <IaSection
        :isSendingMessage="isSendingMessage"
        @create-new-conversation="onCreateNewConversation"
        :conversations="iaConversations"
        @update:conversations="updateConversations"
        :streamMessage="streamMessage"
        @send-message-ia="sendMessageIa"
        ref="IAsection"
        :userMessage="userMessage"
        @update:userMessage="userMessage = $event"
      />
      <!-- Par où commencer (bulles) EN DESSOUS du chat IA -->
      <BubbleSelector
        class="predefined-messages"
        :bubbles="predefinedMessages"
        @bubble-clicked="handleBubbleMessage"
      />
    </main>

    <!--BannerAd style="margin-top: 50px;" /-->

    <!-- section offres -->
    <!-- <div class="offers-section">
      <div class="text-section">
        <h2>Découvre toutes nos offres</h2>
        <p>
          Parcours des offres variées, choisis celle<br />
          qui te parle, et postule en quelques clics.<br />
          Ta carrière, c'est toi qui la pilotes !
        </p>
        <button class="offers-button" @click="$router.push('/recherche')">
          Je me lance
        </button>
      </div>
      <div class="image-section">
        <picture>
          <source
            srcset="
              @/assets/relaxed-lazy-upbeat-woman-keeps-arms-raised-up-air.png
            "
            type="image/png"
          />
          <img
            src="@/assets/relaxed-lazy-upbeat-woman-keeps-arms-raised-up-air.png"
            alt="Relaxed lazy upbeat woman keeps arms raised up air"
            class="offers-image"
          />
        </picture>
      </div>
    </div> -->
  </div>
</template>

<script>
  import IaSection from '@/components/views-models/messaging/ia-section/IaSection.vue';
  import BubbleSelector from '@/components/common/CustomSelectBubbles.vue';
  import { mapActions } from 'vuex';
  import { axiosInstance, AIbaseUrl } from '../../../services/axios';
  import {
    createConversation,
    sendIaConversation,
    saveIaResponse,
    getAllIaConversation
  } from '../../../services/conversation.service';
  import { getJobOfferById } from '../../../services/search.service';
  import { fetchEventSource } from '@microsoft/fetch-event-source';
  import BannerAd from '@/components/google-ad/BannerAd.vue';
  import { initializeGlobalPrivateWebSocket } from '../../../services/global-websocket.service';

  export default {
    name: 'ChatIA',
    components: {
      IaSection,
      BannerAd,
      BubbleSelector,
    },
    data() {
      return {
        streamMessage: [],
        detectedJobIds: [], // IDs des offres d'emploi détectés dans le streaming
        jobIdsProcessed: false, // Pour éviter de traiter plusieurs fois
        file: null,
        isSendingMessage: false,
        predefinedMessages: [
          "J'améliore mon CV",
          "J'améliore ma lettre de motivation",
          'Je recherche un job',
          "Je prépare un entretien d'embauche",
        ],
        userMessage: '',
      };
    },
    computed: {
      getUser() {
        return this.$store.getters.getUser;
      },
      iaConversations() {
        // S'assurer que cette propriété renvoie toujours un tableau, même vide
        return this.$store.state.userModule.user.conversation || [];
      },
    },
    methods: {
      ...mapActions(['handleLlmConversationChange']),
      redirectToForm() {
        window.location.href =
          'https://docs.google.com/forms/d/1LJRYl1mms3I90rH_hPppYPQkKqbuxPEf6Trd_0L41Lo/viewform?pli=1&pli=1&edit_requested=true';
      },
      updateConversations(updatedConversations) {
        this.conversations = [...updatedConversations];
      },
      handleBubbleMessage(message) {
        this.userMessage = message;
      },
      async sendMessageIa(message, selectedConversationId) {
        this.isSendingMessage = true;
        const fichier = this.$refs.IAsection.file;
        let fileName = '';
        if (fichier) {
          fileName = fichier;
        }

        try {
          // Envoyer le message au backend d'abord
          console.log('Envoi du message au backend...');
          const response = await sendIaConversation(
            selectedConversationId,
            message,
            fileName
          );

          console.log('Réponse du backend:', response);

          // Vérifier que la réponse est valide
          if (!response || !response.data || !response.data.id) {
            throw new Error('Réponse invalide du serveur');
          }

          // Maintenant faire l'appel streaming à l'IA pour l'affichage en temps réel
          const IAFOrmData = new FormData();
          IAFOrmData.append('phrase', message);

          console.log('Début du streaming vers:', AIbaseUrl);

          await fetchEventSource(AIbaseUrl, {
            method: 'POST',
            headers: {
              'x-api-key': 'thanks-boss-team-test',
            },
            body: IAFOrmData,
            retry: 120000,
            onmessage: (ev) => {
              console.log('Message reçu:', ev.data);
              if (ev.data) {
                try {
                  const data = ev.data;
                  if (data === '') {
                    console.warn('Received empty data.');
                    return;
                  }
                  if (data.error === '500') {
                    console.log('Erreur 500 détectée');
                    throw new Error(
                      'Une erreur a été détectée, veuillez renouveler votre réponse.'
                    );
                  } else {
                    console.log('Streamed data received:', data);
                    // Ajouter un espace avant chaque nouveau chunk (sauf le premier)
                    if (this.streamMessage.length > 0 && !data.startsWith(' ')) {
                      this.streamMessage.push(' ' + data);
                    } else {
                      this.streamMessage.push(data);
                    }

                    // Détecter les IDs d'offres d'emploi dans le format "IDs: [41823, 41674]"
                    const fullMessage = this.streamMessage.join('');
                    console.log('🔍 Message complet actuel:', fullMessage);
                    this.detectJobIdsInStream(fullMessage);

                    this.$forceUpdate();
                  }
                } catch (error) {
                  console.error('Error in processing message:', error);
                  throw new Error(ev.data);
                }
              }
            },
            onerror: (error) => {
              console.error('EventSource error:', error);
              throw error;
            },
          });

          // Sauvegarder le message streamé avec les offres d'emploi si détectées
          if (this.streamMessage.length > 0) {
            const completeMessage = this.streamMessage.join('');

            if (this.detectedJobIds.length > 0) {
              console.log('🎯 Traitement des IDs détectés:', this.detectedJobIds);
              await this.processDetectedJobIds(selectedConversationId);
            } else {
              // Sauvegarder le message normal sans offres d'emploi
              await this.saveStreamedMessageToConversation(completeMessage, selectedConversationId);
            }

            // Vider le streamMessage après sauvegarde
            this.streamMessage = [];
          }

          // Récupérer la conversation complète avec les offres d'emploi
          await this.handleBestjobs(selectedConversationId);

          // S'assurer que les offres sont bien stockées après la récupération
          if (this.detectedJobIds.length > 0) {
            const jobOffers = await this.fetchJobOffers(this.detectedJobIds);
            if (jobOffers.length > 0) {
              this.storeJobOffersLocally(selectedConversationId, jobOffers);
            }
          }
        } catch (error) {
          this.isSendingMessage = false;
          console.log(error);
          // En cas d'erreur, sauvegarder quand même le message streamé
          if (this.streamMessage.length > 0) {
            const completeMessage = this.streamMessage.join('');
            console.log('Sauvegarde du message streamé en cas d\'erreur:', completeMessage);
            await this.saveStreamedMessageToConversation(completeMessage, selectedConversationId);
            this.streamMessage = [];
          }
        } finally {
          this.isSendingMessage = false;
        }
      },

      // async sseListening(iaClass, selectedConversationId, message) {
      //   try {
      //     const url = `${baseUrl}/api/conversation/llm-streaming-gpu-docker/${selectedConversationId}/${iaClass}/`;

      //     const formData = new FormData();
      //     formData.append('phrase', message);
      //     const file = this.$refs.IAsection.file;
      //     formData.append('file', file);

      //    /* const refreshTokenUrl = "/user/refresh-token/";
      //     const { data: { access } } = await axiosInstance.post(refreshTokenUrl);
      //     */
      //     const accessToken = this.$store.getters.token;
      //     console.log('accessToken', accessToken);

      //     await fetchEventSource(url, {
      //       method: 'POST',
      //       headers: {
      //         'Authorization': `Bearer ${accessToken}`,
      //       },
      //       body: formData,
      //       onmessage: (ev) => {
      //         if (ev.data) {
      //           try {
      //             const data = JSON.parse(ev.data);
      //             if (data.error === "500") {
      //               console.log("Erreur 500 détectée");
      //               data = "Une erreur a été détectée, veuillez renouveler votre réponse.";
      //             }
      //             const array = [];
      //             array.push(data);
      //             this.streamMessage.push(array);
      //           } catch (error) {
      //             this.streamMessage.push(ev.data);
      //           }
      //         }
      //       },
      //       onerror: (error) => {
      //         console.error('EventSource error:', error);
      //       },
      //     });

      //     let conv = await axiosInstance.get(`/conversation/full-conversation/${selectedConversationId}/`);
      //     conv.data.messages = conv.data.messages.sort((a, b) => (a.id) - (b.id));

      //     this.handleLlmConversationChange(conv.data);
      //     this.streamMessage = [];
      //     this.$refs.IAsection.file = null;
      //   } catch (error) {
      //     console.log(error);
      //   } finally {
      //     this.isSendingMessage = false;
      //   }
      // },

      detectJobIdsInStream(messageText) {
        console.log('🔍 Recherche d\'IDs dans:', messageText);

        // Détecter le format spécifique "IDs: [41823, 41674]"
        const jobIdPattern = /IDs:\s*\[([^\]]+)\]/;
        const match = messageText.match(jobIdPattern);

        console.log('🔍 Pattern match result:', match);

        if (match && match[1]) {
          // Extraire les IDs de la chaîne "[41823, 41674]"
          const idsString = match[1];
          console.log('🔍 IDs string trouvé:', idsString);

          const ids = idsString.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
          console.log('🔍 IDs parsés:', ids);

          if (ids.length > 0 && !this.jobIdsProcessed) {
            console.log('🎯 IDs détectés dans le streaming:', ids);
            this.detectedJobIds = ids;
            this.jobIdsProcessed = true; // Éviter de traiter plusieurs fois
          }
        } else {
          console.log('🔍 Aucun ID détecté dans le message');
        }
      },

      async processDetectedJobIds(conversationId) {
        if (this.detectedJobIds.length === 0) return;

        try {
          console.log('🎯 Traitement des IDs détectés:', this.detectedJobIds);
          const jobOffers = await this.fetchJobOffers(this.detectedJobIds);

          if (jobOffers.length > 0) {
            console.log('🎯 Offres d\'emploi récupérées:', jobOffers);

            // Nettoyer le message en supprimant les IDs
            let cleanMessage = this.streamMessage.join('');
            cleanMessage = cleanMessage.replace(/IDs:\s*\[[^\]]+\]/g, '').trim();

            // Sauvegarder le message nettoyé avec les offres d'emploi
            await saveIaResponse(conversationId, cleanMessage, "best_jobs", jobOffers);

            // CONTOURNEMENT : Stocker les offres côté frontend car le backend les ignore
            this.storeJobOffersLocally(conversationId, jobOffers);

            console.log('🎯 Message IA sauvegardé avec offres d\'emploi');
          }

          // Réinitialiser
          this.detectedJobIds = [];
          this.jobIdsProcessed = false;
        } catch (error) {
          console.error('Erreur lors du traitement des IDs:', error);
        }
      },

      async fetchJobOffers(jobIds) {
        const jobOffers = [];

        for (const jobId of jobIds) {
          try {
            console.log(`Récupération de l'offre d'emploi ${jobId}...`);
            const jobOffer = await getJobOfferById(jobId);
            if (jobOffer) {
              jobOffers.push(jobOffer);
              console.log(`Offre ${jobId} récupérée:`, jobOffer.titre || jobOffer.title);
            }
          } catch (error) {
            console.error(`Erreur lors de la récupération de l'offre ${jobId}:`, error);
          }
        }

        console.log(`${jobOffers.length} offres d'emploi récupérées sur ${jobIds.length} IDs`);
        return jobOffers;
      },

      storeJobOffersLocally(conversationId, jobOffers) {
        // Trouver la conversation actuelle
        const currentConversation = this.iaConversations.find(
          conv => conv.conversation_id === conversationId
        );

        if (currentConversation && currentConversation.messages) {
          // Trouver le dernier message IA avec detected_class: 'best_jobs'
          const lastIaMessage = [...currentConversation.messages]
            .reverse()
            .find(msg => msg.message_Mc && msg.detected_class === 'best_jobs');

          if (lastIaMessage) {
            // Ajouter les offres d'emploi au message
            lastIaMessage.recommended_job_offers = jobOffers;
            console.log('🎯 Offres d\'emploi stockées localement:', jobOffers.length);

            // Forcer la réactivité
            this.$forceUpdate();
          }
        }
      },

      async saveStreamedMessageToConversation(messageText, conversationId) {
        if (messageText.trim()) {
          try {
            // Sauvegarder la réponse IA en backend
            console.log('Sauvegarde de la réponse IA en backend...');
            await saveIaResponse(conversationId, messageText);
            console.log('Réponse IA sauvegardée avec succès en backend');
          } catch (error) {
            console.error('Erreur lors de la sauvegarde de la réponse IA:', error);
          }
        }
      },

      async extractJobIdsFromMessage(messageText) {
        // Extraire les IDs des offres d'emploi de la réponse de l'IA
        // L'IA peut retourner des IDs sous différents formats
        const jobIdPatterns = [
          /job[_\s]*id[:\s]*(\d+)/gi,
          /offre[_\s]*(\d+)/gi,
          /id[:\s]*(\d+)/gi,
          /\b(\d{1,6})\b/g // IDs numériques simples
        ];

        const jobIds = new Set();

        for (const pattern of jobIdPatterns) {
          const matches = messageText.matchAll(pattern);
          for (const match of matches) {
            const id = parseInt(match[1]);
            if (id && id > 0 && id < 1000000) { // Validation basique
              jobIds.add(id);
            }
          }
        }

        console.log('IDs extraits du message IA:', Array.from(jobIds));
        return Array.from(jobIds);
      },

      async fetchJobOffers(jobIds) {
        const jobOffers = [];

        for (const jobId of jobIds) {
          try {
            console.log(`Récupération de l'offre d'emploi ${jobId}...`);
            const jobOffer = await getJobOfferById(jobId);
            if (jobOffer) {
              jobOffers.push(jobOffer);
              console.log(`Offre ${jobId} récupérée:`, jobOffer.titre || jobOffer.title);
            }
          } catch (error) {
            console.error(`Erreur lors de la récupération de l'offre ${jobId}:`, error);
          }
        }

        console.log(`${jobOffers.length} offres d'emploi récupérées sur ${jobIds.length} IDs`);
        return jobOffers;
      },

      async saveStreamedMessageToConversation(messageText, conversationId) {
        if (messageText.trim()) {
          try {
            // Extraire les IDs des offres d'emploi de la réponse IA
            const jobIds = await this.extractJobIdsFromMessage(messageText);
            let detectedClass = null;
            let recommendedJobOffers = [];

            if (jobIds.length > 0) {
              detectedClass = "best_jobs";
              recommendedJobOffers = await this.fetchJobOffers(jobIds);
              console.log('Offres d\'emploi trouvées:', recommendedJobOffers.length);
            }

            // Sauvegarder la réponse IA en backend avec les offres d'emploi
            console.log('Sauvegarde de la réponse IA en backend...');
            await saveIaResponse(conversationId, messageText, detectedClass, recommendedJobOffers);
            console.log('Réponse IA sauvegardée avec succès en backend');
          } catch (error) {
            console.error('Erreur lors de la sauvegarde de la réponse IA:', error);

            // Fallback : sauvegarder localement si le backend échoue
            const currentConversation = this.iaConversations.find(
              conv => conv.conversation_id === conversationId
            );

            if (currentConversation) {
              const newMessage = {
                id: Date.now(), // ID temporaire
                message_Mc: messageText,
                message_applicant: null,
                date_creation: new Date().toISOString(),
                detected_class: null,
                recommended_job_offers: []
              };

              if (!currentConversation.messages) {
                currentConversation.messages = [];
              }
              currentConversation.messages.push(newMessage);
              console.log('Message IA ajouté localement en fallback:', newMessage);
            }
          }
        }
      },
      async fetchConversation() {
        this.user = this.getUser;
        try {
          console.log('Récupération des conversations via WebSocket');
          // Récupérer toutes les conversations via WebSocket
          //await getAllIaConversation();

          // Si aucune conversation n'existe, en créer une nouvelle
          if (!this.iaConversations || this.iaConversations.length === 0) {
            console.log('Aucune conversation trouvée, création d\'une nouvelle conversation');
            await this.onCreateNewConversation();
          }

          console.log('Conversations récupérées:', this.iaConversations);
        } catch (error) {
          console.error('Erreur lors de la récupération/création des conversations:', error);
          // En cas d'erreur, créer quand même une conversation par défaut
          if (!this.iaConversations || this.iaConversations.length === 0) {
            await this.onCreateNewConversation();
          }
        }
      },
      async handleBestjobs(selectedConversationId) {
        try {
          console.log('🎯 handleBestjobs - Récupération de la conversation complète via WebSocket');
          console.log('🎯 handleBestjobs - selectedConversationId:', selectedConversationId);
          if (this.$refs.IAsection) {
            this.$refs.IAsection.file = null;
          }

          // Utiliser directement l'API REST pour récupérer la conversation complète
          console.log('🎯 handleBestjobs - Utilisation de l\'API REST pour récupérer la conversation');
          const { data } = await axiosInstance.get(
            `/conversation/full-conversation/${selectedConversationId}/`
          );
          console.log('🎯 handleBestjobs - Conversation complète récupérée:', data);

          if (data && data.messages) {
            data.messages.sort((a, b) => a.id - b.id);
            console.log('🎯 handleBestjobs - Messages triés:', data.messages);

            // Vérifier les messages IA avec detected_class et recommended_job_offers
            data.messages.forEach((message, index) => {
              if (message.message_Mc) {
                console.log(`🎯 Message IA ${index}:`, {
                  id: message.id,
                  detected_class: message.detected_class,
                  recommended_job_offers: message.recommended_job_offers,
                  message_length: message.message_Mc ? message.message_Mc.length : 0,
                  message_content: message.message_Mc // Afficher le contenu complet
                });
              }
            });

            console.log('🎯 handleBestjobs - Mise à jour de la conversation');
            this.handleLlmConversationChange(data);
          }

          if (this.$refs.IAsection) {
            this.$refs.IAsection.file = null;
          }
        } catch (error) {
          console.error('Erreur lors de la récupération de la conversation complète:', error);
          this.isSendingMessage = false;
        }
      },
      async onCreateNewConversation() {
        try {
          const conversation = await createConversation();
          if (conversation) {
            this.iaConversations = [...this.iaConversations, conversation];
          }
        } catch (error) {
          console.error(
            "Erreur lors de la création d'une nouvelle conversation :",
            error
          );
        }
      },
    },
    created() {
      // Initialiser le WebSocket global en utilisant la fonction correcte
      window.globalWebSocket = initializeGlobalPrivateWebSocket();

      // Récupérer les conversations
      this.fetchConversation();
    },
    beforeDestroy() {
      // Fermer le WebSocket global si nécessaire
      if (window.globalWebSocket && window.globalWebSocket.readyState === WebSocket.OPEN) {
        console.log('Fermeture du WebSocket global');
        window.globalWebSocket.close();
        window.globalWebSocket = null;
      }
    },
  };
</script>

<style>
  /* vuetify classes */
  .messagerie-toggle .v-btn__overlay {
    background-color: transparent;
  }

  .messagerie-toggle .v-btn__overlay:active {
    background-color: transparent;
  }

  .messagerie-toggle .v-btn--active {
    color: var(--text-1) !important;
    border-bottom: 1px solid var(--text-1) !important;
  }
</style>

<style scoped>
  .container {
    width: 100%;
    margin-top: 30px;
    margin-bottom: 100px;
  }

  h1 {
  text-align: center;
  margin-bottom: 20px;
}


  .messaging-main-container {
    width: 100%;
    margin-top: 30px;
    gap: 38px;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  /* ######## MOBILE ######## */
  @media screen and (max-width: 480px) {
    h1 {
      margin-bottom: 50px;
    }
  }

  /* ######## TABLETTE PETITE ######## */
  @media screen and (min-width: 481px) and (max-width: 768px) {
    h1 {
      margin-bottom: 50px;
    }
  }

  /* ######## TABLETTE LARGE / TRANSITION VERS DESKTOP ######## */
  @media screen and (min-width: 769px) and (max-width: 991px) {
    .messaging-main-container {
      flex-direction: row;
      align-items: inherit;
      margin-top: 80px;
    }
  }

  /* ######## DÉBUT MODE DESKTOP (Navbar normale) ######## */
  @media screen and (min-width: 992px) {
    .messaging-main-container {
      flex-direction: row;
      align-items: inherit;
      margin-top: 80px;
    }
  }

  /* TO DO : À RETIRER TOUT CE QUI EST EN BAS ? */
  /* Bannière promotionnelle */
  .promo-banner {
    background-color: inherit;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    max-width: 1280px;
    margin-left: auto;
    margin-right: auto;
  }

  .promo-banner p {
    font-size: 25px;
    text-align: left;
  }
  .highlight-text {
    color: #ff9800;
    font-weight: bold;
    font-size: 27px;
    text-align: center;
    display: block;
    margin-top: 30px;
    max-width: 100%;
  }

  .highlight-text .normal-weight {
    font-weight: normal;
  }

  .highlight-text strong {
    font-weight: bold;
  }

  .promo-button {
    padding: 12px 40px;
    background-color: #ff9800;
    color: white;
    border: none;
    border-radius: 30px;
    font-size: 35px;
    cursor: pointer;
    display: block;
    width: fit-content;
    margin: 24px auto;
    margin-bottom: 60px;
    text-align: center;
    white-space: nowrap;
    font-weight: 700;
  }

  .promo-button:hover {
    background-color: #e68900;
  }

  /* section offres */

  .offers-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: inherit;
    border-radius: 10px;
    padding: 0;
    margin: 40px auto 0;
    overflow: hidden;
    width: 100%;
    position: relative;
    max-width: 1280px;
    gap: 50px;
  }

  .text-section {
    flex: 0 1 45%;
    padding: 20px;
    display: flex;
    flex-direction: column;
    text-align: left;
    transform: translateY(-20%);
    position: relative;
    max-width: 600px;
  }

  .text-section h2 {
    font-size: 36px;
    margin-top: 0;
    margin-bottom: 10px;
  }

  .text-section p {
    font-size: 20px;
    margin-bottom: 10px;
    line-height: 1.6;
    white-space: nowrap;
  }

  .offers-button {
    margin: 20px auto;
    background-color: #ff9800;
    color: white;
    padding: 10px 80px;
    border: none;
    border-radius: 25px;
    font-size: clamp(18px, 20px, 32px);
    white-space: nowrap;
    cursor: pointer;
    transition: background-color 0.2s ease;
    text-align: center;
    margin-left: 15%;
    font-weight: 700;
  }

  .offers-button:hover {
    background-color: #e68900;
  }

  .image-section {
    flex: 0 1 55%;
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    position: relative;
    margin-bottom: -40px;
    margin-right: -5%;
  }

  picture {
    display: flex;
    align-items: flex-end;
  }

  .offers-image {
    width: 130%;
    height: auto;
    object-fit: cover;
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }
</style>
