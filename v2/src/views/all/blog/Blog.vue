<template>
  <main class="main-content padding-container">


    <LatestNews blog />

    <PopularArticles />

    <SectionPreview
      :titles="['<PERSON><PERSON><PERSON><PERSON> son embauche', 'Décryptage', 'Success stories']"
      linkTo="candidate"
    />

    <!-- ATTENTION AUX APOSTROPHES, il faut conserver ’ pour être indentique à la base de données, ne pas remplacer par ' -->
    <SectionPreview
      :titles="['Tendances RH', 'J’aime l’IA', 'Recruter autrement']"
      linkTo="recruter"
    />

    <!-- <EbookSales /> -->

    <!--  <NewsLetter />-->

    <BackToTopArrow />
  </main>
</template>

<script>
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import NewsLetter from '@/components/layout/newsletter/NewsLetter.vue';
  import EbookSales from '@/components/views-models/blog/EbookSales.vue';
  import HeroBanner from '@/components/views-models/blog/HeroBanner.vue';
  import LatestNews from '@/components/views-models/blog/LatestNews.vue';
  import PopularArticles from '@/components/views-models/blog/PopularArticles.vue';
  import SectionPreview from '@/components/views-models/blog/SectionPreview.vue';

  export default {
    name: 'BlogPage',

    components: {
      HeroBanner,
      LatestNews,
      PopularArticles,
      SectionPreview,
      // EbookSales,
      // NewsLetter,
      BackToTopArrow,
    },

    beforeMount() {
      this.scrollToTop();
    },

    methods: {
      //  scroll to top of the page
      scrollToTop() {
        window.scrollTo(0, 0);
      },
    },
  };
</script>

<style scoped>
  .main-content {
    height: fit-content;
    width: 100%;
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }
</style>
