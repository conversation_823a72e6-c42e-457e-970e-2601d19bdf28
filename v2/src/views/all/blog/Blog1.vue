<template>
  <div class="blog-container">
    <LatestNews blog />

    <SectionPreview
      :titles="['<PERSON><PERSON><PERSON><PERSON> son embauche', 'Décryptage', 'Success stories']"
      :linkTo="'candidate'"
    />

    <div class="ad-container">
      <ins class="adsbygoogle"
        style="display:block; width:100%; min-height:250px;"
        data-ad-client="ca-pub-6599264593991393"
        data-ad-slot="2896827317"
        data-ad-layout="in-article"
        data-ad-format="fluid">
      </ins>
    </div>
  </div>
</template>

<script>
import LatestNews from '@/components/views-models/blog/LatestNews.vue';
import PopularArticles from '@/components/views-models/blog/PopularArticles.vue';
import SectionPreview from '@/components/views-models/blog/SectionPreview.vue';
import EbookSales from '@/components/views-models/blog/EbookSales.vue';
import NewsLetter from '@/components/layout/newsletter/NewsLetter.vue';
import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';

export default {
  name: 'BlogPage1',
  components: {
    LatestNews,
    PopularArticles,
    SectionPreview,
    EbookSales,
    NewsLetter,
    BackToTopArrow,
  },

  mounted() {
    this.scrollToTop();
    this.$nextTick(() => {
      try {
        const adScript = document.createElement('script');
        adScript.async = true;
        adScript.src = 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-6599264593991393';
        document.head.appendChild(adScript);

        adScript.onload = () => {
          (window.adsbygoogle = window.adsbygoogle || []).push({});
        };
      } catch (error) {
        //console.error('AdSense initialization error:', error);
      }
    });
  },

  methods: {
    scrollToTop() {
      window.scrollTo(0, 0);
    },
  },
};
</script>

<style scoped>
.blog-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.ad-container {
  width: 100%;
  max-width: 970px;
  margin: 20px auto;
  min-height: 250px;
  background-color: #f5f5f5;
}

@media screen and (max-width: 768px) {
  .ad-container {
    max-width: 100%;
    padding: 0 15px;
  }
}
  
</style>
