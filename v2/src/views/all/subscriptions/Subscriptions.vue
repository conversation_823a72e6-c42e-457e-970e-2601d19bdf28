<template>
  <div class="container">
    <!-- header -->
    <div class="header-container padding-container">
      <h1>Abonnement</h1>
    </div>

    <div v-if="currentUser && !currentUser.abonnement">
      <div class="choose-subscription-container padding-container">
        <h2 class="choose-subscription-title">
          Version {{ subscriptionName }}
        </h2>
        <h5 class="choose-subscription-text">
          Vous êtes actuellement sur la version {{ subscriptionName }} que
          propose Thanks-Boss
        </h5>
      </div>
    </div>

    <!-- Si l'utilisateur a un abonnement -->
    <div v-if="currentUser && currentUser.abonnement">
      <div class="choose-subscription-container padding-container">
        <h2 class="choose-subscription-title">{{ currentUser.abonnement }}</h2>
        <h5 class="choose-subscription-text">
          Tu es actuellement sur l'{{ currentUser.abonnement }} que propose
          Thanks-Boss
        </h5>
      </div>
    </div>

    <main class="main-container padding-container">
      <!-- subscription options -->
      <div v-for="subscription in filteredSubscriptions" :key="subscription.id">
        <SubscriptionCard
          :subscription="subscription"
          :activeIndex="activeIndex"
          @update-active="updateActiveIndex"
        />
      </div>

      <!-- <GeneralConditions /> -->
      <div class="change-btn">
        <PrimaryRoundedButton
          v-if="currentUser && currentUser.abonnement"
          textContent="Résilier mon abonnement"
          btnColor="secondary"
          @click="resilierAbonnement()"
        />
        <PrimaryRoundedButton
          v-if="!latestUnpaidFacture"
          textContent="Valider cet abonnement"
          @click="validerAbonnement()"
        />
      </div>

      <div>
        <BackToTopArrow />
      </div>
    </main>
    <EstimateCard
      v-if="!isApplicant && latestUnpaidFacture"
      :factures="userFactures"
      :subscriptions="subscriptions"
      :latestFacture="latestUnpaidFacture"
      @resume-devis="resumeDevis"
    />
    <Facturation
      v-if="userFactures.payer === true && userFactures.length > 0"
      :factures="userFactures"
      :subscriptions="subscriptions"
      :user="currentUser"
    />
  </div>
</template>

<script>
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import gotoPage from '@/utils/router.js';
  import BackToTopArrow from '../../../components/buttons/BackToTopArrow.vue';
  import EstimateCard from '../../../components/cards/estimate-card/EstimateCard.vue';
  import SubscriptionCard from '../../../components/cards/subscription-cards/SubscriptionCard.vue';
  import Facturation from '../../../components/views-models/recruiter/subscription-page/Facturation';
  // import GeneralConditions from '../../recruiter/subscriptions/GeneralConditions.vue';
  import { mapGetters, mapActions } from 'vuex';

  export default {
    name: 'SubscriptionsPage',

    components: {
      PrimaryRoundedButton,
      SubscriptionCard,
      BackToTopArrow,
      // GeneralConditions,
      EstimateCard,
      Facturation,
    },

    data() {
      return {
        activeIndex: null,
        hasInitialized: false, // Ajouter ce drapeau pour l'initialisation
      };
    },

    computed: {
      ...mapGetters(['getUser']),
      ...mapGetters('subscription', ['getSubscription', 'getFacture']),

      currentUser() {
        const user = this.getUser || {};
        //console.log('[Subscriptions.vue] currentUser:', user);
        return user;
      },
      subscriptions() {
        return this.getSubscription || [];
      },
      userFactures() {
        //console.log('this.getFacture', this.getFacture);
        return this.getFacture?.results || [];
      },

      filteredSubscriptions() {
        const subscriptionId = this.$route.params.id;
        if (subscriptionId) {
          return this.subscriptions.filter(
            (s) => s.id === parseInt(subscriptionId)
          );
        }
        return this.subscriptions;
      },

      subscriptionName() {
        const activeSubscription =
          this.$store.state.subscription.activeSubscription;
        return activeSubscription ? activeSubscription.nom : 'gratuite';
      },

      activeSubscription() {
        const storeSub = this.$store.state.subscription.activeSubscription;
        if (storeSub) return storeSub;
        return (
          this.subscriptions.find((sub) => sub.id === this.activeIndex) || null
        );
      },

      isApplicant() {
        return this.currentUser?.type_user === 'applicant';
      },

      latestUnpaidFacture() {
        if (!this.activeSubscription || !this.userFactures.length) return null;

        return this.userFactures.find(
          (facture) =>
            facture.payer === false &&
            facture.type_produit === this.activeSubscription.id
        );
      },
    },

    methods: {
      ...mapActions('subscription', ['fetchSubscriptions', 'fetchFactures']),
      ...mapActions(['fetchUser']),
      gotoPage,

      async initData() {
        try {
          await this.fetchUser();
          await this.fetchSubscriptions();
          await this.fetchFactures();

          const subscriptionId = this.$route.params.id;
          if (subscriptionId) {
            this.updateActiveIndex(parseInt(subscriptionId));
          } else {
            this.setDefaultSubscription();
          }
          this.hasInitialized = true;
        } catch (error) {
          //console.error(
          //  'Erreur lors du chargement initial des données :',
          //  error
          //);
        }
      },

      setDefaultSubscription() {
        // Définir la valeur par défaut uniquement si aucun abonnement actif n'existe dans le store
        if (!this.$store.state.subscription.activeSubscription) {
          const defaultSubscription = this.subscriptions.find(
            (sub) => sub.name === 'Gratuit' || sub.titre === 'Gratuit'
          );
          if (defaultSubscription) {
            this.activeIndex = defaultSubscription.id;
            this.$store.commit(
              'subscription/setActiveSubscription',
              defaultSubscription
            );
          }
        }
      },

      updateActiveIndex(id) {
        this.activeIndex = id;
        const activeSubscription = this.subscriptions.find(
          (sub) => sub.id === id
        );
        if (activeSubscription) {
          this.$store.commit(
            'subscription/setActiveSubscription',
            activeSubscription
          );
          //console.log(
          //  "Store mis à jour avec l'abonnement:",
          //  activeSubscription
          //);
        }
      },

      validerAbonnement() {
        const sub = this.activeSubscription;
        if (sub) {
          this.$store.commit('subscription/setActiveSubscription', sub);
          this.gotoPage('/achat');
        } else {
          console.warn('Aucun abonnement actif sélectionné');
        }
      },

      resilierAbonnement() {
        const defaultSub = this.subscriptions.find(
          (sub) => sub.nom === 'Gratuit' || sub.titre === 'Gratuit'
        );
        if (defaultSub) {
          this.activeIndex = defaultSub.id;
          this.$store.commit('subscription/setActiveSubscription', defaultSub);
          gotoPage('/tarifs');
        } else {
          console.warn('Aucun abonnement par défaut trouvé');
        }
      },

      resumeDevis() {
        const facture = this.latestUnpaidFacture;
        const relatedSubscription = this.subscriptions.find(
          (sub) => sub.id === facture?.type_produit
        );

        if (relatedSubscription) {
          this.$store.commit('subscription/setActiveSubscription', {
            ...relatedSubscription,
            number: facture.number,
          });

          this.$router.push({
            path: '/achat',
            query: {
              resume: 'true',
            },
          });
        }
      },
    },

    async mounted() {
      await this.initData();
    },

    watch: {
      '$route.params.id'(newId) {
        if (newId && this.hasInitialized) {
          this.updateActiveIndex(parseInt(newId));
        }
      },
    },
  };
</script>

<style scoped>
  button {
    width: 150px;
  }

  h2 {
    color: var(--black-100);
  }

  .header-container {
    text-align: center;
    margin-bottom: 20px;
  }

  .choose-subscription-container {
    height: 175px;
    width: 100vw;
    text-align: center;
    background-color: var(--surface-bg-2);
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
  }

  .choose-subscription-title {
    width: 100%;
    padding: 8px;
    display: flex;
    justify-content: center;
    text-transform: capitalize;
  }

  .choose-subscription-text {
    height: 50%;
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .conditions {
    padding: 16px;
    line-height: 1.2;
  }

  .change-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 8px 0 20px 0;
    gap: 10px;
  }

  .v-btn {
    width: 200px;
  }

  .main-container {
    margin-top: 30px;
  }

  .facturation-container {
    margin: auto;
    width: 80%;
  }

  @media screen and (min-width: 992px) {
    .container {
      margin-top: 30px;
    }

    .header-container {
      text-align: left;
    }

    .choose-subscription-container {
      height: 127px;
    }

    .change-btn {
      flex-direction: row;
      justify-content: flex-end;
      margin-left: 8px;
    }

    .arrow-container {
      display: flex !important;
    }
  }

  @media screen and (min-width: 1800px) {
    h1 {
      width: 80%;
      margin: auto;
    }

    .conditions {
      padding: 0;
      line-height: 1.2;
      width: 80%;
      margin: auto;
    }

    .change-btn {
      margin: auto;
      width: 80%;
      margin-top: 20px;
    }

    .container {
      padding: 0;
      gap: 50px;
      justify-content: center;
    }
  }

  @media screen and (min-width: 2400px) {
    h1 {
      width: 70%;
      margin: auto;
    }

    .conditions {
      padding: 0;
      line-height: 1.2;
      width: 70%;
      margin: auto;
    }

    .change-btn {
      margin: auto;
      width: 70%;
      margin-top: 20px;
    }

    .container {
      padding: 0;
      gap: 50px;
      justify-content: center;
    }
  }
</style>
