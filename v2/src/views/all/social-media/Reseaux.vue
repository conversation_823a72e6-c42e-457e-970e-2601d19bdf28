<template>
  <main class="container padding-container">
    <div class="reseau-container">
      <div id="bloc2">
        <!-- User details section -->

        <div class="user-details">
          <UserCard v-if="isLoggedIn" :candidate="currentUser" />
          <SideAd />
        </div>



        <div class="posts-display">
          <PostCreation
            v-if="isLoggedIn"
            @post-added="fetchPosts"
            class="fixed-post-creation"
            :currentUser="currentUser"
          />
          <PostSection
            :posts="posts"
            :currentUser="currentUser"
            :isLoggedIn="isLoggedIn"
            @post-removed="fetchPosts"
            @update-posts="updatePosts"
          />

          <!-- Indicateur de chargement -->
          <div v-if="isLoading" class="loading-indicator">
            <v-progress-circular
              :size="50"
              :width="5"
              color="primary"
              indeterminate
            ></v-progress-circular>
          </div>

          <!-- Message when no more posts -->
          <div v-if="!hasMorePosts && posts.length > 0" class="no-more-posts">
            Plus aucun post à charger
          </div>
        </div>
      </div>

      <BackToTopArrow />
    </div>
  </main>
</template>

<script>
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import PostCreation from '@/components/views-models/social-media/createPost.vue';
  import { getPosts } from '@/services/post.service.js';
  import PostSection from '../../../components/views-models/social-media/PostSection.vue';
  import UserCard from '../../../components/views-models/social-media/UserCard.vue';
  import SideAd from '../../../components/google-ad/SideAd.vue';

  export default {
    name: 'SocialMedia',

    components: {
      PostCreation,
      BackToTopArrow,
      PostSection,
      UserCard,
      SideAd,
    },
    data() {
      return {
        posts: [],
        currentPage: 1,
        postsPerPage: 3, // Réduit à 3 posts par chargement
        isLoading: false,
        hasMorePosts: true,
      };
    },
    computed: {
      currentUser() {
        return this.$store.getters.getUser;
      },
      isLoggedIn() {
        return this.$store.getters.isLoggedIn;
      },
    },
    async mounted() {
      // Charge seulement les 3 premiers posts au montage
      await this.fetchPosts();
      window.addEventListener('scroll', this.handleScroll);
    },
    beforeDestroy() {
      window.removeEventListener('scroll', this.handleScroll);
    },
    methods: {
      async fetchPosts() {
        if (this.isLoading || !this.hasMorePosts) {
          return;
        }

        this.isLoading = true;

        try {
          const response = await getPosts(this.currentPage, this.postsPerPage);

          this.hasMorePosts = response.data.next !== null;

          if (response.data.results && response.data.results.length > 0) {
            // Ajoute les nouveaux posts à la fin de la liste existante
            this.posts = [...this.posts, ...response.data.results];
            this.currentPage++;
          }

        } catch (error) {
          //console.error('Failed to fetch posts:', error);
          this.hasMorePosts = false;
        } finally {
          this.isLoading = false;
        }
      },

      handleScroll() {
        // Déclenche le chargement plus tôt pour une expérience plus fluide
        const scrollPosition = window.innerHeight + window.pageYOffset;
        const threshold = document.documentElement.scrollHeight - 500; // Augmenté à 500px

        if (scrollPosition >= threshold && !this.isLoading && this.hasMorePosts) {
          this.fetchPosts();
        }
      },

      // Méthode pour mettre à jour les posts après une suppression
      updatePosts(updatedPosts) {
        this.posts = updatedPosts;
      },
    },
  };
</script>

<style scoped>
  .fixed-post-creation {
    z-index: 100;
  }
  .spinner-container {
    position: fixed;
    top: 35%;
    left: 54%;
    transform: translate(-50%, -50%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;
  }

  .v-progress-circular {
    display: flex;
  }

  /******************************* General Layout ************************/
  #bloc2 {
    display: grid;
    grid-template-columns: 1fr 4fr;
    gap: 20px;
  }

  .reseau-container {
    margin-top: 30px;
  }

  .user-details {
    grid-column: 1;
    height: fit-content;
    position: sticky;
    top: 89px;
  }

  .posts-display {
    grid-column: 2;
    gap: 10px;
  }

  /*************************  Promotion styles  inutiles pour le moment *************************/
  /*
.promotion {
    background-color: #00A58E;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e6e6e6;
    text-align: center;
    color: white;
    margin-top: 0;
}

.promotion h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.promotion p {
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.promotion button {
    background-color: #e2b25b;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
}*/

  /***********************RESPONSIVE**********************/
  /* ######## MOBILE ######## */
  @media screen and (max-width: 480px) {
    .user-details {
      display: none;
    }
    #bloc2 {
      display: block;
    }
  }

  /* ######## TABLETTE PETITE ######## */
  @media screen and (min-width: 481px) and (max-width: 768px) {
    .user-details {
      display: none;
    }
    #bloc2 {
      display: block;
    }
  }

  /* ######## TABLETTE LARGE / TRANSITION VERS DESKTOP ######## */
  @media screen and (min-width: 769px) and (max-width: 991px) {
    .user-details {
      display: none;
    }
    #bloc2 {
      display: block;
    }
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }

  .loading-indicator {
    display: flex;
    justify-content: center;
    padding: 20px;
  }

  .no-more-posts {
    text-align: center;
    padding: 20px;
    color: #666;
  }
</style>
