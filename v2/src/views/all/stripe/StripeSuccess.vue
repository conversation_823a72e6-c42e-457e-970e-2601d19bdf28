<template>
  <div>
    <h2><PERSON><PERSON><PERSON> r<PERSON><PERSON>i</h2>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';

  export default {
    name: 'StripeSuccess',

    computed: {
      ...mapGetters(['isLoggedIn', 'getUser', 'userRole']),
    },

    methods: {
      // async updateFacture() {
      //   try {
      //     const response = await axios.get(`http://127.0.0.1:8000/api/facture`);
      //     const allInvoices = response.data;
      //     const userInvoices = allInvoices.results.filter(
      //       (invoice) => invoice.user === this.currentUser.id
      //     );
      //     const lastInvoice = userInvoices[userInvoices.length - 1];
      //     const lastInvoiceId = lastInvoice.id;
      //     //console.log(lastInvoiceId);
      //     try {
      //       await axios.post(
      //         `http://127.0.0.1:8000/api/facture/payement/${lastInvoiceId}/`
      //       );
      //     } catch (error) {
      //       //console.log(error);
      //     }
      //   } catch (error) {
      //     //console.log(error);
      //   }
      // },
    },
  };
</script>

<style scoped>
  h2 {
    padding: 50px;
  }
</style>
