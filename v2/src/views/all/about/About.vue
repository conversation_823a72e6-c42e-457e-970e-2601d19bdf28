<template>
  <main class="container padding-container flex flex-col">
    <h1>A propos de Thanks-Boss</h1>

    <!-- hero section -->
    <div class="hero-section">
      <img src="@/assets/about-hero-bg.svg" alt="about-hero-bg" />
      <article class="card-container">
        <h2>
          C’est ainsi que<span style="color: var(--primary-1)"
            >&nbsp;notre histoire&nbsp;</span
          >a commencé...
        </h2>
        <p v-html="heroTextContent"></p>
      </article>
    </div>

    <article class="card-section">
      <AboutCard
        title="Innovation"
        description="Chez Thanks-Boss, l'innovation est bien plus qu'une simple valeur, c'est notre moteur. Chaque jour, nous cherchons de nouvelles façons de rapprocher les talents des opportunités qui leur correspondent vraiment. Notre objectif est de rendre le parcours de chacun plus fluide, plus pertinent et plus intuitif."
      />
      <AboutCard
        title="Qualité"
        description="Nous savons combien il est important pour vous de trouver une opportunité qui vous épanouit, ou de recruter le candidat idéal qui s’intégrera parfaitement à votre équipe. C'est pourquoi nous veillons à ce que chaque interaction avec Thanks-Boss soit une expérience positive et mémorable"
      />
      <AboutCard
        title="Écoute"
        description="Nous prenons le temps de vraiment comprendre vos besoins, vos aspirations et vos défis. Que vous soyez un candidat cherchant la prochaine étape de votre carrière ou un recruteur à la recherche du talent idéal, nous sommes là pour vous accompagner et vous orienter à chaque étape du processus."
      />
    </article>

    <article class="banner-image">
      <figure>
        <img src="@/assets/banner-about.png" alt="about-banner" />
        <figcaption>
          Thanks-Boss représenté par Florentin Dam, le CEO, lors du Marathon
          Numérique à Gaillac - 4 juin 2024
        </figcaption>
      </figure>
    </article>

    <article class="team-section">
      <div class="team-section-wrapper">
        <h2>Les humains derrière la machine</h2>
        <div class="team-grid">
          <figure class="photo-container">
            <img
              src="@/assets/persons/florentin.png"
              alt="team-1"
              class="image"
            />
            <figcaption>Florentin DAM</figcaption>
            <p>Créateur de Thanks-Boss & Data Scientist</p>
            <div class="middle">
              <p>
                +10 ans en intelligence artificielle, big data et développement
                web
              </p>
            </div>
          </figure>

          <figure class="photo-container">
            <img src="@/assets/persons/serge.png" alt="team-1" class="image" />
            <figcaption>Serge DOORGACHURN</figcaption>
            <p>Bras Droit & Lead Développeur</p>
            <div class="middle">
              <p>15 ans de management</p>
            </div>
          </figure>

          <figure class="photo-container">
            <img src="@/assets/persons/hafida.png" alt="team-1" class="image" />
            <figcaption>Hafida AMZELLOUG</figcaption>
            <p>Directrice Marketing & Communication</p>
            <div class="middle">
              <p>+15 ans en stratégie growth et marketing</p>
            </div>
          </figure>

          <figure class="photo-container">
            <img
              src="@/assets/persons/anonyme.png"
              alt="team-1"
              class="image"
            />
            <figcaption>Christian MARECHAL</figcaption>
            <p>Directeur IA</p>
            <div class="middle">
              <p>Master 2 chez Polytechnique et Centrale-Supélec</p>
              <p>+23 ans dans l'intelligence artificielle</p>
            </div>
          </figure>

          <figure class="photo-container">
            <img
              src="@/assets/persons/guillaume.png"
              alt="team-1"
              class="image"
            />
            <figcaption>Guillaume GAUDIN</figcaption>
            <p>Directeur Business & Cybersécurité</p>
            <div class="middle">
              <p>Président de l'ESN Insighnest</p>
            </div>
          </figure>
        </div>
      </div>
    </article>

    <OurPartners />

    <!-- contact form -->
    <div class="form-container">
      <v-form @submit.prevent ref="contactForm" class="form-wrapper">
        <h2>Rejoignez l'équipe qui réinvente le recrutement</h2>
        <p>
          Si vous êtes passionné(e) par l'intelligence artificielle,
          désireux(se) de transformer le monde du travail, et prêt(e) à relever
          des défis stimulants, vous êtes au bon endroit ! Remplissez le
          formulaire ci-dessous pour explorer comment vous pouvez contribuer à
          créer un impact réel et positif dans la vie professionnelle des
          autres.
        </p>
        <div class="row1">
          <div class="field1">
            <h5>Nom</h5>
            <v-text-field
              v-model="formData.last_name"
              :rules="[...nameRules, ...notEmptyRules]"
              label="Votre nom"
              variant="solo"
              flat
            ></v-text-field>
          </div>

          <div class="field1">
            <h5>Prénom</h5>
            <v-text-field
              v-model="formData.first_name"
              :rules="[...nameRules, ...notEmptyRules]"
              label="Votre prénom"
              variant="solo"
              flat
            ></v-text-field>
          </div>
        </div>

        <div class="field2">
          <h5>Email</h5>
          <v-text-field
            v-model="formData.email"
            :rules="[...emailRules, ...notEmptyRules]"
            label="Votre email"
            variant="solo"
            flat
          ></v-text-field>
        </div>

        <div class="field2">
          <h5>Message</h5>
          <v-textarea
            clearable
            label="Votre message"
            :rules="notEmptyRules"
            v-model="formData.message"
            variant="solo"
            flat
          ></v-textarea>
        </div>

        <div class="file-inputs-container">
          <div class="middle-container">
            <div class="disabled-field-wrapper">
              <p v-if="cvName">{{ cvName }}</p>
              <label v-else>CV</label>

              <input
                type="file"
                accept="application/pdf"
                ref="fileInput"
                @change="uploadCV"
                style="display: none"
              />
              <v-text-field
                v-if="!cv"
                label="Choisissez un cv (format pdf uniquement)"
                type="text"
                disabled
                :rules="notEmptyRules"
              />
            </div>
            <div class="btn-wrapper d-flex a-center">
              <PrimaryNormalButton
                textContent="Parcourir"
                btnColor="secondary"
                class="upload-btn"
                @click="triggerCvInput"
              />
            </div>
          </div>
          <div class="middle-container">
            <div class="disabled-field-wrapper">
              <p v-if="motivLetterName">{{ motivLetterName }}</p>
              <label v-else>Lettre de motivation</label>
              <input
                type="file"
                accept="application/pdf"
                ref="motivFileInput"
                @change="uploadMotivLetter"
                style="display: none"
              />
              <v-text-field
                v-if="!motivation"
                label="Choisissez une lettre de motivation (format pdf uniquement)"
                type="text"
                disabled
              />
            </div>
            <div class="btn-wrapper d-flex a-center">
              <PrimaryNormalButton
                textContent="Parcourir"
                btnColor="secondary"
                class="upload-btn"
                @click="triggerMotivLetterInput"
              />
            </div>
          </div>
        </div>

        <div class="row-btn">
          <div @click="submitForm">
            <PrimaryNormalButton textContent="Envoyer ma candidature" send />
          </div>
        </div>
      </v-form>
    </div>

    <BackToTopArrow />
  </main>
</template>

<script>
  import PrimaryNormalButton from '@/components/buttons/PrimaryNormalButton.vue';
  import AboutCard from '../../../components/cards/about-card/AboutCard.vue';
  import OurPartners from '@/components/views-models/home/<USER>';
  import { submitHiringForm } from '../../../services/contact.service';
  import { toaster } from '../../../utils/toast/toast';
  import {
    validateEmail,
    validateName,
    validateNotEmpty,
  } from '../../../utils/validationRules';
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';

  export default {
    name: 'About',
    components: {
      PrimaryNormalButton,
      AboutCard,
      OurPartners,
      BackToTopArrow,
    },
    data() {
      return {
        formData: {},
        cv: null,
        cvName: null,
        motivation: null,
        motivLetterName: null,
        heroTextContent:
          "Imaginez un monde où chaque candidature envoyée est accueillie par le silence, où les promesses d’évolution se révèlent être des mirages, où vous êtes bloqué sous un chef qui ne vous comprend pas. Combien de personnes vivent ce calvaire en espérant secrètement un tournant dans leur carrière ? Le nombre est effrayant. <br> <br> C'est dans ce contexte de désespoir que Florentin a vécu son propre chemin de croix. Diplômé en intelligence artificielle, il débute dans une entreprise qui ne tient pas ses promesses. Sept années passées à naviguer entre déceptions et frustrations, cherchant en vain des défis qui pourraient l’épanouir. <br> <br> Mais plutôt que de céder au découragement, Florentin a pris une décision audacieuse.Avec une équipe de passionnés, il a donné vie à Thanks- Boss, une plateforme dédiée à réinventer le recrutement grâce à l’intelligence artificielle.Leur objectif ? Créer un pont entre les candidats en quête d’opportunités et les recruteurs en quête de talents. <br> <br>Le succès a été immédiat.Les candidats découvrent désormais des opportunités qui résonnent avec leurs aspirations, et les recruteurs trouvent des talents correspondant véritablement à leurs besoins. <br> <br>Thanks - Boss ne se contente pas de simplifier le recrutement; il le transforme en une quête de sens et d’épanouissement.Florentin a montré qu'une grande frustration peut être le tremplin vers une grande innovation. <br> <br> Venez écrire avec nous le prochain chapitre de cette aventure. <br> Rejoignez Thanks - Boss et construisons ensemble un avenir où chaque carrière trouve son véritable sens.",
        /* form rules */
        emailRules: [(v) => validateEmail(v) || true],
        nameRules: [(v) => validateName(v) || true],
        notEmptyRules: [(v) => validateNotEmpty(v) || true],
      };
    },

    methods: {
      //  validate & submit form
      async submitForm() {
        const validate = await this.$refs.contactForm.validate();
        if (validate.valid) await submitHiringForm(this.formData);
        else
          return toaster.showInfoPopup(
            'Les informations sur le formulaire sont incomplètes.'
          );
      },
      //  upload CV
      uploadCV(event) {
        const selectedFile = event.target.files[0];
        if (selectedFile) {
          const reader = new FileReader();
          reader.readAsDataURL(selectedFile);
          this.cv = selectedFile;
          this.cvName = selectedFile.name;
          this.formData.cv = selectedFile;
        }
      },
      uploadMotivLetter(event) {
        const selectedFile = event.target.files[0];
        if (selectedFile) {
          const reader = new FileReader();
          reader.readAsDataURL(selectedFile);
          this.motivation = selectedFile;
          this.motivLetterName = selectedFile.name;
          this.formData.motivation = selectedFile;
        }
      },
      triggerCvInput() {
        this.$refs.fileInput.click();
      },
      triggerMotivLetterInput() {
        this.$refs.motivFileInput.click();
      },
    },
  };
</script>

<style>
  .disabled-field-wrapper .v-input__control {
    background-color: rgb(255, 255, 255);
    color: black;
  }
</style>

<style scoped>
  .container {
    height: fit-content;
  }

  .hero-section {
    height: fit-content;
    width: 100%;
    padding: 20px;
    margin-top: 30px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    object-fit: cover;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 1;
  }

  .hero-section img {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: -1;
    object-fit: cover;
  }
  .hero-section h2 {
    width: 100%;
  }

  .card-container {
    height: auto;
    width: 1000px;
    border-radius: 30px;
    padding: 24px;
    display: flex;
    flex-direction: column;
    align-items: baseline;
    justify-content: center;
    background-color: rgb(255, 253, 252, 0.7);
  }

  .card-container h2 {
    margin-bottom: 20px;
  }

  .card-section {
    padding-top: 50px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 2rem;
  }

  .banner-image {
    padding-top: 80px;
    margin-bottom: 120px;
    width: 100%;
  }

  .banner-image figure {
    height: 100%;
    width: 1050px;
    object-fit: none;
    border-radius: 20px;
    position: relative;
    display: flex;
    flex-direction: column;
    margin: 0 auto;
  }

  .banner-image figure figcaption {
    position: absolute;
    top: 80px;
    right: -33px;
    background-color: rgba(38, 40, 43, 0.8);
    color: white;
    padding-block: 10px;
    padding-inline: 3px;
    max-width: 432px;
    border-radius: 5px;
  }

  .team-section {
    width: 100%;
    padding: 45px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: var(--text-1);
  }

  .team-section-wrapper {
    background-color: var(--surface-bg-2);
    padding-bottom: 80px;
  }

  .team-section-wrapper h2 {
    color: var(--text-1);
    padding-left: 40px;
    padding-block: 40px;
  }

  .team-grid {
    width: 1020px;
    padding: 24px;
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    row-gap: 80px;
  }

  .row1 {
    width: 100%;
  }

  .row-btn {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }

  .form-container {
    background-image: url('../../../assets/background/about-candidature-bg.png');
    background-size: cover;
    width: 100%;
    height: fit-content;
    display: flex;
    justify-content: center;
  }

  .form-wrapper {
    height: fit-content;
    width: 70%;
    margin-top: 50px;
    max-width: 900px;
    margin-bottom: 40px;
    background-color: hsla(216, 6%, 16%, 0.6);
    border-radius: 15px;
    padding-block: 16px;
    padding-inline: 24px;
    color: var(--surface-bg-2);
  }

  .file-inputs-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .middle-container {
    display: flex;
  }

  .middle-container {
    display: flex;
    justify-content: center;
    gap: 10px;
  }

  .disabled-field-wrapper {
    width: 100%;
  }

  .disabled-field-wrapper p {
    font-size: 20px;
    word-break: break-all;
    background-color: var(--surface-bg-2);
    padding: 6px;
    color: var(--text-1);
    border-radius: 4px;
  }

  /* hover effect */
  .image {
    opacity: 1;
    display: block;
    width: 100%;
    height: auto;
    transition: 0.5s ease;
    backface-visibility: hidden;
  }

  .photo-container {
    position: relative;
    width: 270px;
    height: 298px;
  }

  .middle {
    transition: 0.5s ease;
    opacity: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    text-align: center;
  }

  .photo-container:hover .image {
    opacity: 0.3;
  }

  .photo-container:hover .middle {
    opacity: 1;
  }

  /* @media screen and (max-width: 1024px) {
    .team-grid {
      width: auto;
      justify-content: center;
    }
    .team-section-wrapper h2 {
      padding: 20px;
    }
  } */

  /* @media screen and (max-width: 1256px) {
    .banner-image figure {
      height: 100%;
      min-width: inherit;
      width: 100%;
      object-fit: none;
      display: flex;
      flex-direction: column;
    }

    .banner-image figure figcaption {
      margin-top: 20px;
      position: unset;
    }

    .form-wrapper {
      width: 95%;
    }
  } */

  /* @media screen and (min-width: 866px) {
    .hero-section {
      height: 580px;
      padding-block: 20px;
      margin-top: 30px;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      object-fit: cover;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      z-index: 1;
    }

    .hero-section h2 {
      display: flex;
      justify-content: center;
    }

    .card-container {
      height: 100%;
    }

    .card-section {
      flex-direction: row;
      justify-content: center;
      gap: 2rem;
    }
  } */

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }
</style>
