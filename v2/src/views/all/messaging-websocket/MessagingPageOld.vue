<template>
  <main class="container padding-container">
    <div class="messagerie-container">
      <VideoCallWindow
        v-if="isVideoVisible"
        @end_video_call="endVideoCall"
        :stream="localStream"
        ref="videoWindow"
      />
      <VoiceCallWindow
        v-if="isVoiceVisible"
        @end_video_call="endVideoCall"
        :stream="localStream"
        ref="voiceWindow"
      />
      <ChatUsersList
        v-if="!isVideoVisible"
        @selectUser="handleSelectRecipient"
        @lastConversation="handleLastConversation"
      />

      <ChatWindow
        :receiver="
          selectedRecipient || { id: null, first_name: 'Aucun', last_name: 'destinataire' }
        "
        :key="selectedRecipient ? selectedRecipient.id : 'no-recipient'"
        @start-video-call="startVideoCall"
        @start-voice-call="startVoiceCall"
        :lastConversation="lastConversation"
      />
    </div>
  </main>
</template>

<script>
  import ChatUsersList from '@/components/chat/ChatUsersList.vue';
  import ChatWindow from '@/components/chat/ChatWindow.vue';
  import { initializePrivateWebSocket } from '@/services/conversation-websocket.service';
  import VideoCallWindow from '@/components/views-models/messaging/VideoCallWindow.vue';
  import VoiceCallWindow from '@/components/views-models/messaging/VoiceCallWindow.vue';
  import { getUserById } from '@/services/account.service';
  import { handleIncomingCall } from '@/services/video-call.service';
  import { createRingtone } from '@/assets/sounds/ringtone';
  import { addUnreadMessage } from '@/services/unread-messages.service';
  import { initNotificationSound, playNotificationSound } from '@/services/notification.service';
  export default {
    components: {
      VideoCallWindow,
      VoiceCallWindow,
      ChatUsersList,
      ChatWindow,
    },
    props: {
      user: {
        type: Object,
      },
    },
    data() {
      return {
        webSocketConnection: null,
        lastConversation: null,
        selectedRecipient: null,
        isVideoVisible: false,
        isVoiceVisible: false,
        isAuthenticated: false,
        newMessage: '',
        messages: [],
        peerConnection: null,
        localStream: null,
        callerId: null,
        isCallAccepted: false,
        pendingIceCandidates: [],
        isCallInitiator: false,
        pendingOffer: null,
        callType: null,
        isEndingCall: false, // Drapeau pour éviter les appels multiples à endVideoCall
        lastEndCallTimestamp: 0, // Horodatage du dernier message end_call envoyé
        ringtone: null, // Sonnerie pour les appels entrants
        isRinging: false // Indique si la sonnerie est en cours de lecture
      };
    },
    created() {
      // Initialiser la sonnerie avec gestion d'erreur
      try {
        //console.log('%c[APPEL] Initialisation de la sonnerie...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

        // Créer la sonnerie
        this.ringtone = createRingtone();

        // Vérifier que la sonnerie a été correctement initialisée
        if (this.ringtone) {
          //console.log('%c[APPEL] Sonnerie initialisée avec succès dans created()', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');

          // Ajouter des écouteurs d'événements pour déboguer
          this.ringtone.addEventListener('canplaythrough', () => {
            //console.log('%c[APPEL] Sonnerie prête à être jouée (canplaythrough)', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
          });

          this.ringtone.addEventListener('error', (e) => {
            //console.error('%c[APPEL] Erreur avec la sonnerie:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', e);
          });

          // Précharger explicitement le fichier audio
          this.ringtone.preload = 'auto';
          this.ringtone.load();

          // Ne pas tester la sonnerie au chargement de la page pour éviter de jouer un son
          // sans interaction utilisateur
        } else {
          //console.error('%c[APPEL] Échec de l\'initialisation de la sonnerie', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');

          // Tentative de réinitialisation avec un autre fichier
          //console.log('%c[APPEL] Tentative de réinitialisation avec un autre fichier...', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');
          this.ringtone = new Audio('/sounds/notification.wav');
          this.ringtone.loop = true;
          this.ringtone.preload = 'auto';
          this.ringtone.load();
        }
      } catch (error) {
        //console.error('%c[APPEL] Erreur lors de l\'initialisation de la sonnerie:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);

        // Tentative de récupération en cas d'erreur
        try {
          //console.log('%c[APPEL] Tentative de récupération après erreur...', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');
          this.ringtone = new Audio('/sounds/notification.wav');
          this.ringtone.loop = true;
          this.ringtone.preload = 'auto';
          this.ringtone.load();
        } catch (fallbackError) {
          //console.error('%c[APPEL] Échec de la tentative de récupération:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', fallbackError);
        }
      }

      // Initialiser le son de notification
      try {
        initNotificationSound();
        //console.log('%c[NOTIFICATION] Son de notification initialisé avec succès', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
      } catch (error) {
        //console.error('%c[NOTIFICATION] Erreur lors de l\'initialisation du son de notification:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
      }

      // Ajouter l'écouteur d'événement beforeunload
      window.addEventListener('beforeunload', this.resetCallState);

      // Ajouter des écouteurs d'événements pour les appels
      window.addEventListener('stop-ringtone', this.stopRingtone);
      window.addEventListener('call-accepted', this.stopRingtone);
      window.addEventListener('call-rejected', this.stopRingtone);
      window.addEventListener('call-ended', this.stopRingtone);

      // Exposer la méthode stopRingtone globalement pour permettre aux autres composants d'y accéder
      window.stopCallRingtone = this.stopRingtone;

      // Initialiser l'appel depuis l'URL si nécessaire
      this.initializeCallFromUrl();

      // S'assurer que le WebSocket est initialisé
      if (!this.webSocketConnection) {
        //console.log('%c[WEBSOCKET] Initialisation du WebSocket dans created()...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
        this.webSocketConnection = initializePrivateWebSocket();
        if (this.webSocketConnection) {
          this.setupSocketListeners();
        } else {
          //console.error('%c[WEBSOCKET] Échec de l\'initialisation du WebSocket dans created()', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
        }
      }
    },

    beforeDestroy() {
      // Supprimer les écouteurs d'événements
      window.removeEventListener('beforeunload', this.resetCallState);
      window.removeEventListener('stop-ringtone', this.stopRingtone);
      window.removeEventListener('call-accepted', this.stopRingtone);
      window.removeEventListener('call-rejected', this.stopRingtone);
      window.removeEventListener('call-ended', this.stopRingtone);

      // Supprimer la référence globale
      if (window.stopCallRingtone) {
        window.stopCallRingtone = null;
      }

      // Réinitialiser l'état de l'appel
      this.resetCallState();
    },
    methods: {
      // On prend l'utilisateur à partir de l'url si il existe
      // cela est utilisé pour que le message layout peut bien redirigé vers la page de messagerie avec le user seleccioné
      async initializeCallFromUrl() {
        const query = this.$route.query;
        if (query.chat) {
          try {
            // Récupérer les informations de l'utilisateur
            const user = await getUserById(query.chat);
            if (!user) {
              //console.error('Utilisateur non trouvé avec l\'ID:', query.chat);
              return;
            }

            //console.log('Utilisateur récupéré:', user);

            // Si c'est un appel d'entretien, on ajoute des métadonnées supplémentaires
            if (query.entretien === 'true') {
              this.callMetadata = {
                isEntretien: true,
                entretienId: query.entretien_id
              };
            }

            // Vérifier que le WebSocket est bien initialisé
            if (!this.webSocketConnection) {
              //console.error('Impossible d\'initialiser le WebSocket de conversation');
              // Tenter une réinitialisation
              const webSocket = initializePrivateWebSocket();
              if (webSocket) {
                this.webSocketConnection = webSocket;
                this.setupSocketListeners();
                //console.log('WebSocket réinitialisé avec succès');
              } else {
                //console.error('Échec de la réinitialisation du WebSocket');
              }
            }

            if (query.accept === 'true') {
              // C'est un appel entrant à accepter
              //console.log('Acceptation d\'un appel entrant depuis l\'URL');

              // Récupérer le type d'appel
              this.callType = query.call_type || 'video';

              // Récupérer l'offre depuis sessionStorage au lieu de l'URL
              const offerString = sessionStorage.getItem('pendingOffer');
              if (offerString) {
                try {
                  //console.log('%c[APPEL] Offre récupérée depuis sessionStorage', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
                  
                  // Parser l'offre
                  this.pendingOffer = JSON.parse(offerString);
                  //console.log('Offre parsée avec succès:', this.pendingOffer);
                  
                  // Nettoyer sessionStorage après récupération
                  sessionStorage.removeItem('pendingOffer');
                  //console.log('%c[APPEL] Offre supprimée de sessionStorage', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');

                  // L'ID de l'appelant est dans query.chat
                  this.callerId = query.chat;
                  this.isCallInitiator = false;

                  //console.log('Prêt à accepter l\'appel de type', this.callType, 'de l\'utilisateur', this.callerId);

                  // Accepter l'appel avec le type spécifié
                  await this.acceptCall(this.callType);
                } catch (err) {
                  //console.error('Erreur lors du parsing de l\'offre:', err);
                  this.resetCallState();
                }
              } else {
                //console.error('%c[APPEL] Aucune offre trouvée dans sessionStorage', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
                this.resetCallState();
              }
            } else {
              // C'est un appel sortant à initier
              //console.log('Initiation d\'un appel sortant depuis l\'URL');
              this.startVideoCall(user);
            }
          } catch (error) {
            //console.error('Erreur lors de l\'initialisation depuis l\'URL:', error);
            this.resetCallState();
          }
        }
      },
      resetUrlParams() {
        this.$router.replace({ query: {} });
      },

      /**
       * Nettoie l'URL et recharge la page
       * Cette méthode est utilisée pour recharger la page après la fin d'un appel
       * tout en s'assurant que les paramètres d'URL sont nettoyés
       */
      cleanUrlAndReload() {
        //console.log('%c[APPEL] Nettoyage de l\'URL et rechargement de la page...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

        // Vérifier si l'URL contient des paramètres
        const hasQueryParams = window.location.search !== '';

        if (hasQueryParams) {
          // Nettoyer l'URL en supprimant les paramètres
          const cleanUrl = window.location.pathname;

          // Utiliser l'API History pour remplacer l'URL actuelle sans recharger la page
          window.history.replaceState({}, document.title, cleanUrl);

          //console.log('%c[APPEL] URL nettoyée:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', cleanUrl);
        }

        // Recharger la page
        window.location.reload();
      },
      handleSelectRecipient(recipient) {
        //console.log('%c[APPEL] Sélection du destinataire:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', recipient);

        // Vérifier que le destinataire est bien défini
        if (!recipient || !recipient.id) {
          //console.error('%c[APPEL] Erreur: Destinataire non défini ou sans ID', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
          return;
        }

        this.selectedRecipient = recipient;

        // Initialiser le WebSocket privé pour l'utilisateur courant
        const webSocket = initializePrivateWebSocket();

        if (!webSocket) {
          //console.error('%c[APPEL] Erreur: WebSocket non initialisé', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
          return;
        }

        this.webSocketConnection = webSocket;
        this.setupSocketListeners();

        //console.log('%c[APPEL] WebSocket initialisé avec succès pour la conversation avec:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', recipient.id);

        // Vérifier si l'utilisateur a été sélectionné depuis le menu "Mon réseau"
        // et si un appel doit être initié (via l'URL)
        const query = this.$route.query;
        if (query.call === 'true' && !query.accept) {
          //console.log('%c[APPEL] Initiation d\'un appel depuis handleSelectRecipient', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          // Attendre un court instant pour que le WebSocket soit bien initialisé
          setTimeout(() => {
            this.startVideoCall(recipient);
          }, 500);
        }
      },
      async acceptCall(callType) {
        //console.log('%c[APPEL] Début de acceptCall', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', callType);
        try {
          //console.log(`Acceptation de l'appel ${callType}`);

          // Arrêter la sonnerie
          this.stopRingtone();

          this.isCallAccepted = true;

          // Afficher l'interface utilisateur appropriée
          if (callType === 'video') {
            this.isVideoVisible = true;
            this.isVoiceVisible = false;
          } else {
            this.isVideoVisible = false;
            this.isVoiceVisible = true;
          }

          // Initialiser la connexion peer-to-peer
          //console.log("Tentative d'initialisation de la connexion peer-to-peer");
          await this.initializePeerConnection(callType);

          // Obtenir le flux média local
          let stream = await this.handleLocalStream(callType);
          if (!stream) {
            throw new Error("Impossible d'accéder au flux média local");
          }

          // Vérifier s'il y a une offre en attente
          if (!this.pendingOffer) {
            throw new Error("Pas d'offre en attente");
          }

          // Définir la description distante
          //console.log('Définition de la description distante');
          const offerDescription = new RTCSessionDescription(this.pendingOffer);
          await this.peerConnection.setRemoteDescription(offerDescription);

          // Créer et définir la réponse locale
          //console.log('Création de la réponse');
          const answer = await this.peerConnection.createAnswer({
            offerToReceiveAudio: true,
            offerToReceiveVideo: true,
          });
          await this.peerConnection.setLocalDescription(answer);

          // Envoyer la réponse à l'appelant
          //console.log('Envoi de la réponse');
          await this.sendAnswer(answer);

          // Traiter les candidats ICE en attente
          if (this.pendingIceCandidates.length > 0) {
            //console.log(`Traitement de ${this.pendingIceCandidates.length} candidats ICE en attente`);
            for (const candidate of this.pendingIceCandidates) {
              await this.peerConnection.addIceCandidate(candidate);
            }
            this.pendingIceCandidates = [];
          }

          // Réinitialiser l'offre en attente
          this.pendingOffer = null;
          //console.log('Appel accepté avec succès');
        } catch (err) {
          //console.error("Erreur lors de l'acceptation de l'appel:", err);
          this.isCallAccepted = false;
          this.resetCallState();
        }
      },
      rejectCall() {
        //console.log('%c[APPEL] Rejet de l\'appel', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

        // Arrêter la sonnerie
        this.stopRingtone();

        try {
          // Envoyer un message de rejet via WebSocket
          if (this.webSocketConnection && this.webSocketConnection.readyState === WebSocket.OPEN) {
            // Récupérer l'ID de l'utilisateur connecté depuis le store
            const currentUser = this.$store.getters.getUser;
            if (!currentUser || !currentUser.id) {
              //console.error('%c[APPEL] Erreur: Impossible de récupérer l\'ID de l\'utilisateur connecté lors du rejet de l\'appel', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
              this.resetCallState();
              return;
            }

            const rejectCallMessage = {
              type: 'reject_call',
              sender_id: currentUser.id,
              receiver_id: this.callerId
            };
            //console.log('%c[APPEL] Envoi du message de rejet d\'appel:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', rejectCallMessage);
            this.webSocketConnection.send(JSON.stringify(rejectCallMessage));
          }
        } catch (error) {
          //console.error('%c[APPEL] Erreur lors du rejet de l\'appel:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
        } finally {
          // Réinitialiser l'état de l'appel
          this.resetCallState();
        }
      },
      handleLastConversation(conversation) {
        //console.log('Dernière conversation récupérée :', conversation);
        this.lastConversation = conversation;
      },
      // Offre d'appel entrant
      /**
       * Joue la sonnerie pour les appels entrants
       */
      playRingtone() {
        //console.log('%c[APPEL] Tentative de lecture de la sonnerie...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

        // Vérifier si la sonnerie est initialisée
        if (!this.ringtone) {
          console.warn('%c[APPEL] Sonnerie non initialisée, tentative de réinitialisation', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');

          // Tenter de réinitialiser la sonnerie
          const { createRingtone } = require('@/assets/sounds/ringtone');
          this.ringtone = createRingtone();

          if (!this.ringtone) {
            //console.error('%c[APPEL] Échec de la réinitialisation de la sonnerie', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            return;
          }
        }

        // Vérifier si la sonnerie est déjà en cours de lecture
        if (this.isRinging) {
          //console.log('%c[APPEL] Sonnerie déjà en cours de lecture', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          return;
        }

        try {
          // Vérifier l'état de l'élément audio
          //console.log('%c[APPEL] État de la sonnerie avant lecture:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', {
            paused: this.ringtone.paused,
            currentTime: this.ringtone.currentTime,
            duration: this.ringtone.duration,
            readyState: this.ringtone.readyState,
            networkState: this.ringtone.networkState,
            src: this.ringtone.src
          });

          // Réinitialiser la sonnerie au début
          this.ringtone.currentTime = 0;

          // S'assurer que le volume est au maximum
          this.ringtone.volume = 1.0;

          // S'assurer que la boucle est activée
          this.ringtone.loop = true;

          // Jouer la sonnerie
          const playPromise = this.ringtone.play();

          if (playPromise !== undefined) {
            playPromise
              .then(() => {
                //console.log('%c[APPEL] Sonnerie démarrée avec succès', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
                this.isRinging = true;
              })
              .catch(error => {
                //console.error('%c[APPEL] Erreur lors de la lecture de la sonnerie:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);

                // Si l'erreur est liée à l'interaction utilisateur, essayer une approche alternative
                if (error.name === 'NotAllowedError') {
                  //console.log('%c[APPEL] Erreur d\'autorisation, tentative alternative...', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');

                  // Créer un nouvel élément audio directement
                  const alternativeAudio = new Audio('/sounds/sound2.wav');
                  alternativeAudio.loop = true;
                  alternativeAudio.volume = 1.0;

                  // Tenter de jouer l'audio alternatif
                  alternativeAudio.play()
                    .then(() => {
                      //console.log('%c[APPEL] Sonnerie alternative démarrée avec succès', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
                      this.ringtone = alternativeAudio;
                      this.isRinging = true;
                    })
                    .catch(altError => {
                      //console.error('%c[APPEL] Échec de la sonnerie alternative:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', altError);
                      this.isRinging = false;
                    });
                } else {
                  this.isRinging = false;
                }
              });
          } else {
            console.warn('%c[APPEL] La méthode play() n\'a pas retourné de promesse', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');
          }
        } catch (error) {
          //console.error('%c[APPEL] Erreur lors de la lecture de la sonnerie:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
          this.isRinging = false;

          // Tenter une approche alternative en cas d'erreur
          try {
            //console.log('%c[APPEL] Tentative avec une approche alternative...', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');

            // Créer un nouvel élément audio avec un autre fichier
            const fallbackAudio = new Audio('/sounds/notification.wav');
            fallbackAudio.loop = true;
            fallbackAudio.volume = 1.0;

            // Tenter de jouer l'audio de secours
            fallbackAudio.play()
              .then(() => {
                //console.log('%c[APPEL] Sonnerie de secours démarrée avec succès', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
                this.ringtone = fallbackAudio;
                this.isRinging = true;
              })
              .catch(fallbackError => {
                //console.error('%c[APPEL] Échec de la sonnerie de secours:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', fallbackError);
              });
          } catch (fallbackError) {
            //console.error('%c[APPEL] Échec de la tentative alternative:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', fallbackError);
          }
        }
      },

      /**
       * Arrête la sonnerie
       */
      stopRingtone() {
        //console.log('%c[APPEL] Tentative d\'arrêt de la sonnerie...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

        if (!this.ringtone) {
          console.warn('%c[APPEL] Aucune sonnerie à arrêter', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');
          this.isRinging = false;
          return;
        }

        try {
          // Vérifier l'état de l'élément audio avant l'arrêt
          //console.log('%c[APPEL] État de la sonnerie avant arrêt:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', {
            paused: this.ringtone.paused,
            currentTime: this.ringtone.currentTime,
            duration: this.ringtone.duration,
            readyState: this.ringtone.readyState,
            networkState: this.ringtone.networkState
          });

          // Arrêter la sonnerie
          this.ringtone.pause();
          this.ringtone.currentTime = 0;
          this.isRinging = false;

          //console.log('%c[APPEL] Sonnerie arrêtée avec succès', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
        } catch (error) {
          //console.error('%c[APPEL] Erreur lors de l\'arrêt de la sonnerie:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);

          // Réinitialiser l'état de la sonnerie en cas d'erreur
          this.isRinging = false;

          // Tenter de créer une nouvelle instance audio pour éviter les problèmes futurs
          try {
            const { createRingtone } = require('@/assets/sounds/ringtone');
            this.ringtone = createRingtone();
            //console.log('%c[APPEL] Sonnerie réinitialisée après erreur d\'arrêt', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          } catch (reinitError) {
            //console.error('%c[APPEL] Erreur lors de la réinitialisation de la sonnerie:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', reinitError);
          }
        }
      },

      async handleOffer(data) {
        try {
          //console.log('%c[APPEL] Données d\'offre reçues:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', data);

          // Vérifier si l'offre est valide
          if (!data || !data.offer) {
            console.warn('%c[APPEL] Offre invalide reçue', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');
            return;
          }

          // Récupérer l'ID de l'utilisateur connecté depuis le store
          const currentUser = this.$store.getters.getUser;
          if (!currentUser || !currentUser.id) {
            //console.error('%c[APPEL] Erreur: Impossible de récupérer l\'ID de l\'utilisateur connecté lors du traitement de l\'offre', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            return;
          }

          // Ignorer les offres provenant de nous-mêmes
          if (data.sender_id === currentUser.id) {
            //console.log('%c[APPEL] Ignorer l\'offre provenant de nous-mêmes', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
            return;
          }

          // Ignorer les offres si nous sommes déjà en appel
          if (this.isVideoVisible || this.isVoiceVisible) {
            //console.log('%c[APPEL] Déjà en appel, ignorer l\'offre entrante', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
            // TODO: Envoyer un message de rejet à l'appelant
            return;
          }

          // Stocker les informations de l'appel
          this.callerId = data.sender_id;
          this.callType = data.call_type || 'video'; // Par défaut, considérer comme un appel vidéo
          this.isCallInitiator = false;
          this.pendingOffer = data.offer;

          //console.log('%c[APPEL] Appel entrant de type', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;', this.callType, 'de l\'utilisateur', this.callerId);

          // Vérifier l'état de la sonnerie avant de la jouer
          //console.log('%c[APPEL] État de la sonnerie avant de la jouer:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', {
            ringtoneExists: !!this.ringtone,
            ringtoneType: this.ringtone ? typeof this.ringtone : 'undefined',
            ringtoneProperties: this.ringtone ? Object.keys(this.ringtone) : []
          });

          // Jouer la sonnerie pour l'appel entrant
          //console.log('%c[APPEL] Tentative de lecture de la sonnerie...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

          // Si la sonnerie n'est pas initialisée, la réinitialiser
          if (!this.ringtone) {
            console.warn('%c[APPEL] Sonnerie non initialisée, réinitialisation...', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');
            const { createRingtone } = require('@/assets/sounds/ringtone');
            this.ringtone = createRingtone();
          }

          // Jouer la sonnerie
          this.playRingtone();

          // Utiliser le service global de gestion des appels entrants
          // pour afficher la notification globale
          //console.log('%c[APPEL] Affichage de la notification d\'appel entrant...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          handleIncomingCall({
            senderId: data.sender_id,
            call_type: data.call_type,
            offer: data.offer
          });

          // Ne pas tester explicitement la lecture d'un son pour éviter de jouer le son de notification
          // en plus de la sonnerie d'appel
        } catch (err) {
          //console.error('%c[APPEL] Erreur lors du traitement de l\'offre:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', err);
          this.resetCallState();
        }
      },

      async handleAnswer(answerData) {
        try {
          //console.log('Réponse reçue:', answerData);

          // Vérifier si la réponse est valide
          if (!answerData || !answerData.answer) {
            console.warn('Réponse invalide reçue');
            return;
          }

          // Vérifier si la connexion peer existe
          if (!this.peerConnection) {
            console.warn('PeerConnection non initialisée');
            return;
          }

          // Vérifier si l'état de signalisation est déjà stable
          if (this.peerConnection.signalingState === 'stable') {
            //console.log('La connexion est déjà stable, ignorer la réponse');
            return;
          }

          // Définir la description distante
          //console.log('Définition de la description distante (réponse)');
          const answer = new RTCSessionDescription(answerData.answer);
          await this.peerConnection.setRemoteDescription(answer);
          //console.log('Description distante définie avec succès');

          // Mettre à jour l'état de l'appel
          this.isCallAccepted = true;
        } catch (err) {
          //console.error('Erreur lors du traitement de la réponse :', err);
          // Ne pas propager l'erreur, mais réinitialiser l'état de l'appel
          this.resetCallState();
        }
      },
      async sendOffer(offer, callType) {
        try {
          if (!this.webSocketConnection) {
            //console.error('%c[APPEL] Erreur: WebSocket non initialisé lors de l\'envoi de l\'offre', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            return;
          }

          if (this.webSocketConnection.readyState !== WebSocket.OPEN) {
            //console.error('%c[APPEL] Erreur: WebSocket non ouvert lors de l\'envoi de l\'offre', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            return;
          }

          // Vérifier que l'utilisateur est connecté
          if (!this.$store.state.isLoggedIn) {
            //console.error('%c[APPEL] Erreur: Utilisateur non connecté lors de l\'envoi de l\'offre', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            return;
          }

          // Récupérer l'ID de l'utilisateur connecté
          const currentUser = this.$store.getters.getUser;
          if (!currentUser || !currentUser.id) {
            //console.error('%c[APPEL] Erreur: Impossible de récupérer l\'ID de l\'utilisateur connecté', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            return;
          }

          // Vérifier que l'ID du destinataire est défini
          if (!this.callerId) {
            //console.error('%c[APPEL] Erreur: ID du destinataire non défini lors de l\'envoi de l\'offre', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            return;
          }

          const offerData = {
            type: 'video_offer',
            offer: offer,
            sender_id: currentUser.id,
            receiver_id: this.callerId,
            call_type: callType
          };

          //console.log('%c[APPEL] Données d\'offre à envoyer:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', offerData);

          // Envoyer l'offre via le WebSocket
          this.webSocketConnection.send(JSON.stringify(offerData));
          //console.log('%c[APPEL] Offre envoyée avec succès', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

          // Envoyer une pré-offre via le WebSocket global pour s'assurer que le destinataire est notifié
          try {
            const { getGlobalPrivateWebSocket } = await import('@/services/global-websocket.service');
            const globalWs = getGlobalPrivateWebSocket();

            if (globalWs && globalWs.readyState === WebSocket.OPEN) {
              const preOfferData = {
                type: 'video_offer',
                offer: { type: 'pre_offer', sdp: 'pending' },
                sender_id: currentUser.id,
                receiver_id: this.callerId,
                call_type: callType
              };

              globalWs.send(JSON.stringify(preOfferData));
              //console.log('%c[APPEL] Pré-offre envoyée via WebSocket global', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
            }
          } catch (error) {
            console.warn('%c[APPEL] Impossible d\'envoyer la pré-offre via WebSocket global:', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;', error);
          }
        } catch (error) {
          //console.error('%c[APPEL] Erreur lors de l\'envoi de l\'offre:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
        }
      },
      async sendAnswer(answer) {
        try {
          if (!this.webSocketConnection) {
            //console.error('%c[APPEL] Erreur: WebSocket non initialisé lors de l\'envoi de la réponse', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            return;
          }

          if (this.webSocketConnection.readyState !== WebSocket.OPEN) {
            //console.error('%c[APPEL] Erreur: WebSocket non ouvert lors de l\'envoi de la réponse', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            return;
          }

          // Vérifier que l'utilisateur est connecté
          if (!this.$store.state.isLoggedIn) {
            //console.error('%c[APPEL] Erreur: Utilisateur non connecté lors de l\'envoi de la réponse', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            return;
          }

          // Récupérer l'ID de l'utilisateur connecté
          const currentUser = this.$store.getters.getUser;
          if (!currentUser || !currentUser.id) {
            //console.error('%c[APPEL] Erreur: Impossible de récupérer l\'ID de l\'utilisateur connecté', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            return;
          }

          // Vérifier que l'ID du destinataire est défini
          if (!this.callerId) {
            //console.error('%c[APPEL] Erreur: ID du destinataire non défini lors de l\'envoi de la réponse', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            return;
          }

          const answerData = {
            type: 'video_answer',
            answer: answer,
            sender_id: currentUser.id,
            receiver_id: this.callerId
          };

          //console.log('%c[APPEL] Données de réponse à envoyer:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', answerData);

          // Envoyer la réponse via le WebSocket
          this.webSocketConnection.send(JSON.stringify(answerData));
          //console.log('%c[APPEL] Réponse envoyée avec succès', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
        } catch (error) {
          //console.error('%c[APPEL] Erreur lors de l\'envoi de la réponse:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
        }
      },
      async handleIceCandidate(candidateData) {
        try {
          //console.log('%c[APPEL] Traitement du candidat ICE reçu:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', candidateData);

          // Vérifier si le candidat est valide
          if (!candidateData) {
            console.warn('%c[APPEL] Candidat ICE invalide reçu (null ou undefined)', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');
            return;
          }

          // Vérifier que le candidat est destiné à cet utilisateur
          const currentUser = this.$store.getters.getUser;
          if (!currentUser || !currentUser.id) {
            //console.error('%c[APPEL] Erreur: Impossible de récupérer l\'ID de l\'utilisateur connecté lors du traitement du candidat ICE', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            return;
          }

          // Vérifier que le candidat n'est pas envoyé par nous-mêmes
          if (candidateData.sender_id === currentUser.id) {
            //console.log('%c[APPEL] Ignorer le candidat ICE provenant de nous-mêmes', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
            return;
          }

          // Créer un objet RTCIceCandidateInit à partir des données reçues
          const candidateInit = {
            candidate: candidateData.candidate,
            sdpMid: candidateData.sdpMid,
            sdpMLineIndex: candidateData.sdpMLineIndex
          };

          //console.log('%c[APPEL] Objet candidat ICE préparé:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', candidateInit);
          
          // Créer un objet RTCIceCandidate
          let candidate;
          try {
            candidate = new RTCIceCandidate(candidateInit);
          } catch (error) {
            //console.error('%c[APPEL] Erreur lors de la création du candidat ICE:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
            //console.log('%c[APPEL] Données du candidat problématique:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', candidateInit);
            return;
          }

          // Si la connexion peer n'existe pas encore, stocker le candidat pour plus tard
          if (!this.peerConnection) {
            //console.log('%c[APPEL] Stockage du candidat ICE pour utilisation ultérieure', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
            this.pendingIceCandidates.push(candidate);
            return;
          }

          // Si la description distante n'est pas encore définie, stocker le candidat
          if (!this.peerConnection.remoteDescription) {
            //console.log('%c[APPEL] Description distante non définie, stockage du candidat ICE', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
            this.pendingIceCandidates.push(candidate);
            return;
          }

          // Ajouter le candidat à la connexion peer
          //console.log('%c[APPEL] Ajout du candidat ICE à la connexion peer', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          await this.peerConnection.addIceCandidate(candidate);
          //console.log('%c[APPEL] Candidat ICE ajouté avec succès', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
        } catch (err) {
          //console.error('%c[APPEL] Erreur lors de l\'ajout du candidat ICE:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', err);
        }
      },
      // Gestion des événements WebSocket
      setupSocketListeners() {
        if (!this.webSocketConnection) {
          //console.error('%c[WEBSOCKET] WebSocket non connecté', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
          return;
        }

        //console.log('%c[WEBSOCKET] Configuration des écouteurs WebSocket...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', {
          readyState: this.webSocketConnection.readyState,
          url: this.webSocketConnection.url
        });

        // Gérer les messages entrants
        this.webSocketConnection.onmessage = async (event) => {
          try {
            const data = JSON.parse(event.data);
            //console.log('%c[WEBSOCKET] Message reçu:', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;', data.type);

            switch (data.type) {
              case 'video_offer':
                //console.log('%c[APPEL] Offre de connexion vidéo reçue', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;', data);

                // Vérifier si la sonnerie est initialisée
                if (!this.ringtone) {
                  console.warn('%c[APPEL] Sonnerie non initialisée lors de la réception de l\'offre, réinitialisation...', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');
                  const { createRingtone } = require('@/assets/sounds/ringtone');
                  this.ringtone = createRingtone();
                }

                await this.handleOffer(data);
                break;

              case 'video_answer':
                //console.log('Réponse de connexion vidéo reçue');
                await this.handleAnswer(data);
                break;

              case 'ice_candidate':
                //console.log('Candidat ICE reçu');
                await this.handleIceCandidate(data);
                break;

              case 'webrtc_ice_candidate':
                //console.log('Candidat ICE reçu');
                await this.handleIceCandidate(data);
                break;

              case 'end_call':
                //console.log('%c[APPEL] Message de fin d\'appel reçu', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', data);

                // Vérifier si un message de fin d'appel a été traité récemment
                const now = Date.now();
                if (now - this.lastEndCallTimestamp < 2000) {
                  //console.log('%c[APPEL] Message de fin d\'appel déjà traité récemment, ignoré', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
                  break;
                }

                // Vérifier si nous sommes en train de terminer un appel
                if (this.isEndingCall) {
                  //console.log('%c[APPEL] Déjà en train de terminer l\'appel, message ignoré', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
                  break;
                }

                // Arrêter la sonnerie immédiatement, même si nous ne sommes pas en appel
                //console.log('%c[APPEL] Arrêt de la sonnerie suite à la réception de end_call', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
                this.stopRingtone();

                // Vérifier si nous sommes en appel
                const isInCall = this.isVideoVisible || this.isVoiceVisible;
                if (!isInCall) {
                  //console.log('%c[APPEL] Pas en appel, message de fin d\'appel ignoré', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

                  // Même si nous ne sommes pas en appel, envoyons une confirmation de fin d'appel
                  // pour s'assurer que l'autre côté sait que nous avons bien reçu le message
                  try {
                    if (this.webSocketConnection && this.webSocketConnection.readyState === WebSocket.OPEN) {
                      const currentUser = this.$store.getters.getUser;
                      if (currentUser && currentUser.id) {
                        const confirmEndCallMessage = {
                          type: 'end_call_ack',
                          sender_id: currentUser.id,
                          receiver_id: data.sender_id,
                          timestamp: now,
                          is_confirmation: true
                        };
                        //console.log('%c[APPEL] Envoi de la confirmation de fin d\'appel:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', confirmEndCallMessage);
                        this.webSocketConnection.send(JSON.stringify(confirmEndCallMessage));
                      }
                    }
                  } catch (error) {
                    //console.error('%c[APPEL] Erreur lors de l\'envoi de la confirmation de fin d\'appel:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
                  }
                  break;
                }

                //console.log('%c[APPEL] Appel terminé par l\'autre participant', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

                // Mettre à jour le message d'état
                this.updateCallStatusMessage("L'autre participant a raccroché");

                // Mettre à jour l'horodatage pour éviter de traiter plusieurs messages de fin d'appel
                this.lastEndCallTimestamp = now;

                // Terminer l'appel sans envoyer de message de fin d'appel supplémentaire
                this.isEndingCall = true; // Marquer comme en cours de fin d'appel

                // Envoyer une confirmation de fin d'appel à l'expéditeur
                try {
                  if (this.webSocketConnection && this.webSocketConnection.readyState === WebSocket.OPEN) {
                    const currentUser = this.$store.getters.getUser;
                    if (currentUser && currentUser.id) {
                      const confirmEndCallMessage = {
                        type: 'end_call_ack',
                        sender_id: currentUser.id,
                        receiver_id: data.sender_id,
                        timestamp: now,
                        is_confirmation: true
                      };
                      //console.log('%c[APPEL] Envoi de la confirmation de fin d\'appel:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', confirmEndCallMessage);
                      this.webSocketConnection.send(JSON.stringify(confirmEndCallMessage));
                    }
                  }
                } catch (error) {
                  //console.error('%c[APPEL] Erreur lors de l\'envoi de la confirmation de fin d\'appel:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
                }

                // Réinitialiser l'interface utilisateur et l'état de l'appel
                this.isVideoVisible = false;
                this.isVoiceVisible = false;
                this.isCallInitiator = false;
                this.isCallAccepted = false;

                // Nettoyer les ressources
                if (this.localStream) {
                  this.localStream.getTracks().forEach(track => track.stop());
                  this.localStream = null;
                }

                if (this.peerConnection) {
                  this.peerConnection.close();
                  this.peerConnection = null;
                }

                // Réinitialiser le callerId pour permettre de nouveaux appels
                this.callerId = null;

                // Réinitialiser le drapeau après un délai
                setTimeout(() => {
                  this.isEndingCall = false;
                  //console.log('%c[APPEL] Drapeau de fin d\'appel réinitialisé après réception de end_call', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

                  // Nettoyer l'URL et recharger la page immédiatement après avoir reçu end_call
                  //console.log('%c[APPEL] Nettoyage de l\'URL et rechargement de la page après réception de end_call...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
                  this.cleanUrlAndReload();
                }, 1000);

                //console.log('%c[APPEL] Appel terminé avec succès suite à la réception de end_call', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
                break;

              case 'end_call_ack':
                //console.log('%c[APPEL] Confirmation de fin d\'appel reçue', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', data);

                // Vérifier si nous sommes en train de terminer un appel
                if (!this.isEndingCall) {
                  //console.log('%c[APPEL] Pas en train de terminer un appel, confirmation ignorée', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
                  break;
                }

                //console.log('%c[APPEL] L\'autre participant a confirmé la fin de l\'appel', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

                // Arrêter la sonnerie immédiatement
                //console.log('%c[APPEL] Arrêt de la sonnerie suite à la confirmation de fin d\'appel', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
                this.stopRingtone();

                // Forcer la réinitialisation complète de l'état de l'appel
                this.callerId = null;
                this.isEndingCall = false;

                // Réinitialiser l'état de l'appel de manière asynchrone
                try {
                  this.resetCallState().catch(error => {
                    //console.error('%c[APPEL] Erreur lors de la réinitialisation de l\'état de l\'appel:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
                  });
                } catch (error) {
                  //console.error('%c[APPEL] Erreur lors de l\'appel à resetCallState:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
                }

                //console.log('%c[APPEL] État de l\'appel complètement réinitialisé suite à la confirmation', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

                // Nettoyer l'URL et recharger la page immédiatement après avoir reçu end_call_ack
                //console.log('%c[APPEL] Nettoyage de l\'URL et rechargement de la page après réception de end_call_ack...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
                setTimeout(() => {
                  this.cleanUrlAndReload();
                }, 500);
                break;

              case 'reject_call':
                //console.log('%c[APPEL] Message de rejet d\'appel reçu', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', data);

                // Vérifier si nous sommes en train de terminer un appel
                if (this.isEndingCall) {
                  //console.log('%c[APPEL] Déjà en train de terminer l\'appel, message de rejet ignoré', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
                  break;
                }

                //console.log('%c[APPEL] Appel refusé par l\'autre participant', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

                // Arrêter la sonnerie immédiatement
                //console.log('%c[APPEL] Arrêt de la sonnerie suite au rejet de l\'appel', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
                this.stopRingtone();

                // Mettre à jour le message d'état
                this.updateCallStatusMessage("L'autre participant a refusé l'appel");

                // Marquer comme en cours de fin d'appel
                this.isEndingCall = true;

                // Réinitialiser l'état de l'appel de manière asynchrone
                try {
                  this.resetCallState("L'autre participant a refusé l'appel").catch(error => {
                    //console.error('%c[APPEL] Erreur lors de la réinitialisation de l\'état de l\'appel:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
                  });
                } catch (error) {
                  //console.error('%c[APPEL] Erreur lors de l\'appel à resetCallState:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
                }

                // Le drapeau isEndingCall est déjà réinitialisé dans resetCallState()

                // Nettoyer l'URL et recharger la page immédiatement après avoir reçu reject_call
                //console.log('%c[APPEL] Nettoyage de l\'URL et rechargement de la page après réception de reject_call...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
                setTimeout(() => {
                  this.cleanUrlAndReload();
                }, 500);
                break;

              case 'chat_message':
                //console.log('%c[MESSAGERIE] Message de chat reçu', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;', data);

                // Vérifier si le message contient les informations nécessaires
                if (!data.message) {
                  //console.error('%c[MESSAGERIE] Message de chat invalide (pas de contenu)', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', data);
                  break;
                }

                // Récupérer l'ID de l'utilisateur connecté depuis le store
                const currentUser = this.$store.getters.getUser;

                // Si le message ne vient pas de l'utilisateur actuel, ajouter une notification
                if (data.sender_id && data.sender_id !== currentUser.id) {
                  // Ajouter un message non lu
                  addUnreadMessage(data.sender_id, data.message);

                  // Jouer le son de notification
                  playNotificationSound();

                  //console.log('%c[MESSAGERIE] Notification ajoutée pour le message de', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;', data.sender_id);

                  // Vérifier si l'utilisateur est dans la liste des utilisateurs
                  // Si non, il faudra peut-être le récupérer depuis le serveur
                  if (this.$refs.chatUsersList && !this.$refs.chatUsersList.users.some(user => user.id === data.sender_id)) {
                    //console.log('%c[MESSAGERIE] L\'utilisateur n\'est pas dans la liste, récupération des informations...', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');

                    // Vous pourriez ajouter ici un appel API pour récupérer les informations de l'utilisateur
                    // et l'ajouter à la liste des utilisateurs si nécessaire
                  }
                }

                // Émettre un événement personnalisé pour que le composant ChatWindow puisse traiter le message
                try {
                  // Créer un événement personnalisé avec les détails du message
                  const chatMessageEvent = new CustomEvent('chat-message-received', {
                    detail: data
                  });

                  // Déclencher l'événement sur window pour qu'il soit accessible globalement
                  window.dispatchEvent(chatMessageEvent);
                  //console.log('%c[MESSAGERIE] Événement chat-message-received émis', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
                } catch (error) {
                  //console.error('%c[MESSAGERIE] Erreur lors de l\'émission de l\'événement chat-message-received:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
                }
                break;

              default:
                //console.log('%c[MESSAGERIE] Type de message non géré:', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;', data.type);
            }
          } catch (err) {
            //console.error('Erreur lors du traitement du message WebSocket:', err);
          }
        };

        // Gérer les erreurs WebSocket
        this.webSocketConnection.onerror = (error) => {
          //console.error('Erreur WebSocket:', error);
        };

        // Gérer la fermeture de la connexion WebSocket
        this.webSocketConnection.onclose = async () => {
          //console.log('%c[APPEL] Connexion WebSocket fermée', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          // Si un appel est en cours, le terminer
          if (this.isVideoVisible || this.isVoiceVisible) {
            try {
              await this.endVideoCall();
              //console.log('%c[APPEL] Appel terminé suite à la fermeture du WebSocket', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
            } catch (error) {
              //console.error('%c[APPEL] Erreur lors de la fin de l\'appel suite à la fermeture du WebSocket:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
              // Forcer la réinitialisation en cas d'erreur
              this.resetCallState();
            }
          }
        };

        //console.log('Gestionnaires d\'événements WebSocket configurés');
      },

      // TODO : implémenter la récupération des credentials dynamiques pour la config du serveur TURN
      async getIceServerCredentials() {
        try {
          // Récupérer les identifiants dynamiques depuis le backend Django
          const response = await fetch(
            'https://websocket.thanks-boss.com/get-turn-credentials/'
          );
          if (!response.ok)
            throw new Error('Erreur lors de la récupération des credentials');
          const credentials = await response.json();
          return credentials;
        } catch (error) {
          //console.error('Erreur :', error);
          return null;
        }
      },
      async initializePeerConnection(callType) {
        //console.log(`Initialisation de la connexion peer-to-peer`);
        //console.log(`Initialisation de la connexion peer-to-peer pour un appel ${callType}`);

        // Définir les serveurs ICE (STUN & TURN)
        const iceServers = [
          { urls: 'stun:stun.l.google.com:19302' },
          { urls: 'stun:stun1.l.google.com:19302' },
          { urls: 'stun:stun2.l.google.com:19302' },
          { urls: 'stun:stun3.l.google.com:19302' },
          { urls: 'stun:stun4.l.google.com:19302' },
          {
            urls: 'turn:167.86.88.53:3478',
            username: 'trucuser',
            credential: '51c0ff92-d7eb-48f1-96d0-fd1d7089f038',
          },
        ];

        // Configurer la connexion peer
        const config = { iceServers, iceCandidatePoolSize: 10, iceTransportPolicy: 'relay' };

        // Fermer toute connexion existante avant d'en créer une nouvelle
        if (this.peerConnection) {
          this.peerConnection.close();
          this.peerConnection = null;
        }

        this.peerConnection = new RTCPeerConnection(config);
        //console.log('Connexion peer-to-peer créée');

        // Gérer les candidats ICE
        this.peerConnection.onicecandidate = (event) => {
          if (event.candidate && this.webSocketConnection && this.webSocketConnection.readyState === WebSocket.OPEN) {
            // Récupérer l'ID de l'utilisateur connecté depuis le store
            const currentUser = this.$store.getters.getUser;
            if (!currentUser || !currentUser.id) {
              //console.error('%c[APPEL] Erreur: Impossible de récupérer l\'ID de l\'utilisateur connecté lors de l\'envoi du candidat ICE', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
              return;
            }

            const iceCandidateData = {
              type: 'ice_candidate',
              candidate: event.candidate,
              sender_id: currentUser.id,
              receiver_id: this.callerId
            };
            //console.log('%c[APPEL] Envoi du candidat ICE:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', event.candidate.candidate.substring(0, 50) + '...');
            this.webSocketConnection.send(JSON.stringify(iceCandidateData));
          } else if (!event.candidate) {
            //console.log('%c[APPEL] Collecte des candidats ICE terminée', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          }
        };

        // Gérer les changements d'état de la connexion ICE
        this.peerConnection.oniceconnectionstatechange = () => {
          //console.log('État ICE:', this.peerConnection.iceConnectionState);
        };

        // Gérer les changements d'état de la connexion
        this.peerConnection.onconnectionstatechange = () => {
          //console.log('État connexion:', this.peerConnection.connectionState);
        };

        // Gérer les changements d'état de la connexion signaling
        this.peerConnection.onsignalingstatechange = () => {
          //console.log('État signaling:', this.peerConnection.signalingState);
        };

        // Gérer les flux média entrants
        this.peerConnection.ontrack = (event) => {
          //console.log(`Flux ${event.track.kind} reçu`);
          //console.log('Streams disponibles:', event.streams);
          
          // S'assurer que le flux existe
          if (event.streams && event.streams.length > 0) {
            // Assigner directement le flux au composant
            this.remoteStream = event.streams[0];
            //console.log('Flux vidéo distant attaché', this.remoteStream);
            
            // Vérifier si le flux a des pistes vidéo
            const videoTracks = this.remoteStream.getVideoTracks();
            //console.log('Pistes vidéo dans le flux distant:', videoTracks.length);
            
            // Forcer la mise à jour du composant
            this.$nextTick(() => {
              // Vérifier si l'élément vidéo existe et lui assigner le flux
              const remoteVideo = document.getElementById('remote-video');
              if (remoteVideo) {
                remoteVideo.srcObject = this.remoteStream;
                //console.log('Flux assigné à l\'élément vidéo distant');
              }
            });
          }
        };

        //console.log('Gestionnaires d\'événements de la connexion peer-to-peer configurés');
      },
      async startVideoCall(recipient) {
        //console.log('%c[APPEL] Début de startVideoCall', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', recipient);
        if (this.isVideoVisible) {
          //console.log('%c[APPEL] Appel vidéo déjà visible, sortie de startVideoCall', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          return;
        }
        
        try {
          // Vérifier si recipient est un objet ou un ID
          let recipientId;
          if (typeof recipient === 'object' && recipient !== null && recipient.id) {
            recipientId = recipient.id;
            //console.log('%c[APPEL] Destinataire est un objet, extraction de l\'ID:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', recipientId);
          } else {
            recipientId = recipient;
            //console.log('%c[APPEL] Destinataire est déjà un ID:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', recipientId);
          }

          //console.log('%c[APPEL] Démarrage d\'un appel vidéo avec le destinataire ID:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', recipientId);

          // Vérifier que l'ID du destinataire est valide
          if (!recipientId) {
            //console.error('%c[APPEL] Erreur: ID du destinataire non défini ou invalide', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            return;
          }

          this.isVideoVisible = true;
          this.isCallInitiator = true;
          this.callerId = recipientId; // ID du destinataire de l'appel
          this.callType = 'video';

          // S'assurer que le WebSocket est initialisé
          if (!this.webSocketConnection || this.webSocketConnection.readyState !== WebSocket.OPEN) {
            //console.log('%c[APPEL] WebSocket non initialisé ou fermé, initialisation...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
            const webSocket = initializePrivateWebSocket();
            if (webSocket) {
              this.webSocketConnection = webSocket;
              this.setupSocketListeners();

              // Attendre que le WebSocket soit ouvert
              if (webSocket.readyState !== WebSocket.OPEN) {
                //console.log('%c[APPEL] Attente de l\'ouverture du WebSocket...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
                await new Promise((resolve) => {
                  const checkInterval = setInterval(() => {
                    if (webSocket.readyState === WebSocket.OPEN) {
                      clearInterval(checkInterval);
                      resolve();
                    }
                  }, 100);
                  // Timeout de sécurité après 5 secondes
                  setTimeout(() => {
                    clearInterval(checkInterval);
                    resolve();
                  }, 5000);
                });
              }
            } else {
              //console.error('%c[APPEL] Impossible d\'initialiser le WebSocket', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
              this.resetCallState();
              return;
            }
          }

          //console.log("Tentative d'initialisation de la connexion peer-to-peer");
          await this.initializePeerConnection('video');

          const stream = await this.handleLocalStream('video');
          if (!stream) return;

          const offer = await this.peerConnection.createOffer({
            offerToReceiveAudio: true,
            offerToReceiveVideo: true,
          });
          await this.peerConnection.setLocalDescription(offer);

          // Vérifier que callerId est bien défini avant d'envoyer l'offre
          if (!this.callerId) {
            //console.error('%c[APPEL] Erreur: ID du destinataire non défini', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            this.resetCallState();
            return;
          }

          //console.log('%c[APPEL] Envoi de l\'offre au destinataire:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', this.callerId);
          await this.sendOffer(offer, "video");
        } catch (err) {
          //console.error('%c[APPEL] Erreur lors de l\'initialisation de l\'appel vidéo:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', err);
          this.resetCallState();
        }
      },
      async startVoiceCall(recipient) {
        if (this.isVoiceVisible) return;

        try {
          // Vérifier si recipient est un objet ou un ID
          let recipientId;
          if (typeof recipient === 'object' && recipient !== null && recipient.id) {
            recipientId = recipient.id;
            //console.log('%c[APPEL] Destinataire est un objet, extraction de l\'ID:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', recipientId);
          } else {
            recipientId = recipient;
            //console.log('%c[APPEL] Destinataire est déjà un ID:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', recipientId);
          }

          //console.log('%c[APPEL] Démarrage d\'un appel vocal avec le destinataire ID:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', recipientId);

          // Vérifier que l'ID du destinataire est valide
          if (!recipientId) {
            //console.error('%c[APPEL] Erreur: ID du destinataire non défini ou invalide', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            return;
          }

          this.isVideoVisible = false;
          this.isVoiceVisible = true;
          this.isCallInitiator = true;
          this.callerId = recipientId;
          this.callType = 'audio';

          // S'assurer que le WebSocket est initialisé
          if (!this.webSocketConnection || this.webSocketConnection.readyState !== WebSocket.OPEN) {
            //console.log('%c[APPEL] WebSocket non initialisé ou fermé, initialisation...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
            const webSocket = initializePrivateWebSocket();
            if (webSocket) {
              this.webSocketConnection = webSocket;
              this.setupSocketListeners();

              // Attendre que le WebSocket soit ouvert
              if (webSocket.readyState !== WebSocket.OPEN) {
                //console.log('%c[APPEL] Attente de l\'ouverture du WebSocket...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
                await new Promise((resolve) => {
                  const checkInterval = setInterval(() => {
                    if (webSocket.readyState === WebSocket.OPEN) {
                      clearInterval(checkInterval);
                      resolve();
                    }
                  }, 100);
                  // Timeout de sécurité après 5 secondes
                  setTimeout(() => {
                    clearInterval(checkInterval);
                    resolve();
                  }, 5000);
                });
              }
            } else {
              //console.error('%c[APPEL] Impossible d\'initialiser le WebSocket', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
              this.resetCallState();
              return;
            }
          }

          //console.log("Tentative d'initialisation de la connexion peer-to-peer");
          await this.initializePeerConnection('audio');

          const stream = await this.handleLocalStream('audio');
          if (!stream) return;

          // Create an audio-only offer
          const offer = await this.peerConnection.createOffer({
            offerToReceiveAudio: true,
            offerToReceiveVideo: false, // No video
          });

          await this.peerConnection.setLocalDescription(offer);

          // Vérifier que callerId est bien défini avant d'envoyer l'offre
          if (!this.callerId) {
            //console.error('%c[APPEL] Erreur: ID du destinataire non défini', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            this.resetCallState();
            return;
          }

          //console.log('%c[APPEL] Envoi de l\'offre audio au destinataire:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', this.callerId);
          await this.sendOffer(offer, "audio");
        } catch (err) {
          //console.error('%c[APPEL] Erreur lors de l\'initialisation de l\'appel vocal:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', err);
          this.resetCallState();
        }
      },
      /**
       * Vérifie et réinitialise le WebSocket si nécessaire
       */
      checkAndResetWebSocket() {
        //console.log('%c[APPEL] Vérification du WebSocket', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

        // Vérifier si le WebSocket est défini
        if (!this.webSocketConnection) {
          //console.log('%c[APPEL] WebSocket non défini, initialisation...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          const webSocket = initializePrivateWebSocket();
          if (webSocket) {
            this.webSocketConnection = webSocket;
            this.setupSocketListeners();
            //console.log('%c[APPEL] Nouveau WebSocket initialisé', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          } else {
            //console.error('%c[APPEL] Impossible d\'initialiser le WebSocket', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
          }
          return;
        }

        // Vérifier l'état du WebSocket
        if (this.webSocketConnection.readyState !== WebSocket.OPEN) {
          //console.log('%c[APPEL] WebSocket non ouvert (état: ' + this.webSocketConnection.readyState + '), réinitialisation...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

          // Fermer le WebSocket existant si nécessaire
          if (this.webSocketConnection.readyState !== WebSocket.CLOSED && this.webSocketConnection.readyState !== WebSocket.CLOSING) {
            try {
              this.webSocketConnection.close();
              //console.log('%c[APPEL] WebSocket existant fermé', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
            } catch (error) {
              //console.error('%c[APPEL] Erreur lors de la fermeture du WebSocket:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
            }
          }

          // Initialiser un nouveau WebSocket
          const webSocket = initializePrivateWebSocket();
          if (webSocket) {
            this.webSocketConnection = webSocket;
            this.setupSocketListeners();
            //console.log('%c[APPEL] Nouveau WebSocket initialisé', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          } else {
            //console.error('%c[APPEL] Impossible d\'initialiser le WebSocket', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
          }
        } else {
          //console.log('%c[APPEL] WebSocket déjà ouvert et fonctionnel', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
        }
      },

      /**
       * Nettoie complètement les connexions WebRTC
       * @param {string} [reason] - Raison de la déconnexion à afficher à l'utilisateur
       * @returns {Promise<void>}
       */
      async cleanupWebRTCConnections(reason) {
        //console.log('%c[APPEL] Nettoyage complet des connexions WebRTC', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

        // Mettre à jour le message d'état dans les composants d'appel
        if (reason) {
          this.updateCallStatusMessage(reason);
        }

        // 1. Arrêter tous les tracks du flux local
        if (this.localStream) {
          try {
            const tracks = this.localStream.getTracks();
            //console.log('%c[APPEL] Arrêt de', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', tracks.length, 'tracks');

            tracks.forEach((track) => {
              try {
                track.stop();
                //console.log('%c[APPEL] Track arrêté:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', track.kind, track.id);
              } catch (trackError) {
                //console.error('%c[APPEL] Erreur lors de l\'arrêt du track:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', trackError);
              }
            });

            // Vider explicitement le flux
            this.localStream = null;
          } catch (error) {
            //console.error('%c[APPEL] Erreur lors de l\'arrêt des tracks:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
          }
        } else {
          //console.log('%c[APPEL] Aucun flux local à nettoyer', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
        }

        // 2. Fermer la connexion peer-to-peer
        if (this.peerConnection) {
          try {
            // Supprimer tous les event listeners
            if (this.peerConnection.onicecandidate) {
              this.peerConnection.onicecandidate = null;
            }
            if (this.peerConnection.ontrack) {
              this.peerConnection.ontrack = null;
            }
            if (this.peerConnection.oniceconnectionstatechange) {
              this.peerConnection.oniceconnectionstatechange = null;
            }
            if (this.peerConnection.onsignalingstatechange) {
              this.peerConnection.onsignalingstatechange = null;
            }
            if (this.peerConnection.onicegatheringstatechange) {
              this.peerConnection.onicegatheringstatechange = null;
            }
            if (this.peerConnection.onnegotiationneeded) {
              this.peerConnection.onnegotiationneeded = null;
            }

            // Fermer la connexion
            //console.log('%c[APPEL] Fermeture de la connexion peer-to-peer', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
            this.peerConnection.close();

            // Attendre un court instant pour s'assurer que la connexion est bien fermée
            await new Promise(resolve => setTimeout(resolve, 100));

            // Vérifier l'état de la connexion
            //console.log('%c[APPEL] État de la connexion après fermeture:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;',
              this.peerConnection.iceConnectionState,
              this.peerConnection.signalingState);
          } catch (error) {
            //console.error('%c[APPEL] Erreur lors de la fermeture de la connexion peer-to-peer:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
          } finally {
            // Supprimer la référence dans tous les cas
            this.peerConnection = null;
          }
        } else {
          //console.log('%c[APPEL] Aucune connexion peer-to-peer à fermer', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
        }

        // 3. Nettoyer les références vidéo
        if (this.$refs.videoWindow) {
          try {
            const videoWindow = this.$refs.videoWindow;

            // Mettre à jour le message d'état dans le composant vidéo
            if (reason && typeof videoWindow.updateCallStatus === 'function') {
              videoWindow.updateCallStatus(reason);
            }

            // Nettoyer la vidéo locale
            const localVideo = videoWindow.getWebcamRef();
            if (localVideo && localVideo.srcObject) {
              try {
                // Arrêter tous les tracks de la vidéo locale
                const tracks = localVideo.srcObject.getTracks();
                tracks.forEach(track => track.stop());
                //console.log('%c[APPEL] Tracks de la vidéo locale arrêtés:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', tracks.length);

                // Vider la source
                localVideo.srcObject = null;
                //console.log('%c[APPEL] Source de la vidéo locale vidée', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
              } catch (localError) {
                //console.error('%c[APPEL] Erreur lors du nettoyage de la vidéo locale:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', localError);
              }
            }

            // Nettoyer la vidéo distante
            const remoteVideo = videoWindow.getRemoteVideoRef();
            if (remoteVideo && remoteVideo.srcObject) {
              try {
                // Arrêter tous les tracks de la vidéo distante
                const tracks = remoteVideo.srcObject.getTracks();
                tracks.forEach(track => track.stop());
                //console.log('%c[APPEL] Tracks de la vidéo distante arrêtés:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', tracks.length);

                // Vider la source
                remoteVideo.srcObject = null;
                //console.log('%c[APPEL] Source de la vidéo distante vidée', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
              } catch (remoteError) {
                //console.error('%c[APPEL] Erreur lors du nettoyage de la vidéo distante:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', remoteError);
              }
            }
          } catch (error) {
            //console.error('%c[APPEL] Erreur lors du nettoyage des références vidéo:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
          }
        } else {
          //console.log('%c[APPEL] Aucune référence vidéo à nettoyer', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
        }

        // 4. Nettoyer les références audio (appel vocal)
        if (this.$refs.voiceWindow) {
          try {
            const voiceWindow = this.$refs.voiceWindow;

            // Mettre à jour le message d'état dans le composant audio
            if (reason && typeof voiceWindow.updateCallStatus === 'function') {
              voiceWindow.updateCallStatus(reason);
            }

            // Nettoyer l'audio distant
            const remoteAudio = voiceWindow.getRemoteAudioRef();
            if (remoteAudio && remoteAudio.srcObject) {
              try {
                // Arrêter tous les tracks de l'audio distant
                const tracks = remoteAudio.srcObject.getTracks();
                tracks.forEach(track => track.stop());
                //console.log('%c[APPEL] Tracks de l\'audio distant arrêtés:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', tracks.length);

                // Vider la source
                remoteAudio.srcObject = null;
                //console.log('%c[APPEL] Source de l\'audio distant vidée', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
              } catch (audioError) {
                //console.error('%c[APPEL] Erreur lors du nettoyage de l\'audio distant:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', audioError);
              }
            }
          } catch (error) {
            //console.error('%c[APPEL] Erreur lors du nettoyage des références audio:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
          }
        } else {
          //console.log('%c[APPEL] Aucune référence audio à nettoyer', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
        }

        //console.log('%c[APPEL] Nettoyage des connexions WebRTC terminé', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
      },

      /**
       * Met à jour le message d'état de l'appel dans les composants d'appel
       * @param {string} message - Message d'état à afficher
       */
      updateCallStatusMessage(message) {
        //console.log('%c[APPEL] Mise à jour du message d\'état:', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;', message);

        // Mettre à jour le message dans le composant d'appel vidéo
        if (this.$refs.videoWindow && typeof this.$refs.videoWindow.updateCallStatus === 'function') {
          this.$refs.videoWindow.updateCallStatus(message);
        }

        // Mettre à jour le message dans le composant d'appel vocal
        if (this.$refs.voiceWindow && typeof this.$refs.voiceWindow.updateCallStatus === 'function') {
          this.$refs.voiceWindow.updateCallStatus(message);
        }
      },

      /**
       * Réinitialise l'état de l'appel
       * @param {string} [reason] - Raison de la réinitialisation à afficher à l'utilisateur
       * @returns {Promise<void>}
       */
      async resetCallState(reason) {
        //console.log('%c[APPEL] Réinitialisation de l\'état de l\'appel', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

        // Arrêter la sonnerie si elle est en cours
        this.stopRingtone();

        // Réinitialiser les paramètres d'URL
        this.resetUrlParams();

        // Nettoyer les connexions WebRTC avec le message de raison
        await this.cleanupWebRTCConnections(reason);

        // Réinitialiser tous les états
        this.isVideoVisible = false;
        this.isVoiceVisible = false;
        this.isCallInitiator = false;
        this.isCallAccepted = false;
        this.pendingOffer = null;
        this.pendingIceCandidates = [];
        this.callMetadata = null;

        // Réinitialiser explicitement callerId pour permettre de nouveaux appels
        const oldCallerId = this.callerId;
        this.callerId = null;
        //console.log('%c[APPEL] ID du destinataire réinitialisé:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', oldCallerId, '-> null');

        // Réinitialiser les drapeaux de fin d'appel
        this.isEndingCall = false;

        // Réinitialiser l'horodatage du dernier message de fin d'appel
        // pour permettre d'envoyer immédiatement un nouveau message si nécessaire
        this.lastEndCallTimestamp = 0;

        // Vérifier et réinitialiser le WebSocket si nécessaire
        if (typeof this.checkAndResetWebSocket === 'function') {
          try {
            this.checkAndResetWebSocket();
          } catch (error) {
            //console.error('%c[APPEL] Erreur lors de la réinitialisation du WebSocket:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
          }
        }

        //console.log('%c[APPEL] État de l\'appel réinitialisé avec succès', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

        // Forcer le garbage collector après un court délai
        setTimeout(() => {
          //console.log('%c[APPEL] Forçage du garbage collector', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          if (window.gc) {
            window.gc();
          }

          // Ne pas recharger la page ici, car le rechargement est maintenant géré dans les gestionnaires spécifiques
          //console.log('%c[APPEL] Pas de rechargement automatique dans resetCallState, géré par les gestionnaires spécifiques', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
        }, 1000);
      },
      async endVideoCall() {
        // Vérifier si un appel de fin d'appel est déjà en cours
        if (this.isEndingCall) {
          //console.log('%c[APPEL] Fin d\'appel déjà en cours, ignoré', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          return;
        }

        // Vérifier si un message de fin d'appel a été envoyé récemment (moins de 2 secondes)
        const now = Date.now();
        if (now - this.lastEndCallTimestamp < 2000) {
          //console.log('%c[APPEL] Message de fin d\'appel déjà envoyé récemment, ignoré', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          return;
        }

        // Vérifier si nous sommes en appel
        const isInCall = this.isVideoVisible || this.isVoiceVisible;
        if (!isInCall) {
          //console.log('%c[APPEL] Pas en appel, fin d\'appel ignorée', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          return;
        }

        // Vérifier si callerId est défini
        if (!this.callerId) {
          //console.log('%c[APPEL] ID du destinataire non défini, fin d\'appel ignorée', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          this.resetCallState();
          return;
        }

        // Arrêter la sonnerie immédiatement
        //console.log('%c[APPEL] Arrêt de la sonnerie lors de la fin d\'appel', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
        this.stopRingtone();

        // Marquer le début de la fin d'appel
        this.isEndingCall = true;
        this.lastEndCallTimestamp = now;

        //console.log('%c[APPEL] Fin de l\'appel dans MessagingPage', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

        // Mettre à jour le message d'état
        this.updateCallStatusMessage("Fin de l'appel...");

        // Réinitialiser les paramètres d'URL
        this.resetUrlParams();

        // Envoyer un message de fin d'appel via WebSocket
        if (this.webSocketConnection && this.webSocketConnection.readyState === WebSocket.OPEN) {
          try {
            // Récupérer l'ID de l'utilisateur connecté depuis le store
            const currentUser = this.$store.getters.getUser;
            if (!currentUser || !currentUser.id) {
              //console.error('%c[APPEL] Erreur: Impossible de récupérer l\'ID de l\'utilisateur connecté lors de la fin de l\'appel', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            } else {
              const endCallMessage = {
                type: 'end_call',
                sender_id: currentUser.id,
                receiver_id: this.callerId,
                timestamp: now, // Ajouter un horodatage pour identifier les messages uniques
                requires_confirmation: true // Indiquer que nous attendons une confirmation
              };
              //console.log('%c[APPEL] Envoi du message de fin d\'appel:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', endCallMessage);
              this.webSocketConnection.send(JSON.stringify(endCallMessage));

              // Définir un timeout pour forcer la réinitialisation si aucune confirmation n'est reçue
              //console.log('%c[APPEL] Attente de la confirmation de fin d\'appel...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
              setTimeout(() => {
                if (this.isEndingCall) {
                  //console.log('%c[APPEL] Aucune confirmation de fin d\'appel reçue, forçage de la réinitialisation', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');
                  this.callerId = null;
                  this.isEndingCall = false;
                  this.resetCallState();
                }
              }, 5000); // Attendre 5 secondes maximum pour la confirmation
            }
          } catch (error) {
            //console.error('%c[APPEL] Erreur lors de l\'envoi du message de fin d\'appel:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
            // En cas d'erreur, forcer la réinitialisation
            this.callerId = null;
            this.isEndingCall = false;
            this.resetCallState();
          }
        } else {
          //console.log('%c[APPEL] WebSocket non disponible, forçage de la réinitialisation', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');
          // Si le WebSocket n'est pas disponible, forcer la réinitialisation
          this.callerId = null;
          this.isEndingCall = false;
          this.resetCallState();
        }

        // Nettoyer les connexions WebRTC
        try {
          await this.cleanupWebRTCConnections();
          //console.log('%c[APPEL] Nettoyage des connexions WebRTC terminé avec succès', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
        } catch (error) {
          //console.error('%c[APPEL] Erreur lors du nettoyage des connexions WebRTC:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
        }

        // Réinitialiser l'interface utilisateur
        this.isVideoVisible = false;
        this.isVoiceVisible = false;
        this.isCallInitiator = false;
        this.isCallAccepted = false;
        //console.log('%c[APPEL] Interface utilisateur réinitialisée', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

        // Réinitialiser le drapeau de fin d'appel après un délai
        setTimeout(() => {
          this.isEndingCall = false;
          //console.log('%c[APPEL] Drapeau de fin d\'appel réinitialisé', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

          // Forcer la libération de la mémoire
          //console.log('%c[APPEL] Exécution du garbage collector', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          if (window.gc) {
            window.gc();
          }

          // Nettoyer l'URL et recharger la page immédiatement après avoir quitté l'appel
          //console.log('%c[APPEL] Nettoyage de l\'URL et rechargement de la page après fin d\'appel...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          this.cleanUrlAndReload();
        }, 1000);
      },
      async handleLocalStream(callType) {
        try {
          // Arrêter tous les tracks existants avant de créer un nouveau stream
          if (this.localStream) {
            this.localStream.getTracks().forEach((track) => track.stop());
            this.localStream = null;
          }

          let stream;
          // Configuration des contraintes média en fonction du type d'appel
          if (callType === 'video') {
            stream = await navigator.mediaDevices.getUserMedia({
              video: {
                width: { ideal: 1280 },
                height: { ideal: 720 },
              },
              audio: true,
            });
            // Afficher le flux local dans la fenêtre vidéo
            const videoWindow = this.$refs.videoWindow;
            if (videoWindow) {
              const localVideo = videoWindow.getWebcamRef();
              if (localVideo) {
                localVideo.srcObject = stream;
              }
            }
          } else {
            // Pour les appels audio uniquement
            stream = await navigator.mediaDevices.getUserMedia({
              video: false,
              audio: true,
            });
            // Afficher le flux audio dans la fenêtre d'appel vocal
            const voiceWindow = this.$refs.voiceWindow;
            if (voiceWindow) {
              const localAudio = voiceWindow.getWebcamRef();
              if (localAudio) {
                localAudio.srcObject = stream;
              }
            }
          }

          await this.$nextTick();

          // Ajouter les tracks au peer connection
          if (this.peerConnection) {
            stream.getTracks().forEach((track) => {
              this.peerConnection.addTrack(track, stream);
              //console.log(`Track ajouté: ${track.kind}`); // Pour le debug
            });
          }

          this.localStream = stream;
          return stream;
        } catch (err) {
          //console.error("Erreur lors de l\'accès au flux multimédia:", err);
          if (err.name === 'NotReadableError') {
            alert(
              "La caméra est en cours d\'utilisation par une autre application. Veuillez fermer les autres applications qui pourraient l\'utiliser."
            );
          } else if (err.name === 'NotAllowedError') {
            alert(
              "On a besoin d\'accès à votre caméra et microphone pour effectuer l\'appel."
            );
          } else {
            //console.log(err);
            alert(
              "Erreur lors de l\'accès à votre caméra et microphone. Veuillez vérifier que vous êtes connectés et disponibles."
            );
          }
          this.resetCallState();
          return null;
        }
      },
    },
  };
</script>

<style scoped>
  /* Main container */
  .messagerie-container {
    position: relative;
    display: flex;
    justify-content: center;
    gap: 24px;
    width: 100%;
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .padding-container {
      padding-inline: 0vw;
    }
  }
</style>
