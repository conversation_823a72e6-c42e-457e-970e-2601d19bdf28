<template>
  <main>
    <div
      :style="{ backgroundImage: `url(${bgImage})` }"
      class="background-image"
    ></div>
    <!-- Background with Fade Effect -->
    <div class="princing-container">
      <div class="pricing-background"></div>
      <!-- Pricing Cards -->
      <div class="cards-container">
        <div
          class="card-wrapper"
          v-for="plan in filteredSubscriptions"
          :key="plan.id"
        >
          <!-- Popular badge -->
          <div v-if="plan.isPopular" class="popular-badge">Populaire!</div>

          <div
            class="card"
            :class="{ 'highlight-card': expandedCard === plan.id }"
          >
            <div class="card-content">
              <h3 class="card-title">{{ plan.nom }}</h3>
              <div class="price-container">
                <div class="card-price">{{ formatPrice(plan.prix_ttc) }}€</div>
                <span class="card-duration">/mo</span>
              </div>
              <div class="card-interval_count">
                {{ getIntervalCount(plan) }}
              </div>
              <PrimaryRoundedButton
                textContent="Voir l'offre"
                class="responsive-button"
                @click="handleCardClick(plan.id)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Expanded Details Section -->
    <transition name="slide-fade" mode="out-in" @after-enter="scrollToExpanded">
      <div
        v-if="expandedCard !== null"
        :key="expandedCard"
        class="expanded-details-container"
        ref="expandedSection"
      >
        <div class="expanded-details">
          <div class="expanded-header">
            <h3 class="expanded-title">{{ expandedCardData.nom }}</h3>
            <p class="expanded-price">
              {{ formatPrice(getPrice(expandedCardData.prix_ttc)) }}€
              {{ billingPeriod === 'monthly' ? '/mo' : '/an' }}
            </p>
          </div>

          <p class="expanded-subtitle">
            {{ customSubtitles[expandedCardData.nom] }} -
            {{ billingPeriod === 'monthly' ? '3 mois' : 'Annuel' }}
          </p>

          <div class="duration-toggle">
            <span
              :class="{ active: billingPeriod === 'monthly' }"
              @click="billingPeriod = 'monthly'"
            >
              3 mois
            </span>
            <span
              :class="{ active: billingPeriod === 'annual' }"
              @click="billingPeriod = 'annual'"
            >
              Annuel
            </span>
          </div>

          <div class="features-grid">
            <div class="features-list">
              <div
                v-for="feature in sortedFeatures"
                :key="feature.text"
                class="feature-item"
              >
                <span
                  :class="
                    isGrey(feature.description, expandedCardData.nom)
                      ? 'inactive-check-icon'
                      : 'check-icon'
                  "
                >
                  {{ getIcon(feature.description) }}
                </span>
                <span
                  :class="
                    isGrey(feature.description, expandedCardData.nom)
                      ? 'inactive-feature-text'
                      : 'feature-text'
                  "
                >
                  {{ feature.description }}
                </span>
              </div>
            </div>
            <div class="features-image">
              <img
                :src="expandedCardData.image"
                :alt="`${expandedCardData.titre} illustration`"
                class="plan-image"
              />
            </div>
          </div>

          <div class="btn-abonnement">
            <v-tooltip location="top" v-if="!isLoggedIn">
              <template #activator="{ props }">
                <PrimaryRoundedButton
                  v-bind="props"
                  textContent="S'abonner"
                  class="abonner-button"
                />
              </template>
              <span>Inscrivez-vous pour souscrire à un abonnement</span>
            </v-tooltip>

            <PrimaryRoundedButton
              v-if="isLoggedIn"
              textContent="S'abonner"
              class="abonner-button"
              @click="gotoPage(`abonnements/${expandedCardData.id}`)"
            />
          </div>
        </div>
      </div>
    </transition>

    <BackToTopArrow />
  </main>
</template>

<script>
  import bgImage from '@/assets/tarifs/Font_page_site_fade.png';
  import BackToTopArrow from '@/components/buttons/BackToTopArrow.vue';
  import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
  import gotoPage from '@/utils/router.js';
  import { computed, nextTick, onMounted, ref } from 'vue';
  import { useStore } from 'vuex';

  // Mappage des noms des plans d'abonnement aux URLs des images correspondantes
  // pour mieux afficher les plans avec les images
  const packImages = {
    Gratuit: new URL('@/assets/tarifs/gratuit.jpg', import.meta.url).href,
    Essentiel: new URL('@/assets/tarifs/standard.jpg', import.meta.url).href,
    Performance: new URL('@/assets/tarifs/pro.jpg', import.meta.url).href,
    Pro: new URL('@/assets/tarifs/premium.jpg', import.meta.url).href,
    Plus: new URL('@/assets/tarifs/standard.jpg', import.meta.url).href,
    Complete: new URL('@/assets/tarifs/pro.jpg', import.meta.url).href,
  };

  export default {
    name: 'PricesPage',
    components: {
      PrimaryRoundedButton,
      BackToTopArrow,
    },
    props: {
      isViewingAsRecruiter: Boolean,
    },

    setup(props) {
      const billingPeriod = ref('monthly');
      const expandedCard = ref(null);
      const expandedSection = ref(null);
      const store = useStore();

      const isLoggedIn = computed(() => store.getters.isLoggedIn);
      const userRole = computed(() => store.getters.userRole);
      const subscriptions = computed(
        () => store.getters['subscription/getSubscription']
      );

      // Charger les données API au montage
      onMounted(async () => {
        if (!subscriptions.value.length) {
          await store.dispatch('subscription/fetchSubscriptions');
        }
      });

      // Filtrer les cartes en fonction de l'état de connexion et du type utilisateur
      const filteredSubscriptions = computed(() => {
        const mapped = subscriptions.value
          .map((plan) => ({
            ...plan,
            isPopular: /Pro/.test(plan.nom),
            image: getMatchingImage(plan.nom),
          }))
          .sort((a, b) => a.prix_ttc - b.prix_ttc);

        if (isLoggedIn.value) {
          return mapped.filter((p) =>
            userRole.value === 'recruiter' ? p.entreprise : !p.entreprise
          );
        } else {
          return mapped.filter((p) =>
            props.isViewingAsRecruiter ? p.entreprise : !p.entreprise
          );
        }
      });

      // Fonction pour trouver l'image qui correspond au nom du plan
      const getMatchingImage = (planName) => {
        const key = Object.keys(packImages).find((key) =>
          planName.toLowerCase().includes(key.toLowerCase())
        );
        return key ? packImages[key] : '';
      };

      const expandedCardData = computed(() => {
        const found = subscriptions.value.find(
          (plan) => plan.id === expandedCard.value
        );

        return found
          ? {
              ...found,
              image: getMatchingImage(found.nom),
            }
          : {};
      });

      const scrollToExpanded = async () => {
        await nextTick();
        setTimeout(() => {
          if (expandedSection.value) {
            const offset =
              expandedSection.value.getBoundingClientRect().top +
              window.scrollY -
              120;
            window.scrollTo({ top: offset, behavior: 'smooth' });
          }
        }, 0);
      };

      const handleCardClick = (id) => {
        expandedCard.value = id;
      };

      const formatPrice = (price) => {
        return price.toLocaleString('fr-FR', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        });
      };

      const getPrice = (basePrice) => {
        const price =
          billingPeriod.value === 'annual' ? basePrice * 12 : basePrice;

        return Number(price.toFixed(2));
      };

      // Cette fonction retourne 'illimité' si le nom du plan est 'Gratuit',
      // sinon elle retourne '3 mois'.
      const getIntervalCount = (plan) => {
        return plan.nom === 'Gratuit' ? 'illimité' : '3 mois';
      };

      const isGrey = (desc, planName) => {
        return (
          (planName === 'Gratuit' || planName === 'Standard') &&
          (desc === 'Support limité' ||
            desc === "Accès à l'IA réservé aux abonnements payants")
        );
      };

      const getIcon = (desc) => {
        return isGrey(desc) ? '✘' : '✔';
      };

      // Calcule et trie les fonctionnalités du plan d'abonnement développé.
      // Les fonctionnalités actives sont listées en premier, suivies des fonctionnalités inactives (grisées).
      const sortedFeatures = computed(() => {
        const desc = expandedCardData.value.description || [];
        const index = desc.findIndex((d) =>
          d.description.includes('Tout le contenu du pack')
        );
        const special = index !== -1 ? desc.splice(index, 1)[0] : null;
        const active = desc.filter(
          (f) => !isGrey(f.description, expandedCardData.value.nom)
        );
        const inactive = desc.filter((f) =>
          isGrey(f.description, expandedCardData.value.nom)
        );
        return [...(special ? [special] : []), ...active, ...inactive];
      });

      return {
        billingPeriod,
        expandedCard,
        expandedCardData,
        expandedSection,
        gotoPage,
        bgImage,
        filteredSubscriptions,
        handleCardClick,
        scrollToExpanded,
        getPrice,
        getIntervalCount,
        isGrey,
        getIcon,
        sortedFeatures,
        formatPrice,
        isLoggedIn,
        customSubtitles: {
          Gratuit: 'Fonctions basiques',
          'Entreprise Essentiel': 'Pour démarrer simplement',
          'Entreprise Performance': 'Recrutement optimisé',
          'Pro Grande Entreprise': 'Pour gros volumes',
          'Entreprise Plus': 'Bon rapport qualité/prix',
          'Entreprise Complete': 'Recrutement délégué',
          Essentiel: 'Visibilité renforcée',
          'Carrière Pro': 'Carrière accompagnée',
        },
      };
    },
  };
</script>

<style scoped>
  .choose-subscription-toggle-price .v-btn--active {
    color: var(--text-1) !important;
  }

  .choose-subscription-toggle-price button {
    height: 48px !important;
    border: 1px solid var(--primary-1);
    color: var(--surface-bg);
    background-color: transparent;
  }

  .cards-container {
    display: grid;
    grid-template-columns: repeat(3, 333px);
    gap: 20px;
    row-gap: 50px;
    justify-content: center;
    margin: 50px auto;
  }

  .card-wrapper {
    position: relative;
    padding-top: 30px;
  }

  .popular-badge {
    position: absolute;
    top: -15px;
    left: 0;
    width: 100%;
    background-color: var(--yellow-100);
    color: white;
    padding: 10px;
    border-radius: 10px;
    font-size: 24px;
    font-weight: bold;
    text-align: center;
  }

  .card {
    background-color: white;
    border: 1px solid var(--gray-10);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition:
      transform 0.3s,
      box-shadow 0.3s;
    position: relative;
    isolation: isolate;
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 420px;
  }

  .card:hover {
    transform: translateY(-10px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }

  .highlight-card {
    position: relative;
    border: 2px solid var(--yellow-80);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .highlight-card::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border: 5px solid var(--yellow-80);
    border-radius: 10px;
    pointer-events: none;
  }
  .princing-container {
    min-height: 70vh;
  }
  .pricing-background {
    position: relative;
    padding: 50px 0;
  }

  .background-image {
    position: absolute;
    height: 70vh;
    left: 0px;
    width: 100vw;
    background-size: 100%;
  }
  .card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
    padding: 20px;
  }

  .card-title {
    height: 90px;
    font-size: 30px;
    font-weight: 800;
    margin-bottom: 10px;
    color: var(--gray-100);
  }

  .price-container {
    display: flex;
    justify-content: center;
    align-items: baseline;
    gap: 4px;
    width: 100%;
    border-bottom: #a9a9a9 2px solid;
    margin-bottom: 50px;
  }

  .card-price {
    font-size: 42px;
    font-weight: bold;
    color: var(--yellow-80);
    margin-bottom: 10px;
  }

  .card-description {
    flex-grow: 1;
    font-size: 1em;
    color: var(--gray-100);
  }

  .card-duration {
    font-size: 24px;
    font-weight: 600;
    color: var(--gray-100);
  }
  .card-interval_count {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 40px;
    color: var(--gray-100);
  }
  .responsive-button {
    width: 40%;
    padding: 10px 40px;
    background-color: var(--yellow-80);
    color: var(--text-1);
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .expanded-details-container {
    width: 1100px;
    margin: 50px auto;
    padding: 0 20px;
  }

  .expanded-details {
    background-color: white;
    border-radius: 15px;
    padding: 30px 30px 30px 50px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    position: relative;
  }

  .expanded-title {
    font-size: 45px;
    font-weight: bold;
    color: var(--gray-100);
  }

  .expanded-subtitle {
    color: var(--yellow-100);
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 20px;
  }

  .expanded-price {
    font-size: 50px;
    color: var(--yellow-80);
    font-weight: bold;
    margin-bottom: 20px;
  }

  .features-grid {
    display: grid;
    grid-template-columns: 60% 40%;
    margin-bottom: 20px;
  }

  .features-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .feature-item {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .check-icon {
    background-color: var(--yellow-80);
    color: var(--gray-light);
    font-size: 18px;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .feature-text {
    color: var(--gray-light);
    line-height: 1.4;
  }

  .features-image {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .plan-image {
    max-width: 70%;
    border-radius: 10px;
  }

  .btn-abonnement {
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }
  .abonner-button {
    display: block;
    margin-left: auto;
    padding: 14px 100px;
    background-color: var(--yellow-80);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
    width: auto;
  }
  .expanded-header {
    display: flex;
    gap: 20px;
  }

  .title-section {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .price-period {
    font-size: 18px;
    color: var(--gray-100);
  }

  .duration-toggle {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    cursor: pointer;
  }

  .duration-toggle span {
    padding: 5px 15px;
    color: var(--gray-100);
  }

  .duration-toggle .active {
    color: var(--gray-light);
    border-bottom: 2px solid var(--yellow-80);
  }

  .features-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .feature-text {
    color: var(--gray-light);
    line-height: 1.4;
  }

  .inactive-check-icon {
    background-color: var(--gray-100);
    color: var(--gray-light);
    font-size: 18px;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .inactive-feature-text {
    color: var(--gray-100);
  }

  .button-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
  }

  .subscribe-button {
    background-color: var(--yellow-80);
    padding: 12px 40px;
  }

  /* ✅ MOBILE : petits smartphones (iPhone SE, etc.) */
  @media screen and (max-width: 480px) {
    .cards-container {
      grid-template-columns: 250px;
    }
    .background-image {
      background-size: 150%;
    }
  }

  /* ✅ MOBILE LARGE : smartphones standards (iPhone 12, Galaxy S21, etc.) */
  @media screen and (min-width: 481px) and (max-width: 767px) {
    .cards-container {
      grid-template-columns: repeat(2, 250px);
    }
    .background-image {
      background-size: 150%;
    }
  }

  /* ✅ TABLETTE : format portrait (iPad, Galaxy Tab) */
  @media screen and (min-width: 768px) and (max-width: 1023px) {
    .cards-container {
      grid-template-columns: repeat(2, 250px);
    }
  }

  /* ✅ TABLETTE LARGE / PETIT DESKTOP : transition vers écran large */
  @media screen and (min-width: 1024px) and (max-width: 1279px) {
    .cards-container {
      grid-template-columns: repeat(3, 333px);
    }
  }

  /* ✅ DESKTOP STANDARD : PC portable / écrans normaux */
  @media screen and (min-width: 1280px) and (max-width: 1439px) {
    .cards-container {
      grid-template-columns: repeat(3, 333px);
    }
  }

  /* ✅ TON ÉCRAN DE BASE : 1440px */
  @media screen and (min-width: 1440px) and (max-width: 1919px) {
    .cards-container {
      grid-template-columns: repeat(3, 333px);
    }
  }

  /* ✅ GRAND ÉCRAN : écrans Full HD (1920px) */
  @media screen and (min-width: 1920px) and (max-width: 2559px) {
    .cards-container {
      grid-template-columns: repeat(3, 333px);
    }
  }

  /* ✅ ÉCRAN 4K : très grands écrans */
  @media screen and (min-width: 2560px) {
    .cards-container {
      grid-template-columns: repeat(3, 333px);
    }
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .expanded-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 15px;
    }

    .title-section {
      flex-direction: column;
      align-items: flex-start;
    }

    .expanded-details {
      padding: 20px;
    }

    .features-grid {
      grid-template-columns: 1fr;
    }

    .features-image {
      justify-content: center;
    }

    .plan-image {
      max-width: 100%;
    }

    .abonner-button {
      width: 100%;
      padding: 14px;
    }
  }

  .active-icon {
    background-color: var(--yellow-80);
    color: white;
    border-radius: 50%;
    padding: 5px;
  }

  .inactive-icon {
    background-color: var(--gray-10);
    color: white;
    border-radius: 50%;
    padding: 5px;
  }

  .active-text {
    color: var(--text-1);
  }

  .inactive-text {
    color: var(--gray-100);
  }

  .features-image img {
    max-width: 100%;
    height: auto;
  }
</style>
