<template>
  <main class="container padding-container">
    <div class="password-container">
      <h2>Réinitialiser le mot de passe</h2>
      <div v-if="!passwordResetSuccess">
        <v-text-field 
          placeholder="Nouveau mot de passe" 
          :type="showPassword1 ? 'text' : 'password'" 
          :rules="[...passwordRules, ...notEmptyRules]" 
          v-model="formData.password1" 
          :append-inner-icon="showPassword1 ? 'mdi-eye' : 'mdi-eye-off'" 
          @click:append-inner="toggleShowPassword1"
        />
        <v-text-field 
          placeholder="Confirmer le mot de passe" 
          :type="showPassword2 ? 'text' : 'password'" 
          :rules="[...passwordRules, ...notEmptyRules]" 
          v-model="formData.password2" 
          :append-inner-icon="showPassword2 ? 'mdi-eye' : 'mdi-eye-off'"  
          @click:append-inner="toggleShowPassword2"
        />
        <PrimaryRoundedButton textContent="Valider" @click="resetPassword" />
      </div>
      <div v-else>
        <p>
          Votre mot de passe a été réinitialisé avec succès.
        </p>
        <PrimaryRoundedButton textContent="Connexion" @click="goToLogin" />
      </div>
    </div>
  </main>
</template>


<script>
import { toaster } from '../../../utils/toast/toast';
import PrimaryRoundedButton from '../../../components/buttons/PrimaryRoundedButton.vue';
import {passwordResetRequestConfirm} from '../../../services/password.service.js';
import { validatePassword , validateNotEmpty } from "../../../utils/validationRules"; 


export default {
  name: 'PasswordForgetChange',
  components: {
    PrimaryRoundedButton
  },
  data() {
    return {
      formData: {
        password1: '',
        password2: ''
      },
      isLoading: false,
      passwordResetSuccess: false,
      passwordRules:[
      v => validatePassword(v) || true],
      notEmptyRules: [
      v => validateNotEmpty(v) || true],
      showPassword1: false,
      showPassword2: false,
      homeUrl: "/connexion"
    };
  },
  methods: {
    toggleShowPassword1() {
      this.showPassword1 = !this.showPassword1;
    },
    toggleShowPassword2() {
      this.showPassword2 = !this.showPassword2;
    },
    async resetPassword() {
      if (this.formData.password1 !== this.formData.password2) {
        toaster.showErrorPopup("Les mots de passe ne correspondent pas.");
        return;
      }

      const validationErrors = this.passwordRules
        .map(rule => rule(this.formData.password1))
        .filter(errorMessage => errorMessage !== true);

      if (validationErrors.length > 0) {
        toaster.showErrorPopup(validationErrors.join(' '));
        return;
      }
  
      this.isLoading = true;
      try {
        await passwordResetRequestConfirm(this.formData.password1,this.$route.query.uid,this.$route.query["amp;token"])
      this.passwordResetSuccess = true;
      } catch (error) {
        //console.error(error);
        toaster.showErrorPopup("Une erreur s'est produite lors de la réinitialisation de votre mot de passe. Veuillez réessayer.");
      } finally {
        this.isLoading = false;
      }
    },
    goToLogin() {
      this.$router.push(this.homeUrl);
    }
  }
};
</script>


<style scoped>
h2 {
  display: flex;
  justify-content: center;
}
.container {
  height: fit-content;
  display: flex;
  justify-content: center;
  margin-top: 250px;
}
.password-container {
  height: fit-content;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 532px;
  gap: 30px;
  padding: 16px;
  background-color: var(--surface-bg-2);
}
.v-input {
  padding: 15px;
}
button {
  display: block; 
  margin: 0 auto; 
  width: 50%;
}
* {
    width: 100%;
}
p {
  text-align: center;
}
.v-btn {
  padding: 5px;
  margin-top: 20px;
}
</style>