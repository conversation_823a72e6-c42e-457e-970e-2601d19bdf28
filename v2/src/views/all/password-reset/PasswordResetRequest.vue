<template>
<main class="container padding-container">
  <v-form @submit.prevent ref="form" class="password-container border-radius-10">
    <h2>Mot de passe oublié</h2>
    <p>Veuillez saisir votre mail de connexion ci-dessous afin de recevoir un lien pour réinitialiser votre mot de passe.</p>
    <v-text-field label="Votre email" v-model="email" required class="email" :rules="[...emailRules , ...notEmptyRules]" />
    <div class="actions-container">
      <PrimaryRoundedButton :textContent="!isDisabled ? 'Réinitialiser mon mot de passe' : 'Email envoyé !' " @click="send" :isLoading="isLoading" :isDisabled="isDisabled" />
      <p class="text-underline cursor-pointer" @click="this.$router.push('/')">Retourner à l'accueil</p>
    </div>
  </v-form>
</main>
</template>

<script>

import PrimaryRoundedButton from '@/components/buttons/PrimaryRoundedButton.vue';
import { passwordResetRequest } from '../../../services/password.service';
import {  validateEmail , validateNotEmpty } from "../../../utils/validationRules"; 


export default {
  name: 'PasswordForgetRequest',
  components: {
    PrimaryRoundedButton,
  },
  data() {
    return {
      email: '',
      emailRules: [
           v => validateEmail(v) || true],
      notEmptyRules: [
           v => validateNotEmpty(v) || true],
      isLoading: false,
      isDisabled: false,
    }
  },
  methods: {
    async send() {
      try {
        const valid = await this.validateEmail();
        if (!valid) return;
        this.isLoading = true;
        const res = await passwordResetRequest(this.email);
        this.isDisabled = true;
        if(res === undefined) {
          this.isDisabled = false;
        }
      } catch (error) {
        //console.log(error);
      } finally {
        this.isLoading = false;
      }
    },
    async validateEmail() {
      return (await this.$refs.form.validate()).valid;

    } 
  }
}
</script>

<style scoped>
.container {
  height: fit-content;
  margin-top: 250px;
  display: flex;
  justify-content: center;
}

.password-container {
  height: fit-content;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 532px;
  gap: 30px;
  padding: 16px;
  background-color: var(--surface-bg-2);
}

.actions-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding-bottom: 8px;
}

.email {
  width: 100%;
}

</style>