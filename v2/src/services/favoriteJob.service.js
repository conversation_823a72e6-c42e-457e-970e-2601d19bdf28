import { HttpStatusCode } from 'axios';
import { axiosInstance } from './axios';
import store from '@/store';

/**
 * addFavoriteJob 
 * @param {number} jobId
 * @return {Promise}
 * @throws {Error}
 */
const addFavoriteJob = async (jobId, joboffer) => {
    try {
        const response = await axiosInstance.post(`/favorite_job/add/${jobId}`);

        await store.commit('addFavoris',joboffer);

        return response;
    } catch (error) {
        throw new Error('add job to favorite failed');
    }
};

/**
 * removeFavoriteJob 
 * @param {number} jobId
 * @return {Promise}
 * @throws {Error}
 */
const removeFavoriteJob = async (jobId) => {
    try {
        const response = await axiosInstance.post(`/favorite_job/add/${jobId}`);
        store.commit('deleteFavoris', jobId);

        return response;
    } catch (error) {
        throw new Error('remove job from favorite failed');
    }
};

async function toggleJobOfferIsFavorite(id) {
    const { status } = await axiosInstance.post(`/favorite_job/add/${id}`);

    return status == HttpStatusCode.Created
}

export { addFavoriteJob, removeFavoriteJob, toggleJobOfferIsFavorite };
