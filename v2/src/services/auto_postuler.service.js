import store from '@/store';
import { toaster } from '@/utils/toast/toast.js';
import { axiosInstance } from './axios';

/**
 * Met à jour les critères d'auto-postulation de l'utilisateur
 * @param {Object} data - Données à enregistrer
 * @returns {Promise<void>}
 */
const updateUserCriterias = async (data) => {
  try {
    const formData = new FormData();

    formData.append('nom_job', data.nom_job || '');
    formData.append('contrat', data.contrat || '');
    formData.append('experience', data.experience || '');
    formData.append('salaire_souhaite', data.salaire_souhaite || '');
    formData.append('presence', data.presence || '');
    formData.append('selectedCV', data.selectedCV || '');
    formData.append('lettreMotivation', data.lettreMotivation || '');

    await axiosInstance.put('/user/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    // Mise à jour dans le store Vuex
    const updatedUser = {
      ...store.getters.getUser,
      nom_job: data.nom_job,
      contrat: data.contrat,
      experience: data.experience,
      salaire_souhaite: data.salaire_souhaite,
      presence: data.presence,
      selectedCV: data.selectedCV,
      lettreMotivation: data.lettreMotivation,
    };

    store.dispatch('handleUserChange', { type: null, payload: updatedUser });

    toaster.showSuccessPopup('Critères de recherche sauvegardés.');
  } catch (error) {
    console.error(
      'Erreur lors de la sauvegarde des critères de recherche.',
      error
    );
    toaster.showErrorPopup('Erreur lors de la sauvegarde des critères.');
  }
};

/**
 * Récupère les critères de l'utilisateur à partir du store
 * @returns {Object} formData
 */
const getUserCriteriasSectionDatas = () => {
  const userInfos = store.getters.getUser;

  return {
    nom_job: userInfos.nom_job || '',
    contrat: userInfos.contrat || '',
    experience: userInfos.experience || '',
    salaire_souhaite: userInfos.salaire_souhaite || '',
    presence: userInfos.presence || '',
    selectedCV: userInfos.selectedCV || '',
    lettreMotivation: userInfos.lettreMotivation || '',
  };
};

export { updateUserCriterias, getUserCriteriasSectionDatas };
