import { axiosInstance } from './axios';
import store from '@/store';


/**
 * Get all IA conversations
 * @return {Promise<Array>}
 * @throws {Error}
 */
const getAllIaConversation = async () => {
    try {
        const response = await axiosInstance.get('/conversation/all-conversation');
        store.dispatch('handleAddConversation', response.data);
        return response.data;
    } catch (error) {
        //console.error(error);
    }
};

/**
 * Send IA message
 * @param {string} conversationId
 * @param {string} message
 * @param {string} fileName
 * @return {Promise<Object>}
 * @throws {Error}
 */
const sendIaConversation = async (conversationId, message, fileName) => {
    try {
        const formData = new FormData();

        formData.append("message_applicant", message);
        formData.append("conversation", conversationId);
        if (fileName) { formData.append("fileName", fileName.name); }

        //console.log(fileName.file);
        const response = await axiosInstance.post("/conversation/messages/", formData);
        store.dispatch("handleConversationChange", response.data);
        //console.log(response);
        //Get IA response to classify message
        return response;
    } catch (error) {
        //console.error("Error in sendIaConversation:", error);
        throw error;
    }

};

/**
 * Save IA response to backend
 * @param {string} conversationId
 * @param {string} iaMessage
 * @param {string} detectedClass
 * @param {Array} recommendedJobOffers
 * @return {Promise<Object>}
 * @throws {Error}
 */
const saveIaResponse = async (conversationId, iaMessage, detectedClass = null, recommendedJobOffers = []) => {
    try {
        const formData = new FormData();

        formData.append("message_Mc", iaMessage);
        formData.append("conversation", conversationId);

        // Auto-détecter si le message contient des références à des offres d'emploi
        if (!detectedClass) {
            const jobKeywords = ['offre', 'emploi', 'poste', 'job', 'travail', 'candidature', 'recrutement'];
            const hasJobKeywords = jobKeywords.some(keyword =>
                iaMessage.toLowerCase().includes(keyword)
            );
            if (hasJobKeywords) {
                detectedClass = "best_jobs";
                console.log('Auto-détection: classe "best_jobs" détectée dans le message IA');
            }
        }

        if (detectedClass) {
            formData.append("detected_class", detectedClass);
        }
        if (recommendedJobOffers && recommendedJobOffers.length > 0) {
            formData.append("recommended_job_offers", JSON.stringify(recommendedJobOffers));
        }

        console.log('Sauvegarde de la réponse IA en backend...', {
            conversationId,
            messageLength: iaMessage.length,
            detectedClass,
            jobOffersCount: recommendedJobOffers ? recommendedJobOffers.length : 0
        });

        const response = await axiosInstance.post("/conversation/messages/", formData);
        console.log('Réponse IA sauvegardée:', response.data);
        return response;
    } catch (error) {
        console.error("Error in saveIaResponse:", error);
        throw error;
    }
};
/**
 * Create a new conversation
 * @throws {Error}
 */
const createConversation = async () => {
    try {
        const response = await axiosInstance.post("/conversation/new-conversation/");
        response.data.date_modification = new Date().toISOString();
        store.dispatch("handleAddConversation", response.data);
        return response.data;
    } catch (error) {
        //console.error(error);
        throw error;
    }
};

/**
 * Change conversation title
 * @param {string} conversationId
 * @param {string} title
 * @throws {Error}
 */
const changeConversationTitle = async (conversationId, title) => {
    try {
        await axiosInstance.put(`/conversation/new-conversation/${conversationId}/`, { titre: title });
        store.dispatch("handleTitleConversationChange", conversationId);
    } catch (error) {
        //console.error(error);
    }
};

/**
 * Delete a conversation
 * @param {Object} conversation
 * @throws {Error}
 */
const deleteConversation = async (conversation) => {
    try {
        await axiosInstance.delete(`/conversation/delete-conversation/${conversation.conversation_id}/`);
        store.dispatch("handleDeleteConversation", conversation); // Mise à jour du store Vuex
    } catch (error) {
        //console.error(error);
    }
};

/**
 * Like a message
 * @param {string} messageId
 * @throws {Error}
 */
const likeMessage = async (messageId) => {
    try {
        await axiosInstance.post(`/conversation/toggle-like/${messageId}/`);
    } catch (error) {
        //console.error(error);
    }
};

/**
 * Dislike a message
 * @param {string} messageId
 * @param {string} message
 * @throws {Error}
 */
const dislikeMessage = async (messageId, message) => {
    try {
        await axiosInstance.post(`/conversation/toggle-dislike/${messageId}/`, { dislike_raison: message });
    } catch (error) {
        //console.error(error);
    }
};

/**
 * Signal a message
 * @param {string} messageId
 * @param {string} feedback
 * @throws {Error}
 */
const signalMessage = async (messageId, feedback) => {
    try {
        await axiosInstance.post(`/conversation/signaler-message/${messageId}/`, { signal_raison: feedback });
    } catch (error) {
        //console.error(error);
    }
};

/**
 * Récupère l'historique des messages entre deux utilisateurs
 * @param {string} userId1 - ID du premier utilisateur
 * @param {string} userId2 - ID du deuxième utilisateur
 * @return {Promise<Array>} - Liste des messages
 * @throws {Error}
 */
const getConversationHistory = async (userId1, userId2) => {
    try {
        // Vérifier que les IDs sont valides
        if (!userId1 || !userId2) {
            //console.error('IDs utilisateurs invalides pour récupérer l\'historique des messages');
            return [];
        }

        // Convertir les IDs en nombres si nécessaire
        const id1 = parseInt(userId1);
        const id2 = parseInt(userId2);

        // Trier les IDs pour assurer la cohérence de l'URL
        const [minId, maxId] = [id1, id2].sort((a, b) => a - b);

        //console.log(`Récupération de l'historique des messages entre ${minId} et ${maxId}`);

        // Utiliser l'URL complète pour la requête API
        const response = await axiosInstance.get(`https://websocket.thanks-boss.com/conversation/${minId}/${maxId}/`);

        // Vérifier que la réponse contient des données
        if (!response.data || !Array.isArray(response.data)) {
            //console.error('Format de réponse invalide:', response.data);
            return [];
        }

        //console.log(`${response.data.length} messages récupérés entre ${minId} et ${maxId}`);

        // Retourner les messages bruts, le formatage sera fait par le composant
        return response.data;
    } catch (error) {
        //console.error('Erreur lors de la récupération de l\'historique des messages:', error);
        // Retourner un tableau vide en cas d'erreur au lieu de propager l'erreur
        return [];
    }
};

export {
    getAllIaConversation,
    sendIaConversation,
    saveIaResponse,
    createConversation,
    changeConversationTitle,
    deleteConversation,
    likeMessage,
    dislikeMessage,
    signalMessage,
    getConversationHistory
};
