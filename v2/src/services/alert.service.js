import store from '@/store';
import { toaster } from '@/utils/toast/toast.js';
import { axiosInstance } from './axios';

/**
 * createNewJobAlert
 * @param {Object} jobAlertDatas
 * @return {Promise}
 */
const createNewJobAlert = async (jobAlertDatas) => {
  try {
    // Validation des champs obligatoires
    if (
      !jobAlertDatas.alertName?.trim() ||
      !jobAlertDatas.mail?.trim() ||
      !jobAlertDatas.title?.trim()
    ) {
      throw new Error('champs requis obligatoires');
    }
    //  format list to string
    let remote = jobAlertDatas.remote.join();
    let contract = jobAlertDatas.contract.join();
    let activity = jobAlertDatas.sector.join();
    let salary = jobAlertDatas.salaryValue.join();
    let formData = {
      nom: jobAlertDatas.alertName, //  string
      email: jobAlertDatas.mail, //  string
      quoi: jobAlertDatas.title, //  string
      ville: jobAlertDatas.city, //  string
      radius: jobAlertDatas.radius,
      teletravail: remote, //  string
      contrat: contract, //  string
      activite: activity, //  string
      experience: jobAlertDatas.experience, //  string
      salaire: salary, //  string
      //"savoir_pro": jobAlertDatas.skills,         //  ID int FIRST WE NEED TO GET skills and soft SKILL LIST
      //"savoir": jobAlertDatas.softSkills,         //  ID int
      frequency_best_jobs: jobAlertDatas.frequency_best_jobs || 'Jamais', // Update field name
      startDate: jobAlertDatas.startDate || null,
    };

    const response = await axiosInstance.post('/alerte/', formData);

    toaster.showSuccessPopup('Alerte créée.');

    //  update user
    store.dispatch('handleAddAlert', response.data);

    return response;
  } catch (error) {
    // Gestion d'erreur spécifique pour les champs requis
    if (error.message.includes('champs requis')) {
      toaster.showErrorPopup(
        'Veuillez fournir un nom et un poste pour votre nouvelle alerte.'
      );
    }
    // Gestion d'erreur HTTP 403
    else if (error.response && error.response.status === 403) {
      toaster.showErrorPopup(
        'Veuillez vous inscrire ou vous connecter à votre compte pour continuer.'
      );
    }
    // Gestion des autres erreurs
    else {
      toaster.showErrorPopup("Erreur lors de la création de l'alerte.");
    }
  }
};

/**
 * modifyJobAlert
 * @param {Object} jobAlertDatas
 * @param {number} alertIndex
 * @param {number} alertId
 * @return {Promise}
 */
const modifyJobAlert = async (alertId, jobAlertDatas, alertIndex) => {
  try {
    //  format list to string
    let remote,
      contract,
      activity,
      salary = '';
    if (jobAlertDatas.remote) remote = jobAlertDatas.remote.join();
    if (jobAlertDatas.contract) contract = jobAlertDatas.contract.join();
    if (jobAlertDatas.sector) activity = jobAlertDatas.sector.join();
    if (jobAlertDatas.salaryValue) salary = jobAlertDatas.salaryValue.join();

    let formData = {
      nom: jobAlertDatas.alertName, //  string
      email: jobAlertDatas.mail, //  string
      quoi: jobAlertDatas.title, //  string
      ville: jobAlertDatas.city, //  string
      radius: jobAlertDatas.radius,
      teletravail: remote, //  string
      contrat: contract, //  string
      activite: activity, //  string
      experience: jobAlertDatas.experience, //  string
      salaire: salary, //  string
      //"savoir_pro": jobAlertDatas.skills,         //  ID int FIRST WE NEED TO GET skills and soft SKILL LIST
      //"savoir": jobAlertDatas.softSkills,         //  ID int
      frequency_best_jobs: jobAlertDatas.frequency_best_jobs || 'Jamais', // Update field name
      startDate: jobAlertDatas.startDate || null,
    };

    const response = await axiosInstance.put(`/alerte/${alertId}/`, formData);
    toaster.showSuccessPopup('Alerte modifiée.');
    store.dispatch('handleChangeAlert', response.data);

    return response;
  } catch (error) {
    toaster.showErrorPopup("Erreur lors de la modification de l'alerte.");
    return null;
  }
};

/**
 * getSkillList
 * @return {Array}
 * @throws {Error}
 */
const getSkillList = async () => {
  try {
    const response = await axiosInstance.get('/savoirs/');

    return response;
  } catch (error) {
    throw new Error(error.response || 'downloading skill list failed');
  }
};

/**
 * getSoftSkillList
 * @return {Array}
 * @throws {Error}
 */
const getSoftSkillList = async () => {
  try {
    const response = await axiosInstance.get('/savoirs_pro/');
    return response;
  } catch (error) {
    throw new Error(error.response || 'downloading skill list failed');
  }
};

/**
 * deleteJobAlert
 * @param {number} alertId
 * @param {number} alertIndex
 * @return {Promise}
 */
const deleteJobAlert = async (alertId, alertIndex) => {
  try {
    const response = await axiosInstance.delete(`/alerte/${alertId}`, {
      validateStatus: function (status) {
        return status === 204 || status < 400;
      },
    });
    //console.log('Delete response:', response);
    toaster.showSuccessPopup('Alerte supprimée.');

    //  update user
    let user = await getUser();
    user.alerte.splice(alertIndex, 1);
  } catch (error) {
    //console.error('error :', error);

    toaster.showErrorPopup("Erreur lors de la suppression de l'alerte.");
  }
};

/**
 * getAlertById get alert from user in store
 * @param {number} alertId
 * @return {Object}
 * @throws {Error}
 */
const getAlertById = (alertId) => {
  const userInfos = getUser();
  //  iterate through each alert
  for (let i = 0; i < userInfos.alerte.length; i++) {
    if (userInfos.alerte[i].id == alertId) {
      return userInfos.alerte[i];
    }
  }

  throw new Error('Cannot retrieve alert with this id.');
};

/**
 * get user object from vuex (property style access)
 * @return {Object}
 */
const getUser = () => {
  return store.getters.getUser;
};

/**
 * toggleJobAlertActivation
 * @param {number} alertId
 * @return {Promise}
 */
const toggleJobAlertActivation = async (alertId) => {
  try {
    const response = await axiosInstance.post(
      `/alerte/${alertId}/toggle-active/`
    );
    toaster.showSuccessPopup("Statut de l'alerte mis à jour.");

    // Mettre à jour l'alerte dans l'état utilisateur si nécessaire
    let user = await getUser();
    const alert = user.alerte.find((alert) => alert.id === alertId);
    if (alert) {
      alert.isActive = !alert.isActive; // Supposons qu'il y ait un champ isActive
    }

    return response;
  } catch (error) {
    toaster.showErrorPopup(
      "Erreur lors de la mise à jour du statut de l'alerte."
    );
    return null;
  }
};

export {
  createNewJobAlert,
  deleteJobAlert,
  getAlertById,
  getSkillList,
  getSoftSkillList,
  modifyJobAlert,
  toggleJobAlertActivation,
};
