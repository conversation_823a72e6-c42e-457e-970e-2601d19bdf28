import { axiosInstance } from "./axios";
import store from "@/store";
import { toaster } from "@/utils/toast/toast.js";

// Fonction pour récupérer tous les utilisateurs (reste sur Axios si nécessaire)
const fetchUsers = async () => {
  try {
    const response = await axiosInstance.get('/user/search-user/');
    //console.log('Utilisateurs récupérés:', response.data);
    return response.data;  // Retourne la liste des utilisateurs
  } catch (error) {
    //console.error("Erreur lors de la récupération des utilisateurs :", error);
    throw error; // Relancer l'erreur pour gestion ultérieure
  }
};

// Fonction pour récupérer les détails d'un utilisateur spécifique (reste sur Axios si nécessaire)
const fetchUserDetails = async (userId) => {
  try {
    const response = await axiosInstance.get(`/user/${userId}/`);
    //console.log('Détails de l\'utilisateur récupérés:', response.data);
    return response.data; // Retourne les détails de l'utilisateur
  } catch (error) {
    //console.error("Erreur lors de la récupération des détails de l'utilisateur :", error);
    throw error;
  }
};

  

// Fonction pour envoyer un message via WebSocket
const sendMessage = async (conversationId, message) => {
  if (!this.isSocketOpen) {
    //console.error("WebSocket n'est pas ouvert");
    return;
  }

  try {
    const request = JSON.stringify({
      type: 'send_message',
      conversation_id: conversationId,
      message: message
    });
    this.chatSocket.send(request);  // Envoie du message via WebSocket
    //console.log('Message envoyé:', message);
    toaster.success('Message envoyé avec succès');
  } catch (error) {
    //console.error("Erreur lors de l'envoi du message via WebSocket :", error);
    toaster.error('Échec de l\'envoi du message');
    throw error;
  }
};

// Fonction pour récupérer les messages via WebSocket
const fetchMessages = async (conversationId) => {
  if (!this.isSocketOpen) {
    //console.error("WebSocket n'est pas ouvert");
    return;
  }

  try {
    const request = JSON.stringify({
      type: 'fetch_messages',
      conversation_id: conversationId
    });
    this.chatSocket.send(request);  // Récupération des messages via WebSocket
    //console.log('Requête de récupération des messages envoyée');
  } catch (error) {
    //console.error("Erreur lors de la récupération des messages via WebSocket :", error);
    throw error;
  }
};

// Fonction pour créer une nouvelle conversation via WebSocket
const createConversation = async (participantEmail) => {
  if (!this.isSocketOpen) {
    //console.error("WebSocket n'est pas ouvert");
    return;
  }

  try {
    const request = JSON.stringify({
      type: 'create_conversation',
      participant_email: participantEmail
    });
    this.chatSocket.send(request);  // Envoie la requête pour créer la conversation
    //console.log('Conversation créée pour:', participantEmail);
    toaster.success('Conversation commencée avec succès');
  } catch (error) {
    //console.error("Erreur lors de la création de la conversation via WebSocket :", error);
    toaster.error('Échec de la création de la conversation');
    throw error;
  }
};

export { fetchUsers, fetchUserDetails, sendMessage, fetchMessages, createConversation };
