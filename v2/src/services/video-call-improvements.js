/**
 * Améliorations du service d'appel vidéo
 * 
 * Ce fichier contient toutes les améliorations nécessaires pour :
 * 1. Raccrocher automatiquement lors du changement d'onglet/page
 * 2. Arrêter correctement la caméra et le microphone lors du raccrochage
 * 3. Améliorer la détection des messages d'appel entrants
 */

// Importations nécessaires (à adapter selon votre structure)
import router from '@/router';
import store from '@/store';

/**
 * Arrête tous les flux média (caméra et microphone)
 */
const stopAllMediaStreams = () => {
  try {
    //console.log('[APPEL] Arrêt de tous les flux média');
    
    // Arrêter le flux local stocké dans window.localStream
    if (window.localStream) {
      //console.log('[APPEL] Arrêt du flux média local');
      window.localStream.getTracks().forEach(track => {
        //console.log('[APPEL] Arrêt de la piste:', track.kind);
        track.stop();
      });
      window.localStream = null;
    }
    
    // Arrêter tous les flux média qui pourraient être stockés dans des éléments vidéo
    document.querySelectorAll('video').forEach(video => {
      if (video.srcObject) {
        //console.log('[APPEL] Arrêt du flux média d\'un élément vidéo');
        const mediaStream = video.srcObject;
        if (mediaStream && mediaStream.getTracks) {
          mediaStream.getTracks().forEach(track => {
            //console.log('[APPEL] Arrêt de la piste:', track.kind);
            track.stop();
          });
        }
        video.srcObject = null;
      }
    });
    
    // Fermer la connexion WebRTC si elle existe
    if (window.peerConnection) {
      //console.log('[APPEL] Fermeture de la connexion WebRTC');
      
      // Arrêter toutes les pistes des émetteurs
      const senders = window.peerConnection.getSenders();
      if (senders && senders.length > 0) {
        senders.forEach(sender => {
          if (sender.track) {
            //console.log('[APPEL] Arrêt de la piste émetteur:', sender.track.kind);
            sender.track.stop();
          }
        });
      }
      
      // Fermer la connexion
      window.peerConnection.close();
      window.peerConnection = null;
    }
    
    //console.log('[APPEL] Tous les flux média ont été arrêtés');
  } catch (error) {
    //console.error('[APPEL] Erreur lors de l\'arrêt des flux média:', error);
  }
};

/**
 * Réinitialise l'état de l'appel
 */
const resetCallState = () => {
  //console.log('[APPEL] Réinitialisation de l\'état de l\'appel');
  window.isCallActive = false;
  window.currentCallData = null;
};

/**
 * Termine l'appel en cours et nettoie toutes les ressources
 * @param {boolean} [notifyOtherUser=true] - Si true, envoie un message à l'autre utilisateur
 * @returns {Promise<void>}
 */
const endCall = async (notifyOtherUser = true) => {
  try {
    //console.log('[APPEL] Fin de l\'appel (notifyOtherUser:', notifyOtherUser, ')');
    
    // Récupérer l'ID de l'utilisateur actuel
    const currentUser = store.getters.getUser;
    if (!currentUser || !currentUser.id) {
      //console.error('[APPEL] Utilisateur non connecté, impossible de terminer l\'appel');
      return;
    }
    
    // Récupérer l'ID du destinataire (l'autre participant à l'appel)
    let recipientId = null;
    if (window.currentCallData && window.currentCallData.senderId) {
      recipientId = window.currentCallData.senderId;
    }
    
    // Si nous devons notifier l'autre utilisateur
    if (notifyOtherUser && recipientId) {
      // Envoyer un message de fin d'appel via tous les canaux disponibles
      
      // 1. Via le WebSocket de conversation
      if (window.webSocketConnection && window.webSocketConnection.readyState === WebSocket.OPEN) {
        //console.log('[APPEL] Envoi du message de fin d\'appel via le WebSocket de conversation');
        window.webSocketConnection.send(
          JSON.stringify({
            type: 'end_call',
            sender_id: currentUser.id,
            recipient_id: recipientId,
            timestamp: new Date().getTime()
          })
        );
      }
      
      // 2. Via le WebSocket global
      const webSocketUserConnection = store.state.userModule.webSocketUserConnection;
      if (webSocketUserConnection && webSocketUserConnection.readyState === WebSocket.OPEN) {
        //console.log('[APPEL] Envoi du message de fin d\'appel via le WebSocket global');
        webSocketUserConnection.send(
          JSON.stringify({
            type: 'end_call',
            sender_id: currentUser.id,
            recipient_id: recipientId,
            timestamp: new Date().getTime()
          })
        );
      }
    }
    
    // Arrêter tous les flux média
    stopAllMediaStreams();
    
    // Fermer la connexion WebSocket de conversation
    if (window.webSocketConnection) {
      //console.log('[APPEL] Fermeture du WebSocket de conversation');
      window.webSocketConnection.close();
      window.webSocketConnection = null;
    }
    
    // Réinitialiser l'état de l'appel
    resetCallState();
    
    // Déclencher un événement global pour masquer la notification d'appel entrant
    // et mettre à jour l'interface utilisateur
    const callEndedEvent = new CustomEvent('call-ended', {
      detail: {
        sender_id: currentUser.id,
        recipient_id: recipientId,
        timestamp: new Date().getTime()
      }
    });
    window.dispatchEvent(callEndedEvent);
    
    //console.log('[APPEL] Appel terminé avec succès');
    
    // Rediriger vers la page de messagerie sans paramètres d'appel
    // si nous sommes sur la page d'appel
    if (window.location.pathname.includes('/messagerie') && 
        window.location.search.includes('call=true')) {
      //console.log('[APPEL] Redirection vers la page de messagerie sans paramètres d\'appel');
      router.replace({
        path: '/messagerie',
        query: { chat: recipientId }
      });
    }
  } catch (error) {
    //console.error('[APPEL] Erreur lors de la fin de l\'appel:', error);
    
    // En cas d'erreur, essayer quand même d'arrêter les flux média
    stopAllMediaStreams();
    
    // Réinitialiser l'état de l'appel
    resetCallState();
    
    // Déclencher quand même l'événement de fin d'appel
    const callEndedEvent = new CustomEvent('call-ended');
    window.dispatchEvent(callEndedEvent);
  }
};

/**
 * Configure les écouteurs d'événements pour raccrocher automatiquement
 * lors du changement de page ou d'onglet
 */
const setupAutoHangupListeners = () => {
  try {
    //console.log('[APPEL] Configuration des écouteurs pour le raccrochage automatique');
    
    // Raccrocher lors du changement de page (navigation interne)
    const originalPush = router.push;
    router.push = function(location) {
      // Vérifier si un appel est actif
      if (window.isCallActive || window.currentCallData) {
        //console.log('[APPEL] Navigation détectée pendant un appel, raccrochage automatique');
        endCall(true);
      }
      
      // Appeler la fonction push originale
      return originalPush.apply(this, arguments);
    };
    
    // Raccrocher lors du changement d'onglet ou de la mise en arrière-plan
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden' && (window.isCallActive || window.currentCallData)) {
        //console.log('[APPEL] Changement d\'onglet ou mise en arrière-plan pendant un appel, raccrochage automatique');
        endCall(true);
      }
    });
    
    // Raccrocher lors de la fermeture de la page
    window.addEventListener('beforeunload', (event) => {
      if (window.isCallActive || window.currentCallData) {
        //console.log('[APPEL] Fermeture de la page pendant un appel, raccrochage automatique');
        endCall(true);
        
        // Afficher une confirmation à l'utilisateur
        event.preventDefault();
        event.returnValue = 'Un appel est en cours. Êtes-vous sûr de vouloir quitter la page ?';
      }
    });
    
    //console.log('[APPEL] Écouteurs pour le raccrochage automatique configurés avec succès');
  } catch (error) {
    //console.error('[APPEL] Erreur lors de la configuration des écouteurs pour le raccrochage automatique:', error);
  }
};

/**
 * Configure le gestionnaire de messages pour un WebSocket donné
 * Version améliorée qui détecte mieux les messages d'appel
 * @param {WebSocket} webSocket - La connexion WebSocket à utiliser
 * @param {string} userId - L'ID de l'utilisateur connecté
 * @returns {boolean} - True si le gestionnaire a été configuré avec succès
 */
const setupMessageHandler = (webSocket, userId) => {
  try {
    // Sauvegarder l'ancien gestionnaire onmessage s'il existe
    const oldOnMessage = webSocket.onmessage;
    
    //console.log('[APPEL] Configuration du gestionnaire de messages pour l\'utilisateur', userId);
    
    // Configurer le nouveau gestionnaire d'événements
    webSocket.onmessage = async (event) => {
      try {
        // Analyser les données reçues
        let data;
        try {
          data = JSON.parse(event.data);
        } catch (jsonError) {
          // Continuer avec l'ancien gestionnaire pour les messages non-JSON
          if (oldOnMessage && typeof oldOnMessage === 'function') {
            oldOnMessage(event);
          }
          return;
        }
        
        // Fonction pour rechercher récursivement un type spécifique dans l'objet
        const findTypeInObject = (obj, targetType) => {
          if (!obj || typeof obj !== 'object') return false;
          
          // Vérifier si l'objet a une propriété type ou message_type qui correspond
          if (obj.type === targetType || obj.message_type === targetType) return true;
          
          // Vérifier dans les propriétés de premier niveau
          for (const key in obj) {
            if (obj[key] && typeof obj[key] === 'object') {
              // Vérifier si cette propriété est un objet avec le type recherché
              if (obj[key].type === targetType || obj[key].message_type === targetType) return true;
              
              // Vérifier récursivement dans cet objet
              if (findTypeInObject(obj[key], targetType)) return true;
            }
          }
          
          return false;
        };
        
        // Fonction pour extraire les données pertinentes d'un message
        const extractDataFromMessage = (message, targetType) => {
          if (!message || typeof message !== 'object') return null;
          
          // Si l'objet a directement le type recherché
          if (message.type === targetType || message.message_type === targetType) {
            return message;
          }
          
          // Chercher dans les propriétés de premier niveau
          for (const key in message) {
            if (message[key] && typeof message[key] === 'object') {
              // Si cette propriété est un objet avec le type recherché
              if (message[key].type === targetType || message[key].message_type === targetType) {
                return message[key];
              }
              
              // Chercher récursivement dans cet objet
              const result = extractDataFromMessage(message[key], targetType);
              if (result) return result;
            }
          }
          
          return null;
        };
        
        // Vérifier si le message contient une offre d'appel
        const isOfferMessage = findTypeInObject(data, 'offer');
        
        // Vérifier si le message contient une fin d'appel
        const isEndCallMessage = findTypeInObject(data, 'end_call');
        
        // Vérifier si le message contient un rejet d'appel
        const isRejectCallMessage = findTypeInObject(data, 'reject_call');
        
        // Traiter les messages d'appel
        if (isOfferMessage) {
          //console.log('[APPEL] Message d\'offre d\'appel détecté');
          
          // Extraire les données d'offre
          const offerData = extractDataFromMessage(data, 'offer');
          
          if (!offerData) {
            //console.error('[APPEL] Impossible d\'extraire les données d\'offre du message');
            // Continuer avec l'ancien gestionnaire
            if (oldOnMessage && typeof oldOnMessage === 'function') {
              oldOnMessage(event);
            }
            return;
          }
          
          // Extraire l'ID de l'expéditeur (chercher dans plusieurs endroits possibles)
          let senderId = null;
          
          // Chercher dans l'objet d'offre
          senderId = offerData.sender_id || offerData.senderId || 
                    (offerData.sender && offerData.sender.id);
          
          // Si non trouvé, chercher dans l'objet parent
          if (!senderId) {
            senderId = data.sender_id || data.senderId || 
                      (data.sender && data.sender.id);
          }
          
          if (!senderId) {
            //console.error('[APPEL] Impossible de trouver l\'ID de l\'expéditeur dans le message');
            // Continuer avec l'ancien gestionnaire
            if (oldOnMessage && typeof oldOnMessage === 'function') {
              oldOnMessage(event);
            }
            return;
          }
          
          // Vérifier que l'offre n'est pas destinée à nous-mêmes
          if (senderId === userId) {
            //console.log('[APPEL] Ignorer l\'offre provenant de nous-mêmes');
            // Continuer avec l'ancien gestionnaire
            if (oldOnMessage && typeof oldOnMessage === 'function') {
              oldOnMessage(event);
            }
            return;
          }
          
          //console.log('[APPEL] Traitement de l\'appel entrant de l\'utilisateur', senderId);
          
          // Extraire l'offre SDP (chercher dans plusieurs endroits possibles)
          let offer = offerData.offer || offerData.sdp || offerData;
          
          // Extraire le type d'appel (chercher dans plusieurs endroits possibles)
          let callType = offerData.call_type || offerData.callType || 'video';
          
          // Réinitialiser l'état de l'appel
          resetCallState();
          
          // Importer dynamiquement le service pour éviter les dépendances circulaires
          const { handleIncomingCall } = await import('./video-call.service');
          
          // Traiter l'appel entrant
          const success = await handleIncomingCall({
            senderId: senderId,
            call_type: callType,
            offer: offer
          });
          
          //console.log('[APPEL] Traitement de l\'appel entrant:', success ? 'Réussi' : 'Échoué');
        }
        // Traiter les messages de fin d'appel
        else if (isEndCallMessage) {
          //console.log('[APPEL] Message de fin d\'appel détecté');
          
          // Extraire les données de fin d'appel
          const endCallData = extractDataFromMessage(data, 'end_call');
          
          // Extraire l'ID de l'expéditeur
          let senderId = null;
          
          if (endCallData) {
            senderId = endCallData.sender_id || endCallData.senderId || 
                      (endCallData.sender && endCallData.sender.id);
          }
          
          if (!senderId) {
            senderId = data.sender_id || data.senderId || 
                      (data.sender && data.sender.id);
          }
          
          // Arrêter tous les flux média
          stopAllMediaStreams();
          
          // Réinitialiser l'état de l'appel
          resetCallState();
          
          // Déclencher un événement global pour masquer la notification d'appel entrant
          const callEndedEvent = new CustomEvent('call-ended', {
            detail: {
              sender_id: senderId
            }
          });
          window.dispatchEvent(callEndedEvent);
        }
        // Traiter les messages de rejet d'appel
        else if (isRejectCallMessage) {
          //console.log('[APPEL] Message de rejet d\'appel détecté');
          
          // Extraire les données de rejet d'appel
          const rejectCallData = extractDataFromMessage(data, 'reject_call');
          
          // Extraire l'ID de l'expéditeur
          let senderId = null;
          
          if (rejectCallData) {
            senderId = rejectCallData.sender_id || rejectCallData.senderId || 
                      (rejectCallData.sender && rejectCallData.sender.id);
          }
          
          if (!senderId) {
            senderId = data.sender_id || data.senderId || 
                      (data.sender && data.sender.id);
          }
          
          // Arrêter tous les flux média
          stopAllMediaStreams();
          
          // Réinitialiser l'état de l'appel
          resetCallState();
          
          // Déclencher un événement global pour masquer la notification d'appel entrant
          const callRejectedEvent = new CustomEvent('call-rejected', {
            detail: {
              sender_id: senderId
            }
          });
          window.dispatchEvent(callRejectedEvent);
        }

        // Appeler l'ancien gestionnaire s'il existe
        if (oldOnMessage && typeof oldOnMessage === 'function') {
          oldOnMessage(event);
        }
      } catch (err) {
        //console.error('[APPEL] Erreur lors du traitement du message WebSocket:', err);
        
        // Appeler l'ancien gestionnaire malgré l'erreur
        if (oldOnMessage && typeof oldOnMessage === 'function') {
          oldOnMessage(event);
        }
      }
    };

    //console.log('[APPEL] Écouteur d\'appels vidéo initialisé pour l\'utilisateur', userId);
    return true;
  } catch (error) {
    //console.error('[APPEL] Erreur lors de la configuration du gestionnaire de messages:', error);
    return false;
  }
};

// Exporter les fonctions
export {
  stopAllMediaStreams,
  resetCallState,
  endCall,
  setupAutoHangupListeners,
  setupMessageHandler
};
