import store from '@/store';
import { axiosInstance } from './axios';

/**
 * postPost
 * @param {Object} postDatas
 * @return {Promise}
 * @throws {Error}
 */
const postPost = async (postDatas) => {
  try {
    const response = await axiosInstance.post(`/murs/`, postDatas);
    return response;
  } catch (error) {
    throw new Error(error.response || 'post failed to submit');
  }
};

/**
 * getPosts
 * @param {number} page - Page number to fetch
 * @param {number} pageSize - Number of posts per page
 * @return {Promise}
 * @throws {Error}
 */
const getPosts = async (page = 1, pageSize = 3) => {
  try {
    return await axiosInstance.get(`/murs/?page=${page}&page_size=${pageSize}`);
  } catch (error) {
    //console.error('Error fetching posts:', error);
    throw error;
  }
};
/**
 * @param {String} userId
 * @returns {Promise}
 */
const getPostsByUser = async (userId) => {
  try {
    const response = await axiosInstance.get(`/users/${userId}/posts/`);
    return response;
  } catch (error) {
    throw new Error(error.response || 'failed to get post list');
  }
};

/**
 * updatePost
 * @param {number} postId
 * @param {Object} postDatas
 * @return {Promise}
 * @throws {Error}
 */
const updatePost = async (postId, postDatas) => {
  try {
    const response = await axiosInstance.put(`/murs/${postId}/`, postDatas);
    return response;
  } catch (error) {
    throw new Error(error.response || 'post failed to submit');
  }
};

/**
 * getCommentsForPost
 * @param {number} postId
 * @return {Promise}
 * @throws {Error}
 */
const getCommentsForPost = async (postId) => {
  try {
    const response = await axiosInstance.get(`/murs/${postId}/commentaires`);
    return response;
  } catch (error) {
    throw new Error(error.response || 'failed to comment post');
  }
};

/**
 * sendCommentForPost
 * @param {number} postId
 * @param {Object} commentData
 * @return {Promise}
 * @throws {Error}
 */
const sendCommentForPost = async (postId, commentData) => {
  try {
    const response = await axiosInstance.post(
      `/murs/${postId}/add-comment/`,
      commentData
    );
    return response;
  } catch (error) {
    throw new Error(error.response || 'failed to comment post');
  }
};

/**
 * getCommentsForPost
 * @param {number} commentId
 * @return {Promise}
 * @throws {Error}
 */
const deleteComment = async (commentId) => {
  try {
    const response = await axiosInstance.delete(
      `/commentaires/${commentId}/delete/`
    );
    //console.log('Commentaire supprimé', response.data);
    // Mettez à jour l'interface utilisateur ou le store pour retirer le commentaire
  } catch (error) {
    //console.error(
    //  'Erreur lors de la suppression du commentaire',
    //  error.response.data
    //);
    alert("Une erreur s'est produite lors de la suppression du commentaire.");
  }
};

// pour récupérer les likes d'un utilisateur : users/<int:user_id>/liked-posts/

/**
 * isPostLikeById
 * @param {Object} post - L'objet post contenant les informations du post
 * @param {number} userId - L'identifiant de l'utilisateur actuel
 * @return {boolean} - Retourne true si le post est liké par l'utilisateur, sinon false
 */
const isPostLikeById = (post, userId) => {
  if (!post || !post.likers) {
    return false;
  }

  return post.likers.includes(userId);
};

/**
 * addLikePost
 * @param {number} postId
 * @return {Promise}
 * @throws {Error}
 */
const addLikePost = async (postId) => {
  try {
    const response = await axiosInstance.post(
      `/murs/${postId}/increment-like/`
    );
    return response;
  } catch (error) {
    throw new Error("Erreur lors de l'ajout du like.");
  }
};

/**
 * removeLikePost
 * @param {number} postId
 * @return {Promise}
 * @throws {Error}
 */
const removeLikePost = async (postId) => {
  try {
    const response = await axiosInstance.post(
      `/murs/${postId}/decrement-like/`
    );
    return response;
  } catch (error) {
    throw new Error('Erreur lors de la suppression du like.');
  }
};

/**
 * sharePost
 * @param {number} postId
 * @return {Promise}
 * @throws {Error}
 */
const sharePost = async (postId) => {
  try {
    const response = await axiosInstance.post(`/murs/${postId}/share`);
    return response;
  } catch (error) {
    throw new Error(error.response || 'failed to share post');
  }
};

/**
 * deletePost
 * @param {number} postId
 * @param {number} userId
 * @return {Promise}
 * @throws {Error}
 */
const deletePost = async (postId, userId) => {
  try {
    const response = await axiosInstance.delete(`/murs/${postId}/`);
    return response.data;
  } catch (error) {
    throw new Error(error.response || 'failed to delete post');
  }
};

/**
 * signalPost
 * @param {number} postId
 * @param {number} userId
 * @return {Promise}
 * @throws {Error}
 */
const signalPost = async (postId, userId) => {
  try {
    const response = await axiosInstance.post(`/murs/${postId}/`, userId);
    return response;
  } catch (error) {
    throw new Error(error.response || 'failed to signal post');
  }
};

/**
 * hidePost
 * @param {number} postId
 * @param {number} userId
 * @return {Promise}
 * @throws {Error}
 */
const hidePost = async (postId, userId) => {
  try {
    const response = await axiosInstance.post(`/murs/${postId}/`, userId);
    return response;
  } catch (error) {
    throw new Error(error.response || 'failed to hide post');
  }
};

const fetchCommentCounts = async () => {
  const response = await axiosInstance.get('/commentaires/count-by-post/');
  return response.data; // [{ postId: 1, commentCount: 15 }, ...]
};

const getPostLikers = async (postId) => {
  try {
    const response = await axiosInstance.get(`/murs/${postId}/likers/`);
    return response.data;
  } catch (error) {
    //console.error(
    //  'Erreur lors de la récupération des utilisateurs ayant liké le post :',
    //  error
    //);
    throw error;
  }
};

/**
 * get user object from vuex (property style access)
 * @return {Object}
 */
const getUser = () => {
  return store.getters.getUser;
};

export {
  addLikePost,
  deleteComment,
  deletePost,
  fetchCommentCounts,
  getCommentsForPost,
  getPostLikers,
  getPosts,
  getPostsByUser,
  getUser,
  hidePost,
  isPostLikeById,
  postPost,
  removeLikePost,
  sendCommentForPost,
  sharePost,
  signalPost,
  updatePost,
};
