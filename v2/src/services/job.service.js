import store from '@/store';
import { toaster } from '@/utils/toast/toast.js';
import { axiosInstance } from './axios';

/**
 * getJobList
 * @return {Promise}
 * @throws {Error}
 */
const getJobList = async () => {
  try {
    const response = await axiosInstance.get(`/jobs/`);
    return response.data.results;
  } catch (error) {
    throw new Error('Jobs download failed');
  }
};

/**
 * getPublishedJobList
 * @return {Promise}
 * @throws {Error}
 */
const getPublishedJobList = async (page = 1, pageSize = 20) => {
  try {
    // Utiliser l'URL correcte pour les offres d'emploi publiées
    const response = await axiosInstance.get(`/jobs/`, {
      params: {
        page: page,
        page_size: pageSize,
        publie: true,
        closed: false
      },
    });
    //console.log('API Response:', response.data);
    const jobs = response.data.results;
    const hasNext = response.data.next !== null;
    return { jobs, hasNext };
  } catch (error) {
    throw new Error('Failed to retrieve published jobs');
  }
};

/**
 * getJobById
 * @param {number} jobId
 * @return {Promise}
 * @throws {Error}
 */
const getJobById = async (jobId) => {
  try {
    const response = await axiosInstance.get(`/jobs/${jobId}`);
    return response.data;
  } catch (error) {
    throw new Error('Jobs download failed');
  }
};

/**
 * getJobofferById
 * @param {number} jobId
 * @return {Promise}
 * @throws {Error}
 */
const getJobofferById = async (jobId) => {
  try {
    const response = await axiosInstance.get(`/job-offers/${jobId}`);
    return response.data;
  } catch (error) {
    throw new Error('Jobs download failed');
  }
};

/**
 * postApplicationToJob
 * @param {String} jobId
 * @param {Object} formData
 * @return {Promise}
 */
const postApplicationToJob = async (jobId, formData) => {
  try {
    const response = await axiosInstance.post(`/postuler/${jobId}/`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response;
  } catch (error) {
    toaster.showErrorPopup(
      'Erreur lors de la candidature. Veuillez réessayer ultérieurement.'
    );
  }
};
/**
 *
 * @param {String} jobId
 * @returns {Promise}
 */
const cancelApplicationToJob = async (jobId) => {
  try {
    //console.log('Job ID:', jobId.toString());
    const response = await axiosInstance.post(
      `/postuler/sup/${jobId.toString()}/`,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
    return response;
  } catch (error) {
    toaster.showErrorPopup(
      'Erreur lors de la désignation de la candidature. Veuillez réessayer ultérieurement.'
    );
  }
};

/**
 * isJobFavoriteById
 * @param {String} jobId
 * @return {Boolean}
 * @throws {Error}
 */
const isJobFavoriteById = (jobId) => {
  let user = getUser();
  let isJobFavorite = false;

  if (user.favorie_job === undefined || user.favorie_job === null)
    return isJobFavorite;

  for (let i = 0; i < user.favorie_job.length; i++) {
    if (user.favorie_job[i].job_offer.id == jobId) isJobFavorite = true;
  }

  return isJobFavorite;
};

/**
 * get user object from vuex (property style access)
 * @return {Object}
 */
const getUser = () => {
  return store.getters.getUser;
};

/**
 * getJobsByUser
 * @return {Promise}
 * @throws {Error}
 */
const getJobsByUser = async () => {
  try {
    const user = await getUser(); // Obtenir l'utilisateur connecté
    const userId = user.id; // Obtenir l'ID de l'utilisateur
    const response = await axiosInstance.get(`/job_author/${userId}`);
    return response.data;
  } catch (error) {
    //console.error("Erreur lors de la récupération des offres d'emploi:", error);
    throw error;
  }
};

export {
  cancelApplicationToJob,
  getJobById,
  getJobList,
  getJobsByUser,
  getPublishedJobList,
  isJobFavoriteById,
  postApplicationToJob,
  getJobofferById,
};
