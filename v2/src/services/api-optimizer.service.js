/**
 * Service d'optimisation des requêtes API
 * Ce service permet d'optimiser les requêtes API en utilisant des techniques comme
 * la pagination, le chargement à la demande, et la mise en cache
 */

import { cachedGet } from './axios';
import cacheService from './cache.service';
import { debounce, throttle } from '@/utils/performance';

import { apiBaseUrl } from './axios';

// Vérifier si cachedGet existe, sinon utiliser une fonction de secours
const safeGet =
  cachedGet ||
  ((url, params, options = {}) => {
    console.warn('cachedGet not available, using fallback');

    // Construire l'URL complète avec l'URL de base de l'API
    const fullUrl = url.startsWith('http') ? url : `${apiBaseUrl}${url}`;

    //console.log('safeGet - URL complète:', fullUrl);

    return fetch(fullUrl + '?' + new URLSearchParams(params).toString())
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        // Si options.cacheTTL est défini, mettre en cache les données
        if (options.cacheTTL) {
          const cacheKey = cacheService.createKey(url, params);
          cacheService.set(cacheKey, data, options.cacheTTL);
        }
        return data;
      });
  });

/**
 * Charge les données avec pagination et mise en cache
 * @param {string} url - URL de l'API
 * @param {Object} options - Options de chargement
 * @param {number} options.page - Numéro de page
 * @param {number} options.pageSize - Taille de la page
 * @param {Object} options.filters - Filtres à appliquer
 * @param {number} options.cacheTTL - Durée de vie du cache en millisecondes
 * @returns {Promise<Object>} - Données paginées
 */
export const loadPaginatedData = async (url, options = {}) => {
  const {
    page = 1,
    pageSize = 10,
    filters = {},
    cacheTTL = 5 * 60 * 1000, // 5 minutes par défaut
  } = options;

  // Construire les paramètres de requête
  const params = {
    page,
    page_size: pageSize,
    ...filters,
  };

  //console.log('loadPaginatedData - URL:', url);
  //console.log('loadPaginatedData - Params:', params);

  // Créer une clé de cache unique pour cette requête
  const cacheKey = cacheService.createKey(url, params);

  try {
    // Essayer de récupérer les données du cache
    const cachedData = cacheService.get(cacheKey);
    if (cachedData) {
      //console.log(
      //  'loadPaginatedData - Données récupérées du cache:',
      //  cachedData
      //);
      return cachedData;
    }

    // Si les données ne sont pas en cache, faire la requête
    //console.log(
    //  'loadPaginatedData - Données non trouvées en cache, exécution de la requête'
    //);

    // Vérifier si l'URL commence par un slash
    const apiUrl = url.startsWith('/') ? url : `/${url}`;

    const response = await safeGet(apiUrl, params, { cacheTTL });
    //console.log('loadPaginatedData - Réponse reçue:', response);

    // Gérer les deux formats de réponse possibles (axios ou fetch)
    const data = response.data || response;

    // Traiter spécifiquement les données pour les offres d'emploi
    if (url.includes('jobs')) {
      //console.log(
      //  "loadPaginatedData - Traitement spécifique pour les offres d'emploi"
      //);

      // Si les données contiennent des résultats et next, les formater correctement
      if (data && data.results) {
        const formattedData = {
          jobs: data.results,
          hasNext: data.next && data.next.length > 0,
        };

        // Mettre en cache les données formatées
        cacheService.set(cacheKey, formattedData, cacheTTL);

        //console.log('loadPaginatedData - Données formatées:', formattedData);
        return formattedData;
      }
    }

    // Mettre en cache les données
    cacheService.set(cacheKey, data, cacheTTL);

    return data;
  } catch (error) {
    //console.error(`Error loading paginated data from ${url}:`, error);
    throw error;
  }
};

/**
 * Version debounced de loadPaginatedData pour les recherches
 * @param {string} url - URL de l'API
 * @param {Object} options - Options de chargement
 * @returns {Function} - Fonction debounced
 */
export const debouncedSearch = debounce(loadPaginatedData, 300);

/**
 * Version throttled de loadPaginatedData pour le scroll infini
 * @param {string} url - URL de l'API
 * @param {Object} options - Options de chargement
 * @returns {Function} - Fonction throttled
 */
export const throttledLoadMore = throttle(loadPaginatedData, 300);

/**
 * Charge les données visibles à l'écran (pour l'optimisation des listes)
 * @param {string} url - URL de l'API
 * @param {Array} visibleItems - Identifiants des éléments visibles
 * @param {Object} options - Options supplémentaires
 * @returns {Promise<Array>} - Données des éléments visibles
 */
export const loadVisibleData = async (url, visibleItems, options = {}) => {
  if (!visibleItems || visibleItems.length === 0) {
    return [];
  }

  // Construire les paramètres de requête
  const params = {
    ids: visibleItems.join(','),
    ...options,
  };

  try {
    const response = await safeGet(url, params);
    return response.data || response;
  } catch (error) {
    //console.error(`Error loading visible data from ${url}:`, error);
    throw error;
  }
};

/**
 * Précharge les données pour améliorer l'expérience utilisateur
 * @param {string} url - URL de l'API
 * @param {Object} params - Paramètres de la requête
 */
export const preloadData = (url, params = {}) => {
  // Utiliser setTimeout pour ne pas bloquer le thread principal
  setTimeout(() => {
    safeGet(url, params).catch((error) => {
      //console.error(`Error preloading data from ${url}:`, error);
    });
  }, 0);
};

/**
 * Optimise les requêtes en batch pour réduire le nombre d'appels API
 * @param {string} url - URL de l'API
 * @param {Array} items - Éléments à traiter par lots
 * @param {number} batchSize - Taille du lot
 * @param {Object} options - Options supplémentaires
 * @returns {Promise<Array>} - Résultats combinés
 */
export const batchRequests = async (
  url,
  items,
  batchSize = 50,
  options = {}
) => {
  if (!items || items.length === 0) {
    return [];
  }

  const results = [];

  // Traiter les éléments par lots
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);

    // Construire les paramètres de requête pour ce lot
    const params = {
      ids: batch.join(','),
      ...options,
    };

    try {
      const response = await safeGet(url, params);
      results.push(...(response.data || response));

      // Permettre au navigateur de respirer entre les lots
      if (i + batchSize < items.length) {
        await new Promise((resolve) => setTimeout(resolve, 0));
      }
    } catch (error) {
      //console.error(`Error in batch request to ${url}:`, error);
      // Continuer avec le lot suivant malgré l'erreur
    }
  }

  return results;
};

export default {
  loadPaginatedData,
  debouncedSearch,
  throttledLoadMore,
  loadVisibleData,
  preloadData,
  batchRequests,
};
