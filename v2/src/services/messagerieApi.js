import { axiosInstance } from './axios';

export async function fetchChatUsers() {
  // Reprend la logique de ChatUsersList.vue
  const response = await axiosInstance.get('/friends');
  const users = [];
  for (const user of response.data.friends) {
    const userInfos = await axiosInstance.get(`/user/${user.id}`);
    users.push(userInfos.data);
  }
  return users;
}

export async function fetchConversations() {
  // À adapter selon votre API
  // Exemple :
  // const response = await axiosInstance.get('/conversations');
  // return response.data;
  return [];
}
