/**
 * Service pour gérer les messages non lus
 */

// Store pour les messages non lus
let unreadMessagesCount = 0;
let unreadMessagesByUser = {};

/**
 * Ajoute un message non lu pour un utilisateur spécifique
 * @param {string|number} userId - ID de l'utilisateur qui a envoyé le message
 * @param {string} message - Contenu du message
 */
export function addUnreadMessage(userId, message) {
  // Incrémenter le compteur global
  unreadMessagesCount++;
  
  // Incrémenter le compteur pour cet utilisateur
  if (!unreadMessagesByUser[userId]) {
    unreadMessagesByUser[userId] = 1;
  } else {
    unreadMessagesByUser[userId]++;
  }
  
  // Émettre un événement pour notifier les composants
  const event = new CustomEvent('unread-messages-updated', {
    detail: {
      totalCount: unreadMessagesCount,
      byUser: unreadMessagesByUser
    }
  });
  
  window.dispatchEvent(event);
  
  return {
    totalCount: unreadMessagesCount,
    byUser: unreadMessagesByUser
  };
}

/**
 * Marque tous les messages d'un utilisateur comme lus
 * @param {string|number} userId - ID de l'utilisateur dont les messages doivent être marqués comme lus
 */
export function markUserMessagesAsRead(userId) {
  if (unreadMessagesByUser[userId]) {
    // Soustraire le nombre de messages non lus de cet utilisateur du total
    unreadMessagesCount -= unreadMessagesByUser[userId];
    
    // Réinitialiser le compteur pour cet utilisateur
    unreadMessagesByUser[userId] = 0;
    
    // Émettre un événement pour notifier les composants
    const event = new CustomEvent('unread-messages-updated', {
      detail: {
        totalCount: unreadMessagesCount,
        byUser: unreadMessagesByUser
      }
    });
    
    window.dispatchEvent(event);
  }
  
  return {
    totalCount: unreadMessagesCount,
    byUser: unreadMessagesByUser
  };
}

/**
 * Marque tous les messages comme lus
 */
export function markAllMessagesAsRead() {
  unreadMessagesCount = 0;
  unreadMessagesByUser = {};
  
  // Émettre un événement pour notifier les composants
  const event = new CustomEvent('unread-messages-updated', {
    detail: {
      totalCount: unreadMessagesCount,
      byUser: unreadMessagesByUser
    }
  });
  
  window.dispatchEvent(event);
  
  return {
    totalCount: unreadMessagesCount,
    byUser: unreadMessagesByUser
  };
}

/**
 * Récupère le nombre total de messages non lus
 * @returns {number} Nombre total de messages non lus
 */
export function getTotalUnreadCount() {
  return unreadMessagesCount;
}

/**
 * Récupère le nombre de messages non lus par utilisateur
 * @returns {Object} Objet avec les IDs des utilisateurs comme clés et le nombre de messages non lus comme valeurs
 */
export function getUnreadMessagesByUser() {
  return unreadMessagesByUser;
}
