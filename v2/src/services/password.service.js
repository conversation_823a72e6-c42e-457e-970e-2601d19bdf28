import { toaster } from "../utils/toast/toast";
import { axiosInstance } from "./axios";

/**
 * password reset request 
 * @param {Object} formData
 * @return {Promise}
 * @throws {Error}
 */
const passwordResetRequest = async (email) => {
  try {
    await axiosInstance.post("/user/password_reset_request/", { email: email });
    toaster.showSuccessPopup(`Un email de réinitialisation de mot de passe vous a été envoyé à ${email}.`);
    return true;
  } catch (error) {
    //console.log(error);
    return toaster.showErrorPopup("Une erreur est survenue. Veuillez vérifier votre email et réessayer.");
  }
};

const passwordResetRequestConfirm = async (password, uid, token) => {
  if (!uid || !token) {
    toaster.showErrorPopup("L'ID utilisateur ou le token sont manquants.");
    return false;
  }

  try {
    const response = await axiosInstance.post(`/user/password_reset_confirm/${uid}/${token}/`, {
      new_password: password
    });

    if (response.status === 200) {
      toaster.showSuccessPopup("Votre mot de passe a été réinitialisé avec succès.");
      return true;
    } else {
      toaster.showErrorPopup("Une erreur s'est produite lors de la réinitialisation de votre mot de passe. Veuillez réessayer.");
      return false;
    }
  } catch (error) {
    if (error.response && error.response.status === 400) {
      toaster.showErrorPopup("Le jeton ou l'ID utilisateur est invalide.");
    } else {
      toaster.showErrorPopup("Une erreur s'est produite lors de la réinitialisation de votre mot de passe. Veuillez réessayer.");
    }
    return false;
  }
};


/**
 * password reset request 
 * @param {Object} data
 * @return {Promise}
 * @throws {Error}
 */
const passworchangetRequest = async (data) => {
  try {
    await axiosInstance.post("/user/change-password/", data);
    return true;
  } catch (error) {
    //console.log(error);
    return toaster.showErrorPopup("Une erreur est survenue");
  }
};


/**
 * password reset request 
 * @param {Object} data
 * @return {Promise}
 * @throws {Error}
 */
const emailchangetRequest = async (data) => {
  try {
    await axiosInstance.post("/user/change-email/", {new_email:data.new_email});
    toaster.showSuccessPopup(`Email changé avec succés.`);
    return true;
  } catch (error) {
    //console.log(error);
    return toaster.showErrorPopup("Une erreur est survenue");
  }
};

export { passwordResetRequest, passworchangetRequest, emailchangetRequest, passwordResetRequestConfirm };
