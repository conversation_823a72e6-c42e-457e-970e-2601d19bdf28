import { axiosInstance } from './axios';

const getJobOffersById = async (id) => {
  try {
    const response = await axiosInstance.get(`/jobs_author/${id}/`);
    return response.data;
  } catch (error) {
    //console.error(
    //  `Erreur lors de la modification de l'offre d'emploi avec ID ${id} :`,
    //  error
    //);
    throw new Error("Échec de la modification de l'offre d'emploi");
  }
};

// Fonction pour modifier une offre d'emploi
const modifyJobOffers = async (id, updatedData) => {
  try {
    // Filtrer les champs autorisés
    const allowedFields = [
      'title',
      'publie',
      'descriptif',
      'contract',
      'local',
      'jours_teletravail',
      'expérience',
      'updated_at',
      'savoir_pro',
      'savoir_etre',
      'nom_recruteur',
      'company',
    ];
    const payload = Object.keys(updatedData)
      .filter((key) => allowedFields.includes(key))
      .reduce((obj, key) => {
        obj[key] = updatedData[key];
        return obj;
      }, {});

    // Envoi des données au serveur
    const response = await axiosInstance.put(`/jobs_author/${id}/`, payload, {
      headers: { 'Content-Type': 'application/json' },
    });

    return response.data;
  } catch (error) {
    //console.error(
    //  `Erreur lors de la modification de l'offre d'emploi avec ID ${id} :`,
    //  error
    //);
    throw new Error("Échec de la modification de l'offre d'emploi");
  }
};

const deleteJobOffers = async (id) => {
  try {
    const response = await axiosInstance.delete(`/jobs_author/${id}`);
    return response.data;
  } catch (error) {
    //console.error(
    //  `Erreur lors de la suppression de l'offre d'emploi avec ID ${id} :`,
    //  error
    //);
    throw new Error("Échec de la suppression de l'offre d'emploi");
  }
};

const archiveJobOffers = async (job_offer_id) => {
  try {
    const response = await axiosInstance.post(`/jobs/close/${job_offer_id}/`);
    return response.data;
  } catch (error) {
    // Vérifiez si une réponse a été reçue
    if (error.response) {
      //console.error(
      //  `Erreur d'API (Statut : ${error.response.status}) :`,
      //  error.response.data
      //);
    } else {
      //console.error("Erreur d'API :", error.message);
    }
    throw new Error("Échec de l'archivage de l'offre d'emploi");
  }
};

const unarchiveJobOffers = async (job_offer_id) => {
  try {
    const response = await axiosInstance.post(`/jobs/open/${job_offer_id}/`);
    return response.data;
  } catch (error) {
    // Vérifiez si une réponse a été reçue
    if (error.response) {
      //console.error(
      //  `Erreur d'API (Statut : ${error.response.status}) :`,
      //  error.response.data
      //);
    } else {
      //console.error("Erreur d'API :", error.message);
    }
    throw new Error("Échec de l'archivage de l'offre d'emploi");
  }
};

// Fonction pour modifier une offre d'emploi
const createJobOffers = async (formData) => {
  try {
    //console.log(
    //  "🔹 Données envoyées à l'API :",
    //  JSON.stringify(formData, null, 2)
    //);

    const response = await axiosInstance.post(`/jobs/`, formData);

    //console.log('🔹 Réponse API après création :', response.data);
    return response.data;
  } catch (error) {
    //console.error("❌ Erreur lors de la création de l'offre :", error);
    throw new Error("Échec de la création de l'offre d'emploi");
  }
};

export {
  getJobOffersById,
  modifyJobOffers,
  deleteJobOffers,
  archiveJobOffers,
  unarchiveJobOffers,
  createJobOffers,
};
