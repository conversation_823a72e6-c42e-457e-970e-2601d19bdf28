/**
 * Service de cache pour les appels API
 * Permet de mettre en cache les résultats des appels API pour éviter les appels redondants
 */

// Stockage du cache
const cache = new Map();

// Durée de vie par défaut du cache en millisecondes (5 minutes)
const DEFAULT_TTL = 5 * 60 * 1000;

/**
 * Récupère une valeur du cache
 * @param {string} key - Clé de cache
 * @returns {any|null} - Valeur du cache ou null si non trouvée ou expirée
 */
export const getCacheItem = (key) => {
  if (!cache.has(key)) {
    return null;
  }

  const cachedItem = cache.get(key);
  
  // Vérifier si l'élément a expiré
  if (cachedItem.expiry && cachedItem.expiry < Date.now()) {
    cache.delete(key);
    return null;
  }

  return cachedItem.value;
};

/**
 * Stocke une valeur dans le cache
 * @param {string} key - Clé de cache
 * @param {any} value - Valeur à mettre en cache
 * @param {number} ttl - Durée de vie en millisecondes (par défaut: 5 minutes)
 */
export const setCacheItem = (key, value, ttl = DEFAULT_TTL) => {
  const expiry = ttl > 0 ? Date.now() + ttl : null;
  
  cache.set(key, {
    value,
    expiry
  });
};

/**
 * Supprime une valeur du cache
 * @param {string} key - Clé de cache
 */
export const removeCacheItem = (key) => {
  cache.delete(key);
};

/**
 * Vide tout le cache
 */
export const clearCache = () => {
  cache.clear();
};

/**
 * Fonction utilitaire pour créer une clé de cache à partir d'une URL et de paramètres
 * @param {string} url - URL de l'API
 * @param {Object} params - Paramètres de la requête
 * @returns {string} - Clé de cache
 */
export const createCacheKey = (url, params = {}) => {
  return `${url}:${JSON.stringify(params)}`;
};

/**
 * Fonction utilitaire pour exécuter une fonction avec mise en cache
 * @param {Function} fn - Fonction à exécuter (généralement un appel API)
 * @param {string} cacheKey - Clé de cache
 * @param {number} ttl - Durée de vie en millisecondes
 * @returns {Promise<any>} - Résultat de la fonction ou valeur du cache
 */
export const withCache = async (fn, cacheKey, ttl = DEFAULT_TTL) => {
  // Vérifier si la valeur est dans le cache
  const cachedValue = getCacheItem(cacheKey);
  if (cachedValue !== null) {
    return cachedValue;
  }

  // Exécuter la fonction
  const result = await fn();
  
  // Mettre en cache le résultat
  setCacheItem(cacheKey, result, ttl);
  
  return result;
};

export default {
  get: getCacheItem,
  set: setCacheItem,
  remove: removeCacheItem,
  clear: clearCache,
  createKey: createCacheKey,
  withCache
};
