import axios from 'axios';
/**
 * getCityList 
 * @return {Promise}
 * @throws {Error}
 */
const getCityList = async (ville) => {
    try {
        // J'ai commenté la ligne ci-dessous car elle récupère toutes les communes sans filtrer par nom,
        // ce qui peut entraîner une surcharge de données inutile.
        // const response = await axios.get(`https://geo.api.gouv.fr/communes?fields=nom,codesPostaux&format=json`);
        const response = await axios.get(`https://geo.api.gouv.fr/communes?nom=${ville}&fields=nom,codesPostaux&format=json`);

        let rawList = response.data;
        let listNom = [];
        let listCode = [];
        for (let i = 0; i<rawList.length; i++) {
            ////console.log(rawList[i]['nom']);
            ////console.log(rawList[i]['codesPostaux']);
            listNom.push(rawList[i]['nom']);
            listCode.push(rawList[i]['codesPostaux']);
        }
        return [listNom,listCode];
        
    } catch (error) {
        throw new Error(error.response || 'fetching geo api list failed');
    }
};

export { getCityList };
