import router from '../router';
import store from '../store';
import { toaster } from '../utils/toast/toast';
import { initializePrivateWebSocket } from './conversation-websocket.service';

// État global des appels
let isCallActive = false;
let currentCallData = null;
let webSocketConnection = null;
let lastCallId = null; // Pour éviter les doublons d'appels

/**
 * Réinitialise l'état de l'appel
 * @param {boolean} resetLastCallId - Si true, réinitialise également lastCallId immédiatement
 */
const resetCallState = (resetLastCallId = false) => {
  //console.log('%c[APPEL] Réinitialisation de l\'\u00e9tat de l\'appel', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
  isCallActive = false;
  currentCallData = null;

  if (resetLastCallId) {
    // Réinitialiser lastCallId immédiatement si demandé
    //console.log('%c[APPEL] Réinitialisation immédiate de lastCallId', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
    lastCallId = null;
  } else {
    // Réinitialiser lastCallId après un délai pour éviter les doublons d'appels immédiats
    //console.log('%c[APPEL] Planification de la réinitialisation de lastCallId après délai', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
    setTimeout(() => {
      //console.log('%c[APPEL] Réinitialisation de lastCallId après délai', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
      lastCallId = null;
    }, 5000); // Délai de 5 secondes
  }
};

/**
 * Vérifie l'état du WebSocket global et le réinitialise si nécessaire
 * @returns {Promise<boolean>} - True si le WebSocket est opérationnel
 */
const checkAndResetGlobalWebSocket = async () => {
  try {
    ////console.log('%c[APPEL] Vérification de l\'\u00e9tat du WebSocket global...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

    // Récupérer la connexion WebSocket du store
    const webSocketUserConnection = store.state.userModule.webSocketUserConnection;

    // Vérifier si le WebSocket existe et est ouvert
    if (!webSocketUserConnection || webSocketUserConnection.readyState !== WebSocket.OPEN) {
      ////console.log('%c[APPEL] WebSocket global non disponible ou fermé, réinitialisation...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

      // Réinitialiser le WebSocket global
      store.dispatch('initializeUserConnectionWebSocket');

      // Attendre un court instant pour que le WebSocket se connecte
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Vérifier à nouveau l'état du WebSocket
      const reconnectedWebSocket = store.state.userModule.webSocketUserConnection;
      if (!reconnectedWebSocket || reconnectedWebSocket.readyState !== WebSocket.OPEN) {
        //console.error('%c[APPEL] Impossible de réinitialiser le WebSocket global', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
        return false;
      }

      ////console.log('%c[APPEL] WebSocket global réinitialisé avec succès', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
      return true;
    }

    ////console.log('%c[APPEL] WebSocket global opérationnel', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
    return true;
  } catch (error) {
    //console.error('%c[APPEL] Erreur lors de la vérification du WebSocket global:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
    return false;
  }
};

/**
 * Initialise l'écouteur d'appels global
 * Cette fonction est appelée au démarrage de l'application pour configurer
 * la réception des appels même lorsque l'utilisateur n'est pas sur la page de messagerie
 */
const initializeCallListener = async () => {
  try {
    //console.log('%c[APPEL] Initialisation de l\'\u00e9couteur d\'appels global...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

    const currentUser = store.getters.getUser;
    if (!currentUser || !currentUser.id) {
      //console.log('%c[APPEL] Utilisateur non connecté, impossible d\'initialiser l\'écouteur d\'appels', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
      return false;
    }

    //console.log('%c[APPEL] Utilisateur connecté:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', currentUser.id);

    // Importer dynamiquement le service de websocket global pour éviter les dépendances circulaires
    try {
      const { getGlobalPrivateWebSocket } = await import('./global-websocket.service');

      // Utiliser le WebSocket privé global déjà initialisé
      const privateWs = getGlobalPrivateWebSocket();
      if (privateWs) {
        //console.log('%c[APPEL] WebSocket privé global récupéré avec succès', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

        // Configurer l'écouteur sur le WebSocket privé global
        const success = setupWebSocketListener(privateWs, currentUser.id);
        //console.log('%c[APPEL] Configuration de l\'\u00e9couteur sur le WebSocket privé global:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', success ? 'Réussie' : 'Échouée');

        // Stocker la connexion WebSocket pour l'utiliser plus tard
        webSocketConnection = privateWs;

        return success;
      }
    } catch (error) {
      //console.error('%c[APPEL] Erreur lors de l\'importation du service de websocket global:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
    }

    // Fallback: utiliser le WebSocket de notification utilisateur
    //console.log('%c[APPEL] Fallback: utilisation du WebSocket de notification utilisateur...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

    // Récupérer la connexion WebSocket du store
    const webSocketUserConnection = store.state.userModule.webSocketUserConnection;
    //console.log('%c[APPEL] WebSocket de notification utilisateur:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', webSocketUserConnection);

    if (!webSocketUserConnection) {
      //console.log('%c[APPEL] WebSocket de notification utilisateur non initialisé, tentative de réinitialisation...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

      // Tenter de réinitialiser le WebSocket
      store.dispatch('initializeUserConnectionWebSocket');
      //console.log('%c[APPEL] Dispatch de initializeUserConnectionWebSocket effectué', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

      // Vérifier à nouveau après un court délai
      setTimeout(() => {
        const reconnectedWebSocket = store.state.userModule.webSocketUserConnection;
        //console.log('%c[APPEL] Après réinitialisation, WebSocket:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', reconnectedWebSocket);

        if (reconnectedWebSocket) {
          //console.log('%c[APPEL] WebSocket réinitialisé avec succès, configuration de l\'\u00e9couteur...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          const success = setupWebSocketListener(reconnectedWebSocket, currentUser.id);
          //console.log('%c[APPEL] Configuration de l\'\u00e9couteur:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', success ? 'Réussie' : 'Échouée');
        } else {
          //console.error('%c[APPEL] Impossible de réinitialiser le WebSocket', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
        }
      }, 1000);

      return false;
    }

    // Configurer l'écouteur sur le WebSocket existant
    const success = setupWebSocketListener(webSocketUserConnection, currentUser.id);
    //console.log('%c[APPEL] Configuration de l\'\u00e9couteur sur le WebSocket existant:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', success ? 'Réussie' : 'Échouée');
    return success;

  } catch (error) {
    //console.error('%c[APPEL] Erreur lors de l\'initialisation de l\'écouteur d\'appels:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
    return false;
  }
};

/**
 * Configure l'écouteur d'appels sur un WebSocket donné
 * @param {WebSocket} webSocket - La connexion WebSocket à utiliser
 * @param {string} userId - L'ID de l'utilisateur connecté
 * @returns {boolean} - True si l'écouteur a été configuré avec succès
 */
const setupWebSocketListener = (webSocket, userId) => {
  try {
    if (!webSocket) {
      //console.error('%c[APPEL] WebSocket non défini', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
      return false;
    }

    //console.log('%c[APPEL] État du WebSocket:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', {
    //  readyState: webSocket.readyState,
    //  readyStateText: ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'][webSocket.readyState] || 'UNKNOWN'
    //});

    // Si le WebSocket n'est pas encore ouvert, configurer un gestionnaire pour l'ouverture
    if (webSocket.readyState !== WebSocket.OPEN) {
      ////console.log('%c[APPEL] WebSocket pas encore ouvert, configuration du gestionnaire d\'ouverture', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

      // Sauvegarder l'ancien gestionnaire onopen s'il existe
      const oldOnOpen = webSocket.onopen;
      ////console.log('%c[APPEL] Ancien gestionnaire onopen:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', oldOnOpen ? 'Existe' : 'N\'existe pas');

      webSocket.onopen = (event) => {
        ////console.log('%c[APPEL] WebSocket global ouvert, configuration de l\'\u00e9couteur d\'appels', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

        // Configurer le gestionnaire de messages une fois le WebSocket ouvert
        const success = setupMessageHandler(webSocket, userId);
        ////console.log('%c[APPEL] Configuration du gestionnaire de messages:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', success ? 'Réussie' : 'Échouée');

        // Appeler l'ancien gestionnaire s'il existe
        if (oldOnOpen && typeof oldOnOpen === 'function') {
          ////console.log('%c[APPEL] Appel de l\'ancien gestionnaire onopen', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          oldOnOpen(event);
        }
      };

      ////console.log('%c[APPEL] Gestionnaire onopen configuré avec succès', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
      return true; // L'écouteur sera configuré une fois le WebSocket ouvert
    }

    // Si le WebSocket est déjà ouvert, configurer le gestionnaire de messages immédiatement
    ////console.log('%c[APPEL] WebSocket déjà ouvert, configuration immédiate du gestionnaire de messages', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
    return setupMessageHandler(webSocket, userId);
  } catch (error) {
    //console.error('%c[APPEL] Erreur lors de la configuration de l\'\u00e9couteur WebSocket:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
    return false;
  }
};

/**
 * Configure le gestionnaire de messages pour un WebSocket donné
 * @param {WebSocket} webSocket - La connexion WebSocket à utiliser
 * @param {string} userId - L'ID de l'utilisateur connecté
 * @returns {boolean} - True si le gestionnaire a été configuré avec succès
 */
const setupMessageHandler = (webSocket, userId) => {
  try {
    // Sauvegarder l'ancien gestionnaire onmessage s'il existe
    const oldOnMessage = webSocket.onmessage;
    //console.log('%c[APPEL] Ancien gestionnaire onmessage:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', oldOnMessage ? 'Existe' : 'N\'existe pas');

    // Configurer le nouveau gestionnaire d'événements
    webSocket.onmessage = async (event) => {
      try {
        // Analyser les données reçues
        let data;
        try {
          data = JSON.parse(event.data);
        } catch (jsonError) {
          //console.error('%c[APPEL] Erreur lors du parsing des données WebSocket:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', jsonError);
          //console.log('%c[APPEL] Données non-JSON reçues:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', event.data);
          // Continuer avec l'ancien gestionnaire pour les messages non-JSON
          if (oldOnMessage && typeof oldOnMessage === 'function') {
            oldOnMessage(event);
          }
          return;
        }

        // Vérifier si le message est destiné à cet utilisateur
        const recipientId = data.recipient_id || data.receveir_id;
        if (recipientId && recipientId !== userId && data.type !== 'system_message') {
          // Message destiné à un autre utilisateur, ignorer
          if (oldOnMessage && typeof oldOnMessage === 'function') {
            oldOnMessage(event);
          }
          return;
        }

        //console.log('%c[APPEL] Message WebSocket reçu:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', data);

        // Vérifier si le message est une initialisation d'appel
        if (data.type === 'init_call_connection') {
          //console.log('%c[APPEL] Initialisation d\'appel reçue:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', data);

          // Stocker la connexion WebSocket pour l'utiliser lors de l'appel
          webSocketConnection = webSocket;

          // Pas d'autre action nécessaire, juste s'assurer que le WebSocket est prêt
          if (oldOnMessage && typeof oldOnMessage === 'function') {
            oldOnMessage(event);
          }
          return;
        }

        // Vérifier si le message contient une offre d'appel (plusieurs formats possibles)
        const isOfferMessage =
          data.type === 'offer' ||
          data.message_type === 'offer' ||
          (data.message && data.message.type === 'offer') ||
          (data.data && data.data.type === 'offer');

        // Extraire les données d'offre selon le format
        let offerData = data;
        if (data.message && data.message.type === 'offer') {
          offerData = data.message;
        } else if (data.data && data.data.type === 'offer') {
          offerData = data.data;
        }

        // Extraire l'ID de l'expéditeur selon le format
        const senderId = offerData.sender_id || offerData.senderId ||
                        (offerData.sender && offerData.sender.id) ||
                        data.sender_id || data.senderId;

        // Traiter les messages d'appel
        if (isOfferMessage) {
          //console.log('%c[APPEL] Offre d\'appel détectée dans le message WebSocket:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', offerData);

          // Vérifier que l'offre n'est pas destinée à nous-mêmes
          if (senderId === userId) {
            //console.log('%c[APPEL] Ignorer l\'offre provenant de nous-mêmes', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          } else {
            //console.log('%c[APPEL] Traitement de l\'appel entrant de l\'utilisateur ' + senderId, 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

            // Extraire l'offre SDP selon le format
            const offer = offerData.offer || offerData.sdp || offerData;

            // Extraire le type d'appel selon le format
            const callType = offerData.call_type || offerData.callType || 'video';

            // Stocker la connexion WebSocket pour l'utiliser lors de l'appel
            webSocketConnection = webSocket;

            // Traiter l'appel entrant
            const success = await handleIncomingCall({
              senderId: senderId,
              call_type: callType,
              offer: offer
            });
            //console.log('%c[APPEL] Traitement de l\'appel entrant:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', success ? 'Réussi' : 'Échoué');
          }
        }
        // Traiter les messages d'acceptation d'appel
        else if (data.type === 'accept_call' || data.message_type === 'accept_call') {
          //console.log('%c[APPEL] Acceptation d\'appel reçue via le WebSocket global:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', data);

          // Déclencher un événement global pour indiquer que l'appel a été accepté
          const callAcceptedEvent = new CustomEvent('call-accepted', {
            detail: { senderId: data.sender_id || data.senderId }
          });
          window.dispatchEvent(callAcceptedEvent);
          //console.log('%c[APPEL] Événement call-accepted déclenché', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
        }
        // Traiter les messages de rejet d'appel
        else if (data.type === 'reject_call' || data.message_type === 'reject_call') {
          //console.log('%c[APPEL] Rejet d\'appel reçu via le WebSocket global:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', data);

          // Déclencher un événement global pour masquer la notification d'appel entrant
          const callRejectedEvent = new CustomEvent('call-rejected');
          window.dispatchEvent(callRejectedEvent);
          //console.log('%c[APPEL] Événement call-rejected déclenché', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

          // Réinitialiser l'état de l'appel
          currentCallData = null;
          isCallActive = false;
        }
        // Traiter les messages de fin d'appel
        else if (data.type === 'end_call' || data.message_type === 'end_call') {
          //console.log('%c[APPEL] Fin d\'appel reçue via le WebSocket global:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', data);

          // Réinitialiser l'état de l'appel
          currentCallData = null;
          isCallActive = false;

          // Déclencher un événement global pour masquer la notification d'appel entrant
          const callEndedEvent = new CustomEvent('call-ended');
          window.dispatchEvent(callEndedEvent);
          //console.log('%c[APPEL] Événement call-ended déclenché', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
        }
        // Traiter les candidats ICE
        else if (data.type === 'icecandidate' || data.type === 'ice_candidate' || data.message_type === 'icecandidate' || data.message_type === 'ice_candidate' || data.message_type === 'webrtc_ice_candidate') {
          //console.log('%c[APPEL] Candidat ICE reçu via le WebSocket global:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', data);

          // Déclencher un événement global pour traiter le candidat ICE
          const iceCandidateEvent = new CustomEvent('ice-candidate', {
            detail: {
              candidate: data.candidate,
              senderId: data.sender_id || data.senderId
            }
          });
          window.dispatchEvent(iceCandidateEvent);
          //console.log('%c[APPEL] Événement ice-candidate déclenché', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
        }

        // Appeler l'ancien gestionnaire s'il existe
        if (oldOnMessage && typeof oldOnMessage === 'function') {
          //console.log('%c[APPEL] Appel de l\'ancien gestionnaire onmessage', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          oldOnMessage(event);
        }
      } catch (err) {
        //console.error('%c[APPEL] Erreur lors du traitement du message WebSocket global:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', err);
      }
    };

    //console.log('%c[APPEL] Écouteur d\'appels vidéo initialisé pour l\'utilisateur ' + userId, 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
    return true;
  } catch (error) {
    //console.error('%c[APPEL] Erreur lors de la configuration du gestionnaire de messages:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
    return false;
  }
};

/**
 * Initialise un appel vidéo avec un utilisateur
 * @param {Object} recipient - L'utilisateur à appeler
 * @returns {Promise<boolean>} - True si l'appel a été initié avec succès
 */
const initiateVideoCall = async (recipient) => {
  try {
    if (isCallActive) {
      toaster.showErrorPopup('Un appel est déjà en cours');
      return false;
    }

    const currentUser = store.getters.getUser;
    if (!currentUser || !currentUser.id) {
      toaster.showErrorPopup('Vous devez être connecté pour passer un appel');
      return false;
    }

    if (!recipient || !recipient.id) {
      toaster.showErrorPopup('Destinataire invalide');
      return false;
    }

    // Utiliser le WebSocket privé global pour l'appel
    try {
      const { getGlobalPrivateWebSocket } = await import('./global-websocket.service');

      // Utiliser le WebSocket privé global déjà initialisé
      const webSocket = getGlobalPrivateWebSocket();
      if (webSocket) {
        //console.log('%c[APPEL] WebSocket privé global récupéré avec succès pour l\'appel', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
        webSocketConnection = webSocket;
      } else {
        //console.error('%c[APPEL] WebSocket privé global non disponible, fallback vers un nouveau WebSocket privé', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');

        // Fallback: initialiser un nouveau WebSocket privé
        const privateWs = initializePrivateWebSocket();
        if (!privateWs) {
          toaster.showErrorPopup("Impossible d'établir la connexion");
          return false;
        }
        webSocketConnection = privateWs;
      }
    } catch (error) {
      //console.error('%c[APPEL] Erreur lors de l\'importation du service de websocket global:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);

      // Fallback: initialiser un nouveau WebSocket privé
      const privateWs = initializePrivateWebSocket();
      if (!privateWs) {
        toaster.showErrorPopup("Impossible d'établir la connexion");
        return false;
      }
      webSocketConnection = privateWs;
    }

    // S'assurer que le WebSocket est ouvert avant de continuer
    if (webSocketConnection.readyState !== WebSocket.OPEN) {
      //console.log('Attente de l\'ouverture du WebSocket pour l\'appel...');
      await new Promise((resolve) => {
        const checkInterval = setInterval(() => {
          if (webSocketConnection.readyState === WebSocket.OPEN) {
            clearInterval(checkInterval);
            resolve();
          }
        }, 100);
        // Timeout de sécurité après 5 secondes
        setTimeout(() => {
          clearInterval(checkInterval);
          resolve();
        }, 5000);
      });
    }

    // Envoyer un message d'initialisation d'appel au destinataire via le WebSocket privé
    // Cela permet d'activer le WebSocket côté destinataire même s'il n'a pas cliqué sur la conversation
    if (webSocketConnection.readyState === WebSocket.OPEN) {
      const initCallMessage = {
        type: 'init_call_connection',
        sender_id: store.getters.getUser.id,
        receiver_id: recipient.id
      };
      webSocketConnection.send(JSON.stringify(initCallMessage));
      //console.log('Message d\'initialisation d\'appel envoyé:', initCallMessage);
    }

    // Rediriger vers la page de messagerie avec les paramètres d'appel
    router.push({
      path: '/messagerie',
      query: { chat: recipient.id, call: true },
    });

    return true;
  } catch (error) {
    //console.error("Erreur lors de l'initiation de l'appel vidéo:", error);
    toaster.showErrorPopup(
      "Une erreur est survenue lors de l'initiation de l'appel"
    );
    return false;
  }
};

/**
 * Gère un appel entrant
 * @param {Object} callData - Les données de l'appel entrant
 * @returns {Promise<boolean>} - True si l'appel a été traité avec succès
 */
const handleIncomingCall = async (callData) => {
  try {
    //console.log('%c[APPEL] handleIncomingCall appelé avec les données:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', callData);

    if (isCallActive) {
      //console.log('%c[APPEL] Un appel est déjà en cours, rejet automatique', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
      return false;
    }

    // Vérifier que les données d'appel sont valides
    if (!callData || !callData.senderId) {
      //console.error('%c[APPEL] Données d\'appel invalides:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', callData);
      return false;
    }

    // Vérifier si c'est une offre préliminaire (envoyée depuis ChatUsersList)
    const isPreliminaryOffer = callData.offer &&
                              (callData.offer.type === 'pre_offer' ||
                               callData.offer.sdp === 'pending');

    if (isPreliminaryOffer) {
      //console.log('%c[APPEL] Offre préliminaire détectée, attente de l\'offre réelle...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
      // Ne pas afficher la notification pour une offre préliminaire
      // mais stocker l'ID de l'appelant pour éviter les doublons
      lastCallId = `${callData.senderId}_${Date.now()}`;
      return true;
    }

    // Générer un ID unique pour cet appel
    const callId = `${callData.senderId}_${Date.now()}`;

    // Vérifier si c'est un doublon d'appel (même appelant dans un court laps de temps)
    if (lastCallId && lastCallId.startsWith(`${callData.senderId}_`) &&
        Date.now() - parseInt(lastCallId.split('_')[1]) < 5000) {
      //console.log('%c[APPEL] Doublon d\'appel détecté, ignoré:', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;', {
      //  lastCallId,
      //  newCallId: callId,
      //  timeDiff: Date.now() - parseInt(lastCallId.split('_')[1])
      //});
      return true; // Retourner true pour indiquer que l'appel a été "traité" (ignoré)
    }

    // Stocker l'ID de cet appel
    lastCallId = callId;

    // Stocker les données de l'appel pour pouvoir y accéder lors de l'acceptation/rejet
    currentCallData = callData;
    //console.log('%c[APPEL] Données d\'appel stockées:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', currentCallData);

    // Initialiser le WebSocket privé pour l'appel si nécessaire
    if (!webSocketConnection || webSocketConnection.readyState !== WebSocket.OPEN) {
      //console.log('%c[APPEL] Initialisation du WebSocket privé pour l\'appel...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

      // Vérifier et réinitialiser le WebSocket global
      await checkAndResetGlobalWebSocket();

      // Initialiser un nouveau WebSocket privé pour l'appel
      const webSocket = initializePrivateWebSocket();
      if (webSocket) {
        webSocketConnection = webSocket;
        //console.log('%c[APPEL] WebSocket privé initialisé avec succès', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

        // Attendre que le WebSocket soit ouvert
        if (webSocket.readyState !== WebSocket.OPEN) {
          //console.log('%c[APPEL] Attente de l\'ouverture du WebSocket...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          await new Promise((resolve) => {
            const checkInterval = setInterval(() => {
              if (webSocket.readyState === WebSocket.OPEN) {
                clearInterval(checkInterval);
                resolve();
              }
            }, 100);
            // Timeout de sécurité après 5 secondes
            setTimeout(() => {
              clearInterval(checkInterval);
              resolve();
            }, 5000);
          });
        }
      }
    }

    // Si nous avons besoin d'informations supplémentaires sur l'appelant
    // comme son nom, nous pouvons les récupérer ici
    let senderName = '';
    if (callData.senderId) {
      try {
        //console.log('%c[APPEL] Récupération des informations de l\'appelant avec ID:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', callData.senderId);
        // Importer dynamiquement le service pour éviter les dépendances circulaires
        const { getUserById } = await import('../services/account.service');
        const caller = await getUserById(callData.senderId);
        if (caller) {
          senderName = `${caller.first_name} ${caller.last_name}`;
          //console.log('%c[APPEL] Informations de l\'appelant récupérées:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', senderName);
        } else {
          console.warn('%c[APPEL] Appelant non trouvé avec l\'ID:', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;', callData.senderId);
          senderName = 'Utilisateur inconnu';
        }
      } catch (err) {
        //console.error('%c[APPEL] Erreur lors de la récupération des informations de l\'appelant:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', err);
        senderName = 'Utilisateur inconnu';
      }
    }

    // Enrichir les données de l'appel avec le nom de l'appelant
    const enrichedCallData = {
      ...callData,
      senderName
    };

    //console.log('%c[APPEL] Déclenchement de l\'événement incoming-call avec les données:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', enrichedCallData);

    // Déclencher un événement global pour afficher la notification d'appel entrant
    const incomingCallEvent = new CustomEvent('incoming-call', {
      detail: enrichedCallData,
    });

    // Déclencher l'événement immédiatement
    window.dispatchEvent(incomingCallEvent);
    //console.log('%c[APPEL] Événement incoming-call déclenché', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

    // Déclencher à nouveau l'événement après un court délai pour s'assurer qu'il est bien capturé
    setTimeout(() => {
      //console.log('%c[APPEL] Redéclenchement de l\'événement incoming-call après délai', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
      window.dispatchEvent(new CustomEvent('incoming-call', { detail: enrichedCallData }));
    }, 500);

    // Déclencher une alerte dans la console pour attirer l'attention
    //console.log('%c[APPEL ENTRANT] Vous avez un appel entrant de ' + senderName + ' !', 'background: #f6b337; color: black; font-size: 16px; padding: 5px 10px; border-radius: 3px; font-weight: bold;');

    return true; // Indiquer que l'appel a été traité avec succès
  } catch (error) {
    //console.error('%c[APPEL] Erreur lors de la gestion de l\'appel entrant:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
    return false;
  }
};

/**
 * Accepte un appel entrant
 * @returns {Promise<void>}
 */
const acceptCall = async () => {
  try {
    if (!currentCallData) {
      toaster.showErrorPopup('Aucun appel à accepter');
      return;
    }

    //console.log('%c[APPEL] Acceptation de l\'appel avec les données:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', currentCallData);

    // Marquer l'appel comme actif
    isCallActive = true;

    // S'assurer que l'offre est bien un objet
    let offer = currentCallData.offer;
    if (!offer) {
      //console.error('%c[APPEL] Aucune offre disponible dans les données d\'appel', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
      toaster.showErrorPopup("Impossible d'accepter l'appel: données manquantes");
      return;
    }

    // Stocker l'offre dans sessionStorage au lieu de la passer dans l'URL
    try {
      sessionStorage.setItem('pendingOffer', JSON.stringify(offer));
      //console.log('%c[APPEL] Offre stockée dans sessionStorage', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
    } catch (storageError) {
      //console.error('%c[APPEL] Erreur lors du stockage de l\'offre dans sessionStorage:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', storageError);
      toaster.showErrorPopup("Erreur lors du traitement des données d'appel");
      return;
    }

    // Vérifier et réinitialiser le WebSocket global si nécessaire
    await checkAndResetGlobalWebSocket();

    // Initialiser le WebSocket de conversation si nécessaire
    // Cela garantit que nous avons une connexion WebSocket pour l'appel même si
    // l'utilisateur n'a pas sélectionné l'appelant avant
    try {
      if (!webSocketConnection || webSocketConnection.readyState !== WebSocket.OPEN) {
        //console.log('%c[APPEL] Initialisation du WebSocket global pour l\'appel...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
        const webSocket = initializePrivateWebSocket();
        if (webSocket) {
          webSocketConnection = webSocket;
          //console.log('%c[APPEL] WebSocket global initialisé avec succès', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

          // Attendre que le WebSocket soit ouvert
          if (webSocket.readyState !== WebSocket.OPEN) {
            //console.log('%c[APPEL] Attente de l\'ouverture du WebSocket...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
            await new Promise((resolve) => {
              const checkInterval = setInterval(() => {
                if (webSocket.readyState === WebSocket.OPEN) {
                  clearInterval(checkInterval);
                  resolve();
                }
              }, 100);
              // Timeout de sécurité après 5 secondes
              setTimeout(() => {
                clearInterval(checkInterval);
                resolve();
              }, 5000);
            });
          }

          // Envoyer un message d'acceptation d'appel via le WebSocket global
          if (webSocket.readyState === WebSocket.OPEN) {
            const acceptMessage = {
              type: 'accept_call',
              sender_id: store.getters.getUser.id,
              receiver_id: currentCallData.senderId
            };
            webSocket.send(JSON.stringify(acceptMessage));
            //console.log('%c[APPEL] Message d\'acceptation d\'appel envoyé:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', acceptMessage);
          }
        } else {
          //console.error('%c[APPEL] Impossible d\'initialiser le WebSocket global', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
        }
      } else {
        // Si le WebSocket est déjà ouvert, envoyer directement le message d'acceptation
        const acceptMessage = {
          type: 'accept_call',
          sender_id: store.getters.getUser.id,
          receiver_id: currentCallData.senderId
        };
        webSocketConnection.send(JSON.stringify(acceptMessage));
        //console.log('%c[APPEL] Message d\'acceptation d\'appel envoyé via WebSocket existant:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', acceptMessage);
      }
    } catch (wsError) {
      //console.error('%c[APPEL] Erreur lors de l\'initialisation du WebSocket:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', wsError);
      // Continuer malgré l'erreur, car la page de messagerie tentera également d'initialiser le WebSocket
    }

    // Réinitialiser lastCallId après un court délai pour permettre la réception de nouveaux appels
    // au cas où l'appel actuel se terminerait rapidement
    //setTimeout(() => {
    //  //console.log('%c[APPEL] Réinitialisation de lastCallId pour permettre de nouveaux appels', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
    //  lastCallId = null;
    //}, 5000); // Délai plus long pour l'acceptation car l'appel est en cours

    // Arrêter la sonnerie d'appel
    //console.log('%c[APPEL] Tentative d\'arrêt de la sonnerie depuis acceptCall...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

    // Utiliser la fonction globale pour arrêter la sonnerie si elle existe
    if (window.stopCallRingtone && typeof window.stopCallRingtone === 'function') {
      //console.log('%c[APPEL] Appel de la fonction globale stopCallRingtone', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
      window.stopCallRingtone();
    } else {
      // Sinon, émettre un événement pour arrêter la sonnerie
      //console.log('%c[APPEL] Émission de l\'événement stop-ringtone', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
      window.dispatchEvent(new CustomEvent('stop-ringtone'));
    }

    // Déclencher un événement global pour masquer la notification d'appel entrant
    const callAcceptedEvent = new CustomEvent('call-accepted');
    window.dispatchEvent(callAcceptedEvent);
    //console.log('%c[APPEL] Événement call-accepted déclenché', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

    // Rediriger vers la page de messagerie avec les paramètres d'appel
    // Le paramètre accept=true indique qu'il s'agit d'un appel à accepter, pas à initier
    //console.log('%c[APPEL] Redirection vers la page de messagerie pour accepter l\'appel', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
    router.push({
      path: '/messagerie',
      query: {
        chat: currentCallData.senderId,
        call: true,
        accept: true,
        call_type: currentCallData.call_type || 'video'
      },
    });
  } catch (error) {
    //console.error('%c[APPEL] Erreur lors de l\'acceptation de l\'appel:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
    toaster.showErrorPopup(
      "Une erreur est survenue lors de l'acceptation de l'appel"
    );
  }
};

/**
 * Rejette un appel entrant
 * @returns {Promise<void>}
 */
const rejectCall = async () => {
  try {
    if (!currentCallData) {
      //console.log('%c[APPEL] Aucun appel à rejeter', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
      return;
    }

    //console.log('%c[APPEL] Rejet de l\'appel de l\'utilisateur ' + currentCallData.senderId, 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

    // Vérifier et réinitialiser le WebSocket global si nécessaire
    if (!webSocketConnection || webSocketConnection.readyState !== WebSocket.OPEN) {
      //console.log('%c[APPEL] WebSocket non disponible, initialisation...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
      await checkAndResetGlobalWebSocket();

      // Initialiser un nouveau WebSocket privé pour l'appel
      const webSocket = initializePrivateWebSocket();
      if (webSocket) {
        webSocketConnection = webSocket;
        //console.log('%c[APPEL] WebSocket global initialisé avec succès', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

        // Attendre que le WebSocket soit ouvert
        if (webSocket.readyState !== WebSocket.OPEN) {
          //console.log('%c[APPEL] Attente de l\'ouverture du WebSocket...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
          await new Promise((resolve) => {
            const checkInterval = setInterval(() => {
              if (webSocket.readyState === WebSocket.OPEN) {
                clearInterval(checkInterval);
                resolve();
              }
            }, 100);
            // Timeout de sécurité après 5 secondes
            setTimeout(() => {
              clearInterval(checkInterval);
              resolve();
            }, 5000);
          });
        }
      }
    }

    // Envoyer un message de rejet d'appel
    if (webSocketConnection && webSocketConnection.readyState === WebSocket.OPEN) {
      const rejectMessage = {
        type: 'reject_call',
        sender_id: store.getters.getUser.id,
        receiver_id: currentCallData.senderId
      };
      webSocketConnection.send(JSON.stringify(rejectMessage));
      //console.log('%c[APPEL] Message de rejet d\'appel envoyé:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', rejectMessage);
    } else {
      //console.error('%c[APPEL] Impossible d\'envoyer le message de rejet: WebSocket non disponible', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
    }

    // Réinitialiser l'état de l'appel
    currentCallData = null;
    isCallActive = false;

    // Réinitialiser lastCallId après un court délai pour permettre la réception de nouveaux appels
    setTimeout(() => {
      //console.log('%c[APPEL] Réinitialisation de lastCallId pour permettre de nouveaux appels', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
      lastCallId = null;
    }, 1000);

    // Arrêter la sonnerie d'appel
    //console.log('%c[APPEL] Tentative d\'arrêt de la sonnerie depuis rejectCall...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

    // Utiliser la fonction globale pour arrêter la sonnerie si elle existe
    if (window.stopCallRingtone && typeof window.stopCallRingtone === 'function') {
      //console.log('%c[APPEL] Appel de la fonction globale stopCallRingtone', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
      window.stopCallRingtone();
    } else {
      // Sinon, émettre un événement pour arrêter la sonnerie
      //console.log('%c[APPEL] Émission de l\'événement stop-ringtone', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
      window.dispatchEvent(new CustomEvent('stop-ringtone'));
    }

    // Déclencher un événement global pour masquer la notification d'appel entrant
    const callRejectedEvent = new CustomEvent('call-rejected');
    window.dispatchEvent(callRejectedEvent);
    //console.log('%c[APPEL] Événement call-rejected déclenché', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
  } catch (error) {
    //console.error('%c[APPEL] Erreur lors du rejet de l\'appel:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
  }
};

/**
 * Force la réinitialisation complète de l'état des appels
 * Utile pour résoudre les problèmes où l'on ne peut plus recevoir d'appel
 */
const forceResetCallState = () => {
  //console.log('%c[APPEL] Réinitialisation forcée de l\'état des appels', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
  isCallActive = false;
  currentCallData = null;
  lastCallId = null;

  // Déclencher un événement global pour indiquer la réinitialisation
  const callResetEvent = new CustomEvent('call-reset');
  window.dispatchEvent(callResetEvent);
  //console.log('%c[APPEL] Événement call-reset déclenché', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

  return true;
};

/**
 * Termine l'appel en cours
 * @returns {Promise<void>}
 */
const endCall = async () => {
  try {
    if (!isCallActive) {
      //console.log('%c[APPEL] Aucun appel actif à terminer', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
      return;
    }

    //console.log('%c[APPEL] Fin de l\'appel en cours', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

    // Récupérer l'ID du destinataire depuis currentCallData si disponible
    const recipientId = currentCallData ? currentCallData.senderId : null;
    if (!recipientId) {
      console.warn('%c[APPEL] ID du destinataire non disponible pour la fin d\'appel', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');
    }

    // Vérifier et réinitialiser le WebSocket global si nécessaire
    if (!webSocketConnection || webSocketConnection.readyState !== WebSocket.OPEN) {
      //console.log('%c[APPEL] WebSocket non disponible, initialisation...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

      if (recipientId) {
        await checkAndResetGlobalWebSocket();

        // Initialiser un nouveau WebSocket privé pour l'appel
        const webSocket = initializePrivateWebSocket();
        if (webSocket) {
          webSocketConnection = webSocket;
          //console.log('%c[APPEL] WebSocket global initialisé avec succès', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

          // Attendre que le WebSocket soit ouvert
          if (webSocket.readyState !== WebSocket.OPEN) {
            //console.log('%c[APPEL] Attente de l\'ouverture du WebSocket...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
            await new Promise((resolve) => {
              const checkInterval = setInterval(() => {
                if (webSocket.readyState === WebSocket.OPEN) {
                  clearInterval(checkInterval);
                  resolve();
                }
              }, 100);
              // Timeout de sécurité après 5 secondes
              setTimeout(() => {
                clearInterval(checkInterval);
                resolve();
              }, 5000);
            });
          }
        }
      }
    }

    // Envoyer un message de fin d'appel
    if (webSocketConnection && webSocketConnection.readyState === WebSocket.OPEN && recipientId) {
      const endCallMessage = {
        type: 'end_call',
        sender_id: store.getters.getUser.id,
        receiver_id: recipientId
      };
      webSocketConnection.send(JSON.stringify(endCallMessage));
      //console.log('%c[APPEL] Message de fin d\'appel envoyé:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', endCallMessage);
    } else {
      console.warn('%c[APPEL] Impossible d\'envoyer le message de fin d\'appel: WebSocket non disponible ou destinataire inconnu', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');
    }

    // Fermer la connexion WebSocket
    if (webSocketConnection) {
      // Ne pas fermer le WebSocket global, car il peut être utilisé pour d'autres fonctionnalités
      // Nous allons simplement le réinitialiser
      webSocketConnection = null;
    }

    // Réinitialiser l'état de l'appel
    isCallActive = false;
    currentCallData = null;

    // Réinitialiser lastCallId après un court délai pour permettre la réception de nouveaux appels
    setTimeout(() => {
      //console.log('%c[APPEL] Réinitialisation de lastCallId pour permettre de nouveaux appels', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
      lastCallId = null;

      // Forcer une réinitialisation complète après un délai supplémentaire
      setTimeout(() => {
        forceResetCallState();
      }, 1000);
    }, 1000);

    // Déclencher un événement global pour indiquer la fin de l'appel
    const callEndedEvent = new CustomEvent('call-ended');
    window.dispatchEvent(callEndedEvent);
    //console.log('%c[APPEL] Événement call-ended déclenché', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
  } catch (error) {
    //console.error('%c[APPEL] Erreur lors de la fin de l\'appel:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
  }
};

/**
 * Fonction de test pour simuler un appel entrant (utilisée pour le débogage)
 * @param {string} senderId - ID de l'appelant
 * @param {string} senderName - Nom de l'appelant
 * @returns {Promise<boolean>} - True si la simulation a réussi
 */
const testIncomingCall = async (senderId = '1', senderName = 'Utilisateur Test') => {
  try {
    ////console.log('%c[APPEL] Simulation d\'un appel entrant pour le débogage', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');

    // Réinitialiser l'état de l'appel avant de simuler un nouvel appel
    resetCallState(true); // Réinitialiser immédiatement lastCallId

    // Vérifier et réinitialiser le WebSocket global si nécessaire
    const webSocketOk = await checkAndResetGlobalWebSocket();
    if (!webSocketOk) {
      console.warn('%c[APPEL] WebSocket global non disponible, la simulation pourrait ne pas fonctionner correctement', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');
    }

    // Créer des données d'appel factices
    const fakeCallData = {
      senderId,
      senderName,
      call_type: 'video',
      offer: { type: 'offer', sdp: 'test_sdp_string' }
    };

    ////console.log('%c[APPEL] Données d\'appel factices créées:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', fakeCallData);
    ////console.log('%c[APPEL] État de l\'appel avant simulation:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', { isCallActive, hasCurrentCallData: !!currentCallData });

    // Simuler un appel entrant
    const result = await handleIncomingCall(fakeCallData);
    ////console.log('%c[APPEL] Résultat de la simulation d\'appel:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', result ? 'Réussi' : 'Échoué');

    return result;
  } catch (error) {
    //console.error('%c[APPEL] Erreur lors de la simulation d\'appel entrant:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
    return false;
  }
};

// Export unique à la fin du fichier
export {
  initiateVideoCall,
  handleIncomingCall,
  acceptCall,
  rejectCall,
  endCall,
  initializeCallListener,
  resetCallState,
  forceResetCallState,
  checkAndResetGlobalWebSocket,
  testIncomingCall // Export de la fonction de test
};
