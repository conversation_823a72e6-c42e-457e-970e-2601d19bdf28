// use-simple-peer.service.js
import Peer from 'simple-peer';

let peer = null;

export function createPeer({ initiator, stream, trickle = false, config = {} }) {
  peer = new Peer({
    initiator,
    stream,
    trickle, // Si false, on attend d’avoir tous les candidats ICE pour envoyer le signal complet
    config,
  });

  return peer;
}

export function signalPeer(data) {
  if (peer) peer.signal(data);
}

export function getPeer() {
  return peer;
}

export function destroyPeer() {
  if (peer) {
    peer.destroy();
    peer = null;
  }
}
