import axios from 'axios';
import store from '../store';
import { logout } from './account.service';
import cacheService from './cache.service';

export const baseUrl = process.env.VUE_APP_API_BASE_URL || "https://paris.thanks-boss.com";
// Assurez-vous que l'URL de base se termine par un slash
const normalizedBaseUrl = baseUrl.endsWith('/') ? baseUrl : `${baseUrl}/`;
export const apiBaseUrl = `${normalizedBaseUrl}api`;
export const AIbaseUrl = process.env.VUE_APP_AI_API_URL;
export const AIbaseUrlOptimizer = process.env.VUE_APP_AI_API_URL_OPTIMIZER;
export let accessToken = localStorage.getItem('access_token');
/**
 * Save access token in localStorage
 * @param {*} Token
 */
export function setAccessToken(value) {
  // On afecte la variable accessToken pour l'utiliser dans la config de axios
  accessToken = value;
  localStorage.setItem('access_token', value);
}
/**
 * Get access token from localStorage
 * @returns {string} access token
 */
export function getAccessToken() {
  return localStorage.getItem('access_token');
}
// Cette logique correspond à celle de refresh_token dans les localStorage
export function getRefreshToken() {
  return localStorage.getItem('refresh_token');
}
export function setRefreshToken(value) {
  localStorage.setItem('refresh_token', value);
}

/**
 * Vérifier si le token est valide en faisant un appel test
 * @returns {Promise<boolean>}
 */
export async function validateToken() {
  const token = getAccessToken();
  if (!token || token === 'null') {
    return false;
  }

  try {
    // Faire un appel simple pour vérifier la validité du token
    const response = await axios.get(`${apiBaseUrl}/user/`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.status === 200;
  } catch (error) {
    // Si le token est invalide, essayer de le rafraîchir
    if (error.response && (error.response.status === 401 || error.response.status === 403)) {
      try {
        const refreshToken = getRefreshToken();
        if (refreshToken) {
          const refreshResponse = await axios.post(`${apiBaseUrl}/user/refresh-token/`, {
            refresh: refreshToken,
          });

          if (refreshResponse.data && refreshResponse.data.access) {
            setAccessToken(refreshResponse.data.access);
            return true;
          }
        }
      } catch (refreshError) {
        console.warn('Token refresh failed:', refreshError);
      }
    }
    return false;
  }
}

// ⚠️ Cette logique correspond à celle de refresh_token dans les cookies
// ⚠️ On ne l'utilise pas pour le moment
/*
export function setRefreshToken(value) {
  document.cookie = `refresh_token=${value}; path=/; secure; samesite=strict; max-age=2592000`; // 30 jours
}
export function getRefreshToken() {
  try {
    return document.cookie
      .split('; ')
      .find(row => row.startsWith('refresh_token='))
      ?.split('=')[1] || null;
  } catch (error) {
    console.warn('Error getting refresh token from cookies', error);
    return null;
  }
}
*/

function getCookie(name) {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop().split(';').shift();
}

// Configuration de base pour axios
const axiosConfig = {
  baseURL: apiBaseUrl,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
  },
};

// Instance axios standard
export const axiosInstance = axios.create(axiosConfig);

// Instance axios avec cache
export const cachedAxiosInstance = axios.create(axiosConfig);

axiosInstance.interceptors.request.use((config) => {
  const csrfToken = getCookie('csrftoken');
  if (csrfToken) {
    config.headers['X-CSRFToken'] = csrfToken;
  }

  // Toujours récupérer le token le plus récent du localStorage
  const currentToken = getAccessToken();
  if (currentToken && currentToken !== 'null') {
    config.headers['Authorization'] = `Bearer ${currentToken}`;
    // Mettre à jour la variable globale
    accessToken = currentToken;
  }

  if (
    config.data instanceof FormData &&
    config.headers['Content-Type'] === 'application/json'
  ) {
    config.headers['Content-Type'] = 'multipart/form-data';
  }
  return config;
});
axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    const authenticationStatusCodes = new Set([401, 403]);
    const loginUrl = '/user/token/';
    const refreshUrl = '/user/refresh-token/';

    // Si pas de réponse, on rejette simplement l'erreur
    if (!error.response) {
      return Promise.reject(error);
    }

    const {
      config,
      response: { status },
    } = error;

    // Si ce n'est pas une erreur d'authentification, ou si c'est déjà une requête de login/refresh, ou si l'utilisateur n'est pas connecté
    if (
      !authenticationStatusCodes.has(status) ||
      config.url === loginUrl ||
      config.url === refreshUrl ||
      store.getters.isLoggedIn === false
    ) {
      return Promise.reject(error);
    }

    // Tenter de rafraîchir le token avant de déconnecter l'utilisateur
    try {
      const refreshToken = getRefreshToken();
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      // Essayer de rafraîchir le token
      const response = await axios.post(`${apiBaseUrl}/user/refresh-token/`, {
        refresh: refreshToken,
      });

      if (response.data && response.data.access) {
        // Mettre à jour le token
        const newAccessToken = response.data.access;
        setAccessToken(newAccessToken);

        // Mettre à jour le header de la requête originale et la réessayer
        config.headers['Authorization'] = `Bearer ${newAccessToken}`;
        return axios(config);
      } else {
        throw new Error('Failed to refresh token');
      }
    } catch (refreshError) {
      //console.error('Error refreshing token:', refreshError);
      // Si le rafraîchissement échoue, déconnecter l'utilisateur
      await logout();
      return Promise.reject(error);
    }
  }
);

// Ajouter des intercepteurs pour le cache
cachedAxiosInstance.interceptors.request.use(async (config) => {
  // Appliquer les mêmes intercepteurs que l'instance standard
  const csrfToken = getCookie('csrftoken');
  if (csrfToken) {
    config.headers['X-CSRFToken'] = csrfToken;
  }

  // Toujours récupérer le token le plus récent du localStorage
  const currentToken = getAccessToken();
  if (currentToken && currentToken !== 'null') {
    config.headers['Authorization'] = `Bearer ${currentToken}`;
    // Mettre à jour la variable globale
    accessToken = currentToken;
  }

  if (
    config.data instanceof FormData &&
    config.headers['Content-Type'] === 'application/json'
  ) {
    config.headers['Content-Type'] = 'multipart/form-data';
  }

  // Vérifier si la requête est en GET et si elle peut être mise en cache
  if (config.method === 'get' && config.cache !== false) {
    const cacheKey = cacheService.createKey(config.url, config.params);
    const cachedData = cacheService.get(cacheKey);

    if (cachedData) {
      // Si les données sont en cache, annuler la requête et retourner les données du cache
      config.adapter = () => {
        return Promise.resolve({
          data: cachedData,
          status: 200,
          statusText: 'OK',
          headers: {},
          config,
          request: {},
        });
      };
    }
  }

  return config;
});

cachedAxiosInstance.interceptors.response.use(
  (response) => {
    // Mettre en cache les réponses GET réussies
    if (response.config.method === 'get' && response.config.cache !== false) {
      const cacheKey = cacheService.createKey(
        response.config.url,
        response.config.params
      );
      const ttl = response.config.cacheTTL || 5 * 60 * 1000; // 5 minutes par défaut
      cacheService.set(cacheKey, response.data, ttl);
    }
    return response;
  },
  // Utiliser le même gestionnaire d'erreur que l'instance standard
  async (error) => {
    const authenticationStatusCodes = new Set([401, 403]);
    const loginUrl = '/user/token/';
    const refreshUrl = '/user/refresh-token/';

    // Si pas de réponse, on rejette simplement l'erreur
    if (!error.response) {
      return Promise.reject(error);
    }

    const {
      config,
      response: { status },
    } = error;

    // Si ce n'est pas une erreur d'authentification, ou si c'est déjà une requête de login/refresh, ou si l'utilisateur n'est pas connecté
    if (
      !authenticationStatusCodes.has(status) ||
      config.url === loginUrl ||
      config.url === refreshUrl ||
      store.getters.isLoggedIn === false
    ) {
      return Promise.reject(error);
    }

    // Tenter de rafraîchir le token avant de déconnecter l'utilisateur
    try {
      const refreshToken = getRefreshToken();
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      // Essayer de rafraîchir le token
      const response = await axios.post(`${apiBaseUrl}/user/refresh-token/`, {
        refresh: refreshToken,
      });

      if (response.data && response.data.access) {
        // Mettre à jour le token
        const newAccessToken = response.data.access;
        setAccessToken(newAccessToken);

        // Mettre à jour le header de la requête originale et la réessayer
        config.headers['Authorization'] = `Bearer ${newAccessToken}`;
        return axios(config);
      } else {
        throw new Error('Failed to refresh token');
      }
    } catch (refreshError) {
      //console.error('Error refreshing token:', refreshError);
      // Si le rafraîchissement échoue, déconnecter l'utilisateur
      await logout();
      return Promise.reject(error);
    }
  }
);

/**
 * Fonction utilitaire pour effectuer des requêtes GET avec mise en cache
 * @param {string} url - URL de la requête
 * @param {Object} params - Paramètres de la requête
 * @param {Object} options - Options supplémentaires
 * @param {boolean} options.cache - Activer/désactiver le cache pour cette requête
 * @param {number} options.cacheTTL - Durée de vie du cache en millisecondes
 * @returns {Promise<any>} - Réponse de la requête
 */
export const cachedGet = (url, params = {}, options = {}) => {
  return cachedAxiosInstance.get(url, {
    params,
    cache: options.cache !== false,
    cacheTTL: options.cacheTTL,
  });
};

/**
 * @deprecated use axiosInstance directly
 */
export default function axiosRequest(_contentType = 'application/json') {
  return axiosInstance;
}
