import store from '@/store';
import { toaster } from '@/utils/toast/toast.js';
import { axiosInstance } from './axios';

/**
 * Soumettre une candidature avec uniquement une lettre de motivation
 * @param {Object} data  - { lettre: 'texte de motivation' }
 * @return {Promise<any|false>}
 */
const submitLetter = async (data) => {
  try {
    const response = await axiosInstance.post('/applications/', {
      lettre: data.lettre, // ici c'est un string
    });
    toaster.showSuccessPopup('Lettre de motivation envoyée avec succès.');
    return response.data;
  } catch (error) {
    //console.error('Erreur lettre:', error.response?.data || error);
    toaster.showErrorPopup(
      "Erreur lors de l'envoi de la lettre de motivation."
    );
    return false;
  }
};
const getMotivation = async () => {
  try {
    const response = await axiosInstance.get('/applications/motivation/');
    return response.data; // ← adapte en fonction de ce que ton backend renvoie
  } catch (error) {
    toaster.showErrorPopup('Erreur lors du chargement de la lettre.');
    console.error('Erreur getMotivation:', error);
    throw error;
  }
};
export { submitLetter, getMotivation };
