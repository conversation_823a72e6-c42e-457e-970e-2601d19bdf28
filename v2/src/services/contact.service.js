import { axiosInstance } from './axios';
import { toaster } from '@/utils/toast/toast.js';

/**
 * submitContactForm 
 * @param {Object} formData
 * @return {Promise}
 * @throws {Error}
 */
const submitContactForm = async (formData) => {
    try {
        const response = await axiosInstance.post("/contact/", {
            "nom": formData.nom,
            "prenom": formData.prenom,
            "email": formData.email,
            "objects_name": formData.options,
            "message": formData.message,
            "user_type": formData.userType,
        });

        toaster.showSuccessPopup("Message envoyé.");
        return response;
    } catch (error) {
        //console.error(error);
        toaster.showErrorPopup("Une erreur est survenue.");
    }
};

/**
 * submitHiringForm 
 * @param {Object} formData
 * @return {Promise}
 * @throws {Error}
 */
const submitHiringForm = async (formData) => {
    try {
        await axiosInstance.post("/candidatthanksboss/addcandidat/", formData, {
            headers: {
                "Content-Type": "multipart/form-data"
            }
        });

        return toaster.showSuccessPopup("Candidature envoyée !.");
    } catch (error) {
        //console.error(error);
        return toaster.showErrorPopup("Une erreur est survenue.");
    }
};


export { submitContactForm, submitHiringForm };
