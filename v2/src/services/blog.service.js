import { axiosInstance } from './axios';
import { toaster } from '@/utils/toast/toast.js';

/**
 * submitEbookForm 
 * @param {Object} formData
 * @return {Promise}
 * @throws {Error}
 */
const submitEbookForm = async (formData) => {
    try {
        const response = await axiosInstance.post("/contact/", {
            "nom": formData.nom,
            "prenom": formData.prenom,
            "email": formData.email,
            "objects_name": formData.options,
            "message": formData.message,
        });

        toaster.showSuccessPopup("Informations enregistrés.");
        return response;
    } catch (error) {
        //console.error(error);
        toaster.showErrorPopup("Une erreur est survenue.");
    }
};

/**
 * submitEmail 
 * @param {Object} formData
 * @return {Promise}
 * @throws {Error}
 */
const submitEmail = async (formData) => {
    try {
        const response = await axiosInstance.post("/contact/", {
            "email": formData.email,
        });

        toaster.showSuccessPopup("Informations enregistrés.");
        return response;
    } catch (error) {
        //console.error(error);
        toaster.showErrorPopup("Une erreur est survenue.");
    }
};

/**
 * get a list of article
 * @return {Promise}
 * @throws {Error}
 */
const getArticleList = async (query, params) => {
    try {
        let response;
        if (query) response = await axiosInstance.get(`/actualites/?${query}=${params}`);
        else response = await axiosInstance.get(`/actualites/`);
        return response.data.results;
    } catch (error) {
        //console.error(error);
    }
};

/**
 * get a list of article
 * @return {Promise}
 * @throws {Error}
 */
const getHighlightedArticles = async () => {
    try {
        const response = await axiosInstance.get(`/actualites/highlighted`);
        return response.data.results;
    } catch (error) {
        //console.error(error);
    }
};

/**
 * get a list of article
 * @return {Promise}
 * @throws {Error}
 */
const getLastTwoArticles = async () => {
    try {
        const response = await axiosInstance.get(`/actualites/deux_derniers/`);
        return response.data;
    } catch (error) {
        //console.error(error);
    }
};

/**
 * get the top 3 most popular articles 
 * @return {Promise}
 * @throws {Error}
 */
const getMostPopularArticles = async () => {
    try {
        const response = await axiosInstance.get("/actualites/top/");
        return response.data;
    } catch (error) {
        //console.error(error);
    }
};

/**
 * get the top 3 most popular articles by theme
 * @return {Promise}
 * @throws {Error}
 */
const getMostPopularArticlesByTheme = async (articleTheme) => {
    try {
        const response = await axiosInstance.get(`/actualites/top/${articleTheme}`);
        return response.data;
    } catch (error) {
        //console.error(error);
    }
};

/**
 * get an article by id
 * @return {Promise}
 * @throws {Error}
 */
const getArticleById = async (articleId) => {
    try {
        const response = await axiosInstance.get(`/actualites/${articleId}/`);
        return response.data;
    } catch (error) {
        //console.error(error);
    }
};

export { submitEbookForm, submitEmail, getMostPopularArticles, getArticleById, getMostPopularArticlesByTheme, getArticleList, getHighlightedArticles,getLastTwoArticles };
