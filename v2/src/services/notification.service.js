/**
 * Service pour gérer les notifications sonores
 */

// Chemin vers le fichier de notification
const notificationSoundPath = '/sounds/notification.wav';

// Instance de l'élément audio pour la notification
let notificationSound = null;

/**
 * Initialise le son de notification
 */
export function initNotificationSound() {
  if (!notificationSound) {
    notificationSound = new Audio(notificationSoundPath);
    notificationSound.preload = 'auto';
    notificationSound.volume = 0.5; // Volume à 50%
    
    // Charger le son
    notificationSound.load();
    
    //console.log('%c[NOTIFICATION] Son de notification initialisé', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
  }
}

/**
 * Joue le son de notification
 * @returns {Promise<void>} Une promesse qui se résout lorsque le son a commencé à être joué
 */
export async function playNotificationSound() {
  try {
    // S'assurer que le son est initialisé
    if (!notificationSound) {
      initNotificationSound();
    }
    
    // Réinitialiser le son au début
    notificationSound.currentTime = 0;
    
    // Jouer le son
    const playPromise = notificationSound.play();
    
    if (playPromise !== undefined) {
      playPromise
        .then(() => {
          //console.log('%c[NOTIFICATION] Son de notification joué avec succès', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
        })
        .catch(error => {
          //console.error('%c[NOTIFICATION] Erreur lors de la lecture du son de notification:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
        });
    }
  } catch (error) {
    //console.error('%c[NOTIFICATION] Erreur lors de la lecture du son de notification:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
  }
}

/**
 * Arrête le son de notification
 */
export function stopNotificationSound() {
  if (notificationSound) {
    try {
      // Arrêter le son
      notificationSound.pause();
      notificationSound.currentTime = 0;
      //console.log('%c[NOTIFICATION] Son de notification arrêté', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
    } catch (error) {
      //console.error('%c[NOTIFICATION] Erreur lors de l\'arrêt du son de notification:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
    }
  }
}
