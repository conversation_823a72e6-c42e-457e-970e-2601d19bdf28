import store from '@/store';

// Référence globale au WebSocket privé de l'utilisateur
let privateWebSocket = null;

/**
 * Initialise le WebSocket privé pour les conversations utilisateur
 * @returns {WebSocket} - La connexion WebSocket privée
 */
const initializePrivateWebSocket = () => {
  if (privateWebSocket && privateWebSocket.readyState === WebSocket.OPEN) {
    //console.log('WebSocket privé déjà initialisé');
    return privateWebSocket;
  }

  try {
    const currentUser = store.getters.getUser;
    if (!currentUser || !currentUser.id) {
      //console.error('Utilisateur non connecté, impossible d\'initialiser le WebSocket');
      return null;
    }

    // Utiliser l'endpoint WebSocket privé avec l'ID de l'utilisateur actuel
    const url = `wss://websocket.thanks-boss.com/ws/chat/private/${currentUser.id}/`;
    //console.log(`Initialisation du WebSocket privé: ${url}`);
    const webSocket = new WebSocket(url);

    webSocket.onopen = () => {
      //console.log('WebSocket privé pour conversations utilisateur ouvert');

      // Pas besoin d'envoyer un message d'initialisation car l'ID est déjà dans l'URL
      //console.log('WebSocket privé initialisé pour l\'utilisateur:', currentUser.id);
    };

    webSocket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        //console.log('Message WebSocket utilisateur reçu:', data);

        // Traiter les messages entrants selon le format spécifié
        // Format attendu pour les messages reçus:
        // {
        //   "type": "chat_message",
        //   "message": "Salut comment ça va?",
        //   "time": "15:45",
        //   "date": "25-04-2025",
        //   "sender_id": 84,
        //   "receiver_id": 92,
        //   "is_sent": false
        // }

        if (data.type === 'chat_message' && data.message !== undefined) {
          //console.log(`Message reçu de l'utilisateur ${data.sender_id} pour ${data.receiver_id}`);

          const currentUser = store.getters.getUser;
          if (!currentUser || !currentUser.id) {
            //console.error('Utilisateur non connecté, impossible de traiter le message');
            return;
          }

          // Déterminer si le message est pour l'utilisateur actuel
          const isForCurrentUser = data.receiver_id === currentUser.id;
          const isFromCurrentUser = data.sender_id === currentUser.id;

          if (!isForCurrentUser && !isFromCurrentUser) {
            //console.log('Message ignoré car il n\'est ni pour ni de l\'utilisateur actuel');
            return;
          }

          // Récupérer les informations de l'expéditeur et du destinataire
          let senderName = `Utilisateur ${data.sender_id}`;
          let receiverName = `Utilisateur ${data.receiver_id}`;

          // Si l'expéditeur est l'utilisateur actuel
          if (isFromCurrentUser) {
            senderName = `${currentUser.first_name} ${currentUser.last_name}`;
          }

          // Formater le message pour l'affichage
          const formattedMessage = {
            id: data.id || `msg_${Date.now()}`,
            sender_id: data.sender_id,
            sender_name: senderName,
            receiver_id: data.receiver_id,
            receiver_name: receiverName,
            message: data.message,
            time: data.time,
            date: data.date,
            read: data.is_sent || false,
            isCurrentUser: isFromCurrentUser,
            isImage: data.message.startsWith('data:image/')
          };

          // Déclencher un événement pour notifier l'interface utilisateur
          const messageEvent = new CustomEvent('new-message', {
            detail: formattedMessage
          });
          window.dispatchEvent(messageEvent);
          //console.log('Événement new-message déclenché avec les données:', formattedMessage);

          // Mettre à jour le store si nécessaire
          if (store.state.userModule && store.state.userModule.activeConversation) {
            const activeConversation = store.state.userModule.activeConversation;

            // Vérifier si le message appartient à la conversation active
            const isForActiveConversation =
              (isFromCurrentUser && activeConversation.receiver_id === data.receiver_id) ||
              (!isFromCurrentUser && activeConversation.receiver_id === data.sender_id);

            if (isForActiveConversation) {
              // Ajouter le message à la conversation active
              store.dispatch('addMessageToConversation', formattedMessage);
            } else if (!isFromCurrentUser) {
              // Incrémenter le compteur de messages non lus pour cette conversation
              store.dispatch('incrementUnreadCount', data.sender_id);
            }
          }
        }
        // Format alternatif avec objets sender et receiver
        else if (data.message !== undefined && data.sender && data.receiver) {
          //console.log(`Message alternatif reçu de l'utilisateur ${data.sender.id} pour ${data.receiver.id}`);

          const currentUser = store.getters.getUser;

          // Formater le message pour l'affichage
          const formattedMessage = {
            id: data.id,
            sender_id: data.sender.id,
            sender_name: `${data.sender.first_name} ${data.sender.last_name}`,
            receiver_id: data.receiver.id,
            receiver_name: `${data.receiver.first_name} ${data.receiver.last_name}`,
            message: data.message,
            time: data.time,
            date: data.date,
            read: data.read || false,
            isCurrentUser: data.sender.id === currentUser.id,
            isImage: data.message.startsWith('data:image/')
          };

          // Déclencher un événement pour notifier l'interface utilisateur
          const messageEvent = new CustomEvent('new-message', {
            detail: formattedMessage
          });
          window.dispatchEvent(messageEvent);
          //console.log('Événement new-message déclenché avec les données (format alternatif):', formattedMessage);

          // Mettre à jour le store si nécessaire
          if (store.state.userModule && store.state.userModule.activeConversation) {
            const activeConversation = store.state.userModule.activeConversation;
            const isForActiveConversation =
              (activeConversation.receiver_id === data.sender.id) ||
              (activeConversation.receiver_id === data.receiver.id && data.sender.id === currentUser.id);

            if (isForActiveConversation) {
              // Ajouter le message à la conversation active
              store.dispatch('addMessageToConversation', formattedMessage);
            } else {
              // Incrémenter le compteur de messages non lus pour cette conversation
              store.dispatch('incrementUnreadCount', data.sender.id);
            }
          }
        }
        else if (data.type === 'message_sent' || data.status === 'sent') {
          // Confirmation que notre message a été envoyé
          //console.log('Message envoyé avec succès:', data);

          // Déclencher un événement pour notifier l'interface utilisateur
          const messageSentEvent = new CustomEvent('message-sent', {
            detail: data
          });
          window.dispatchEvent(messageSentEvent);
        }
        else if (data.type === 'error') {
          // Erreur lors de l'envoi du message
          //console.error('Erreur WebSocket:', data.message || 'Erreur inconnue');

          // Déclencher un événement pour notifier l'interface utilisateur
          const errorEvent = new CustomEvent('message-error', {
            detail: data
          });
          window.dispatchEvent(errorEvent);
        }
        // Gestion des messages d'appel vidéo
        else if (data.type === 'video_offer' || data.message_type === 'video_offer') {
          //console.log('%c[APPEL] Offre d\'appel vidéo reçue via WebSocket privé:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', data);

          // Extraire les données d'appel selon le format
          let senderId = data.sender_id;
          let senderName = data.sender_name;
          let callType = data.call_type || 'video';
          let offer = data.offer;

          // Vérifier les formats alternatifs
          if (data.senderId && !senderId) senderId = data.senderId;
          if (data.sender && data.sender.id && !senderId) senderId = data.sender.id;
          if (data.sender && data.sender.name && !senderName) senderName = data.sender.name;
          if (data.callType && !callType) callType = data.callType;
          if (data.sdp && !offer) offer = data.sdp;

          // Vérifier que les données essentielles sont présentes
          if (!senderId) {
            //console.error('%c[APPEL] Données d\'appel incomplètes: ID d\'expéditeur manquant', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
            return;
          }

          // Préparer les données d'appel
          const callData = {
            senderId: senderId,
            senderName: senderName || `Utilisateur ${senderId}`,
            call_type: callType,
            offer: offer
          };

          //console.log('%c[APPEL] Traitement de l\'offre d\'appel vidéo avec les données:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', callData);

          // Importer dynamiquement le service d'appel vidéo pour éviter les dépendances circulaires
          import('./video-call.service').then(async ({ handleIncomingCall }) => {
            try {
              // Traiter l'appel entrant
              const success = await handleIncomingCall(callData);
              //console.log('%c[APPEL] Traitement de l\'appel entrant:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', success ? 'Réussi' : 'Échoué');

              // Si le traitement a échoué, essayer à nouveau après un court délai
              if (!success) {
                //console.log('%c[APPEL] Nouvelle tentative de traitement après délai...', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;');
                setTimeout(async () => {
                  const retrySuccess = await handleIncomingCall(callData);
                  //console.log('%c[APPEL] Nouvelle tentative de traitement:', 'background: #f6b337; color: black; padding: 2px 5px; border-radius: 3px;', retrySuccess ? 'Réussie' : 'Échouée');
                }, 1000);
              }
            } catch (error) {
              //console.error('%c[APPEL] Erreur lors du traitement de l\'offre d\'appel vidéo:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
            }
          }).catch(error => {
            //console.error('%c[APPEL] Erreur lors de l\'importation du service d\'appel vidéo:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
          });
        }
        else {
          //console.log('Format de message non reconnu:', data);
        }
      } catch (error) {
        //console.error('Erreur lors du traitement du message WebSocket utilisateur:', error);
      }
    };

    webSocket.onerror = (error) => {
      //console.error('Erreur WebSocket utilisateur:', error);
    };

    webSocket.onclose = () => {
      //console.log('WebSocket utilisateur fermé');
      privateWebSocket = null;
    };

    privateWebSocket = webSocket;
    return webSocket;
  } catch (error) {
    //console.error('Erreur lors de l\'initialisation du WebSocket utilisateur:', error);
    return null;
  }
};

// La fonction getConversationHistory a été déplacée vers conversation.service.js

/**
 * Crée ou charge une conversation utilisateur via API REST
 * @param {string} receiverId - ID du destinataire
 * @returns {Promise<Object>} - La conversation avec son historique de messages
 */
const createConversation = async (receiverId) => {
  try {
    const currentUser = store.getters.getUser;
    if (!currentUser || !currentUser.id) {
      //console.error('Utilisateur non connecté');
      return null;
    }

    if (!receiverId) {
      //console.error('ID du destinataire manquant');
      return null;
    }

    //console.log(`Chargement/création de la conversation avec l'utilisateur ${receiverId}`);

    // Importer dynamiquement le service API pour éviter les dépendances circulaires
    const { getConversationHistory } = await import('./conversation.service');

    // Récupérer l'historique des messages via l'API GET
    const messages = await getConversationHistory(currentUser.id, receiverId);

    // Récupérer les informations du destinataire
    let receiverInfo = { first_name: 'Utilisateur', last_name: receiverId };

    // Si nous avons des messages, nous pouvons extraire les informations du destinataire
    if (messages.length > 0) {
      const firstMessage = messages[0];
      if (firstMessage.receiver_id === parseInt(receiverId)) {
        receiverInfo = {
          id: firstMessage.receiver_id,
          first_name: firstMessage.receiver_name.split(' ')[0],
          last_name: firstMessage.receiver_name.split(' ')[1] || ''
        };
      } else if (firstMessage.sender_id === parseInt(receiverId)) {
        receiverInfo = {
          id: firstMessage.sender_id,
          first_name: firstMessage.sender_name.split(' ')[0],
          last_name: firstMessage.sender_name.split(' ')[1] || ''
        };
      }
    }

    // Créer l'objet conversation
    return {
      id: `${currentUser.id}_${receiverId}`,
      messages: messages,
      receiver: receiverInfo,
      receiver_id: parseInt(receiverId),
      last_message: messages.length > 0 ? messages[messages.length - 1] : null,
      unread_count: messages.filter(msg => !msg.read && msg.sender_id === parseInt(receiverId)).length
    };
  } catch (error) {
    //console.error('Erreur lors de la création/chargement de la conversation:', error);
    return null;
  }
};

// Ces fonctions ne sont plus nécessaires avec le nouveau système de WebSocket privé
// Elles sont remplacées par des appels API REST directs

/**
 * Envoie un message utilisateur via WebSocket
 * @param {string} receiverId - ID du destinataire
 * @param {string} message - Message à envoyer
 * @param {Object} file - Fichier à envoyer (optionnel)
 * @returns {Promise<Object>} - La réponse avec le message envoyé
 */
const sendUserMessage = async (receiverId, message, file = null) => {
  try {
    const currentUser = store.getters.getUser;
    if (!currentUser || !currentUser.id) {
      //console.error('Utilisateur non connecté');
      return null;
    }

    // Initialiser le WebSocket privé
    const webSocket = initializePrivateWebSocket();
    if (!webSocket || webSocket.readyState !== WebSocket.OPEN) {
      //console.error('WebSocket privé non disponible');
      return null;
    }

    // Traitement des images/fichiers
    let messageContent = message;

    // Si un fichier est fourni, le convertir en base64
    if (file) {
      try {
        //console.log('Traitement du fichier pour envoi via WebSocket:', file);

        // Vérifier si c'est une image
        if (file.type && file.type.startsWith('image/')) {
          // Convertir l'image en base64
          const reader = new FileReader();
          const base64Promise = new Promise((resolve) => {
            reader.onload = (e) => resolve(e.target.result);
            reader.readAsDataURL(file);
          });

          // Attendre la conversion en base64
          messageContent = await base64Promise;
          //console.log('Image convertie en base64 pour envoi');
        } else {
          //console.error('Type de fichier non pris en charge:', file.type);
          return null;
        }
      } catch (fileError) {
        //console.error('Erreur lors du traitement du fichier:', fileError);
        return null;
      }
    }

    // Préparer le message au format attendu exactement comme spécifié
    const request = {
      message: messageContent,
      receiver_id: parseInt(receiverId)
    };

    // Envoyer le message via WebSocket
    webSocket.send(JSON.stringify(request));
    //console.log('Message utilisateur envoyé via WebSocket:', {
    //  ...request,
    //  message: messageContent.length > 100 ? messageContent.substring(0, 100) + '...' : messageContent
    //});

    // Créer un objet représentant le message envoyé
    const sentMessage = {
      id: `temp_${Date.now()}`, // ID temporaire
      sender_id: currentUser.id,
      sender_name: `${currentUser.first_name} ${currentUser.last_name}`,
      receiver_id: parseInt(receiverId),
      message: messageContent,
      time: new Date().toTimeString().split(' ')[0],
      date: new Date().toISOString().split('T')[0],
      read: false,
      isCurrentUser: true,
      isImage: messageContent.startsWith('data:image/'),
      status: 'sent'
    };

    // Retourner immédiatement le message envoyé
    // La confirmation sera traitée par le gestionnaire d'événements WebSocket
    return sentMessage;
  } catch (error) {
    //console.error('Erreur lors de l\'envoi du message:', error);
    return null;
  }
};

// Ces fonctions ne sont plus nécessaires avec le nouveau système de WebSocket privé
// Elles sont remplacées par des appels API REST directs

export {
  initializePrivateWebSocket,
  createConversation,
  sendUserMessage
};
