import { toaster } from '@/utils/toast/toast.js';
import { axiosInstance } from './axios';
import store from '@/store';

// URL de base de l'API Flask
const FLASK_API_URL =
  process.env.VUE_APP_FLASK_API_URL || 'http://127.0.0.1:5000';

/**
 * Crée un nouvel entretien avec un CV et une description de poste
 * @param {FormData} formData - Les données de l'entretien (CV et description de poste)
 * @return {Promise} - La réponse de l'API
 */
const createInterview = async (formData) => {
  try {
    //console.log("Création d'un entretien...");

    // Créer l'entretien dans le backend Django uniquement
    const interviewData = new FormData();

    // Ajouter la date actuelle
    interviewData.append('date', new Date().toISOString());
    interviewData.append('status', 'pending');
    interviewData.append('simulation', 'true');

    // Ajouter explicitement l'ID de l'utilisateur actuel comme candidat
    const currentUser = store.getters.getUser;
    if (currentUser && currentUser.id) {
      interviewData.append('candidate', currentUser.id);
    } else {
      console.warn(
        "Aucun utilisateur connecté trouvé. L'entretien ne pourra pas être créé."
      );
    }

    // Transférer les données du CV
    if (formData.has('cv_id')) {
      interviewData.append('cv_id', formData.get('cv_id'));
    }

    // Transférer le fichier CV s'il existe
    if (formData.has('file')) {
      interviewData.append('cv_file', formData.get('file'));
    }

    // Transférer les données du job - au moins un des deux champs est requis
    if (formData.has('job_id')) {
      interviewData.append('job', formData.get('job_id'));
    }

    if (formData.has('description_job')) {
      interviewData.append('job_description', formData.get('description_job'));
    }

    // Créer l'entretien dans le backend Django
    const djangoResponse = await axiosInstance.post(
      '/interviews/create/',
      interviewData
    );

    if (!djangoResponse.data || !djangoResponse.data.id) {
      throw new Error(
        "Erreur lors de la création de l'entretien dans le backend"
      );
    }

    //console.log(
    //  'Entretien créé avec succès dans le backend:',
    //  djangoResponse.data
    //);

    // Stocker l'ID de l'entretien dans le localStorage pour le récupérer plus tard
    localStorage.setItem('currentInterviewId', djangoResponse.data.id);

    // Maintenant, appeler l'API Flask avec l'ID de l'entretien
    try {
      // Préparer les données pour l'API Flask
      const flaskFormData = new FormData();
      flaskFormData.append('interview_id', djangoResponse.data.id);

      // Transférer le fichier CV s'il existe
      if (formData.has('file')) {
        flaskFormData.append('file', formData.get('file'));
      }

      // Transférer la description du job
      if (formData.has('description_job')) {
        flaskFormData.append(
          'description_job',
          formData.get('description_job')
        );
      }

      // Appeler l'API Flask
      const flaskResponse = await fetch(`${FLASK_API_URL}/first_question`, {
        method: 'POST',
        body: flaskFormData,
      });

      if (!flaskResponse.ok) {
        console.warn(
          `Erreur lors de l'appel à l'API Flask: ${flaskResponse.status}`
        );
        // Même en cas d'erreur, on continue avec les données de l'entretien créé
      } else {
        const flaskData = await flaskResponse.json();

        // Stocker les données de l'entretien dans le localStorage pour les utiliser dans Interview.vue
        const interviewData = {
          interview_id: djangoResponse.data.id,
          feedback: flaskData.feedback || null,
          next_question: flaskData.next_question || null,
          audio_url: flaskData.audio_url || null,
        };

        localStorage.setItem('interviewData', JSON.stringify(interviewData));
      }
    } catch (flaskError) {
      //console.error("Erreur lors de l'appel à l'API Flask:", flaskError);
      // On continue même en cas d'erreur
    }

    // Préparer les données pour le retour
    const interviewInfo = {
      interview_id: djangoResponse.data.id,
      message:
        "Entretien créé avec succès. Vous allez être redirigé vers la page d'entretien.",
      redirect_to_interview: true,
    };

    return interviewInfo;
  } catch (error) {
    //console.error("Erreur lors de la création de l'entretien:", error);
    toaster.showErrorPopup(
      "Erreur lors de la création de l'entretien. Veuillez réessayer."
    );
    throw error;
  }
};

/**
 * Envoie une réponse à l'entretien en cours
 * @param {Object} data - La réponse du candidat
 * @param {Number} interviewId - L'ID de l'entretien en cours
 * @return {Promise} - La réponse de l'API
 */
const sendResponse = async (data, interviewId) => {
  try {
    // Ajouter l'ID de l'entretien aux données
    const requestData = {
      ...data,
      interview_id: interviewId,
    };

    const response = await fetch(`${FLASK_API_URL}/respond`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      throw new Error(`Erreur HTTP: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    //console.error("Erreur lors de l'envoi de la réponse:", error);
    toaster.showErrorPopup(
      "Erreur lors de l'envoi de la réponse. Veuillez réessayer."
    );
    throw error;
  }
};

/**
 * Génère un rapport d'entretien
 * @param {Number} interviewId - L'ID de l'entretien
 * @return {Promise} - La réponse de l'API
 */
const generateReport = async (interviewId) => {
  try {
    const response = await fetch(
      `${FLASK_API_URL}/generate_report?interview_id=${interviewId}`
    );

    if (!response.ok) {
      throw new Error(`Erreur HTTP: ${response.status}`);
    }

    const reportData = await response.json();

    // Retourner directement les données du rapport sans mettre à jour l'entretien
    return reportData;
  } catch (error) {
    //console.error('Erreur lors de la génération du rapport:', error);
    toaster.showErrorPopup(
      'Erreur lors de la génération du rapport. Veuillez réessayer.'
    );
    throw error;
  }
};

/**
 * Démarre un nouvel entretien
 * @param {FormData} formData - Les données pour démarrer l'entretien (CV et description de poste)
 * @return {Promise} - La réponse de l'API
 */
const startInterview = async (formData) => {
  try {
    //console.log("Démarrage d'un nouvel entretien...");

    // Créer l'entretien dans le backend Django uniquement
    const interviewData = new FormData();

    // Ajouter la date actuelle
    interviewData.append('date', new Date().toISOString());
    interviewData.append('status', 'pending');
    interviewData.append('simulation', 'true');

    // Ajouter explicitement l'ID de l'utilisateur actuel comme candidat
    const currentUser = store.getters.getUser;
    if (currentUser && currentUser.id) {
      interviewData.append('candidate', currentUser.id);
    } else {
      console.warn(
        "Aucun utilisateur connecté trouvé. L'entretien ne pourra pas être créé."
      );
    }

    // Transférer le fichier CV s'il existe
    if (formData.has('file')) {
      interviewData.append('cv_file', formData.get('file'));
    }

    // Ajouter la description du job - au moins un des deux champs est requis
    if (formData.has('job_id')) {
      interviewData.append('job', formData.get('job_id'));
    }

    if (formData.has('description_job')) {
      interviewData.append('job_description', formData.get('description_job'));
    }

    // Créer l'entretien dans le backend Django
    const djangoResponse = await axiosInstance.post(
      '/interviews/',
      interviewData
    );

    if (!djangoResponse.data || !djangoResponse.data.id) {
      throw new Error(
        "Erreur lors de la création de l'entretien dans le backend"
      );
    }

    //console.log(
    //  'Entretien créé avec succès dans le backend:',
    //  djangoResponse.data
    //);

    // Stocker l'ID de l'entretien dans le localStorage pour le récupérer plus tard
    localStorage.setItem('currentInterviewId', djangoResponse.data.id);

    // Maintenant, appeler l'API Flask avec l'ID de l'entretien
    try {
      // Préparer les données pour l'API Flask
      const flaskFormData = new FormData();
      flaskFormData.append('interview_id', djangoResponse.data.id);

      // Transférer le fichier CV s'il existe
      if (formData.has('file')) {
        flaskFormData.append('file', formData.get('file'));
      }

      // Transférer la description du job
      if (formData.has('description_job')) {
        flaskFormData.append(
          'description_job',
          formData.get('description_job')
        );
      }

      // Appeler l'API Flask
      const flaskResponse = await fetch(`${FLASK_API_URL}/first_question`, {
        method: 'POST',
        body: flaskFormData,
      });

      if (!flaskResponse.ok) {
        console.warn(
          `Erreur lors de l'appel à l'API Flask: ${flaskResponse.status}`
        );
        // Même en cas d'erreur, on continue avec les données de l'entretien créé
      } else {
        const flaskData = await flaskResponse.json();

        // Stocker les données de l'entretien dans le localStorage pour les utiliser dans Interview.vue
        const interviewData = {
          interview_id: djangoResponse.data.id,
          feedback: flaskData.feedback || null,
          next_question: flaskData.next_question || null,
          audio_url: flaskData.audio_url || null,
        };

        localStorage.setItem('interviewData', JSON.stringify(interviewData));
      }
    } catch (flaskError) {
      //console.error("Erreur lors de l'appel à l'API Flask:", flaskError);
      // On continue même en cas d'erreur
    }

    // Préparer les données pour le retour
    const interviewInfo = {
      interview_id: djangoResponse.data.id,
      message:
        "Entretien créé avec succès. Vous allez être redirigé vers la page d'entretien.",
      redirect_to_interview: true,
    };

    return interviewInfo;
  } catch (error) {
    //console.error("Erreur lors du démarrage de l'entretien:", error);
    toaster.showErrorPopup(
      "Erreur lors du démarrage de l'entretien. Veuillez réessayer."
    );
    throw error;
  }
};

/**
 * Envoie un fichier audio pour transcription
 * @param {Blob} audioBlob - Le blob audio à envoyer
 * @param {Number} interviewId - L'ID de l'entretien en cours
 * @return {Promise} - La réponse de l'API
 */
const uploadAudio = async (audioBlob, interviewId) => {
  try {
    const formData = new FormData();
    formData.append('audio', audioBlob, 'audio.wav');

    // Ajouter l'ID de l'entretien si disponible
    if (interviewId) {
      formData.append('interview_id', interviewId);
    }

    const response = await fetch(`${FLASK_API_URL}/upload-audio`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`Erreur HTTP: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    //console.error("Erreur lors de l'envoi de l'audio:", error);
    toaster.showErrorPopup(
      "Erreur lors de l'envoi de l'audio. Veuillez réessayer."
    );
    throw error;
  }
};

/**
 * Vérifie si l'API Flask est disponible
 * @return {Promise<boolean>} - true si l'API est disponible, false sinon
 */
const checkFlaskApiAvailability = async () => {
  try {
    // Utiliser un timeout pour éviter d'attendre trop longtemps
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 secondes de timeout

    const response = await fetch(`${FLASK_API_URL}/health`, {
      method: 'GET',
      signal: controller.signal,
    });

    clearTimeout(timeoutId);
    return response.ok;
  } catch (error) {
    //console.error("Erreur lors de la vérification de l'API Flask:", error);
    return false;
  }
};

export {
  createInterview,
  sendResponse,
  generateReport,
  startInterview,
  uploadAudio,
  checkFlaskApiAvailability,
  FLASK_API_URL,
};
