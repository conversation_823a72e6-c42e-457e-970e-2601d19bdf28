import { axiosInstance } from './axios';

/**
 * friendsList
 * @return {Array}
 */

const friendsList = async () => {
  try {
    const response = await axiosInstance.get(`/friends/`);
    // retourne 1 tableau d'objets: "friends": [{"id": 27}, {"id": 10}]
    return response.data.friends;
  } catch (error) {
    //console.error('Erreur lors de la récupération des friends:', error);
    return [];
  }
};

/**
 * friendsInvitations
 * @return {Array}
 */

const friendsInvitations = async () => {
  try {
    const response = await axiosInstance.post(`/friends/invitations/`);
    // retourne 2 tableaux d'objets: "sent_invitations": [{},{}] & "received_invitations": [{},{}]
    return response.data;
  } catch (error) {
    //console.error('Erreur lors de la récupération des invitations:', error);
    return { sent_invitations: [], received_invitations: [] };
  }
};

/**
 * removeFriend
 * @param {number} userId
 */
const removeFriend = async (userId) => {
  try {
    await axiosInstance.delete(`/delete-invitation/${userId}/`);
  } catch (error) {
    throw new Error("Échec de la suppression de l'ami");
  }
};

/**
 * sendFriendInvitation
 * @param {string} userId
 */
const sendFriendInvitation = async (userId) => {
  try {
    await axiosInstance.post(`/friend/invitation/${userId}/`);
  } catch (error) {
    throw new Error("Échec lors de l'envoi de l'invitation");
  }
};

/**
 * acceptFriendInvitation
 * @param {number} invitationId
 * @throws {Error}
 */
const acceptFriendInvitation = async (invitationId) => {
  try {
    const response = await axiosInstance.patch(
      `/friend/accept-invitation/${invitationId}/`
    );
    return response;
  } catch (error) {
    throw new Error('invitation from friend failed');
  }
};

/**
 * declinedFriendInvitation
 * @param {number} invitationId
 * @throws {Error}
 */
const declinedFriendInvitation = async (invitationId) => {
  try {
    const response = await axiosInstance.patch(
      `/friend/decline-invitation/${invitationId}/`
    );
    return response;
  } catch (error) {
    throw new Error('invitation from friend failed');
  }
};

/**
 * Fetches a list of user friends from the API.
 *
 * @param {string} [sort='asc'] - The sorting order of the users, either 'asc' or 'desc'.
 * @param {string} [group_by='all'] - The grouping criteria for the users.
 * @returns {Promise<Object>} A promise that resolves to the user data.
 * @throws Will throw an error if the request fails.
 */
const fetchUsers = async (sort = 'asc', group_by = 'all') => {
  try {
    const response = await axiosInstance.get(`/user/friends/`, {
      params: { sort, group_by },
    });
    return response.data; // Returning the users
  } catch (error) {
    //console.error('Error fetching users:', error);
    throw error; // Propagate the error for handling
  }
};

export {
  friendsList,
  friendsInvitations,
  removeFriend,
  sendFriendInvitation,
  acceptFriendInvitation,
  declinedFriendInvitation,
  fetchUsers,
};
