import { isCandidateFavoriteById } from '@/services/favoriteProfil.service';
import store from '@/store';
import { decodeCredential, googleOneTap } from 'vue3-google-login';
import { toaster } from '../utils/toast/toast';

import {
  axiosInstance,
  getRefreshToken,
  setAccessToken,
  setRefreshToken,
} from './axios';

/**
Login user by api
@param {Object} data
@param {Boolean} rememberMe
@param {Boolean} isGoogle
@return {Promise}
@throws {Error}*/
const login = async (data, rememberMe, isGoogle) => {
  try {
    const response = await axiosInstance.post('/user/token/', data, {
      withCredentials: false,
    });

    // ⚠️ On n'utilise que le refresh token pour l'instant
    const accessToken = response.data.access;
    console.log(accessToken);
    const refreshToken = response.data.refresh;
    // ⚠️ On utilise le refresh token en localStorage pour le moment
    // ⚠️ refresh token au lieu de access token pour éviter la déconnexion de l'utilisateur
    // ⚠️ Cette fonction doit recevoir le access_token mais pour l'instant on utilise le refresh_token
    setAccessToken(accessToken);
    // ⚠️ On stocke le refresh token dans le localStorage pour le moment
    setRefreshToken(refreshToken);

    // ⚠️ On stocke le refresh token dans le localStorage alors les cookies ne sont pas utilisés pour le moment
    // setRefreshToken(refreshToken);

    store.dispatch('fetchUser', isGoogle);
    return response;
  } catch (error) {
    throw new Error(error.response.data?.detail || 'Login failed');
  }
};
/**
 * Ask for a new access token using the refresh token
 * @returns {Promise} {newAccessToken: string}
 * @throws {Error}
 */
export const refreshAccessToken = async () => {
  try {
    //console.log('access token was expired, trying to refresh it');
    const response = await axiosInstance.post('/user/refresh-token/', {
      refresh: getRefreshToken(),
    });
    setAccessToken(response.data.access);
    return { newAccessToken: response.data.access };
  } catch (error) {
    throw new Error(error.response.data?.detail || 'Refresh token failed');
  }
};

/**
 * Check if user access token exist and if it is valid
 * return true if the auth status is valid and false if it is not
 * @returns {boolean}
 * @throws {Error}
 */
const checkAuthStatus = async () => {
  try {
    const token = localStorage.getItem('access_token');
    if (!token) {
      // Vérifier si un ancien format de token existe
      const oldToken = localStorage.getItem('token');
      if (oldToken) {
        // Migrer l'ancien format vers le nouveau
        localStorage.setItem('access_token', oldToken);
        return true;
      }
      return false;
    }

    // Decomposer le token pour le vérifier
    const tokenParts = token.split('.');
    if (tokenParts.length !== 3) {
      return false; // Le token n'a pas un format valide
    }

    const payload = JSON.parse(atob(tokenParts[1]));
    const expirationTime = payload.exp * 1000; // Convertir en millisecondes

    // Vérifier si le token expire bientôt (dans les 5 minutes)
    const expirationThreshold = 5 * 60 * 1000; // 5 minutes en millisecondes
    const isExpiringSoon = Date.now() >= expirationTime - expirationThreshold;

    // Si le token est expiré ou expire bientôt, essayer de le renouveler
    if (Date.now() >= expirationTime || isExpiringSoon) {
      const refreshToken = getRefreshToken();
      if (!refreshToken) {
        // Essayer de récupérer un ancien format de refresh token
        const oldToken = localStorage.getItem('token');
        if (oldToken) {
          // Utiliser l'ancien token comme refresh token (solution temporaire)
          localStorage.setItem('refresh_token', oldToken);
          await refreshAccessToken();
          return true;
        }

        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('token'); // Nettoyer aussi l'ancien format
        return false;
      }

      try {
        await refreshAccessToken();
        return true;
      } catch (refreshError) {
        //console.error('Failed to refresh token:', refreshError);
        // Ne pas déconnecter immédiatement en cas d'erreur réseau temporaire
        // Vérifier si le token est vraiment expiré ou juste bientôt expiré
        if (Date.now() >= expirationTime) {
          // Token réellement expiré, déconnecter
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
          localStorage.removeItem('token'); // Nettoyer aussi l'ancien format
          return false;
        } else {
          // Token pas encore expiré, continuer avec le token actuel
          return true;
        }
      }
    }
    return true;
  } catch (error) {
    //console.error('Error checking auth status:', error);
    // En cas d'erreur, vérifier si un token existe toujours
    const hasToken =
      localStorage.getItem('access_token') || localStorage.getItem('token');
    return !!hasToken;
  }
};
/**
/**
Login user by google one tap
@returns {Promise}
@throws {Error}*/
const googleLogin = async () => {
  try {
    const response = await googleOneTap({
      clientId:
        '499109195466-3cgb1ucg8oon8ncdemjpveuqkv7n0i61.apps.googleusercontent.com',
      context: 'signin',
    });

    const regData = decodeCredential(response.credential);
    // try to login pass regular
    const loginResponse = await login({
      email: regData.email,
      password: regData.sub,
      isGoogle: true,
    });
    // get user profile, and store it in vuex
    addUserToStore();
    return;
  } catch (error) {
    throw new Error(error.response.data.detail || 'Login failed');
  }
};
/**
Register user
@param {Object|FormData} credentials
@returns {Promise}*/
const register = async (credentials) => {
  try {
    const formData = new FormData();

    // suppression des cookies sessionid qui peuvent être en conflit avec les cookies de l'application
    if (document.cookie.includes('sessionid')) {
      document.cookie =
        'sessionid=; path=/; domain=paris.thanks-boss.com; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      document.cookie =
        'sessionid=; path=/; domain=marseille.thanks-boss.com; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    }

    let dataToSend;
    let headers = { withCredentials: false };

    if (credentials instanceof FormData) {
      dataToSend = credentials;
      headers.headers = { 'Content-Type': 'multipart/form-data' };
    } else {
      // fallback: si credentials est un objet simple
      dataToSend = new FormData();
      for (const key in credentials) {
        // S'assurer que le champ fichier s'appelle bien 'photo'
        if (key === 'profilePicture' && credentials[key]) {
          dataToSend.append('photo', credentials[key]);
        } else {
          dataToSend.append(key, credentials[key]);
        }
      }
      headers.headers = { 'Content-Type': 'multipart/form-data' };
    }

    const response = await axiosInstance.post('/user/register/', dataToSend, headers);
    return response;
  } catch (error) {
    const errorData = error.response && error.response.data;
    let message = 'Erreur de connexion au serveur';

    if (errorData) {
      if (errorData.email) {
        message = errorData.email.join(', ');
      } else if (errorData.detail) {
        message = errorData.detail;
      } else {
        message = "Erreur lors de l\'inscription";
      }
    }
    if (error.response && error.response.status === 403) {
      message =
        'Nous vous prions de désactiver les bloqueurs de publicité pour créer un compte.';
    }

    throw new Error(message);
  }
};
/**
 * Logout user
 */
const logout = () => {
  try {
    // Essayer de notifier le serveur de la déconnexion
    axiosInstance.post('/user/log-out/').catch((error) => {
      console.warn('Error notifying server about logout:', error);
      // Continuer le processus de déconnexion même si la requête échoue
    });
  } catch (error) {
    //console.error('Error during logout:', error);
  } finally {
    // Nettoyer le store
    store.dispatch('logout');

    // Nettoyer tous les formats de tokens possibles
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('token'); // Ancien format
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('isGoogle');

    // Nettoyer les cookies potentiels
    document.cookie =
      'refresh_token=; path=/; secure; samesite=strict; max-age=0';
    document.cookie =
      'access_token=; path=/; secure; samesite=strict; max-age=0';
    document.cookie = 'token=; path=/; secure; samesite=strict; max-age=0';

    // Rediriger vers la page d'accueil
    window.location.href = '/';
  }
};

const deleteAccount = async () => {
  try {
    toaster.showInfoPopup(
      'Votre compte est en cours de suppression. Vous allez être redirigé vers la page de connexion.'
    );

    await axiosInstance.delete('/user/');

    window.location.href = '/';

    return;
  } catch (error) {
    toaster.showErrorPopup();
    //console.error('Error deleting user account:', error);
  }
};

// à deplacer dans un fichier user.js
const getUser = async () => {
  try {
    const response = await axiosInstance.get('/user/');
    return response.data;
  } catch (error) {
    throw new Error(error.response.data.detail || 'User not found');
  }
};
const getUserById = async (id) => {
  try {
    const response = await axiosInstance.get(`/user/${id}`);
    return response.data;
  } catch (error) {
    throw new Error(error.response.data.detail || 'User not found');
  }
};
const getUsersByIds = async (userIds) => {
  if (!userIds.length) return []; // Si le tableau est vide, retourner un tableau vide

  try {
    const userRequests = userIds.map((id) =>
      axiosInstance.get(`/user/${id}`).catch((error) => ({ error }))
    ); // Capturez les erreurs individuellement
    const usersResponses = await Promise.all(userRequests);

    return usersResponses
      .map((response) => (response.error ? null : response.data))
      .filter((user) => user); // Ignorez les réponses nulles
  } catch (error) {
    //console.error('Erreur lors de la récupération des utilisateurs : ', error);
    throw error; // Propager l'erreur
  }
};

const addUserToStore = async () => {
  try {
    // Verify if user is already in store
    const user = store.getters['getUser'];
    if (user && user.id && user.email) {
      return;
    }

    // If user is not in store, get it and save it
    // ⚠️ getUser directement dans le store. pour éviter un await ici
    let userData = await getUser();
    if (!userData || !userData.type_user) {
      return;
    }
    if (userData.type_user === 'recruiter') {
      const recruteurFavorites = await isCandidateFavoriteById();
      userData = { ...userData, recruteurFavorites };
    }
    store.dispatch('handleUserChange', { type: null, payload: userData });
    store.dispatch('handleUserRoleChange', userData.type_user);
  } catch (error) {
    //console.error('Error adding user to store:', error);
  }
};

export {
  checkAuthStatus,
  addUserToStore,
  deleteAccount,
  getUser,
  getUserById,
  getUsersByIds,
  googleLogin,
  login,
  logout,
  register,
};
