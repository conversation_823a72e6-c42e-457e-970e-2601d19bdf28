import { axiosInstance } from './axios';

/**
 *
 *
 * @returns entretien
 */
const createEntretien = async (data) => {
  try {
    const response = await axiosInstance.post(`/create_event/`, data, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    //console.error(`Erreur lors de la création de l'entretien`, error);
    throw error;
  }
};

/**
 *
 * @param {int} recruiterId
 * @returns entretiens
 */
const getEntretiensListById = async (recruiterId) => {
  try {
    const response = await axiosInstance.get(`/list_dates/${recruiterId}`);
    return response.data;
  } catch (error) {
    //console.error(
    //  `Erreur lors de la récupération de la liste des dates pour le recruteur id ${recruiterId}`,
    //  error
    //);
    throw error;
  }
};

/**
 *
 *
 * @param {int} dateId
 * @returns entretiens
 */
const acceptEntretien = async (dateId, data) => {
  try {
    const response = await axiosInstance.post(
      `/update_job_and_candidates/${dateId}/`,
      data,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    return response.data;
  } catch (error) {
    //console.error(`Erreur lors de l'acceptation d'une date ${dateId}:`, error);

    throw error;
  }
};

export { createEntretien, getEntretiensListById, acceptEntretien };
