# Guide d'intégration des améliorations d'appel vidéo

Ce guide explique comment intégrer les améliorations du service d'appel vidéo dans votre application.

## 1. Remplacer les fonctions existantes

Dans le fichier `v2/src/services/video-call.service.js`, remplacez les fonctions suivantes par celles du fichier `video-call-improvements.js` :

- `endCall`
- `resetCallState`
- `setupMessageHandler`

## 2. Ajouter les nouvelles fonctions

Ajoutez les nouvelles fonctions suivantes à `v2/src/services/video-call.service.js` :

- `stopAllMediaStreams`
- `setupAutoHangupListeners`

## 3. Mettre à jour l'export

Mettez à jour l'export à la fin du fichier `v2/src/services/video-call.service.js` pour inclure les nouvelles fonctions :

```javascript
export {
  initiateVideoCall,
  handleIncomingCall,
  acceptCall,
  rejectCall,
  endCall,
  initializeCallListener,
  resetCallState,
  stopAllMediaStreams,
  setupAutoHangupListeners,
  // autres fonctions existantes...
};
```

## 4. Initialiser les écouteurs de raccrochage automatique

Dans le fichier `v2/src/App.vue`, ajoutez l'initialisation des écouteurs de raccrochage automatique dans la méthode `mounted` :

```javascript
// Importer la fonction
import { setupAutoHangupListeners } from '@/services/video-call.service';

export default {
  // ...
  mounted() {
    // Code existant...
    
    // Configurer les écouteurs pour le raccrochage automatique
    if (this.$store.state.isLoggedIn) {
      setupAutoHangupListeners();
    }
  }
};
```

## 5. Utiliser stopAllMediaStreams dans les composants d'appel

Dans tous les composants qui gèrent les appels vidéo, utilisez la fonction `stopAllMediaStreams` pour arrêter les flux média lorsqu'un appel est terminé :

```javascript
// Importer la fonction
import { stopAllMediaStreams } from '@/services/video-call.service';

// Dans la méthode qui gère la fin d'appel
methods: {
  handleCallEnded() {
    // Arrêter tous les flux média
    stopAllMediaStreams();
    
    // Autres actions...
  }
}
```

## Résumé des améliorations

Ces modifications apportent les améliorations suivantes :

1. **Raccrochage automatique** lors du changement d'onglet/page
2. **Arrêt complet des flux média** (caméra et microphone) lors du raccrochage
3. **Meilleure détection des messages d'appel** entrants

## Test des améliorations

Pour tester ces améliorations :

1. Effectuez un appel entre deux utilisateurs
2. Vérifiez que l'appel est automatiquement raccroché lorsque vous changez de page ou d'onglet
3. Vérifiez que la caméra et le microphone sont bien arrêtés après avoir raccroché (l'indicateur de caméra/micro dans le navigateur devrait disparaître)
4. Vérifiez que les appels entrants sont correctement détectés, même sur des pages autres que la messagerie
