import store from '@/store';
import { toaster } from '@/utils/toast/toast.js';
import { axiosInstance } from './axios';

/**
 * Update user information in database
 * @param {Object} data
 * @return {void}
 * @throws {Error}
 */
const updateUserInformations = async (data) => {
  try {
    //  Create a FormData object to send the data
    let formData = new FormData();
    formData.append('metier', data.metier);
    formData.append('first_name', data.first_name);
    formData.append('last_name', data.last_name);
    formData.append('email', data.email);
    formData.append('numberPhone', data.numberPhone);
    formData.append('about', data.about);
    formData.append('contractType', data.contractType);
    //  Only append URL fields if they are valid and not empty
    if (data.site_url != null) formData.append('site_url', data.site_url);
    if (data.linkedin != null) formData.append('linkedin', data.linkedin);
    if (data.porfolio_url != null)
      formData.append('porfolio_url', data.porfolio_url);
    if (data.autre_url != null) formData.append('autre_url', data.autre_url);
    if (data.instagram != null) formData.append('instagram', data.instagram);
    if (data.tiktok != null) formData.append('tiktok', data.tiktok);
    if (data.facebook != null) formData.append('facebook', data.facebook);

    formData.append('ville', data.ville);
    formData.append('code_postal', data.code_postal);

    //  Add the photo only if it's a file (not a string or URL)
    if (data.photo && typeof data.photo !== 'string') {
      formData.append('photo', data.photo);
    }
    //console.log(...formData);
    //  Send the data using axios
    const response = await axiosInstance.put('/user/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data', // Ensure the content type is set correctly
      },
    });
    toaster.showSuccessPopup('Informations sauvergardées.');
  } catch (error) {
    //console.log('Erreur lors de la sauvegarde des données.', error);
    toaster.showErrorPopup('Erreur lors de la sauvegarde des données.');
  }
};

const insertSelfApplciation = async (data) => {
  try {
    console.log(data);
    const payload = {
      nom_job: data.nom_job,
      experience: data.experience,
      contrat: data.contrat,
      presence: data.presence,
      desactive: false,
      lm: data.lm,
      user: data.user, // ID ou email, selon l'API
      cv: data.cv, // ID du CV ou URL si le backend l'accepte
    };

    const response = await axiosInstance.post('/auto-postuler/', payload, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    toaster.showSuccessPopup('Informations sauvegardées.');
  } catch (error) {
    console.error('❌ Erreur de requête :', error?.response?.data || error);
    toaster.showErrorPopup('Erreur lors de la sauvegarde des données.');
  }
};

const updateSelfApplciation = async (id, data) => {
  try {
    console.log('dododo', id);
    const payload = {
      nom_job: data.nom_job,
      experience: data.experience,
      contrat: data.contrat,
      presence: data.presence,
      desactive: false,
      lm: data.lm,
      user: data.user, // ID ou email, selon l'API
      cv: data.cv, // ID du CV ou URL si le backend l'accepte
    };

    const response = await axiosInstance.put(`/auto-postuler/${id}/`, payload, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    toaster.showSuccessPopup('Informations modifiées.');
  } catch (error) {
    console.error('❌ Erreur de requête :', error?.response?.data || error);
    toaster.showErrorPopup('Erreur lors de la sauvegarde des données.');
  }
};

const getSelfApplications = async () => {
  try {
    const response = await axiosInstance.get('/auto-postuler/', {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Tu peux logguer ou retourner les données récupérées
    console.log('Postulations récupérées :', response.data);
    return response.data;
  } catch (error) {
    console.error(
      '❌ Erreur lors de la récupération des postulations :',
      error?.response?.data || error
    );
    toaster.showErrorPopup('Erreur lors de la récupération des postulations.');
    return [];
  }
};

const getSelfApplication = async (id) => {
  try {
    const response = await axiosInstance.get(`/auto-postuler/${id}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    return response.data;
  } catch (error) {
    console.error(
      '❌ Erreur lors de la récupération des postulations :',
      error?.response?.data || error
    );
    toaster.showErrorPopup('Erreur lors de la récupération des postulations.');
    return [];
  }
};

const deleteSelfApplications = async (id) => {
  try {
    const response = await axiosInstance.delete(`/auto-postuler/${id}/`);

    // Tu peux logguer ou retourner les données récupérées
    console.log('Postulation Supprimée :', response.data);
    toaster.showSuccessPopup('Postulation Supprimée.');
  } catch (error) {
    console.error(
      '❌ Erreur lors de la récupération des postulations :',
      error?.response?.data || error
    );
    toaster.showErrorPopup('Erreur lors de la récupération des postulations.');
  }
};

/**
 * Update company information in database, ignoring identical values
 * @param {Object} data - The new company data to update
 * @return {void}
 * @throws {Error}
 */
const updateCompanyInformations = async (data) => {
  try {
    // Récupérer les informations actuelles de l'utilisateur
    const user = store.getters.getUser;

    // Créer un nouvel objet pour les informations de l'entreprise à mettre à jour
    let newCompanyInfos = {};

    // Liste des champs à comparer
    const fieldsToCompare = {
      mail: 'email',
      phone: 'numberPhone',
      website: 'site_url',
      linkedin: 'linkedin',
      facebook: 'facebook',
      instagram: 'instagram',
      tiktok: 'tiktok',
      photo: 'photo',
      company: 'company',
      adress: 'adress',
      siret: 'siret',
      resume: 'résumé',
      postalCode: 'code_postal',
      city: 'ville',
      logo_img: 'logo_img',
    };

    // Comparer chaque champ et n'inclure que les champs modifiés
    for (const [dataKey, userKey] of Object.entries(fieldsToCompare)) {
      if (data[dataKey] !== user[userKey]) {
        newCompanyInfos[userKey] = data[dataKey];
      }
    }

    // Ne pas inclure la photo si elle n'a pas été modifiée
    if (typeof data.photo !== 'string') newCompanyInfos.photo = data.photo;

    // Vérifiez également si logo_img a été modifié avant l'enregistrement
    if (typeof data.logo_img !== 'string')
      newCompanyInfos.logo_img = data.logo_img;

    // Mettre à jour les informations de l'utilisateur dans la base de données
    await axiosInstance.put('/user/', newCompanyInfos, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    // Mettre à jour les informations de l'utilisateur dans le store pour une utilisation locale
    const updatedUser = { ...user, ...newCompanyInfos };
    store.dispatch('handleUserChange', { type: null, payload: updatedUser });

    // Afficher une notification de succès
    toaster.showSuccessPopup('Informations sauvegardées.');
  } catch (error) {
    // Gérer les erreurs
    //console.log('Erreur lors de la sauvegarde des données.', error);
    toaster.showErrorPopup('Erreur lors de la sauvegarde des données.');
  }
};

/**
 * Update company information in database, ignoring identical values
 * @param {Object} data - The new company data to update
 * @return {void}
 * @throws {Error}
 */
const updateCompanyProfile = async (data) => {
  try {
    // Récupérer les informations actuelles de l'utilisateur
    const user = store.getters.getUser;

    // Validation des champs spécifiques
    if (data.about && data.about.length > 500) {
      throw new Error(
        "Le champ 'À propos' ne doit pas dépasser 500 caractères."
      );
    }

    if (data.rejoindre && data.rejoindre.length > 1550) {
      throw new Error(
        "Le champ 'Pourquoi nous rejoindre' ne doit pas dépasser 1550 caractères."
      );
    }

    if (
      data.processus_recrutement &&
      data.processus_recrutement.length > 1550
    ) {
      throw new Error(
        "Le champ 'Processus de recrutement' ne doit pas dépasser 1550 caractères."
      );
    }

    // Mettre à jour les informations de l'utilisateur dans la base de données
    await axiosInstance.put('/user/', data, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    const updatedUser = {
      ...user,
      ...data,
    };

    // Mettre à jour les informations de l'utilisateur dans le store pour une utilisation locale
    store.dispatch('handleUserChange', {
      type: null,
      payload: updatedUser,
    });
    // Afficher une notification de succès
    toaster.showSuccessPopup('Informations sauvegardées.');
  } catch (error) {
    // Gérer les erreurs
    //console.log('Erreur lors de la sauvegarde des données.', error);
    // Messages d'erreur détaillés
    if (error.message.includes('caractères')) {
      toaster.showErrorPopup(error.message);
    } else if (error.response) {
      // Erreurs côté serveur
      toaster.showErrorPopup(
        `Erreur serveur : ${error.response.statusText} (${error.response.status})`
      );
    } else {
      // Autres erreurs
      toaster.showErrorPopup(
        'Une erreur imprévue est survenue. Veuillez réessayer plus tard.'
      );
    }
  }
};

/**
 * Update user criterias in database
 * @param {Object} data
 * @return {void}
 * @throws {Error}
 */
const updateUserCriterias = async (data) => {
  try {
    let newUserInfos = {
      wanted_job: data.wanted_job,
      contrat: data.contrat,
      experiences: data.experiences,
      salaire_souhaite: data.salaire_souhaite,
      teletravail: data.teletravail,
      secteur: data.secteur,
      // selectedCV: data.selectedCV,
      // lettreMotivation: date.lettreMotivation
    };
    await axiosInstance.put('/user/', newUserInfos);
    toaster.showSuccessPopup('Informations sauvergardées.');
  } catch (error) {
    //console.log('Erreur lors de la sauvegarde des données.', error);
    toaster.showErrorPopup('Erreur lors de la sauvegarde des données.');
  }
};

const deleteLanguage = async (languageId) => {
  try {
    await axiosInstance.delete(`/langue/${languageId}/`); // Assurez-vous que l'URL correspond à votre configuration
  } catch (error) {
    //console.error(
    //  'Erreur lors de la suppression de la langue:',
    //  error.response ? error.response.data : error.message
    //);
    toaster.showErrorPopup('Erreur lors de la suppression de la langue.');
  }
};

const getSkillById = async (id) => {
  try {
    const response = await axiosInstance.get(`/skill/${id}`);
    return response.data;
  } catch (error) {
    //console.error(
    //  `Erreur lors de la récupération de la compétence avec ID: ${id}`,
    //  error
    //);
    return null;
  }
};

/**
 * get user languages
 * @return {Array}
 */

const getAllSkills = async () => {
  try {
    const response = await axiosInstance.get('/skill/');
    const skills = response.data;
    return skills;
  } catch (error) {
    //console.log('Erreur lors de la récupération des compétences.', error);
    return [];
  }
};
/**
 * get user skills section informations from vuex
 * @return {Array}
 */
// TODO : verifier l'utilite de cette fonction...
const getUserSkillsSectionDatas = () => {
  const userInfos = getUser();
  let formData = {};

  formData.skillsList = userInfos.skill.map((skill) => ({
    id: skill.id,
    nom: skill.nom,
  }));
  formData.mobilityList = userInfos.mobilité;
  formData.permisList = userInfos.permis;
  formData.langueList = userInfos.langue;

  //updateUserSkills(formData);
  return formData;
};

/**
 * Update user skills in database
 * @param {Object} data
 * @return {void}
 * @throws {Error}
 */
const updateUserSkills = async (data) => {
  try {
    let user = getUser();

    const currentUserLangues = user.langue;
    // Determiner les langues à supprimer
    const languesToRemove = currentUserLangues.filter(
      (langue) => !data.langue.includes(langue.langue)
    );
    // Determiner les langues à ajouter
    const languesToAdd = data.langue.filter(
      (langue) =>
        !currentUserLangues.some((userLangue) => userLangue.langue === langue)
    );
    if (languesToRemove.length > 0) {
      await Promise.all(
        languesToRemove.map((langue) => deleteLanguage(langue.id))
      );
    }
    if (languesToAdd.length > 0) {
      await Promise.all(
        languesToAdd.map((langue) =>
          postLanguage({ langue: langue, niveau: 'null' })
        )
      );
    }

    const fetchUserLangues = async () => {
      try {
        const response = await axiosInstance.get('/langue/');
        return response.data.results;
      } catch (error) {
        //console.error(
        //  'Erreur lors de la récupération des langues existantes:',
        //  error
        //);
        toaster.showErrorPopup('Erreur lors de la récupération des langues.');
      }
    };

    // Langues sont trates en dehors de la requête /user/
    // pour cette requête le nom de cle c'est 'skills' (pluriel)
    const newUserInfos = {
      mobilité: data.mobilité,
      permis: data.permis,
      skills: data.updatedSkills.map((skill) => skill.id),
    };
    console.log('new user');
    console.log(newUserInfos);
    await axiosInstance.put('/user/', newUserInfos);

    //  dans le store le nom de cle c'est 'skill' (singulier)
    let updatedUser = {
      ...user,
      skill: data.updatedSkills,
      mobilité: data.mobilité,
      permis: data.permis,
    };
    console.log(updatedUser);
    // Si les langues sont changes on les recupere pour avoir les ids.
    if (languesToAdd.length > 0 || languesToRemove.length > 0) {
      const updatedUserLangues = await fetchUserLangues();
      updatedUser.langue = updatedUserLangues;
    }

    store.dispatch('handleUserChange', { type: null, payload: updatedUser });
    toaster.showSuccessPopup('Informations sauvegardées.');
  } catch (error) {
    //console.error(
    //  'Erreur lors de la sauvegarde des données:',
    //  error.response ? error.response.data : error.message
    //);
    toaster.showErrorPopup('Erreur lors de la sauvegarde des données.');
  }
};

/**
 * post CV to database
 * @param {Object} cv
 * @return {boolean}
 * @throws {Error}
 */
const postCV = async (cv) => {
  try {
    const formData = new FormData();
    formData.append('file', cv['cv']);
    formData.append('title', cv['title']);
    formData.append('default', cv['defaultCV']);

    const response = await axiosInstance.post('/user/cvs/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    toaster.showSuccessPopup('CV sauvegardé.');
    return true;
  } catch (error) {
    //throw new Error(error.response.data.detail || 'User information failed to update');
    //console.log('Erreur lors de la sauvegarde du CV.', error);
    toaster.showErrorPopup('Erreur lors de la sauvegarde du CV.');
    return false;
  }
};

/**
 * update CV statut
 * @param {number} cvIndex
 * @return {void}
 * @throws {Error}
 */
const updateCVStatut = async (cvId, isDefault) => {
  try {
    const response = await axiosInstance.post(
      `/user/cvs/toggle-default/${cvId}/`,
      { default: isDefault }
    );
    return true;
  } catch (error) {
    //console.error('Erreur lors de la mise à jour du CV:', error);
    return false;
  }
};

/**
 * delete CV from database
 * @param {number} cvIndex
 * @return {void}
 * @throws {Error}
 */
const deleteCV = async (cvIndex) => {
  try {
    const response = await axiosInstance.delete(`/user/cvs/${cvIndex}/`);
    toaster.showSuccessPopup('CV supprimé avec succès !');
    return true;
  } catch (error) {
    //throw new Error(error.response.data.detail || 'User information failed to update');
    //console.log('Erreur lors de la sauvegarde du CV.', error);
    toaster.showErrorPopup('Erreur lors de la suppression du CV.');
    return false;
  }
};

/**
 * post formation to database
 * @param {Object} formation
 * @return {Promise}
 * @throws {Error}
 */
const postFormation = async (formation) => {
  try {
    // Format objet pour correspondre à la structure de la base de données
    let newFormation = {
      titre: formation.title,
      etude: formation.degree,
      organisme: formation.school,
      obtention: formation.date,
    };

    // Vérifier si un ID est présent pour déterminer si c'est une mise à jour
    let response;
    if (formation.id) {
      // Mise à jour
      response = await axiosInstance.put(
        `/formations/${formation.id}/`,
        newFormation
      );
    } else {
      // Création
      response = await axiosInstance.post('/formations/', newFormation);
    }

    toaster.showSuccessPopup('Formation sauvegardée.');
    return response.data;
  } catch (error) {
    //console.log('Erreur lors de la sauvegarde de la formation.', error);
    toaster.showErrorPopup('Erreur lors de la sauvegarde de la formation.');
    return false;
  }
};

/**
 * delete formation from database
 * @param {number} formationIndex
 * @return {void}
 * @throws {Error}
 */
const deleteFormation = async (formationIndex) => {
  try {
    const response = await axiosInstance.delete(
      `/formations/${formationIndex}/`
    );
    toaster.showSuccessPopup('Formation supprimée avec succès !');
    return true;
  } catch (error) {
    //throw new Error(error.response.data.detail || 'User information failed to update');
    //console.log('Erreur lors de la suppression de la Formation.', error);
    toaster.showErrorPopup('Erreur lors de la suppression de la Formation.');
    return false;
  }
};

/**
 * post experience to database
 * @param {Object} experience
 * @return {Promise}
 * @throws {Error}
 */
const postExperience = async (experience) => {
  try {
    //  format objet for db match
    let newExperience = {
      position: experience.position,
      company: experience.company,
      place: experience.place,
      debut: experience.debut,
      fin: experience.fin,
      exp_detail: experience.exp_detail,
      resume: null, //  field dont exist in actual version
    };

    let response;
    if (experience.id) {
      response = await axiosInstance.put(
        `/experience/${experience.id}/`,
        newExperience
      );
    } else {
      response = await axiosInstance.post('/experience/', newExperience);
    }
    toaster.showSuccessPopup('Expérience sauvegardée.');
    return response.data;
  } catch (error) {
    //throw new Error(error.response.data.detail || 'User information failed to update');
    //console.log('Erreur lors de la sauvegarde de votre expérience.', error);
    toaster.showErrorPopup('Erreur lors de la sauvegarde de votre expérience.');
    return false;
  }
};

/**
 * delete experience from database
 * @param {number} experienceIndex
 * @return {void}
 * @throws {Error}
 */
const deleteExperience = async (experienceIndex) => {
  try {
    await axiosInstance.delete(`/experience/${experienceIndex}/`);
    toaster.showSuccessPopup('Expérience supprimée avec succès !');
    return true;
  } catch (error) {
    //throw new Error(error.response.data.detail || 'User information failed to update');
    //console.log('Erreur lors de la suppression de votre expérience.', error);
    toaster.showErrorPopup(
      'Erreur lors de la suppression de votre expérience.'
    );
    return false;
  }
};

/**
 * get cv with axios
 * @return {Blob}
 */
const getCvBlob = async () => {
  try {
    const response = await axiosInstance.get(`/user/cvs/`, {
      responseType: 'blob',
    });
    if (response.data.size === 52) {
      toaster.showErrorPopup('Aucun CV trouvé.');
      return false;
    }
    toaster.showSuccessPopup('CV importé avec succès.');
    return response.data;
  } catch (error) {
    //throw new Error(error.response.data.detail || 'User information failed to update');
    //console.log("Erreur lors de l'importation du CV", error);
    toaster.showErrorPopup("Erreur lors de l'importation du CV");
    return false;
  }
};

/**
 * verifier si le cv existe
 * @return {true/false}
 */
const verifyCVuploaded = async () => {
  try {
    const response = await axiosInstance.get(`/user/cvs/`, {
      responseType: 'blob',
    });
    if (response.data.size === 52) {
      return false;
    }
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * parse cv with server api endpoint
 * @return {JSON}
 */
const parseCvBlob = async (cvBlob) => {
  try {
    const formData = new FormData();
    formData.append('resume', cvBlob);
    await axiosInstance.post('/resume_parser/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    toaster.showSuccessPopup('Informations du CV récupérées avec succès.');
  } catch (error) {
    //throw new Error(error.response.data.detail || 'User information failed to update');
    //console.log('Erreur lors de la récupération des informations du CV', error);
    toaster.showErrorPopup(
      'Erreur lors de la récupération des informations du CV'
    );
    return false;
  }
};

/**
 * get cv list from vuex
 * @return {Array}
 */
const getCvList = () => {
  const user = getUser();
  return user.cvs;
};

/**
 * get formation list from vuex
 * @return {Array}
 */
const getFormationList = () => {
  const user = getUser();
  return user.formation;
};

/**
 * get experience list from vuex
 * @return {Array}
 */
const getExperienceList = () => {
  const user = getUser();
  return user.experience;
};

/**
 * get user profile section informations from vuex
 * @return {Object}
 */
// TODO : supprimer cette fonction, elle ne serve a rien et cause des erreur par tout
const getUserProfilSectionDatas = () => {
  const userInfos = store.getters.getUser;
  let formData = {};
  formData.metier = userInfos.metier;
  formData.contractType = userInfos.contractType;
  formData.first_name = userInfos.first_name;
  formData.last_name = userInfos.last_name;
  formData.email = userInfos.email;
  formData.numberPhone = userInfos.numberPhone;
  formData.about = userInfos.about;
  formData.linkedin = userInfos.linkedin;
  formData.porfolio_url = userInfos.porfolio_url;
  formData.autre_url = userInfos.autre_url;
  formData.site_url = userInfos.site_url;
  formData.ville = userInfos.ville;
  formData.code_postal = userInfos.code_postal;
  formData.photo = userInfos.photo;
  formData.company = userInfos.company;
  formData.adress = userInfos.adress;
  formData.siret = userInfos.siret;
  formData.secteur = userInfos.secteur;
  formData.taille = userInfos.taille;
  formData.rejoindre = userInfos.rejoindre;
  formData.résumé = userInfos.résumé;
  formData.company_creation_year = userInfos.company_creation_year;
  formData.processus_recrutement = userInfos.processus_recrutement;
  formData.avantages =
    userInfos.avantages.length > 0 ? userInfos.avantages?.split(',') : [];
  formData.facebook = userInfos.facebook;
  formData.instagram = userInfos.instagram;
  formData.tiktok = userInfos.tiktok;
  formData.logo_img = userInfos.logo_img || '';
  return formData;
};

const getUserImgData = () => {
  const userInfos = getUser();
  return userInfos.photo;
};
/**
 * get user criterias section informations from vuex
 * @return {Object}
 */
const getUserCriteriasSectionDatas = () => {
  const userInfos = store.getters.getUser;
  let formData = {};
  formData.wanted_job = userInfos.wanted_job;
  formData.contrat = userInfos.contrat;
  formData.experiences = userInfos.experiences;
  formData.salaire_souhaite = userInfos.salaire_souhaite;
  formData.teletravail = userInfos.teletravail;
  formData.secteur = userInfos.secteur;

  return formData;
};

/**
 * get user languages
 * @return {Array}
 */

const getAllLanguages = async () => {
  const userInfos = getUser();
  try {
    const response = await axiosInstance.get('/langue/').then((res) => res);
    const languages = response.data.results;
    const langues = languages.filter(
      (language) => language.user === userInfos.email
    );
    return langues;
  } catch (error) {
    //console.log('Erreur lors de la récupération des langues.', error);
    return [];
  }
};

/**
 * post language to database
 * @param {Object} language
 * @return {Promise}
 * @throws {Error}
 */
const postLanguage = async (language) => {
  try {
    // Format l'objet pour correspondre au modèle de la base de données
    let newLanguage = {
      langue: language.langue,
      niveau: language.niveau,
    };

    const response = await axiosInstance.post('/langue/', newLanguage);
    return response.data;
  } catch (error) {
    //console.error(
    //  'Erreur lors de la sauvegarde de la langue:',
    //  error.response ? error.response.data : error.message
    //);
    return false;
  }
};

const toggle_visibility = async () => {
  try {
    const token = localStorage.getItem('access_token');
    const response = await axiosInstance.post('/user/toggle-visibility/', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    //console.error('Erreur lors de l activation de la visibilité:', error);
    return [];
  }
};

/**
 * get user object from vuex (property style access)
 * @return {Object}
 */
const getUser = () => {
  return store.getters.getUser;
};

/**
 * Récupérer les avis concernant l'entreprise
 * @param {number} userId
 * @return {Promise<Object[]>}
 * @throws {Error}
 */
const getCompanyReviews = async (userId) => {
  try {
    const response = await axiosInstance.get(`/commentaires/user/${userId}`);
    return response.data.results;
  } catch (error) {
    //console.error('Erreur lors de la récupération des avis:', error);
    throw new Error(
      error.response ? error.response.data : 'failed to get company reviews'
    );
  }
};

const postReview = async (userId, reviewData) => {
  try {
    const response = await axiosInstance.post(
      `/commentaires/utilisateur/${userId}/`,
      {
        ...reviewData, // Les données du commentaire
        cible_id: userId, // Assurez-vous d'envoyer l'ID de la cible
      }
    );
    return response.data;
  } catch (error) {
    //console.error(
    //  "Erreur lors de l'ajout du commentaire :",
    //  error.response || error
    //);
    throw error;
  }
};

// cvperso --> theme & sections
const postCvPerso = async (sectionsOrder, themeId) => {
  try {
    const response = await axiosInstance.post(`/user/cvperso/`, {
      sections_order: sectionsOrder,
      theme: themeId,
    });

    //console.log('post sectionsOrder', response.data.sections_order);
    //console.log('post theme', response.data.theme);
    return response.data;
  } catch (error) {
    //console.error("Erreur lors de l'enregistrement du cvperso :", error);
    throw error;
  }
};

export {
  deleteCV,
  deleteExperience,
  deleteFormation,
  getAllLanguages,
  getAllSkills,
  getCompanyReviews,
  getCvBlob,
  getCvList,
  getExperienceList,
  getFormationList,
  getSkillById,
  getUser,
  getUserCriteriasSectionDatas,
  getUserImgData,
  getUserProfilSectionDatas,
  getUserSkillsSectionDatas,
  parseCvBlob,
  postCV,
  postExperience,
  postFormation,
  postLanguage,
  postReview,
  toggle_visibility,
  updateCompanyInformations,
  updateCompanyProfile,
  updateCVStatut,
  updateUserCriterias,
  updateUserInformations,
  insertSelfApplciation,
  updateSelfApplciation,
  getSelfApplications,
  deleteSelfApplications,
  getSelfApplication,
  updateUserSkills,
  verifyCVuploaded,
  postCvPerso,
};
