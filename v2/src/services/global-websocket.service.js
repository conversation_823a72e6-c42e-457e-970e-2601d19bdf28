import store from '@/store';
import { initializePrivateWebSocket } from './conversation-websocket.service';

// Référence globale au WebSocket privé
let globalPrivateWebSocket = null;
let reconnectInterval = null;
let checkConnectionInterval = null;

/**
 * Initialise le WebSocket privé global pour l'application
 * Cette fonction doit être appelée au démarrage de l'application
 * @returns {WebSocket} - La connexion WebSocket privée
 */
const initializeGlobalPrivateWebSocket = () => {
  try {
    //console.log('%c[WEBSOCKET] Initialisation du WebSocket privé global...', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');

    // Vérifier si l'utilisateur est connecté
    const currentUser = store.getters.getUser;
    if (!currentUser || !currentUser.id) {
      //console.error('%c[WEBSOCKET] Utilisateur non connecté, impossible d\'initialiser le WebSocket privé global', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');
      return null;
    }

    // Nettoyer les intervalles existants
    if (reconnectInterval) {
      clearInterval(reconnectInterval);
      reconnectInterval = null;
    }

    if (checkConnectionInterval) {
      clearInterval(checkConnectionInterval);
      checkConnectionInterval = null;
    }

    // Initialiser le WebSocket privé
    const webSocket = initializePrivateWebSocket();
    if (!webSocket) {
      //console.error('%c[WEBSOCKET] Échec de l\'initialisation du WebSocket privé global', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;');

      // Planifier une nouvelle tentative
      reconnectInterval = setTimeout(() => {
        //console.log('%c[WEBSOCKET] Nouvelle tentative d\'initialisation du WebSocket privé global...', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
        initializeGlobalPrivateWebSocket();
      }, 5000);

      return null;
    }

    //console.log('%c[WEBSOCKET] WebSocket privé global initialisé avec succès', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
    globalPrivateWebSocket = webSocket;

    // Configurer la reconnexion automatique en cas de déconnexion
    webSocket.onclose = () => {
      //console.log('%c[WEBSOCKET] WebSocket privé global fermé, tentative de reconnexion...', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');
      globalPrivateWebSocket = null;

      // Attendre un court instant avant de tenter de se reconnecter
      reconnectInterval = setTimeout(() => {
        initializeGlobalPrivateWebSocket();
      }, 3000);
    };

    // Configurer la gestion des erreurs
    webSocket.onerror = (error) => {
      //console.error('%c[WEBSOCKET] Erreur sur le WebSocket privé global:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
    };

    // Vérifier périodiquement que le WebSocket est toujours connecté
    checkConnectionInterval = setInterval(() => {
      if (!globalPrivateWebSocket || globalPrivateWebSocket.readyState !== WebSocket.OPEN) {
        //console.log('%c[WEBSOCKET] WebSocket privé global déconnecté, tentative de reconnexion...', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');
        initializeGlobalPrivateWebSocket();
      }
    }, 30000); // Vérifier toutes les 30 secondes

    return webSocket;
  } catch (error) {
    //console.error('%c[WEBSOCKET] Erreur lors de l\'initialisation du WebSocket privé global:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);

    // Planifier une nouvelle tentative
    reconnectInterval = setTimeout(() => {
      //console.log('%c[WEBSOCKET] Nouvelle tentative d\'initialisation du WebSocket privé global après erreur...', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
      initializeGlobalPrivateWebSocket();
    }, 5000);

    return null;
  }
};

/**
 * Vérifie l'état du WebSocket privé global et le réinitialise si nécessaire
 * @returns {WebSocket} - La connexion WebSocket privée
 */
const getGlobalPrivateWebSocket = () => {
  try {
    // Vérifier si le WebSocket est déjà initialisé et ouvert
    if (globalPrivateWebSocket && globalPrivateWebSocket.readyState === WebSocket.OPEN) {
      //console.log('%c[WEBSOCKET] WebSocket privé global déjà initialisé et ouvert', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
      return globalPrivateWebSocket;
    }

    // Si le WebSocket est en cours de connexion, attendre
    if (globalPrivateWebSocket && globalPrivateWebSocket.readyState === WebSocket.CONNECTING) {
      //console.log('%c[WEBSOCKET] WebSocket privé global en cours de connexion, attente...', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');
      return null;
    }

    //console.log('%c[WEBSOCKET] WebSocket privé global non disponible, réinitialisation...', 'background: orange; color: black; padding: 2px 5px; border-radius: 3px;');

    // Réinitialiser le WebSocket privé global
    return initializeGlobalPrivateWebSocket();
  } catch (error) {
    //console.error('%c[WEBSOCKET] Erreur lors de la récupération du WebSocket privé global:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
    return null;
  }
};

export {
  initializeGlobalPrivateWebSocket,
  getGlobalPrivateWebSocket
};
