import { toaster } from '../utils/toast/toast';
import { axiosInstance } from './axios';

/**
 * getAlertById get alert from BD
 * @param {number} alertId
 * @return {Object}
 * @throws {Error}
 */
const getAlertById = async (alertId) => {
  try {
    const response = await axiosInstance.delete(`/alerte_profils/${alertId}`);
    return response.data;
  } catch (error) {
    //console.error("Erreur lors de la recuperation de l'alerte : ", error);
    toaster.showErrorPopup("Erreur lors de la recuperation de l'alerte.");
  }
};

/**
 * getAlertById get alert from user in store
 * @param {number} alertId
 * @return {Object}
 * @throws {Error}
 */
const getAlertByUser = async (userId) => {
  const alerts = await axiosInstance('/alerte/');

  //console.log('alertes', alerts);
  //  iterate through each alert
  //const userAlerts =

  throw new Error('Cannot retrieve alert with this id.');
};

const createNewProfileAlert = async (AlertData) => {
  try {
    const response = await axiosInstance.post('/alerte_profils/', AlertData);
    toaster.showSuccessPopup('Alerte créée.');
    return response;
  } catch (error) {
    // Gestion d'erreur spécifique pour les champs requis
    if (error.message.includes('champs requis')) {
      toaster.showErrorPopup(
        'Veuillez fournir un nom et un poste pour votre nouvelle alerte.'
      );
    }
    // Gestion d'erreur HTTP 403
    else if (error.response && error.response.status === 403) {
      toaster.showErrorPopup(
        'Veuillez vous inscrire ou vous connecter à votre compte pour continuer.'
      );
    }
    // Gestion des autres erreurs
    else {
      toaster.showErrorPopup("Erreur lors de la création de l'alerte.");
    }
  }
};

const deleteProfileAlert = async (alertId) => {
  try {
    const response = await axiosInstance.delete(`/alerte_profils/${alertId}`);

    toaster.showSuccessPopup('Alerte supprimée.');
    return response;
  } catch (error) {
    //console.error('error :', error);
    toaster.showErrorPopup("Erreur lors de la suppression de l'alerte.");
  }
};

const modifyProfileAlert = async (alertId, data) => {
  try {
    const response = await axiosInstance.put(
      `/alerte_profils/${alertId}/`,
      data
    );
    toaster.showSuccessPopup('Alerte modifiée.');
    return response;
  } catch (error) {
    toaster.showErrorPopup("Erreur lors de la modification de l'alerte.");
    return null;
  }
};

export {
  createNewProfileAlert,
  deleteProfileAlert,
  getAlertById,
  getAlertByUser,
  modifyProfileAlert,
};
