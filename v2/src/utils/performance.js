/**
 * Utilitaires pour améliorer les performances JavaScript
 */

/**
 * Exécute une fonction après un délai pour éviter les appels trop fréquents
 * @param {Function} func - Fonction à exécuter
 * @param {number} wait - <PERSON><PERSON><PERSON> en millisecondes
 * @param {boolean} immediate - Exécuter immédiatement
 * @returns {Function} - Fonction avec debounce
 */
export const debounce = (func, wait = 300, immediate = false) => {
  let timeout;
  
  return function executedFunction(...args) {
    const context = this;
    
    const later = () => {
      timeout = null;
      if (!immediate) func.apply(context, args);
    };
    
    const callNow = immediate && !timeout;
    
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func.apply(context, args);
  };
};

/**
 * Limite le nombre d'appels à une fonction dans un intervalle de temps
 * @param {Function} func - Fonction à exécuter
 * @param {number} limit - Intervalle en millisecondes
 * @returns {Function} - Fonction avec throttle
 */
export const throttle = (func, limit = 300) => {
  let inThrottle;
  
  return function executedFunction(...args) {
    const context = this;
    
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => {
        inThrottle = false;
      }, limit);
    }
  };
};

/**
 * Exécute une fonction de manière asynchrone pour ne pas bloquer le thread principal
 * @param {Function} func - Fonction à exécuter
 * @param {Array} args - Arguments de la fonction
 * @returns {Promise} - Promise qui résout avec le résultat de la fonction
 */
export const asyncExecution = (func, ...args) => {
  return new Promise((resolve) => {
    // Utiliser setTimeout avec délai 0 pour exécuter de manière asynchrone
    setTimeout(() => {
      const result = func(...args);
      resolve(result);
    }, 0);
  });
};

/**
 * Divise un tableau en lots pour traiter de grandes quantités de données
 * @param {Array} array - Tableau à diviser
 * @param {number} batchSize - Taille de chaque lot
 * @param {Function} callback - Fonction à exécuter pour chaque lot
 * @returns {Promise} - Promise qui résout quand tous les lots sont traités
 */
export const processBatch = async (array, batchSize = 100, callback) => {
  if (!array.length) return [];
  
  const results = [];
  
  for (let i = 0; i < array.length; i += batchSize) {
    const batch = array.slice(i, i + batchSize);
    // Traiter le lot et attendre avant de passer au suivant
    const batchResults = await callback(batch, i / batchSize);
    results.push(...batchResults);
    
    // Permettre au navigateur de respirer entre les lots
    await new Promise(resolve => setTimeout(resolve, 0));
  }
  
  return results;
};

/**
 * Mesure le temps d'exécution d'une fonction
 * @param {Function} func - Fonction à mesurer
 * @param {string} label - Étiquette pour l'affichage
 * @param {Array} args - Arguments de la fonction
 * @returns {any} - Résultat de la fonction
 */
export const measurePerformance = (func, label, ...args) => {
  console.time(label);
  const result = func(...args);
  console.timeEnd(label);
  return result;
};

/**
 * Mesure le temps d'exécution d'une fonction asynchrone
 * @param {Function} func - Fonction asynchrone à mesurer
 * @param {string} label - Étiquette pour l'affichage
 * @param {Array} args - Arguments de la fonction
 * @returns {Promise} - Promise qui résout avec le résultat de la fonction
 */
export const measureAsyncPerformance = async (func, label, ...args) => {
  console.time(label);
  const result = await func(...args);
  console.timeEnd(label);
  return result;
};

/**
 * Mémoïse une fonction pour éviter de recalculer des résultats déjà connus
 * @param {Function} func - Fonction à mémoïser
 * @returns {Function} - Fonction mémoïsée
 */
export const memoize = (func) => {
  const cache = new Map();
  
  return function memoized(...args) {
    const key = JSON.stringify(args);
    
    if (cache.has(key)) {
      return cache.get(key);
    }
    
    const result = func.apply(this, args);
    cache.set(key, result);
    
    return result;
  };
};

export default {
  debounce,
  throttle,
  asyncExecution,
  processBatch,
  measurePerformance,
  measureAsyncPerformance,
  memoize
};
