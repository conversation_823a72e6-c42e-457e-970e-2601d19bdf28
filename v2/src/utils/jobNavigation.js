import router from '@/router/index.js';
import { toaster } from '@/utils/toast/toast.js';

/**
 * Navigate to job offer detail page
 * Handles both internal and external job offers with proper error handling
 * @param {string|number} jobId - The job offer ID
 * @param {Object} job - Optional job object for additional context
 * @param {boolean} openInNewTab - Whether to open in new tab (default: false)
 * @returns {Promise<void>}
 */
export const navigateToJobOffer = async (jobId, job = null, openInNewTab = false) => {
  try {
    // Validate jobId
    if (!jobId) {
      console.error('Job ID is required for navigation');
      toaster.showErrorPopup('Erreur: ID de l\'offre manquant');
      return;
    }

    // Construct the job offer URL
    const jobUrl = `/offre-d-emplois/${jobId}`;
    
    // Handle external job offers (France Travail, etc.)
    if (job && job.source === 'FT' && job.external_url) {
      window.open(job.external_url, '_blank');
      return;
    }

    // Handle navigation based on preference
    if (openInNewTab) {
      window.open(jobUrl, '_blank');
    } else {
      // Use router for internal navigation with error handling
      try {
        await router.push(jobUrl);
        // Scroll to top after navigation
        window.scrollTo({ top: 0, behavior: 'smooth' });
      } catch (navigationError) {
        console.error('Router navigation failed, falling back to window.open:', navigationError);
        // Fallback to window.open if router navigation fails
        window.open(jobUrl, '_blank');
      }
    }
  } catch (error) {
    console.error('Error navigating to job offer:', error);
    toaster.showErrorPopup('Erreur lors de l\'ouverture de l\'offre');
  }
};

/**
 * Check if a job is from Thanks-Boss source and within priority period
 * @param {Object} job - Job object
 * @returns {boolean} - True if job should have priority styling
 */
export const isJobPriority = (job) => {
  if (!job || job.source !== 'Thanks_boss') {
    return false;
  }

  if (!job.created_at) {
    return false;
  }

  const createdDate = new Date(job.created_at);
  const currentDate = new Date();
  const daysDifference = Math.floor((currentDate - createdDate) / (1000 * 60 * 60 * 24));
  
  return daysDifference <= 14;
};

/**
 * Get priority class for job cards
 * @param {Object} job - Job object
 * @returns {string} - CSS class for priority styling
 */
export const getPriorityClass = (job) => {
  return isJobPriority(job) ? 'priority-job' : '';
};

/**
 * Sort jobs with priority logic
 * Thanks-Boss jobs within 14 days get priority in sorting
 * @param {Array} jobs - Array of job objects
 * @returns {Array} - Sorted array with priority jobs first
 */
export const sortJobsWithPriority = (jobs) => {
  if (!Array.isArray(jobs)) {
    return [];
  }

  return jobs.sort((a, b) => {
    const aPriority = isJobPriority(a);
    const bPriority = isJobPriority(b);

    // Priority jobs come first
    if (aPriority && !bPriority) return -1;
    if (!aPriority && bPriority) return 1;

    // If both have same priority status, sort by creation date (newest first)
    const aDate = new Date(a.created_at || 0);
    const bDate = new Date(b.created_at || 0);
    return bDate - aDate;
  });
};

export default {
  navigateToJobOffer,
  isJobPriority,
  getPriorityClass,
  sortJobsWithPriority
};
