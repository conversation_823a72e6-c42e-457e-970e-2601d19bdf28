export const getNormalizedPath = (id1, id2) => {
  const [minId, maxId] = [id1, id2].sort((a, b) => a - b);
  return `${minId}/${maxId}/`;
};

export const initializeChatWebSocket = (senderId, receiverId) => {
  try {
    // Utilisation de l'endpoint privé au lieu de l'endpoint global
    const url = `wss://websocket.thanks-boss.com/ws/chat/private/${senderId}/`;
    const webSocket = new WebSocket(url);

    webSocket.onopen = () => {
      //console.log('WebSocket privé ouvert');

      try {
        // Envoyer un message d'initialisation pour les appels vidéo
        const initCallMessage = {
          type: 'init_call_connection',
          sender_id: senderId,
          receiver_id: receiverId
        };
        webSocket.send(JSON.stringify(initCallMessage));
        //console.log('Message d\'initialisation d\'appel envoyé:', initCallMessage);
      } catch (sendError) {
        //console.error('Erreur lors de l\'envoi du message d\'initialisation:', sendError);
      }
    };

    webSocket.onerror = (error) => {
      //console.error('Error de WebSocket:', error);
    };
    webSocket.onclose = () => {
      //console.log('WebSocket ferme');
    };

    return { webSocket };
  } catch (error) {
    //console.error('Erreur lors de la initialisation du webSocket', error);
    return { webSocket: null };
  }
};

// Ce websocket est utilisé pour écouter les notifications de l'utilisateur et pour definir son état de connection
let retryCount = 0;
export const handleWebSocketUserConnection = (id) => {
  const maxRetries = 3; // Maximum des essayes de connection
  const retryDelay = 3000; // Delay pour les assay de connection

  const connectWebSocket = () => {
    try {
      // Utilisation de l'endpoint privé au lieu de l'endpoint global
      const url = `wss://websocket.thanks-boss.com/ws/chat/private/${id}/`;
      const webSocket = new WebSocket(url);

      webSocket.onopen = () => {
        //console.log('WebSocket privé ouvert');
        retryCount = 0;
        
        // Le reste du code reste inchangé car l'ID est maintenant dans l'URL
        // et l'initialisation se fait automatiquement côté serveur
        try {
          // Envoyer un message pour activer les notifications d'appel
          const initCallNotificationsMessage = {
            type: 'init_call_notifications',
            user_id: id
          };
          webSocket.send(JSON.stringify(initCallNotificationsMessage));
          //console.log('Message d\'activation des notifications d\'appel envoyé:', initCallNotificationsMessage);

          // Importer dynamiquement le service d'appel vidéo
          import('../services/video-call.service')
            .then(({ initializeCallListener }) => {
              initializeCallListener();
              //console.log('Écouteur d\'appels initialisé sur le WebSocket privé');
            })
            .catch(error => {
              //console.error('Erreur lors de l\'initialisation de l\'écouteur d\'appels:', error);
            });
        } catch (sendError) {
          //console.error('Erreur lors de l\'envoi du message d\'initialisation:', sendError);
        }
      };

      webSocket.onerror = (error) => {
        //console.error('Error de WebSocket:', error);
      };

      webSocket.onclose = () => {
        //console.log('WebSocket fermé');
        if (retryCount < maxRetries) {
          //console.log(`Reconnexion du WebSocket en ${retryDelay / 1000}s...`);
          setTimeout(() => {
            retryCount++;
            connectWebSocket();
          }, retryDelay);
        } else {
          //console.error('Max des reconnections dépassé.');
        }
      };

      return { webSocket };
    } catch (error) {
      //console.error('Erreur lors de l\'initialisation du WebSocket', error);
      return { webSocket: null };
    }
  };

  return connectWebSocket();
};
