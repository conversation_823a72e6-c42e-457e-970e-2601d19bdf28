import { baseUrl } from '@/services/axios';

/**
 * Optimise les images en fonction de leur utilisation
 * @param {string|File} path - Chemin de l'image ou objet File
 * @param {Object} options - Options d'optimisation
 * @param {number} options.width - Largeur souhaitée
 * @param {number} options.height - Hauteur souhaitée
 * @param {string} options.format - Format souhaité (webp, jpeg, png)
 * @param {number} options.quality - Qualité de l'image (1-100)
 * @param {boolean} options.placeholder - Générer un placeholder
 * @returns {string} - URL de l'image optimisée
 */
export const getOptimizedImage = (path, options = {}) => {
  // Valeurs par défaut
  const defaults = {
    width: null,
    height: null,
    format: 'webp',
    quality: 80,
    placeholder: false
  };

  const settings = { ...defaults, ...options };

  // Si path est vide, retourne l'image par défaut
  if (!path) {
    return require('@/assets/home/<USER>');
  }

  // Si path est un objet File (image téléchargée)
  if (path instanceof File) {
    return URL.createObjectURL(path);
  }

  // Si le chemin commence par 'http' ou 'https', c'est déjà une URL complète
  if (typeof path === 'string' && (path.startsWith('http://') || path.startsWith('https://'))) {
    // Pour les URLs externes, on ne peut pas appliquer notre optimisation
    return path;
  }

  // Construire l'URL de base
  let imageUrl = baseUrl + path;

  // Si nous avons un service d'optimisation d'images (comme Cloudinary, imgix, etc.)
  // Nous pourrions transformer l'URL ici
  // Exemple avec un service hypothétique:
  // if (process.env.VUE_APP_IMAGE_OPTIMIZER_URL) {
  //   const params = [];
  //   if (settings.width) params.push(`w=${settings.width}`);
  //   if (settings.height) params.push(`h=${settings.height}`);
  //   if (settings.format) params.push(`fm=${settings.format}`);
  //   if (settings.quality) params.push(`q=${settings.quality}`);
  //   
  //   return `${process.env.VUE_APP_IMAGE_OPTIMIZER_URL}/${params.join(',')}/${encodeURIComponent(imageUrl)}`;
  // }

  return imageUrl;
};

/**
 * Précharge une image pour améliorer les performances
 * @param {string} src - URL de l'image à précharger
 */
export const preloadImage = (src) => {
  if (!src) return;
  
  const img = new Image();
  img.src = src;
};

/**
 * Génère un placeholder de faible qualité pour une image
 * @param {string} src - URL de l'image
 * @returns {string} - URL du placeholder
 */
export const generatePlaceholder = (src) => {
  if (!src) return '';
  
  // Ici, on pourrait générer un placeholder très petit (10x10px) de l'image
  // Pour l'instant, on retourne simplement une version basse qualité
  return getOptimizedImage(src, { width: 20, quality: 20 });
};

export default getOptimizedImage;
