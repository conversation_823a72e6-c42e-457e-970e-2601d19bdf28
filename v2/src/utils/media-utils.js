/**
 * Utilitaires pour la gestion des flux média (caméra, microphone)
 */

/**
 * Arrête tous les flux média actifs dans le navigateur
 * Cette fonction est utile pour s'assurer que la caméra et le microphone sont bien éteints
 * après un appel vidéo
 */
export const stopAllMediaTracks = () => {
  try {
    //console.log('%c[MEDIA] Arrêt de tous les flux média...', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
    
    // 1. Arrêter tous les flux média stockés dans les variables globales
    if (window.localStream) {
      //console.log('%c[MEDIA] Arrêt du flux local (window.localStream)', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
      window.localStream.getTracks().forEach(track => {
        track.stop();
        //console.log(`%c[MEDIA] Piste ${track.kind} arrêtée (window.localStream)`, 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
      });
      window.localStream = null;
    }
    
    // 2. Arrêter tous les flux média attachés aux éléments vidéo et audio
    document.querySelectorAll('video, audio').forEach(element => {
      if (element.srcObject) {
        //console.log(`%c[MEDIA] Arrêt du flux attaché à un élément ${element.tagName}`, 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
        const mediaStream = element.srcObject;
        if (mediaStream && mediaStream.getTracks) {
          mediaStream.getTracks().forEach(track => {
            track.stop();
            //console.log(`%c[MEDIA] Piste ${track.kind} arrêtée (élément ${element.tagName})`, 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
          });
        }
        element.srcObject = null;
      }
    });
    
    // 3. Arrêter tous les flux média attachés à la connexion peer
    if (window.peerConnection) {
      //console.log('%c[MEDIA] Arrêt des flux attachés à la connexion peer', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
      const senders = window.peerConnection.getSenders();
      if (senders && senders.length > 0) {
        senders.forEach(sender => {
          if (sender.track) {
            sender.track.stop();
            //console.log(`%c[MEDIA] Piste ${sender.track.kind} arrêtée (connexion peer)`, 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
          }
        });
      }
    }
    
    // 4. Forcer l'arrêt de toutes les pistes média via l'API MediaDevices
    navigator.mediaDevices.getUserMedia({ audio: true, video: true })
      .then(stream => {
        //console.log('%c[MEDIA] Obtention d\'un nouveau flux pour forcer l\'arrêt des anciens', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
        stream.getTracks().forEach(track => {
          track.stop();
          //console.log(`%c[MEDIA] Nouvelle piste ${track.kind} arrêtée`, 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
        });
      })
      .catch(error => {
        //console.error('%c[MEDIA] Erreur lors de l\'obtention d\'un nouveau flux:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
      });
    
    //console.log('%c[MEDIA] Tous les flux média ont été arrêtés', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
    
    // 5. Forcer le garbage collector pour libérer la mémoire
    setTimeout(() => {
      //console.log('%c[MEDIA] Exécution du garbage collector', 'background: #4caf50; color: white; padding: 2px 5px; border-radius: 3px;');
      if (window.gc) {
        window.gc();
      }
    }, 1000);
    
    return true;
  } catch (error) {
    //console.error('%c[MEDIA] Erreur lors de l\'arrêt des flux média:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
    return false;
  }
};

/**
 * Vérifie si la caméra est actuellement active
 * @returns {Promise<boolean>} - True si la caméra est active, false sinon
 */
export const isCameraActive = async () => {
  try {
    // Vérifier si des pistes vidéo sont actives dans les éléments vidéo
    let isActive = false;
    
    document.querySelectorAll('video').forEach(element => {
      if (element.srcObject) {
        const videoTracks = element.srcObject.getVideoTracks();
        if (videoTracks && videoTracks.length > 0 && videoTracks[0].enabled) {
          isActive = true;
        }
      }
    });
    
    if (isActive) {
      return true;
    }
    
    // Essayer d'accéder à la caméra pour voir si elle est déjà utilisée
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      // Si on arrive ici, la caméra n'était pas active
      stream.getTracks().forEach(track => track.stop());
      return false;
    } catch (error) {
      // Si l'erreur est NotReadableError ou AbortError, la caméra est probablement déjà utilisée
      if (error.name === 'NotReadableError' || error.name === 'AbortError') {
        return true;
      }
      return false;
    }
  } catch (error) {
    //console.error('%c[MEDIA] Erreur lors de la vérification de l\'état de la caméra:', 'background: red; color: white; padding: 2px 5px; border-radius: 3px;', error);
    return false;
  }
};
