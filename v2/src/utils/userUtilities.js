export const requiredFieldsCandidate = [
  'about',
  'code_postal',
  'contrat',
  'default_cv',
  'email',
  'experience',
  'experiences',
  'salaire_souhaite',
  'formation',
  'langue',
  'last_name',
  'first_name',
  'metier',
  'mobilité',
  'numberPhone',
  'permis',
  'photo',
  'skill',
  'ville',
  'wanted_job',
  'teletravail',
  'secteur',
];
export const requiredFieldsRecruteur = [...requiredFieldsCandidate, 'siret'];

/**
 * Cette fonction prend en paramètre un objet et un tableau de clés.
 * Elle retourne le nombre de clés qui sont vides. *
 * @param {Object} user - L'objet dont on veut compter les clés vides.
 * @param {Array} targetsKeys - Un tableau de clés à considérer.
 */
export const takeUserNotifications = (user, customTargetsKeys) => {
  // Determinar las claves objetivo
  let targetsKeys;
  if (customTargetsKeys && customTargetsKeys.length > 0) {
    targetsKeys = customTargetsKeys;
  } else if (user.type_user === 'recruiter') {
    targetsKeys = requiredFieldsRecruteur;
  } else {
    targetsKeys = requiredFieldsCandidate;
  }

  let result = 0;
  for (const [key, value] of Object.entries(user)) {
    if (
      (!value || value.length === 0) &&
      (!targetsKeys || targetsKeys.includes(key))
    ) {
      result++;
    }
  }

  return result;
};

export const takeUserNotifications1 = (user, customTargetsKeys) => {
  // Determinar las claves objetivo
  let targetsKeys;
  if (customTargetsKeys && customTargetsKeys.length > 0) {
    targetsKeys = customTargetsKeys;
  } else if (user.type_user === 'recruiter') {
    targetsKeys = requiredFieldsRecruteur;
  } else {
    targetsKeys = requiredFieldsCandidate;
  }

  let result = 0;
  let array = [];
  for (const [key, value] of Object.entries(user)) {
    if (
      (!value || value.length === 0) &&
      (!targetsKeys || targetsKeys.includes(key))
    ) {
      array.push(key);
      result++;
    }
  }

  return array;
};

export const getProgressBarCompletion = (user, requiredFields) => {
  const individualScoreFields = 100 / requiredFields.length;
  let completedFields = 0;

  for (const [key, value] of Object.entries(user)) {
    if (!requiredFields.includes(key)) continue;

    const isCompleted = (() => {
      if (value && typeof value === 'object' && 'length' in value) {
        return value.length > 0;
      }
      if (typeof value === 'string') {
        return value !== '' && value !== 'null';
      }
      return Boolean(value);
    })();

    if (isCompleted) {
      completedFields++;
    }
  }

  return Math.floor(completedFields * individualScoreFields);
};

export const parsePhoneNumber = (phoneNumber) => {
  if (!phoneNumber) {
    return '';
  }

  const parsedNumber = phoneNumber.match(/.{1,2}/g);

  return parsedNumber ? parsedNumber.join(' ') : '';
};
import html2pdf from 'html2pdf.js';
/**
 * Cette fonction sert à télécharger le CV d'un candidat.
 * Le CV doit être le composant CvTemplatePrintVersion.
 * Ce CV est généralement placé dans un conteneur caché pour ne pas l'afficher
 * car il possède des styles CSS spécifiques pour l'impression.
 * @param {Object} candidate
 * @param {HTMLElement} CvTemplatePrintVersion
 */
export const downloadCvTemplate = (candidate, cvElement) => {
  if (!cvElement || !candidate) {
    throw new Error('Element or candidate data manquent');
  }
  const options = {
    margin: 0,
    filename: `CV-thanks-boss-${candidate.first_name}-${candidate.last_name}.pdf`,
    image: { type: 'jpeg', quality: 1 },
    // Permets d'incluir l'image de profile.
    html2canvas: {
      scale: 2,
      useCORS: true,
    },
    jsPDF: { unit: 'mm', format: 'A4', orientation: 'portrait' },
  };
  html2pdf().set(options).from(cvElement).save();
};
