/*import * as CookieConsent from "vanilla-cookieconsent";
//console.log("vanilla-cookieconsent importé :", CookieConsent);

export const truConversion = () => {
    //console.log("truConversion appelée");
  if (CookieConsent.acceptedService('TruConversion', 'analytics')) {
    //console.log("Consentement accordé pour TruConversion");

    var _tip = _tip || [];
    (function (d, s, id) {
      var js, tjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) {
        //console.log("Le script TruConversion est déjà chargé.");
        return;
      }
      js = d.createElement(s); 
      js.id = id;
      js.async = true;
      js.src = d.location.protocol + '//app.truconversion.com/ti-js/37118/cccde.js';
      //console.log("Chargement du script TruConversion depuis :", js.src);
      tjs.parentNode.insertBefore(js, tjs);
    }(document, 'script', 'ti-js'));
  } else {
    //console.log("Consentement non accordé pour TruConversion");
    var truConversionScript = document.getElementById('ti-js');
    if (truConversionScript) {
      //console.log("Suppression du script TruConversion");
      truConversionScript.parentNode.removeChild(truConversionScript);
      window.location.reload();
    }
  }
}*/
