export const slugify = (text) => {
    return text
        .toString()
        .toLowerCase()
        .normalize("NFD")                    // Décompose les caractères accentués en caractères de base
        .replace(/[\u0300-\u036f]/g, "")     // Retire les marques diacritiques (accents, tilde, etc.)
        .replace(/\s+/g, '-')                // Remplace les espaces par des tirets
        .replace(/[^\w\-]+/g, '')            // Retire les caractères non alphanumériques
        .replace(/\-\-+/g, '-')              // Remplace les doubles tirets par un seul
        .replace(/^-+/, '')                  // Retire les tirets au début
        .replace(/-+$/, '');                 // Retire les tirets à la fin
};
