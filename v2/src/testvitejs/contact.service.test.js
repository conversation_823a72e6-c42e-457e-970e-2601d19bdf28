import { describe, it, expect, vi } from 'vitest';
import { faker } from '@faker-js/faker';
import { submitEmailNewsletter, activate_newletter } from '@/services/newsletter.service';

// Mock du service
vi.mock('@/services/newsletter.service', () => ({
  submitEmailNewsletter: vi.fn().mockResolvedValue({}),
  activate_newletter: vi.fn().mockResolvedValue({ success: true }),
}));

describe('Tests du service Newsletter', () => {
  it('devrait soumettre un email valide pour la newsletter', async () => {
    const email = faker.internet.email(); // Génère un email aléatoire

    const response = await submitEmailNewsletter(email);

    expect(submitEmailNewsletter).toHaveBeenCalledWith(email);
    expect(response).toEqual({});
  });

  it('devrait échouer si l\'email est invalide', async () => {
    const email = faker.internet.email(); // Email aléatoire généré

    submitEmailNewsletter.mockImplementationOnce(() => {
      throw new Error('Email invalide');
    });

    try {
      await submitEmailNewsletter(email);
    } catch (error) {
      expect(error).toEqual(new Error('Email invalide'));
    }
  });

  it('devrait activer la newsletter', async () => {
    const response = await activate_newletter();

    expect(activate_newletter).toHaveBeenCalled();
    expect(response).toEqual({ success: true });
  });
});
