import { describe, it, expect, vi } from 'vitest';
import {
  createNewProfileAlert,
  deleteProfileAlert,
  getAlertById,
  modifyProfileAlert,
} from '../services/alert-recruiter.service';

// Simulation des services directement
vi.mock('../services/alert-recruiter.service', () => ({
  createNewProfileAlert: vi.fn(),
  deleteProfileAlert: vi.fn(),
  getAlertById: vi.fn(),
  modifyProfileAlert: vi.fn(),
}));

// Données de test fictives
const mockAlertData = { id: 1, name: 'Test Alert', position: 'Développeur' };

describe('Tests du service d\'alertes', () => {
  it('doit récupérer une alerte par ID avec succès', async () => {
    getAlertById.mockResolvedValueOnce(mockAlertData);

    const response = await getAlertById(1);

    expect(getAlertById).toHaveBeenCalledWith(1);
    expect(response).toEqual(mockAlertData);
  });

  it('doit gérer une erreur API lors de la récupération d\'une alerte par ID', async () => {
    getAlertById.mockRejectedValueOnce(new Error('Erreur API'));

    try {
      await getAlertById(999);
    } catch (error) {
      expect(error).toBeInstanceOf(Error);
      expect(error.message).toBe('Erreur API');
    }
  });

  it('doit créer une nouvelle alerte de profil avec succès', async () => {
    createNewProfileAlert.mockResolvedValueOnce({ success: true });

    const response = await createNewProfileAlert(mockAlertData);

    expect(createNewProfileAlert).toHaveBeenCalledWith(mockAlertData);
    expect(response).toEqual({ success: true });
  });

  it('doit gérer une erreur lors de la création d\'une alerte', async () => {
    createNewProfileAlert.mockRejectedValueOnce(new Error('Erreur API'));

    try {
      await createNewProfileAlert(mockAlertData);
    } catch (error) {
      expect(error).toBeInstanceOf(Error);
      expect(error.message).toBe('Erreur API');
    }
  });

  it('doit supprimer une alerte de profil avec succès', async () => {
    deleteProfileAlert.mockResolvedValueOnce({ success: true });

    const response = await deleteProfileAlert(1);

    expect(deleteProfileAlert).toHaveBeenCalledWith(1);
    expect(response).toEqual({ success: true });
  });

  it('doit gérer une erreur lors de la suppression d\'une alerte', async () => {
    deleteProfileAlert.mockRejectedValueOnce(new Error('Erreur API'));

    try {
      await deleteProfileAlert(999);
    } catch (error) {
      expect(error).toBeInstanceOf(Error);
      expect(error.message).toBe('Erreur API');
    }
  });

  it('doit modifier une alerte de profil avec succès', async () => {
    modifyProfileAlert.mockResolvedValueOnce({ success: true });

    const response = await modifyProfileAlert(1, mockAlertData);

    expect(modifyProfileAlert).toHaveBeenCalledWith(1, mockAlertData);
    expect(response).toEqual({ success: true });
  });

  it('doit gérer une erreur lors de la modification d\'une alerte', async () => {
    modifyProfileAlert.mockRejectedValueOnce(new Error('Erreur API'));

    try {
      await modifyProfileAlert(999, mockAlertData);
    } catch (error) {
      expect(error).toBeInstanceOf(Error);
      expect(error.message).toBe('Erreur API');
    }
  });
});
