import { describe, it, expect, vi } from 'vitest';
import { faker } from '@faker-js/faker';
import {
  postPost,
  getPosts,
  getPostsByUser,
  updatePost,
  deletePost
} from '@/services/post.service';

// Mock du service post.service
vi.mock('@/services/post.service', () => ({
  postPost: vi.fn(async (postDatas) => {
    // Vérifie que le titre et le contenu sont bien fournis
    if (!postDatas.title || !postDatas.content) {
      throw new Error('Le titre et le contenu sont obligatoires');
    }
    return { id: faker.string.uuid(), ...postDatas };
  }),

  getPosts: vi.fn(async (page = 1, limit = 10) => {
    return Array.from({ length: limit }, () => ({
      id: faker.string.uuid(),
      title: faker.lorem.sentence(),
      content: faker.lorem.paragraph(),
    }));
  }),

  getPostsByUser: vi.fn(async (userId) => {
    return Array.from({ length: 3 }, () => ({
      id: faker.string.uuid(),
      userId,
      title: faker.lorem.sentence(),
      content: faker.lorem.paragraph(),
    }));
  }),

  updatePost: vi.fn(async (postId, postDatas) => {
    // Vérifie que le titre et le contenu sont fournis pour la mise à jour
    if (!postDatas.title || !postDatas.content) {
      throw new Error('Le titre et le contenu sont obligatoires');
    }
    return { id: postId, ...postDatas };
  }),

  deletePost: vi.fn(async (postId) => {
    return { success: true, message: 'Post supprimé' };
  }),
}));

describe('Service de gestion des posts', () => {
  it('doit créer un post avec des données valides', async () => {
    const postDatas = { title: faker.lorem.sentence(), content: faker.lorem.paragraph() };
    const post = await postPost(postDatas);

    // Vérifie que le post est bien défini et contient un ID
    expect(post).toBeDefined();
    expect(post.id).toBeDefined();
    expect(post.title).toBe(postDatas.title);
  });

  it('doit récupérer une liste de posts', async () => {
    const posts = await getPosts();

    // Vérifie que la liste retournée est bien un tableau de 10 éléments
    expect(posts).toBeInstanceOf(Array);
    expect(posts).toHaveLength(10);
  });

  it('doit récupérer les posts d’un utilisateur spécifique', async () => {
    const userId = faker.string.uuid();
    const userPosts = await getPostsByUser(userId);

    // Vérifie que les posts appartiennent bien à l'utilisateur demandé
    expect(userPosts).toBeInstanceOf(Array);
    expect(userPosts.length).toBeGreaterThan(0);
    expect(userPosts[0].userId).toBe(userId);
  });

  it('doit mettre à jour un post', async () => {
    const postId = faker.string.uuid();
    const postDatas = { title: faker.lorem.sentence(), content: faker.lorem.paragraph() };
    const updatedPost = await updatePost(postId, postDatas);

    // Vérifie que le post a bien été mis à jour avec les nouvelles valeurs
    expect(updatedPost.id).toBe(postId);
    expect(updatedPost.title).toBe(postDatas.title);
  });

  it('doit supprimer un post', async () => {
    const postId = faker.string.uuid();
    const response = await deletePost(postId);

    // Vérifie que la suppression s'est bien déroulée
    expect(response.success).toBe(true);
  });
});
