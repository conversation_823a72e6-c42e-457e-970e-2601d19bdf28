import { describe, it, expect, vi } from "vitest";
import { faker } from "@faker-js/faker";
import { register, login } from "@/services/account.service.js";

// Mock de la fonction register pour éviter les appels API réels
vi.mock("@/services/account.service.js", () => ({
  register: vi.fn(async (userData) => {
    if (userData.password !== userData.password2) {
      throw new Error("Les mots de passe ne correspondent pas");
    }
    if (!userData.email.includes("@")) {
      throw new Error("Email invalide");
    }
    return {
      ...userData,
      id: faker.string.uuid(),
    };
  }),

  // Mock de la fonction login
  login: vi.fn(async (email, password) => {
    const validUser = {
      email: emailss,
      password: passwordss,
      id: faker.string.uuid(),
    };

    if (email === validUser.email && password === validUser.password) {
      return validUser; // Connexion réussie
    } else {
      throw new Error("Identifiants incorrects"); // Connexion échouée
    }
  }),
}));

const emailss = faker.internet.email();
const passwordss = "Password123!";
const wrongPassword = "WrongPassword123!";

describe("Création de comptes", () => {
  it("devrait créer un compte avec des données valides", async () => {
    const userData = {
      email: emailss,
      password: passwordss,
      password2: passwordss,
      type_user: "recruiter",
      company: faker.company.name(),
      adress: faker.location.streetAddress(),
      siret: faker.string.numeric(14),
      first_name: faker.person.firstName(),
      last_name: faker.person.lastName(),
      metier: faker.person.jobTitle(),
      numberPhone: faker.phone.number("+33 6 ## ## ## ##"),
      ville: faker.location.city(),
    };

    const account = await register(userData);

    // Vérifications
    expect(account).toBeDefined();
    expect(account.id).toBeDefined();
    expect(account.email).toBe(userData.email);
    expect(account.type_user).toBe("recruiter");
    expect(account.siret).toMatch(/^\d{14}$/); // Vérifie que c'est bien 14 chiffres
  });

  it("devrait échouer si les mots de passe ne correspondent pas", async () => {
    const userData = {
      email: faker.internet.email(),
      password: "Password123!",
      password2: "Password456!", // Mauvais mot de passe
      type_user: "recruiter",
    };

    // Test de l'échec
    await expect(register(userData)).rejects.toThrow(
      "Les mots de passe ne correspondent pas"
    );
  });

  it("devrait échouer si l’email est invalide", async () => {
    const userData = {
      email: "email-invalide", // Email invalide
      password: "Password123!",
      password2: "Password123!",
      type_user: "recruiter",
    };

    // Test de l'échec
    await expect(register(userData)).rejects.toThrow("Email invalide");
  });
});

describe("Connexion de comptes", () => {
  it("devrait permettre de se connecter avec des identifiants valides", async () => {
    const userEmail = emailss; // Utilisation de l'email généré dans la création
    const userPassword = passwordss;

    const user = await login(userEmail, userPassword);

    // Vérifications
    expect(user).toBeDefined();
    expect(user.id).toBeDefined();
    expect(user.email).toBe(userEmail);
  });

  it("devrait échouer si l'email est incorrect", async () => {
    const userEmail = "<EMAIL>"; // Email incorrect
    const userPassword = passwordss;

    await expect(login(userEmail, userPassword)).rejects.toThrow(
      "Identifiants incorrects"
    );
  });

  it("devrait échouer si le mot de passe est incorrect", async () => {
    const userEmail = emailss;
    const userPassword = wrongPassword; // Mauvais mot de passe

    await expect(login(userEmail, userPassword)).rejects.toThrow(
      "Identifiants incorrects"
    );
  });
});
