import { describe, it, expect, vi } from 'vitest';
import { faker } from '@faker-js/faker';
import { getAllIaConversation, sendIaConversation } from '@/services/conversation.service';

// Mock des fonctions importées
vi.mock('@/services/conversation.service', () => ({
  getAllIaConversation: vi.fn(async () => ({
    id: faker.string.uuid(),
    messages: [
      {
        id: faker.string.uuid(),
        content: faker.lorem.sentence(),
        timestamp: faker.date.recent(),
      },
    ],
  })),
  sendIaConversation: vi.fn(async (userId, message, file) => {
    return {
      id: faker.string.uuid(),
      content: message,
      timestamp: faker.date.recent(),
    };
  }),
}));

describe('Services Conversation', () => {
  it('devrait récupérer toutes les conversations IA', async () => {
    const conversations = await getAllIaConversation();

    // Vérifier que les données retournées sont correctes
    expect(conversations).toBeDefined();
    expect(conversations.id).toBeDefined();
    expect(conversations.messages).toBeDefined();
    expect(conversations.messages[0].id).toBeDefined();
    expect(conversations.messages[0].content).toBeDefined();
    expect(conversations.messages[0].timestamp).toBeDefined();
  });

  it('devrait envoyer un message et retourner la réponse', async () => {
    const mockMessage = 'Test message';
    const mockFile = new FormData();

    const result = await sendIaConversation('123', mockMessage, mockFile);

    // Vérifier que la réponse est correcte
    expect(result).toBeDefined();
    expect(result.id).toBeDefined();
    expect(result.content).toBe(mockMessage); // Vérifier que le message est bien retourné
    expect(result.timestamp).toBeDefined();
  });

  it('devrait gérer une erreur en cas d\'échec de la récupération des conversations', async () => {
    const mockError = new Error('Erreur dans la récupération des conversations');

    // Simuler une erreur dans getAllIaConversation
    getAllIaConversation.mockRejectedValueOnce(mockError);

    await expect(getAllIaConversation()).rejects.toThrow('Erreur dans la récupération des conversations');
  });

  it('devrait gérer une erreur en cas d\'échec de l\'envoi du message', async () => {
    const mockError = new Error('Erreur dans l\'envoi du message');

    // Simuler une erreur dans sendIaConversation
    sendIaConversation.mockRejectedValueOnce(mockError);

    await expect(sendIaConversation('123', 'Test message', new FormData())).rejects.toThrow('Erreur dans l\'envoi du message');
  });
});