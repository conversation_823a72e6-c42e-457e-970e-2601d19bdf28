import { describe, it, expect, vi } from 'vitest';
import {
  fetchUsers,
  fetchUserDetails,
  sendMessage,
  fetchMessages,
  createConversation,
} from '@/services/websocket.service';

// Simulation des services directement
vi.mock('@/services/websocket.service', () => ({
  fetchUsers: vi.fn(),
  fetchUserDetails: vi.fn(),
  sendMessage: vi.fn(),
  fetchMessages: vi.fn(),
  createConversation: vi.fn(),
}));

// Données de test fictives
const mockUserData = [{ id: 1, name: '<PERSON>' }, { id: 2, name: '<PERSON>' }];
const mockUserDetails = { id: 1, name: '<PERSON>', email: '<EMAIL>' };

describe("Tests du service des utilisateurs", () => {
  it("doit récupérer la liste des utilisateurs avec succès", async () => {
    fetchUsers.mockResolvedValueOnce(mockUserData);

    const response = await fetchUsers();

    expect(fetchUsers).toHaveBeenCalled();
    expect(response).toEqual(mockUserData);
  });

  it("doit gérer une erreur lors de la récupération des utilisateurs", async () => {
    fetchUsers.mockRejectedValueOnce(new Error("Erreur API"));

    await expect(fetchUsers()).rejects.toThrow("Erreur API");
  });

  it("doit récupérer les détails d'un utilisateur avec succès", async () => {
    fetchUserDetails.mockResolvedValueOnce(mockUserDetails);

    const response = await fetchUserDetails(1);

    expect(fetchUserDetails).toHaveBeenCalledWith(1);
    expect(response).toEqual(mockUserDetails);
  });

  it("doit gérer une erreur lors de la récupération des détails d'un utilisateur", async () => {
    fetchUserDetails.mockRejectedValueOnce(new Error("Erreur API"));

    await expect(fetchUserDetails(999)).rejects.toThrow("Erreur API");
  });

  it("doit envoyer un message via WebSocket avec succès", async () => {
    sendMessage.mockResolvedValueOnce({ success: true });

    const response = await sendMessage(1, "Bonjour");

    expect(sendMessage).toHaveBeenCalledWith(1, "Bonjour");
    expect(response).toEqual({ success: true });
  });

  it("doit gérer une erreur lors de l'envoi d'un message", async () => {
    sendMessage.mockRejectedValueOnce(new Error("Erreur WebSocket"));

    await expect(sendMessage(1, "Bonjour")).rejects.toThrow("Erreur WebSocket");
  });

  it("doit récupérer les messages d'une conversation via WebSocket avec succès", async () => {
    fetchMessages.mockResolvedValueOnce([{ id: 1, text: "Hello" }]);

    const response = await fetchMessages(1);

    expect(fetchMessages).toHaveBeenCalledWith(1);
    expect(response).toEqual([{ id: 1, text: "Hello" }]);
  });

  it("doit gérer une erreur lors de la récupération des messages", async () => {
    fetchMessages.mockRejectedValueOnce(new Error("Erreur WebSocket"));

    await expect(fetchMessages(1)).rejects.toThrow("Erreur WebSocket");
  });

  it("doit créer une conversation via WebSocket avec succès", async () => {
    createConversation.mockResolvedValueOnce({ success: true });

    const response = await createConversation("<EMAIL>");

    expect(createConversation).toHaveBeenCalledWith("<EMAIL>");
    expect(response).toEqual({ success: true });
  });

  it("doit gérer une erreur lors de la création d'une conversation", async () => {
    createConversation.mockRejectedValueOnce(new Error("Erreur WebSocket"));

    await expect(createConversation("<EMAIL>")).rejects.toThrow("Erreur WebSocket");
  });
});
