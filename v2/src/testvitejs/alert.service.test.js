import { describe, it, expect, vi } from 'vitest';
import {
  createNewJobAlert,
  deleteJob<PERSON>lert,
  getAlertById,
  getSkillList,
  getSoftSkillList,
  modifyJobAlert,
  toggleJobAlertActivation,
} from '@/services/alert.service';

// Simulation des services directement
vi.mock('@/services/alert.service', () => ({
  createNewJobAlert: vi.fn(),
  deleteJobAlert: vi.fn(),
  getAlertById: vi.fn(),
  getSkillList: vi.fn(),
  getSoftSkillList: vi.fn(),
  modifyJobAlert: vi.fn(),
  toggleJobAlertActivation: vi.fn(),
}));

// Données de test fictives
const mockJobAlertData = {
  id: 1,
  alertName: 'Test Alert',
  title: 'Développeur',
  city: 'Paris',
};

describe("Tests du service d'alertes", () => {
  it("doit récupérer une alerte par ID avec succès", async () => {
    getAlertById.mockResolvedValueOnce(mockJobAlertData);

    const response = await getAlertById(1);

    expect(getAlertById).toHaveBeenCalledWith(1);
    expect(response).toEqual(mockJobAlertData);
  });

  it("doit gérer une erreur API lors de la récupération d'une alerte par ID", async () => {
    getAlertById.mockRejectedValueOnce(new Error("Erreur API"));

    await expect(getAlertById(999)).rejects.toThrow("Erreur API");
  });

  it("doit créer une nouvelle alerte avec succès", async () => {
    createNewJobAlert.mockResolvedValueOnce({ success: true });

    const response = await createNewJobAlert(mockJobAlertData);

    expect(createNewJobAlert).toHaveBeenCalledWith(mockJobAlertData);
    expect(response).toEqual({ success: true });
  });

  it("doit gérer une erreur lors de la création d'une alerte", async () => {
    createNewJobAlert.mockRejectedValueOnce(new Error("Erreur API"));

    await expect(createNewJobAlert(mockJobAlertData)).rejects.toThrow("Erreur API");
  });

  it("doit supprimer une alerte avec succès", async () => {
    deleteJobAlert.mockResolvedValueOnce({ success: true });

    const response = await deleteJobAlert(1);

    expect(deleteJobAlert).toHaveBeenCalledWith(1);
    expect(response).toEqual({ success: true });
  });

  it("doit gérer une erreur lors de la suppression d'une alerte", async () => {
    deleteJobAlert.mockRejectedValueOnce(new Error("Erreur API"));

    await expect(deleteJobAlert(999)).rejects.toThrow("Erreur API");
  });

  it("doit modifier une alerte avec succès", async () => {
    modifyJobAlert.mockResolvedValueOnce({ success: true });

    const response = await modifyJobAlert(1, mockJobAlertData);

    expect(modifyJobAlert).toHaveBeenCalledWith(1, mockJobAlertData);
    expect(response).toEqual({ success: true });
  });

  it("doit gérer une erreur lors de la modification d'une alerte", async () => {
    modifyJobAlert.mockRejectedValueOnce(new Error("Erreur API"));

    await expect(modifyJobAlert(999, mockJobAlertData)).rejects.toThrow("Erreur API");
  });

  it("doit basculer l'activation d'une alerte avec succès", async () => {
    toggleJobAlertActivation.mockResolvedValueOnce({ success: true });

    const response = await toggleJobAlertActivation(1);

    expect(toggleJobAlertActivation).toHaveBeenCalledWith(1);
    expect(response).toEqual({ success: true });
  });

  it("doit gérer une erreur lors du basculement de l'activation d'une alerte", async () => {
    toggleJobAlertActivation.mockRejectedValueOnce(new Error("Erreur API"));

    await expect(toggleJobAlertActivation(999)).rejects.toThrow("Erreur API");
  });

  it("doit récupérer la liste des compétences avec succès", async () => {
    getSkillList.mockResolvedValueOnce([{ id: 1, name: "JavaScript" }]);

    const response = await getSkillList();

    expect(getSkillList).toHaveBeenCalled();
    expect(response).toEqual([{ id: 1, name: "JavaScript" }]);
  });

  it("doit gérer une erreur lors de la récupération de la liste des compétences", async () => {
    getSkillList.mockRejectedValueOnce(new Error("Erreur API"));

    await expect(getSkillList()).rejects.toThrow("Erreur API");
  });
});
