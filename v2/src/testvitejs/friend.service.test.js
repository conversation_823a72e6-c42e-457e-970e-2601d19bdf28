import { describe, it, expect, vi } from 'vitest';
import { faker } from '@faker-js/faker';
import {
  friendsList,
  friendsInvitations,
  removeFriend,
  sendFriendInvitation,
  acceptFriendInvitation,
  declinedFriendInvitation,
  fetchUsers,
} from '@/services/friend.service';

// Mock des fonctions importées depuis '@/services/friend.service'
vi.mock('@/services/friend.service', () => ({
  friendsList: vi.fn().mockResolvedValue([
    { id: 1, name: faker.person.fullName() },
    { id: 2, name: faker.person.fullName() },
  ]),
  friendsInvitations: vi.fn().mockResolvedValue({
    sent_invitations: [{ id: 3, email: faker.internet.email() }],
    received_invitations: [{ id: 4, email: faker.internet.email() }],
  }),
  removeFriend: vi.fn().mockResolvedValue({}),
  sendFriendInvitation: vi.fn().mockResolvedValue({ success: true }),
  acceptFriendInvitation: vi.fn().mockResolvedValue({ success: true }),
  declinedFriendInvitation: vi.fn().mockResolvedValue({ success: true }),
  fetchUsers: vi.fn().mockResolvedValue([
    { id: 1, name: faker.person.fullName(), email: faker.internet.email() },
  ]),
}));

describe('Tests du service Friend', () => {
  it('devrait récupérer la liste des amis', async () => {
    const friends = await friendsList();
    expect(friends).toBeDefined();
    expect(friends).toHaveLength(2);
    expect(friends[0].id).toBeDefined();
    expect(friends[0].name).toBeDefined();
  });

  it('devrait récupérer les invitations envoyées et reçues', async () => {
    const invitations = await friendsInvitations();
    expect(invitations).toBeDefined();
    expect(invitations.sent_invitations).toHaveLength(1);
    expect(invitations.received_invitations).toHaveLength(1);
  });

  it('devrait supprimer un ami', async () => {
    const result = await removeFriend(1);
    expect(result).toEqual({});
  });

  it('devrait envoyer une invitation à un ami', async () => {
    const result = await sendFriendInvitation('<EMAIL>');
    expect(result).toEqual({ success: true });
  });

  it('devrait accepter une invitation à un ami', async () => {
    const result = await acceptFriendInvitation(1);
    expect(result).toEqual({ success: true });
  });

  it('devrait refuser une invitation à un ami', async () => {
    const result = await declinedFriendInvitation(1);
    expect(result).toEqual({ success: true });
  });

  it('devrait récupérer la liste des utilisateurs', async () => {
    const users = await fetchUsers();
    expect(users).toBeDefined();
    expect(users).toHaveLength(1);
    expect(users[0].id).toBeDefined();
    expect(users[0].name).toBeDefined();
    expect(users[0].email).toBeDefined();
  });
});
