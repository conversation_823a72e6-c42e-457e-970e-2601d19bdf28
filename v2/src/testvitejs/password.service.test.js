import { describe, it, expect, vi } from 'vitest';
import {
  passwordResetRequest,
  passwordChangeRequest,
  emailChangeRequest,
} from '@/services/password.service.js';

// Mock du service
vi.mock('@/services/password.service.js', () => ({
  passwordResetRequest: vi.fn(async (email) => {
    if (!email) throw new Error('Aucun email fourni');
    if (email === '<EMAIL>') throw new Error('Token invalide');
    if (email === '<EMAIL>')
      throw new Error('Erreur de réinitialisation');
    return true;
  }),
  passwordChangeRequest: vi.fn(async (data) => {
    if (data.old_password === 'fail')
      throw new Error('Échec du changement de mot de passe');
    return true;
  }),
  emailChangeRequest: vi.fn(async (data) => {
    if (data.new_email === '<EMAIL>')
      throw new Error("Échec du changement d'email");
    return true;
  }),
}));

describe('Services de gestion de compte', () => {
  it('devrait envoyer une demande de réinitialisation de mot de passe', async () => {
    await expect(passwordResetRequest('<EMAIL>')).resolves.toBe(true);
  });

  it('devrait gérer une erreur lors de la demande de réinitialisation de mot de passe', async () => {
    await expect(passwordResetRequest('<EMAIL>')).rejects.toThrow(
      'Erreur de réinitialisation'
    );
  });

  it('devrait confirmer la réinitialisation du mot de passe', async () => {
    await expect(passwordResetRequest('<EMAIL>')).resolves.toBe(true);
  });

  it('devrait gérer une erreur si les paramètres sont manquants', async () => {
    await expect(passwordResetRequest()).rejects.toThrow('Aucun email fourni');
  });

  it('devrait gérer une erreur serveur avec un token invalide', async () => {
    await expect(passwordResetRequest('<EMAIL>')).rejects.toThrow(
      'Token invalide'
    );
  });

  it('devrait changer le mot de passe', async () => {
    await expect(
      passwordChangeRequest({
        old_password: 'correct',
        new_password: 'newpass123',
      })
    ).resolves.toBe(true);
  });

  it("devrait afficher une erreur en cas d'échec du changement de mot de passe", async () => {
    await expect(
      passwordChangeRequest({
        old_password: 'fail',
        new_password: 'newpass123',
      })
    ).rejects.toThrow('Échec du changement de mot de passe');
  });

  it("devrait changer l'email", async () => {
    await expect(
      emailChangeRequest({ new_email: '<EMAIL>' })
    ).resolves.toBe(true);
  });

  it("devrait afficher une erreur en cas d'échec du changement d'email", async () => {
    await expect(
      emailChangeRequest({ new_email: '<EMAIL>' })
    ).rejects.toThrow("Échec du changement d'email");
  });
});
