import { describe, it, expect, vi } from 'vitest';
import { faker } from '@faker-js/faker';
import { getUser, getUserLastFacture } from '@/services/subscription.service.js';

// Mock des fonctions importées
vi.mock('@/services/subscription.service.js', () => ({
  getUser: vi.fn(async () => ({
    id: faker.string.uuid(),
    email: faker.internet.email(),
    name: faker.person.fullName(), // Modification ici
  })),
  getUserLastFacture: vi.fn(async () => ({
    facture_id: faker.string.uuid(),
    montant: faker.finance.amount(),
    date: faker.date.recent(),
  })),
}));

describe('Services Subscription', () => {
  it('devrait récupérer les informations de l\'utilisateur', async () => {
    const user = await getUser();

    expect(user).toBeDefined();
    expect(user.id).toBeDefined();
    expect(user.email).toBeDefined();
    expect(user.name).toBeDefined();
  });

  it('devrait récupérer la dernière facture de l\'utilisateur', async () => {
    const facture = await getUserLastFacture();

    expect(facture).toBeDefined();
    expect(facture.facture_id).toBeDefined();
    expect(facture.montant).toBeDefined();
    expect(facture.date).toBeDefined();
  });
});
