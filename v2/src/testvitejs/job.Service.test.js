import { describe, it, expect, vi } from 'vitest';
import { faker } from '@faker-js/faker';
import {
  getJobById,
  getJobList,
  postApplicationToJob,
} from '@/services/job.service.js';

// Mock des fonctions pour éviter les appels API réels
vi.mock('@/services/job.service.js', () => ({
  getJobList: vi.fn(async () => [{ id: 1, title: faker.person.jobTitle() }]),
  getPublishedJobList: vi.fn(async (page = 1) => ({
    jobs: [{ id: page, title: faker.person.jobTitle() }],
    hasNext: false,
  })),
  getJobById: vi.fn(async (jobId) => ({ id: jobId, title: faker.person.jobTitle() })),
  getJobofferById: vi.fn(async (jobId) => ({ id: jobId, description: faker.lorem.paragraph() })),
  postApplicationToJob: vi.fn(async (jobId, formData) => ({ success: true, jobId, formData })),
  cancelApplicationToJob: vi.fn(async (jobId) => ({ success: true, jobId })),
  isJobFavoriteById: vi.fn((jobId) => faker.datatype.boolean()),
  getJobsByUser: vi.fn(async () => [{ id: 1, title: faker.person.jobTitle() }]),
}));

describe('Tests des services de job', () => {
  it('devrait récupérer la liste des jobs', async () => {
    const jobs = await getJobList();
    expect(getJobList).toHaveBeenCalledTimes(1);
    expect(jobs).toBeInstanceOf(Array);
  });

  it('devrait récupérer un job par ID', async () => {
    const jobId = 1;
    const job = await getJobById(jobId);
    expect(getJobById).toHaveBeenCalledWith(jobId);
    expect(job.id).toBe(jobId);
  });

  it('devrait postuler à une offre de job', async () => {
    const jobId = 1;
    const formData = { name: faker.person.firstName(), resume: 'dummy-resume.pdf' };
    const response = await postApplicationToJob(jobId, formData);
    expect(postApplicationToJob).toHaveBeenCalledWith(jobId, formData);
    expect(response.success).toBe(true);
  });
});
