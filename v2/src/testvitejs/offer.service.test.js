import { describe, it, expect, vi, beforeEach } from 'vitest';
import * as OfferService from '../services/offer.service';
import { faker } from '@faker-js/faker';

// Mock du service OfferService
vi.mock('../services/offer.service', () => ({
  createPost: vi.fn(async (post) => ({ id: faker.string.uuid(), ...post })),
  getPosts: vi.fn(async () => [{ id: faker.string.uuid(), title: faker.lorem.sentence(), content: faker.lorem.paragraph() }]),
  getUserPosts: vi.fn(async (userId) => [{ id: faker.string.uuid(), userId, title: faker.lorem.sentence() }]),
  deletePost: vi.fn(async (postId) => ({ success: true, id: postId }))
}));

describe('OfferService', () => {
  beforeEach(() => {
    vi.clearAllMocks(); // Réinitialisation des mocks avant chaque test
  });

  it('doit créer un nouveau post', async () => {
    const post = { title: 'Post de test', content: 'Ceci est un post de test.' };
    const result = await OfferService.createPost(post);

    // Vérifie que le post créé possède un ID et les bonnes valeurs
    expect(result).toHaveProperty('id');
    expect(result.title).toBe(post.title);
    expect(result.content).toBe(post.content);
  });

  it('doit récupérer la liste des posts', async () => {
    const result = await OfferService.getPosts();

    // Vérifie que le résultat est un tableau contenant au moins un élément avec un ID
    expect(result).toBeInstanceOf(Array);
    expect(result[0]).toHaveProperty('id');
  });

  it('doit récupérer les posts d\'un utilisateur', async () => {
    const userId = faker.string.uuid();
    const result = await OfferService.getUserPosts(userId);

    // Vérifie que les posts appartiennent bien à l'utilisateur demandé
    expect(result).toBeInstanceOf(Array);
    expect(result[0].userId).toBe(userId);
  });

  it('doit supprimer un post', async () => {
    const postId = faker.string.uuid();
    const result = await OfferService.deletePost(postId);

    // Vérifie que la suppression a bien eu lieu et retourne l'ID du post supprimé
    expect(result).toEqual({ success: true, id: postId });
  });
});
