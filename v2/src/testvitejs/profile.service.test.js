import { describe, it, expect, vi } from 'vitest';
import { faker } from '@faker-js/faker';
import { updateUserInformations } from '@/services/profile.service.js';

// Mock de la fonction updateUserInformations pour éviter les appels API réels
vi.mock('@/services/profile.service.js', () => ({
  updateUserInformations: vi.fn(async (userData) => ({
    success: true,
    data: userData,
  })),
}));

describe("Mettre à jour les informations de l'utilisateur", () => {
  it('devrait appeler updateUserInformations avec les bonnes données', async () => {
    const mockUserData = {
      metier: faker.person.jobTitle(),
      first_name: faker.person.firstName(),
      last_name: faker.person.lastName(),
      email: faker.internet.email(),
      numberPhone: faker.phone.number(),
      about: faker.lorem.sentence(),
      site_url: faker.internet.url(),
      linkedin: faker.internet.url(),
      porfolio_url: faker.internet.url(),
      autre_url: faker.internet.url(),
      instagram: faker.internet.url(),
      tiktok: faker.internet.url(),
      facebook: faker.internet.url(),
      ville: faker.location.city(),
      code_postal: faker.location.zipCode(),
      photo: new File(['dummy content'], 'photo.png', { type: 'image/png' }),
    };

    await updateUserInformations(mockUserData);

    expect(updateUserInformations).toHaveBeenCalledWith(mockUserData);
    expect(updateUserInformations).toHaveBeenCalledTimes(1);
  });
});
