import { describe, it, expect, vi } from 'vitest';
import { faker } from '@faker-js/faker';
import {
  addFavoriteProfil,
  removeFavoriteProfil,
  isCandidateFavoriteById,
} from '@/services/favoriteProfil.service.js';

// Mock des fonctions pour éviter les appels API réels
vi.mock('@/services/favoriteProfil.service.js', () => ({
  addFavoriteProfil: vi.fn(async () => ({ success: true })),
  removeFavoriteProfil: vi.fn(async () => ({ success: true })),
  isCandidateFavoriteById: vi.fn(async () => [{ id: 1, name: '<PERSON>' }]),
}));

describe('Favorite Profile Service', () => {
  it('should add a candidate to favorites', async () => {
    const candidateId = faker.number.int();
    const response = await addFavoriteProfil(candidateId);

    expect(addFavoriteProfil).toHaveBeenCalledWith(candidateId);
    expect(response).toEqual({ success: true });
  });

  it('should remove a candidate from favorites', async () => {
    const favoriteId = faker.number.int();
    const response = await removeFavoriteProfil(favoriteId);

    expect(removeFavoriteProfil).toHaveBeenCalledWith(favoriteId);
    expect(response).toEqual({ success: true });
  });

  it('should fetch favorite candidates', async () => {
    const response = await isCandidateFavoriteById();

    expect(isCandidateFavoriteById).toHaveBeenCalled();
    expect(response).toEqual([{ id: 1, name: 'John Doe' }]);
  });
});
