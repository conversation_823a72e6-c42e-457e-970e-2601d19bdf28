import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  searchJobByCityAndTitle,
  getJobTitlesList,
  getJobOfferById,
  getCandidatesForOffer,
  acceptCandidateStatus,
  filterCandidates,
  calculateDistance,
} from '@/services/search.service';

// Mocking imported functions
vi.mock('@/services/search.service', () => ({
  searchJobByCityAndTitle: vi.fn(),
  searchJobWithFilters: vi.fn(),
  getJobTitlesList: vi.fn(),
  getJobOffers: vi.fn(),
  getJobOffersById: vi.fn(),
  getJobOfferById: vi.fn(),
  getCandidatesForOffer: vi.fn(),
  getDetailCandidateForOffer: vi.fn(),
  acceptCandidateStatus: vi.fn(),
  refuseCandidateStatus: vi.fn(),
  holdCandidateStatus: vi.fn(),
  searchCandidates: vi.fn(),
  filterCandidates: vi.fn(),
  calculateDistance: vi.fn(),
}));

// Sample Mock Data
const mockJobOffers = [{ id: 1, title: 'Software Engineer' }];
const mockJobOfferDetails = { id: 1, title: 'Software Engineer', description: 'A job description' };
const mockCandidates = [{ id: 101, name: 'John Doe', metier: 'Developer' }];
const mockJobTitles = ['Software Engineer', 'Product Manager'];

beforeEach(() => {
  vi.clearAllMocks();
});

describe('Tests de recherche d\'emploi', () => {
  it('devrait rechercher des emplois par ville et titre', async () => {
    searchJobByCityAndTitle.mockResolvedValueOnce(mockJobOffers);
    const response = await searchJobByCityAndTitle({ title: 'Engineer', city: 'Paris' });
    expect(searchJobByCityAndTitle).toHaveBeenCalledWith({ title: 'Engineer', city: 'Paris' });
    expect(response).toEqual(mockJobOffers);
  });

  it('devrait gérer l\'erreur API lors de la recherche d\'emplois par ville et titre', async () => {
    searchJobByCityAndTitle.mockRejectedValueOnce(new Error('API Error'));
    await expect(searchJobByCityAndTitle({ title: 'Engineer', city: 'Paris' })).rejects.toThrow('API Error');
  });
});

describe('Tests de récupération d\'offres d\'emploi', () => {
  it('devrait récupérer les titres des emplois', async () => {
    getJobTitlesList.mockResolvedValueOnce(mockJobTitles);
    const response = await getJobTitlesList();
    expect(response).toEqual(mockJobTitles);
  });

  it('devrait récupérer une offre d\'emploi par ID', async () => {
    getJobOfferById.mockResolvedValueOnce(mockJobOfferDetails);
    const response = await getJobOfferById(1);
    expect(response).toEqual(mockJobOfferDetails);
  });
});

describe('Tests de gestion des candidats', () => {
  it('devrait récupérer les candidats pour une offre d\'emploi', async () => {
    getCandidatesForOffer.mockResolvedValueOnce(mockCandidates);
    const response = await getCandidatesForOffer(1);
    expect(response).toEqual(mockCandidates);
  });

  it('devrait accepter un candidat', async () => {
    acceptCandidateStatus.mockResolvedValueOnce({ success: true });
    const response = await acceptCandidateStatus(1, 101);
    expect(acceptCandidateStatus).toHaveBeenCalledWith(1, 101);
    expect(response).toEqual({ success: true });
  });
});

describe('Tests de filtrage des candidats et de calcul de distance', () => {
  it('devrait calculer la distance correctement', () => {
    calculateDistance.mockReturnValue(5837);
    const distance = calculateDistance(48.8566, 2.3522, 40.7128, -74.006);
    expect(distance).toBeGreaterThan(5000);
  });

  it('devrait filtrer les candidats par salaire', () => {
    const candidates = [
      { salaire_souhaite: 40000 },
      { salaire_souhaite: 60000 },
    ];
    const filters = { salary: [45000, 55000] };
    filterCandidates.mockReturnValue([]);
    const result = filterCandidates(candidates, filters);
    expect(result).toEqual([]);
  });
});
