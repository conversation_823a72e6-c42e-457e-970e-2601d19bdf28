import { describe, it, expect, vi } from 'vitest';
import {
  submitEmail,
  getMostPopularArticles,
  getArticleById,
  getMostPopularArticlesByTheme,
  getArticleList,
  getHighlightedArticles,
  getLastTwoArticles,
} from '@/services/blog.service.js';

// Mock du module entier
vi.mock('@/services/blog.service.js', () => ({
  submitEbookForm: vi.fn(async (formData) => ({
    success: true,
    message: "Ebook submitted successfully",
    data: formData,
  })),

  submitEmail: vi.fn(async (email) => {
    if (!email.includes("@")) {
      throw new Error("Email invalide");
    }
    return { success: true, message: "<PERSON>ail submitted successfully" };
  }),

  getMostPopularArticles: vi.fn(async () => [
    { id: 1, title: "Article 1" },
    { id: 2, title: "Article 2" },
  ]),

  getArticleById: vi.fn(async (id) => ({
    id,
    title: `Article ${id}`,
    content: "Contenu de l'article",
  })),

  getMostPopularArticlesByTheme: vi.fn(async (theme) => [
    { id: 1, title: `Article ${theme} 1` },
    { id: 2, title: `Article ${theme} 2` },
  ]),

  getArticleList: vi.fn(async () => [
    { id: 1, title: "Article A" },
    { id: 2, title: "Article B" },
  ]),

  getHighlightedArticles: vi.fn(async () => [
    { id: 3, title: "Article en vedette" },
  ]),

  getLastTwoArticles: vi.fn(async () => [
    { id: 4, title: "Dernier article 1" },
    { id: 5, title: "Dernier article 2" },
  ]),
}));

describe('Blog Service', () => {
  it('should return a list of popular articles', async () => {
    const articles = await getMostPopularArticles();

    // Vérifier que la réponse est un tableau de 2 articles
    expect(articles).toHaveLength(2);

    // Vérifier que chaque article a une propriété "title"
    expect(articles[0]).toHaveProperty('title');
    expect(articles[1]).toHaveProperty('title');

    // Vérifier les titres des articles
    expect(articles[0].title).toBe("Article 1");
    expect(articles[1].title).toBe("Article 2");
  });

  it('should throw an error for invalid email', async () => {
    // Tester un email invalide
    await expect(submitEmail('invalidemail')).rejects.toThrow("Email invalide");

    // Tester un email valide
    const validEmailResponse = await submitEmail('<EMAIL>');
    expect(validEmailResponse).toEqual({
      success: true,
      message: "Email submitted successfully",
    });
  });

  it('should get an article by ID', async () => {
    const article = await getArticleById(1);

    // Vérifier que l'article retourné a le bon ID et titre
    expect(article.id).toBe(1);
    expect(article.title).toBe("Article 1");

    // Vérifier que l'article a une propriété "content"
    expect(article).toHaveProperty('content');
    expect(article.content).toBe("Contenu de l'article");
  });

  it('should return a list of articles by theme', async () => {
    const theme = "Tech";
    const articles = await getMostPopularArticlesByTheme(theme);

    // Vérifier que la réponse est un tableau de 2 articles
    expect(articles).toHaveLength(2);

    // Vérifier que les titres des articles incluent le thème
    expect(articles[0].title).toBe("Article Tech 1");
    expect(articles[1].title).toBe("Article Tech 2");
  });

  it('should return a list of all articles', async () => {
    const articles = await getArticleList();

    // Vérifier que la réponse est un tableau de 2 articles
    expect(articles).toHaveLength(2);

    // Vérifier les titres des articles
    expect(articles[0].title).toBe("Article A");
    expect(articles[1].title).toBe("Article B");
  });

  it('should return a list of highlighted articles', async () => {
    const articles = await getHighlightedArticles();

    // Vérifier que la réponse est un tableau de 1 article
    expect(articles).toHaveLength(1);

    // Vérifier le titre de l'article en vedette
    expect(articles[0].title).toBe("Article en vedette");
  });

  it('should return the last two articles', async () => {
    const articles = await getLastTwoArticles();

    // Vérifier que la réponse est un tableau de 2 articles
    expect(articles).toHaveLength(2);

    // Vérifier les titres des articles
    expect(articles[0].title).toBe("Dernier article 1");
    expect(articles[1].title).toBe("Dernier article 2");
  });
});
