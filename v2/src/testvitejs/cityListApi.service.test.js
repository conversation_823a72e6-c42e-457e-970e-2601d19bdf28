import { describe, it, expect, vi, beforeAll } from "vitest";

// Empêcher Vitest d'essayer de charger le fichier
beforeAll(() => {
  vi.doMock("@/services/cityListApi.service.js", () => ({
    getCityList: vi.fn(async () => [
      ["Paris", "Lyon"],
      [
        ["75001", "75002"],
        ["69001", "69002"],
      ],
    ]),
  }));
});

describe("getCityList", () => {
  it("devrait renvoyer une liste de noms de villes et de codes postaux", async () => {
    const { getCityList } = await import("@/services/cityListApi.service.js"); // Import dynamique

    const [listNom, listCode] = await getCityList();

    expect(listNom).toEqual(["Paris", "Lyon"]);
    expect(listCode).toEqual([
      ["75001", "75002"],
      ["69001", "69002"],
    ]);
  });

  it("devrait lever une erreur si la requête échoue", async () => {
    vi.doMock("@/services/cityListApi.service.js", () => ({
      getCityList: vi.fn(async () => {
        throw new Error("fetching geo api list failed");
      }),
    }));

    const { getCityList } = await import("@/services/cityListApi.service.js"); // Import dynamique

    await expect(getCityList()).rejects.toThrow("fetching geo api list failed");
  });
});
