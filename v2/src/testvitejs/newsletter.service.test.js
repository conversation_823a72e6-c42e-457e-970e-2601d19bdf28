import { describe, it, expect, vi } from 'vitest';
import { submitEmailNewsletter, activate_newletter, desactivate_newletter } from '@/services/newsletter.service';

// Mock du module newsletter.service
vi.mock('@/services/newsletter.service', () => ({
  submitEmailNewsletter: vi.fn().mockResolvedValue({}),
  activate_newletter: vi.fn().mockResolvedValue({ success: true }),
  desactivate_newletter: vi.fn().mockResolvedValue({ success: true }),
}));

describe('Service de Newsletter', () => {
  // Test pour soumettre l'email à la newsletter
  it('devrait soumettre l\'email au service de newsletter', async () => {
    const result = await submitEmailNewsletter('<EMAIL>');
    expect(result).toEqual({});
    expect(submitEmailNewsletter).toHaveBeenCalledWith('<EMAIL>');
  });

  // Test pour activer la newsletter pour l\'email donné
  it('devrait activer la newsletter pour l\'email donné', async () => {
    const result = await activate_newletter('<EMAIL>');
    expect(result).toEqual({ success: true });
    expect(activate_newletter).toHaveBeenCalledWith('<EMAIL>');
  });

  // Test pour désactiver la newsletter pour l\'email donné
  it('devrait désactiver la newsletter pour l\'email donné', async () => {
    const result = await desactivate_newletter('<EMAIL>');
    expect(result).toEqual({ success: true });
    expect(desactivate_newletter).toHaveBeenCalledWith('<EMAIL>');
  });
});
