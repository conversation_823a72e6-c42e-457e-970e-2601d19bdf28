import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import createStore from './store';
import './registerServiceWorker';
// Import Font Awesome
import { library } from '@fortawesome/fontawesome-svg-core';
import { faMicrophone } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import 'element-plus/es/components/progress/style/css';
import { ElProgress } from 'element-plus';
// Importer les styles critiques en premier pour un chargement rapide
import './assets/css/critical.css';
import './assets/css/job-cards-priority.css';

// Importer les autres styles de manière asynchrone
const loadNonCriticalStyles = () => {
  import('vue3-toastify/dist/index.css');
  // Ajouter d'autres styles non critiques ici
};

// Charger les styles non critiques après le rendu initial
if (document.readyState === 'complete') {
  loadNonCriticalStyles();
} else {
  window.addEventListener('load', loadNonCriticalStyles);
}

import { ToastifyContainer as ToastContainer } from 'vue3-toastify';

import Cookie from './utils/cookie/Cookie';

// Vuetify - Chargement standard pour éviter les problèmes de compatibilité
import 'vuetify/styles';
import { createVuetify } from 'vuetify';
import * as components from 'vuetify/components';
import * as directives from 'vuetify/directives';

// Création de l'instance Vuetify
const vuetify = createVuetify({
  components,
  directives,
});

// import VueGtaf for google analytics
import VueGtag from 'vue-gtag';

// Import Pinia
import { createPinia } from 'pinia';

// Création de l'application Vue
const app = createApp(App);
app.use(createStore);
app.use(router);
app.use(vuetify);

// Création de l'instance Pinia
const pinia = createPinia();
app.use(pinia);

// Ajouter l'icône à la bibliothèque
library.add(faMicrophone);
// Enregistrer le composant globalement
app.component('font-awesome-icon', FontAwesomeIcon);

// Fonction d'initialisation de l'application
const initApp = async () => {
  // Vérifier l'authentification au démarrage
  const store = createStore;
  await store.dispatch('checkAuthOnStartup');

  // Monter l'application
  app.mount('#app');
};

// cookie consent
app.use(Cookie, {
  onChange: function ({ changedCategories, changedServices }) {
    if (
      changedServices &&
      changedServices['analytics'] &&
      changedServices['analytics'].includes('GoogleAnalytics')
    ) {
      // Vérifier si le service est accepté en utilisant l'API de Cookie correctement
      if (
        Cookie.acceptedService &&
        Cookie.acceptedService('GoogleAnalytics', 'analytics')
      ) {
        app.use(VueGtag, {
          config: {
            id: 'G-16Z5D1MHNC',
          },
        });
      } else {
        window.location.reload();
      }
    }
  },
  revision: 0,
  categories: {
    necessary: {
      enabled: true,
      readOnly: true,
    },
    analytics: {},
  },

  language: {
    default: 'fr',
    translations: {
      fr: {
        consentModal: {
          title: 'Thanks-boss et les cookies',
          description:
            'Nous utilisons les cookies pour améliorer votre expérience sur notre site web. En cliquant sur "Accepter tout", vous consentez à l\'utilisation de tous les cookies. Vous pouvez également <a href="https://thanks-boss.com/politique-confidentialite" target="_blank">lire notre Politique de Confidentialité</a> et nos <a href="https://thanks-boss.com/cgu" target="_blank">Conditions d\'Utilisation</a>.',
          acceptAllBtn: 'Accepter tout',
          acceptNecessaryBtn: 'Refuser tout',
          showPreferencesBtn: 'Gérer les préférences',
        },
        preferencesModal: {
          title: 'Préférences de cookies',
          acceptAllBtn: 'Accepter tout',
          acceptNecessaryBtn: 'Refuser tout',
          savePreferencesBtn: 'Sauvegarder les préférences',
          closeIconLabel: 'Fermer la fenêtre',
          sections: [
            {
              title: 'Cookies utilisés sur le site',
              description: '',
            },
            {
              title: 'Cookies de fonctionnement',
              description:
                'Ces cookies sont nécessaires au bon fonctionnement de notre site web.',
              linkedCategory: 'necessary',
            },
            {
              title: 'Cookies analytiques',
              description:
                "Ces cookies nous permettent de mesurer le trafic et d'analyser votre comportement sur notre site web. Ces cookies sont utilisés pour améliorer votre expérience utilisateur.",
              linkedCategory: 'analytics',
            },
          ],
        },
      },
    },
  },
});
app.component('ToastContainer', ToastContainer);
app.component('ElProgress', ElProgress);
// Démarrer l'application
initApp();

// Ajouter un gestionnaire pour les mises à jour du service worker
document.addEventListener('swUpdated', (event) => {
  const registration = event.detail;
  // Afficher une notification à l'utilisateur pour rafraîchir la page
  // Utiliser une méthode de notification compatible avec notre configuration
  if (
    window.confirm(
      'Une nouvelle version est disponible. Cliquez sur OK pour mettre à jour.'
    )
  ) {
    if (registration && registration.waiting) {
      // Envoyer un message au service worker pour qu'il prenne le contrôle immédiatement
      registration.waiting.postMessage({ type: 'SKIP_WAITING' });
    }
    window.location.reload();
  }
});
