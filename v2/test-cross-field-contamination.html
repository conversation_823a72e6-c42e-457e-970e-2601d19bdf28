<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Cross-Field Contamination Bug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-case {
            border: 1px solid #ddd;
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            background: #fafafa;
        }
        .test-case h3 {
            margin-top: 0;
            color: #333;
        }
        .test-link {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px 0;
        }
        .test-link:hover {
            background: #0056b3;
        }
        .description {
            color: #666;
            margin: 10px 0;
            font-style: italic;
        }
        .steps {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .expected {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .bug-report {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .console-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 3px;
            display: inline-block;
            margin: 5px 0;
        }
        .status.fixed {
            background: #d4edda;
            color: #155724;
        }
        .status.testing {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🐛 Test Cross-Field Contamination Bug</h1>
        
        <div class="bug-report">
            <h2>Bug Report</h2>
            <p><strong>Issue:</strong> Typing in both search fields causes unwanted cross-field updates</p>
            <p><strong>Symptom:</strong> Job title field gets overwritten with location value</p>
            <p><strong>Status:</strong> <span class="status testing">TESTING FIX</span></p>
        </div>

        <div class="console-info">
            <h3>📊 Debug Information</h3>
            <p><strong>Important:</strong> Ouvrez la console du navigateur (F12) pour voir les logs de débogage pendant les tests.</p>
            <p>Les logs montreront exactement quand et comment les champs sont mis à jour.</p>
        </div>

        <div class="test-case">
            <h3>Test 1: Reproduction du Bug Original</h3>
            <div class="description">
                Test pour reproduire le problème de contamination croisée entre les champs
            </div>
            
            <div class="steps">
                <h4>Étapes à suivre :</h4>
                <ol>
                    <li>Cliquez sur le lien ci-dessous pour aller à la page de recherche</li>
                    <li>Tapez "data" dans le champ "Emploi" (job title)</li>
                    <li>Tapez "paris" dans le champ "Lieu" (location)</li>
                    <li>Observez si le champ "Emploi" change incorrectement</li>
                    <li>Vérifiez les logs dans la console</li>
                </ol>
            </div>
            
            <div class="expected">
                <h4>Comportement attendu :</h4>
                <ul>
                    <li>✅ Champ "Emploi" doit rester "data"</li>
                    <li>✅ Champ "Lieu" doit rester "paris"</li>
                    <li>✅ Aucune contamination croisée</li>
                    <li>✅ Logs de débogage montrent des mises à jour correctes</li>
                </ul>
            </div>
            
            <a href="http://localhost:8080/recherche" class="test-link" target="_blank">
                🧪 Tester la Page de Recherche
            </a>
        </div>

        <div class="test-case">
            <h3>Test 2: Test avec Paramètres URL Pré-remplis</h3>
            <div class="description">
                Test avec des valeurs pré-remplies via l'URL pour vérifier l'initialisation
            </div>
            
            <div class="steps">
                <h4>Étapes à suivre :</h4>
                <ol>
                    <li>Cliquez sur le lien ci-dessous (champs pré-remplis)</li>
                    <li>Vérifiez que les champs sont correctement initialisés</li>
                    <li>Modifiez un champ à la fois</li>
                    <li>Vérifiez qu'il n'y a pas d'interférence</li>
                </ol>
            </div>
            
            <a href="http://localhost:8080/recherche?title=développeur&ville=Lyon" class="test-link" target="_blank">
                🧪 Tester avec Valeurs Pré-remplies
            </a>
        </div>

        <div class="test-case">
            <h3>Test 3: Test de Suppression des Champs</h3>
            <div class="description">
                Test des boutons de suppression (clear) pour vérifier qu'ils n'affectent que leur propre champ
            </div>
            
            <div class="steps">
                <h4>Étapes à suivre :</h4>
                <ol>
                    <li>Remplissez les deux champs</li>
                    <li>Cliquez sur le bouton "X" du champ "Emploi"</li>
                    <li>Vérifiez que seul ce champ est vidé</li>
                    <li>Répétez pour le champ "Lieu"</li>
                </ol>
            </div>
            
            <a href="http://localhost:8080/recherche?title=test&ville=test" class="test-link" target="_blank">
                🧪 Tester la Suppression des Champs
            </a>
        </div>

        <div class="test-case">
            <h3>Test 4: Test de Performance et Réactivité</h3>
            <div class="description">
                Test de saisie rapide pour détecter des problèmes de synchronisation
            </div>
            
            <div class="steps">
                <h4>Étapes à suivre :</h4>
                <ol>
                    <li>Tapez rapidement dans le champ "Emploi"</li>
                    <li>Passez immédiatement au champ "Lieu" et tapez rapidement</li>
                    <li>Alternez entre les champs plusieurs fois</li>
                    <li>Vérifiez que les valeurs restent correctes</li>
                </ol>
            </div>
            
            <a href="http://localhost:8080/recherche" class="test-link" target="_blank">
                🧪 Tester la Saisie Rapide
            </a>
        </div>

        <h2>🔧 Corrections Appliquées</h2>
        <ul>
            <li>✅ Amélioration des gestionnaires d'événements @input</li>
            <li>✅ Gestion robuste des différents types de valeurs (événement vs valeur directe)</li>
            <li>✅ Ajout de vérifications pour éviter les mises à jour inutiles</li>
            <li>✅ Ajout d'identifiants uniques aux champs</li>
            <li>✅ Logs de débogage pour tracer les problèmes</li>
            <li>✅ Watchers pour détecter les changements non désirés</li>
        </ul>

        <h2>📋 Instructions de Test</h2>
        <ol>
            <li><strong>Ouvrez la console du navigateur</strong> (F12 → Console) avant de commencer</li>
            <li><strong>Testez chaque scénario</strong> en suivant les étapes décrites</li>
            <li><strong>Surveillez les logs</strong> pour identifier tout comportement anormal</li>
            <li><strong>Vérifiez l'URL</strong> pour confirmer que les paramètres sont correctement mis à jour</li>
            <li><strong>Testez sur différents navigateurs</strong> si possible</li>
        </ol>

        <div class="console-info">
            <h3>🔍 Que Chercher dans les Logs</h3>
            <ul>
                <li><code>[DEBUG] handleTitleInput:</code> - Logs du champ métier</li>
                <li><code>[DEBUG] handleVilleInput:</code> - Logs du champ lieu</li>
                <li><code>[DEBUG] formData.title changed:</code> - Changements du titre</li>
                <li><code>[DEBUG] formData.ville changed:</code> - Changements de la ville</li>
            </ul>
            <p><strong>Attention:</strong> Si vous voyez des changements inattendus ou des stack traces suspectes, cela indique un problème.</p>
        </div>
    </div>
</body>
</html>
