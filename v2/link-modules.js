const fs = require('fs');
const path = require('path');

// Chemin vers le module vue-template-compiler
const sourceModule = path.join(__dirname, 'node_modules', 'vue-template-compiler');

// Chemin vers les répertoires où le module pourrait être recherché
const targetDirs = [
  path.join(__dirname, 'node_modules', '@vue', 'vue-loader-v15', 'node_modules'),
  path.join(__dirname, 'node_modules', '@vue', 'cli-service', 'node_modules')
];

// Créer les répertoires cibles s'ils n'existent pas
targetDirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    //console.log(`Création du répertoire: ${dir}`);
    fs.mkdirSync(dir, { recursive: true });
  }
  
  const targetModule = path.join(dir, 'vue-template-compiler');
  
  // Vérifier si le module source existe
  if (fs.existsSync(sourceModule)) {
    //console.log(`Module source trouvé: ${sourceModule}`);
    
    // Créer un lien symbolique
    try {
      if (!fs.existsSync(targetModule)) {
        //console.log(`Création d'un lien symbolique vers: ${targetModule}`);
        fs.symlinkSync(sourceModule, targetModule, 'junction');
        //console.log('Lien symbolique créé avec succès!');
      } else {
        //console.log(`Le module existe déjà à: ${targetModule}`);
      }
    } catch (error) {
      //console.error(`Erreur lors de la création du lien symbolique: ${error.message}`);
    }
  } else {
    //console.error(`Module source non trouvé: ${sourceModule}`);
  }
});

//console.log('Terminé!');
