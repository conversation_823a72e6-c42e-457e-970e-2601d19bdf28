# Guide de Test - Améliorations des Cartes d'Offres d'Emploi

## Résumé des Modifications

### ✅ PRIORITÉ 1 - Correction du Bug 404 (RÉSOLU)
- **Problème** : Erreurs 404 lors du clic sur "Voir l'offre"
- **Solution** : Création d'un utilitaire de navigation unifié (`jobNavigation.js`)
- **Composants modifiés** :
  - `LargeJobCard.vue`
  - `CompactJobCard.vue` 
  - `MiniCard.vue`

### ✅ PRIORITÉ 2 - Design Inspiré HelloWork (IMPLÉMENTÉ)
- **Nouveau design** : Cartes modernes avec bordures subtiles, ombres douces
- **Couleurs** : <PERSON><PERSON> (blancs, gris, bleus)
- **Typographie** : Hiérarchie claire et lisible
- **Interactions** : Effets de survol avec translation et ombres

### ✅ PRIORITÉ 3 - Système de Priorité Thanks-Boss (IMPLÉMENTÉ)
- **Logique** : Priorité visuelle pour les offres "Thanks-Boss" pendant 14 jours
- **Indicateurs visuels** : Bordures bleues, badges "PRIORITÉ", étoiles
- **Tri automatique** : Offres prioritaires en premier dans les listes

## Tests à Effectuer

### 1. Test de Navigation (Critique)

#### Test 1.1 - LargeJobCard
1. Aller sur la page de recherche d'emploi
2. Localiser une carte d'offre grande format
3. Cliquer sur "Voir l'offre"
4. **Résultat attendu** : Navigation vers la page de détail sans erreur 404

#### Test 1.2 - CompactJobCard  
1. Aller sur une page avec des cartes compactes
2. Cliquer sur "Voir l'offre"
3. **Résultat attendu** : Navigation correcte

#### Test 1.3 - MiniCard
1. Localiser une mini-carte d'offre
2. Cliquer sur "Voir l'offre"
3. **Résultat attendu** : Ouverture dans un nouvel onglet

#### Test 1.4 - Offres Externes (France Travail)
1. Trouver une offre avec source "FT"
2. Cliquer sur "Voir l'offre"
3. **Résultat attendu** : Redirection vers le site externe

### 2. Test du Design HelloWork

#### Test 2.1 - Apparence Générale
- **Bordures** : Fines, grises (#e5e7eb)
- **Ombres** : Subtiles au repos, plus prononcées au survol
- **Coins arrondis** : 12px de rayon
- **Couleurs de fond** : Blanc pur (#ffffff)

#### Test 2.2 - Effets de Survol
1. Survoler une carte d'offre
2. **Résultat attendu** :
   - Translation vers le haut (-2px)
   - Ombre plus prononcée
   - Transition fluide (0.2s)

#### Test 2.3 - Typographie
- **Titres** : 16px, poids 600, couleur #111827
- **Entreprise** : 14px, poids 500, couleur #6b7280
- **Localisation** : 13px, couleur #9ca3af
- **Salaire** : 14px, poids 600, couleur #059669

### 3. Test du Système de Priorité

#### Test 3.1 - Identification des Offres Prioritaires
1. Rechercher des offres avec source "Thanks_boss"
2. Vérifier la date de création (< 14 jours)
3. **Résultat attendu** :
   - Bordure bleue (#3b82f6)
   - Badge "PRIORITÉ" en haut à droite
   - Ombre bleue subtile

#### Test 3.2 - Tri par Priorité
1. Charger une liste d'offres mixtes
2. **Résultat attendu** :
   - Offres Thanks-Boss récentes en premier
   - Puis autres offres par date décroissante

#### Test 3.3 - Expiration de la Priorité
1. Vérifier une offre Thanks-Boss > 14 jours
2. **Résultat attendu** : Pas de styling prioritaire

### 4. Test de Responsivité

#### Test 4.1 - Mobile (< 768px)
- Badge "PRIORITÉ" plus petit
- Cartes s'adaptent à la largeur
- Textes restent lisibles

#### Test 4.2 - Tablette (768px - 1024px)
- Disposition en grille adaptée
- Espacement approprié

#### Test 4.3 - Desktop (> 1024px)
- Affichage optimal des cartes
- Tous les éléments visibles

### 5. Test de Performance

#### Test 5.1 - Build de Production
```bash
cd v2
npm run build
```
**Résultat attendu** : Build réussi sans erreurs

#### Test 5.2 - Temps de Chargement
- Vérifier que les nouvelles CSS n'impactent pas les performances
- Tester le tri des offres sur de grandes listes

## Fichiers Modifiés

### Nouveaux Fichiers
- `v2/src/utils/jobNavigation.js` - Utilitaire de navigation
- `v2/src/assets/css/job-cards-priority.css` - Styles de priorité
- `v2/.env` - Variables d'environnement corrigées

### Fichiers Modifiés
- `v2/src/components/cards/job-cards/LargeJobCard.vue`
- `v2/src/components/cards/job-cards/CompactJobCard.vue`
- `v2/src/components/cards/job-cards/MiniCard.vue`
- `v2/src/views/candidate/search/Search.vue`
- `v2/src/main.js`
- `v2/vue.config.js` (obfuscation désactivée)

## Problèmes Résolus

### 1. Bug 404 Navigation
- **Cause** : Méthodes de navigation incohérentes
- **Solution** : Utilitaire centralisé avec gestion d'erreurs

### 2. Build Freezing
- **Cause** : Plugin webpack-obfuscator trop lourd
- **Solution** : Désactivation temporaire du plugin

### 3. Variables d'Environnement
- **Cause** : Mismatch entre VUE_APP_BACKEND_BASE_URL et VUE_APP_API_BASE_URL
- **Solution** : Ajout de la variable manquante

## Recommandations Post-Test

1. **Monitoring** : Surveiller les erreurs 404 en production
2. **Performance** : Mesurer l'impact du tri prioritaire sur de gros volumes
3. **UX** : Recueillir les retours utilisateurs sur le nouveau design
4. **Obfuscation** : Réimplémenter avec une configuration plus légère

## Commandes Utiles

```bash
# Développement
cd v2
npm run start

# Build de production
npm run build

# Test de l'application
# Ouvrir http://localhost:8080 dans le navigateur
```
