{"name": "thanks-boss", "version": "0.1.0", "private": true, "scripts": {"start": "vue-cli-service serve", "build": "vue-cli-service build", "test:unit": "vitest"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@microsoft/fetch-event-source": "^2.0.1", "@vue-stripe/vue-stripe": "^4.5.0", "@vueform/multiselect": "^2.6.11", "axios": "^1.7.7", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "element-plus": "^2.9.10", "emoji-picker-element": "^1.26.3", "express": "^5.1.0", "express-basic-auth": "^1.2.1", "html2pdf.js": "^0.10.2", "iso-639-1": "^3.1.3", "lucide-vue-next": "^0.487.0", "marked": "^15.0.7", "pdfmake": "^0.2.18", "playwright": "^1.52.0", "pinia": "^3.0.2", "quill": "^2.0.2", "register-service-worker": "^1.7.2", "simple-peer": "^9.11.1", "swiper": "^11.2.6", "uuid": "^10.0.0", "vanilla-cookieconsent": "^3.0.1", "vfs": "^0.2.2", "vue": "^3.5.6", "vue-cal": "^4.10.2", "vue-gtag": "^2.1.0", "vue-pdf-embed": "^2.1.1", "vue-router": "^4.4.5", "vue3-google-login": "^2.0.33", "vue3-tags-input": "^1.0.12", "vue3-toastify": "^0.2.3", "vuedraggable": "^4.1.0", "vuetify": "^3.7.6", "vuex": "^4.1.0"}, "devDependencies": {"@babel/eslint-parser": "^7.25.9", "@faker-js/faker": "^9.6.0", "@playwright/test": "^1.52.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-router": "~5.0.8", "@vue/cli-plugin-vuex": "~5.0.8", "@vue/cli-service": "^5.0.8", "@vue/test-utils": "^2.4.6", "axios-mock-adapter": "^2.1.0", "eslint": "^9.16.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.32.0", "globals": "^15.13.0", "jsdom": "^26.0.0", "open": "^10.1.2", "prettier": "^3.4.2", "vite": "^6.2.1", "vitest": "^3.0.8", "vue-eslint-parser": "^9.4.3", "vue-template-compiler": "^2.7.16", "webpack-obfuscator": "^3.5.1"}}