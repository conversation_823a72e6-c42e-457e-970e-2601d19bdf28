const { defineConfig } = require("@vue/cli-service");
const WebpackObfuscator = require('webpack-obfuscator');

module.exports = defineConfig({
  transpileDependencies: true,
  publicPath: '/',
  productionSourceMap: false, // Empêche l'accès au code source via F12

  // Configuration pour le serveur de développement
  devServer: {
    historyApiFallback: true, // Support pour Vue Router en mode history
  },

  configureWebpack: (config) => {
    if (process.env.NODE_ENV === 'production') {
      // Supprime les //console.log en production
      if (config.optimization && config.optimization.minimizer &&
        config.optimization.minimizer[0] &&
        config.optimization.minimizer[0].options &&
        config.optimization.minimizer[0].options.terserOptions &&
        config.optimization.minimizer[0].options.terserOptions.compress) {
        config.optimization.minimizer[0].options.terserOptions.compress.drop_console = true;
      }

      // TEMPORARILY DISABLED: Obfuscation du code JS en production - causes build freezing
      // TODO: Re-enable with lighter configuration or alternative solution
      /*
      config.plugins.push(
        new WebpackObfuscator(
          {
            rotateStringArray: false,  // Désactivé car très gourmand en ressources
            stringArray: true,
            stringArrayEncoding: ['none'], // Utilise 'none' pour désactiver l'encodage mais garder le format correct
            stringArrayThreshold: 0.3,  // Réduit à 30% des chaînes pour accélérer le build
            deadCodeInjection: false,   // Désactivé pour accélérer le build
            selfDefending: false,       // Désactivé pour accélérer le build
            simplify: true              // Simplifie le code pour améliorer les performances
          },
          ['excluded.bundle.js', 'vendor.js'] // Exclut aussi les fichiers vendor pour accélérer le build
        )
      );
      */
    }
  }
});
