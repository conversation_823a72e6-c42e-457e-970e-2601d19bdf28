import babelEslintParser from '@babel/eslint-parser';
import config<PERSON>rettier from 'eslint-config-prettier';
import pluginPrettier from 'eslint-plugin-prettier';
import pluginVue from 'eslint-plugin-vue';
import vueEslintParser from 'vue-eslint-parser';

export default [
  {
    files: ['**/*.{js,vue}'],
    languageOptions: {
      parser: vueEslintParser,
      parserOptions: {
        parser: babelEslintParser,
        ecmaVersion: 'latest',
        sourceType: 'module',
        requireConfigFile: false,
      },
    },
    plugins: {
      vue: pluginVue,
      prettier: pluginPrettier,
    },
    rules: {
      ...pluginVue.configs['essential'].rules,
      'prettier/prettier': 'error',
      'vue/no-multiple-template-root': 'off',
      'linebreak-style': 'off',
    },
  },
  configPrettier,
];
