<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Chat IA</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .chat-container {
            border: 1px solid #ccc;
            height: 400px;
            overflow-y: auto;
            padding: 10px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
        }
        .message {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .user-message {
            background-color: #007bff;
            color: white;
            text-align: right;
        }
        .ai-message {
            background-color: #e9ecef;
            color: black;
        }
        .input-container {
            display: flex;
            gap: 10px;
        }
        input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .logs {
            margin-top: 20px;
            padding: 10px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Test Chat IA - Debug</h1>
    
    <div class="chat-container" id="chatContainer">
        <!-- Messages apparaîtront ici -->
    </div>
    
    <div class="input-container">
        <input type="text" id="messageInput" placeholder="Tapez votre message..." />
        <button id="sendButton" onclick="sendMessage()">Envoyer</button>
    </div>
    
    <div class="logs" id="logs">
        <strong>Logs de débogage :</strong><br>
    </div>

    <script type="module">
        import { fetchEventSource } from 'https://cdn.skypack.dev/@microsoft/fetch-event-source';
        
        window.fetchEventSource = fetchEventSource;
        
        let streamedMessage = [];
        
        function log(message) {
            const logs = document.getElementById('logs');
            logs.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
            logs.scrollTop = logs.scrollHeight;
        }
        
        function addMessage(text, isUser = false) {
            const chatContainer = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + (isUser ? 'user-message' : 'ai-message');
            messageDiv.textContent = text;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        function updateStreamedMessage() {
            // Supprimer l'ancien message streamé s'il existe
            const existingStreamedMessage = document.querySelector('.ai-message.streamed');
            if (existingStreamedMessage) {
                existingStreamedMessage.remove();
            }
            
            // Ajouter le nouveau message streamé
            if (streamedMessage.length > 0) {
                const chatContainer = document.getElementById('chatContainer');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message ai-message streamed';
                messageDiv.textContent = streamedMessage.join('');
                chatContainer.appendChild(messageDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }
        }
        
        window.sendMessage = async function() {
            const input = document.getElementById('messageInput');
            const button = document.getElementById('sendButton');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Ajouter le message utilisateur
            addMessage(message, true);
            input.value = '';
            button.disabled = true;
            
            // Réinitialiser le message streamé
            streamedMessage = [];
            
            log('Envoi du message: ' + message);
            
            try {
                const formData = new FormData();
                formData.append('phrase', message);
                formData.append('conversation_id', '123');
                formData.append('message_id', '456');
                
                log('Début du streaming...');
                
                await fetchEventSource('https://ianew.thanks-boss.com/api_llm', {
                    method: 'POST',
                    headers: {
                        'x-api-key': 'thanks-boss-team-test',
                    },
                    body: formData,
                    onmessage: (ev) => {
                        log('Message reçu: ' + JSON.stringify(ev.data));
                        if (ev.data) {
                            streamedMessage.push(ev.data);
                            updateStreamedMessage();
                        }
                    },
                    onerror: (error) => {
                        log('Erreur: ' + error);
                        throw error;
                    },
                    onclose: () => {
                        log('Stream fermé');
                    }
                });
                
                log('Streaming terminé');
                
            } catch (error) {
                log('Erreur lors de l\'envoi: ' + error);
                addMessage('Erreur: ' + error.message, false);
            } finally {
                button.disabled = false;
                streamedMessage = [];
            }
        }
        
        // Permettre l'envoi avec Entrée
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        log('Page de test chargée');
    </script>
</body>
</html>
