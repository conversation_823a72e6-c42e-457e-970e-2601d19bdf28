<!doctype html>
<html lang="fr">
  <head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width,initial-scale=1.0"/>
    <meta
      name="description"
      content="Découvrez une application révolutionnaire de recherche d'emploi assistée par intelligence artificielle, conçue pour vous connecter rapidement aux meilleures opportunités professionnelles."
/>
    <meta
      name="keywords"
      content="recherche d'emploi, intelligence artificielle, IA, job, offres d'emploi, carrière, emploi, recrutement, machine learning, recherche de travail, assistant emploi, matching emploi, job matching, technologies de l'emploi, recherche automatisée d'emploi, algorithmes d'emploi"
/>
    <meta name="author" content="DAM Florentin"/>
    <meta name="robots" content="all"/>

    <meta property="og:locale" content="fr-FR"/>
    <meta property="og:type" content="website"/>
    <meta property="og:title" content="Thanks-boss"/>
    <meta
      property="og:description"
      content="Découvrez notre plateforme innovante de recherche d'emploi, optimisée par une intelligence artificielle pour vous aider à trouver les meilleures opportunités."
/>
    <meta
      property="og:image"
      content="https://media.licdn.com/dms/image/v2/D560BAQH_OJ8KkpOELg/company-logo_100_100/company-logo_100_100/0/*************/thanks_boss_logo?e=**********&v=beta&t=ehzmz0agHPwX4oLI79LsAyDG-GR2JHPVIUWvRYvmMWw"
/>
    <meta property="og:image:width" content="941"/>
    <meta property="og:image:height" content="657"/>
    <meta
      property="og:url"
      content="https://media.licdn.com/dms/image/v2/D560BAQH_OJ8KkpOELg/company-logo_100_100/company-logo_100_100/0/*************/thanks_boss_logo?e=**********&v=beta&t=ehzmz0agHPwX4oLI79LsAyDG-GR2JHPVIUWvRYvmMWw"
    />
    <meta property="og:site_name" content="Thanks-boss" />
    <meta name="google-adsense-account" content="ca-pub-****************" />
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************" crossorigin="anonymous"></script>
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-HVPKPQHS08"></script>
    <!-- Google Tag Manager -->
    <script>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({'gtm.start': new Date().getTime(), event: 'gtm.js'});
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != 'dataLayer'
            ? '&l=' + l
            : '';
        j.async = true;
        j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
        f
          .parentNode
          .insertBefore(j, f);
      })(window, document, 'script', 'dataLayer', 'GTM-NCBVHGGH');
    </script>
    <!-- End Google Tag Manager -->

    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
     crossorigin="anonymous"></script>

    <!-- anton font -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Préchargement des polices critiques -->
    <link
      rel="preload"
      as="style"
      href="https://fonts.googleapis.com/css2?family=Anton&family=Roboto:wght@400;500;700&display=swap"
    />

    <!-- Chargement des polices avec font-display: swap -->
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Anton&family=Roboto:wght@400;500;700&display=swap"
      media="print"
      onload="this.media='all'"
    />

    <!-- Fallback pour les navigateurs qui ne supportent pas onload -->
    <noscript>
      <link
        rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Anton&family=Roboto:wght@400;500;700&display=swap"
      />
    </noscript>

    <!-- Chargement différé des variantes de polices non critiques -->
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Average+Sans&family=Roboto:ital,wght@0,100;0,300;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap"
      media="print"
      onload="this.media='all'"
    />

    <!-- Chargement optimisé de Font Awesome -->
    <link
      rel="preload"
      as="style"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css"
      media="print"
      onload="this.media='all'"
    />

    <!-- Chargement optimisé des icônes Material Design -->
    <link
      rel="preload"
      as="style"
      href="https://cdn.jsdelivr.net/npm/@mdi/font@5.x/css/materialdesignicons.min.css"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/@mdi/font@5.x/css/materialdesignicons.min.css"
      rel="stylesheet"
      media="print"
      onload="this.media='all'"
    />

    <link rel="icon" href="<%= BASE_URL %>favicon.png"/>
    <link rel="stylesheet"/>

    <title>Thanks-boss</title>
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
        crossorigin="anonymous"></script>
    <script
      type="text/plain"
      data-category="analytics"
      data-service="GoogleAnalytics"
    ></script>
    <!-- Google tag (gtag.js) -->

    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag('js', new Date());

      gtag('config', 'G-HVPKPQHS08');
    </script>

    <!-- TruConversion for app.thanks-boss.com -->
    <script
      type="text/plain"
      data-category="analytics"
      data-service="truConversion"
    ></script>

    <!-- TruConversion uniquement en production -->
    <script type="text/javascript">
      if (window.location.hostname !== 'localhost') {
        var _tip = _tip || [];

        (function (d, s, id) {
          var js,
            tjs = d.getElementsByTagName(s)[0];
          if (d.getElementById(id)) {
            return;
          }
          js = d.createElement(s);
          js.id = id;
          js.async = true;
          js.src = d.location.protocol + '//app.truconversion.com/ti-js/37119/e4e31.js';
          tjs
            .parentNode
            .insertBefore(js, tjs);
        })(document, 'script', 'ti-js');

        _tip.push(['_trackIdentity', '37119']);
      } else {
        //console.log('TruConversion désactivé en local');
      }
    </script>
  </head>

  <body>
    <!-- Google Tag Manager (noscript) -->
    <noscript>
      <iframe
        src="https://www.googletagmanager.com/ns.html?id=GTM-NCBVHGGH"
        height="0"
        width="0"
        style="display: none; visibility: hidden"
      ></iframe>
    </noscript>
    <!-- End Google Tag Manager (noscript) -->

    <noscript>
      <strong
        >We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work
        properly without JavaScript enabled. Please enable it to
        continue.</strong
      >
    </noscript>
    <div id="app">
      <!-- Loader initial qui sera remplacé par l'application -->
      <div
        style="
          display: flex;
          justify-content: center;
          align-items: center;
          min-height: 100vh;
        "
      >
        <div
          style="
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #f6b337;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          "
        ></div>
      </div>
    </div>
    <!-- built files will be auto injected -->

    <style>
      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    </style>
  </body>
</html>