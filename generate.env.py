import sys

def main(application_name, environment, username, password):


  

    # Vault path for secrets
    vault_path = f"secret/data/{application_name}/{environment}"

    # Retrieve secrets
    try:
        secret = client.secrets.kv.v2.read_secret_version(path=vault_path.replace("secret/data/", ""))
        secrets = secret['data']['data']
        print(f"Secrets retrieved from {vault_path}: {secrets}")
    except Exception as e:
        print(f"Failed to retrieve secrets: {e}")
        sys.exit(1)

    # Write secrets to .env file
    try:
        with open(".env", "w") as env_file:
            for key, value in secrets.items():
                env_file.write(f"{key}={value}\n")
        print("Secrets have been written to .env file.")
    except Exception as e:
        print(f"Failed to write to .env file: {e}")
        sys.exit(1)

if __name__ == "__main__":
    if len(sys.argv) != 5:
        print("Usage: python3 generate_env.py <application_name> <environment> <username> <password>")
        sys.exit(1)

    application_name = sys.argv[1]
    environment = sys.argv[2]
    username = sys.argv[3]
    password = sys.argv[4]

    main(application_name, environment, username, password)
