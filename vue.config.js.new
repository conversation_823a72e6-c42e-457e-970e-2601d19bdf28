const { defineConfig } = require('@vue/cli-service');

module.exports = defineConfig({
  transpileDependencies: true,
  publicPath: '/',
  lintOnSave: process.env.NODE_ENV !== 'production',
  filenameHashing: true,

  // Configuration pour améliorer les performances
  configureWebpack: {
    // Optimisation pour la production
    optimization: {
      splitChunks: {
        chunks: 'all',
        maxInitialRequests: Infinity,
        minSize: 20000,
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name(module) {
              // Obtenir le nom du package à partir du chemin du module
              if (!module.context) return 'vendors';
              const match = module.context.match(/[\\/]node_modules[\\/](.*?)([\\]|$)/);
              const packageName = match ? match[1] : 'vendors';
              // Retourner un nom de chunk pour les modules npm
              return `npm.${packageName.replace('@', '')}`;
            },
          },
        },
      },
    },
  },

  // Configuration CSS
  css: {
    extract: process.env.NODE_ENV === 'production',
    sourceMap: process.env.NODE_ENV !== 'production',
    loaderOptions: {
      css: {
        // Options pour css-loader
      },
      postcss: {
        // Options pour postcss-loader
      },
    },
  },

  // Configuration du serveur de développement
  devServer: {
    compress: true,
    hot: true,
    open: false,
    client: {
      overlay: {
        warnings: false,
        errors: true,
      },
    },
  },
});
